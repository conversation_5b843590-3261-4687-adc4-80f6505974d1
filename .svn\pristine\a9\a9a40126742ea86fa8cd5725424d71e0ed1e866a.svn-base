﻿namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment
{
    partial class ResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.btnFilter = new System.Windows.Forms.Button();
            this.grpNear = new System.Windows.Forms.GroupBox();
            this.chkNearFar = new System.Windows.Forms.CheckBox();
            this.chkNearMulti = new System.Windows.Forms.CheckBox();
            this.chkNearOverCvr = new System.Windows.Forms.CheckBox();
            this.chkNearSINR = new System.Windows.Forms.CheckBox();
            this.chkNearOverHo = new System.Windows.Forms.CheckBox();
            this.chkNearWeak = new System.Windows.Forms.CheckBox();
            this.grpFar = new System.Windows.Forms.GroupBox();
            this.chkFarFar = new System.Windows.Forms.CheckBox();
            this.chkFarMulti = new System.Windows.Forms.CheckBox();
            this.chkFarOverCvr = new System.Windows.Forms.CheckBox();
            this.chkFarSINR = new System.Windows.Forms.CheckBox();
            this.chkFarOverHo = new System.Windows.Forms.CheckBox();
            this.chkFarWeak = new System.Windows.Forms.CheckBox();
            this.grpHigh = new System.Windows.Forms.GroupBox();
            this.chkHighFar = new System.Windows.Forms.CheckBox();
            this.chkHighMulti = new System.Windows.Forms.CheckBox();
            this.chkHighOverCvr = new System.Windows.Forms.CheckBox();
            this.chkHighSINR = new System.Windows.Forms.CheckBox();
            this.chkHighOverHo = new System.Windows.Forms.CheckBox();
            this.chkHighWeak = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.grpNear.SuspendLayout();
            this.grpFar.SuspendLayout();
            this.grpHigh.SuspendLayout();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.ctxMenu;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1017, 365);
            this.gridControl.TabIndex = 0;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport
            // 
            this.miExport.Name = "miExport";
            this.miExport.Size = new System.Drawing.Size(138, 22);
            this.miExport.Text = "导出Excel...";
            this.miExport.Click += new System.EventHandler(this.miExport_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn11,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn10,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn21});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "序号";
            this.gridColumn11.FieldName = "SN";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 0;
            this.gridColumn11.Width = 47;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "小区";
            this.gridColumn1.FieldName = "CellName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 1;
            this.gridColumn1.Width = 116;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "三超归属问题";
            this.gridColumn2.FieldName = "UltraType";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            this.gridColumn2.Width = 110;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "关联问题";
            this.gridColumn3.FieldName = "Prob";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            this.gridColumn3.Width = 92;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "关联道路";
            this.gridColumn10.FieldName = "RoadDesc";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 4;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "问题小区采样点";
            this.gridColumn4.FieldName = "SegProbPointNum";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 5;
            this.gridColumn4.Width = 100;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "路段总采样点";
            this.gridColumn5.FieldName = "SegPointNum";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 6;
            this.gridColumn5.Width = 98;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "问题小区采样点占比";
            this.gridColumn6.FieldName = "SegProbPer";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 7;
            this.gridColumn6.Width = 107;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "问题小区切换次数";
            this.gridColumn7.FieldName = "CellNum";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 8;
            this.gridColumn7.Width = 103;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "路段总切换次数";
            this.gridColumn8.FieldName = "HoTimes";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 9;
            this.gridColumn8.Width = 105;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "问题小区切换次数占比";
            this.gridColumn9.FieldName = "ProbCellPer";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 10;
            this.gridColumn9.Width = 99;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "持续距离(m)";
            this.gridColumn14.FieldName = "Distance";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 13;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "最大RSRP";
            this.gridColumn15.FieldName = "RSRPMax";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 14;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "最小RSRP";
            this.gridColumn16.FieldName = "RSRPMin";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 15;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "平均RSRP";
            this.gridColumn17.FieldName = "RSRPAvg";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 16;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "最大SINR";
            this.gridColumn18.FieldName = "SINRMax";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 17;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "最小SINR";
            this.gridColumn19.FieldName = "SINRMin";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 18;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "平均SINR";
            this.gridColumn20.FieldName = "SINRAvg";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 19;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "中心经度";
            this.gridColumn12.FieldName = "MidLng";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 11;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "中心纬度";
            this.gridColumn13.FieldName = "MidLat";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 12;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "切换序列";
            this.gridColumn21.FieldName = "HoDesc";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 20;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox1);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControl);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1017, 499);
            this.splitContainerControl1.SplitterPosition = 128;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.btnFilter);
            this.groupBox1.Controls.Add(this.grpNear);
            this.groupBox1.Controls.Add(this.grpFar);
            this.groupBox1.Controls.Add(this.grpHigh);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1017, 128);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "结果过滤设置";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.Red;
            this.label1.Location = new System.Drawing.Point(29, 99);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(451, 14);
            this.label1.TabIndex = 2;
            this.label1.Text = "说明：勾选项目为过滤项。如，超近勾选弱覆盖时，视为超近站将过滤弱覆盖结果。";
            // 
            // btnFilter
            // 
            this.btnFilter.Location = new System.Drawing.Point(916, 99);
            this.btnFilter.Name = "btnFilter";
            this.btnFilter.Size = new System.Drawing.Size(75, 23);
            this.btnFilter.TabIndex = 1;
            this.btnFilter.Text = "过滤";
            this.btnFilter.UseVisualStyleBackColor = true;
            this.btnFilter.Click += new System.EventHandler(this.btnFilter_Click);
            // 
            // grpNear
            // 
            this.grpNear.Controls.Add(this.chkNearFar);
            this.grpNear.Controls.Add(this.chkNearMulti);
            this.grpNear.Controls.Add(this.chkNearOverCvr);
            this.grpNear.Controls.Add(this.chkNearSINR);
            this.grpNear.Controls.Add(this.chkNearOverHo);
            this.grpNear.Controls.Add(this.chkNearWeak);
            this.grpNear.Location = new System.Drawing.Point(680, 21);
            this.grpNear.Name = "grpNear";
            this.grpNear.Size = new System.Drawing.Size(328, 70);
            this.grpNear.TabIndex = 0;
            this.grpNear.TabStop = false;
            this.grpNear.Text = "超近站";
            // 
            // chkNearFar
            // 
            this.chkNearFar.AutoSize = true;
            this.chkNearFar.Location = new System.Drawing.Point(236, 21);
            this.chkNearFar.Name = "chkNearFar";
            this.chkNearFar.Size = new System.Drawing.Size(74, 18);
            this.chkNearFar.TabIndex = 0;
            this.chkNearFar.Text = "过远覆盖";
            this.chkNearFar.UseVisualStyleBackColor = true;
            // 
            // chkNearMulti
            // 
            this.chkNearMulti.AutoSize = true;
            this.chkNearMulti.Location = new System.Drawing.Point(156, 21);
            this.chkNearMulti.Name = "chkNearMulti";
            this.chkNearMulti.Size = new System.Drawing.Size(74, 18);
            this.chkNearMulti.TabIndex = 0;
            this.chkNearMulti.Text = "重叠覆盖";
            this.chkNearMulti.UseVisualStyleBackColor = true;
            // 
            // chkNearOverCvr
            // 
            this.chkNearOverCvr.AutoSize = true;
            this.chkNearOverCvr.Checked = true;
            this.chkNearOverCvr.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkNearOverCvr.Location = new System.Drawing.Point(88, 21);
            this.chkNearOverCvr.Name = "chkNearOverCvr";
            this.chkNearOverCvr.Size = new System.Drawing.Size(62, 18);
            this.chkNearOverCvr.TabIndex = 0;
            this.chkNearOverCvr.Text = "过覆盖";
            this.chkNearOverCvr.UseVisualStyleBackColor = true;
            // 
            // chkNearSINR
            // 
            this.chkNearSINR.AutoSize = true;
            this.chkNearSINR.Location = new System.Drawing.Point(156, 46);
            this.chkNearSINR.Name = "chkNearSINR";
            this.chkNearSINR.Size = new System.Drawing.Size(74, 18);
            this.chkNearSINR.TabIndex = 0;
            this.chkNearSINR.Text = "质差路段";
            this.chkNearSINR.UseVisualStyleBackColor = true;
            // 
            // chkNearOverHo
            // 
            this.chkNearOverHo.AutoSize = true;
            this.chkNearOverHo.Location = new System.Drawing.Point(20, 45);
            this.chkNearOverHo.Name = "chkNearOverHo";
            this.chkNearOverHo.Size = new System.Drawing.Size(74, 18);
            this.chkNearOverHo.TabIndex = 0;
            this.chkNearOverHo.Text = "切换频繁";
            this.chkNearOverHo.UseVisualStyleBackColor = true;
            // 
            // chkNearWeak
            // 
            this.chkNearWeak.AutoSize = true;
            this.chkNearWeak.Checked = true;
            this.chkNearWeak.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkNearWeak.Location = new System.Drawing.Point(20, 21);
            this.chkNearWeak.Name = "chkNearWeak";
            this.chkNearWeak.Size = new System.Drawing.Size(62, 18);
            this.chkNearWeak.TabIndex = 0;
            this.chkNearWeak.Text = "弱覆盖";
            this.chkNearWeak.UseVisualStyleBackColor = true;
            // 
            // grpFar
            // 
            this.grpFar.Controls.Add(this.chkFarFar);
            this.grpFar.Controls.Add(this.chkFarMulti);
            this.grpFar.Controls.Add(this.chkFarOverCvr);
            this.grpFar.Controls.Add(this.chkFarSINR);
            this.grpFar.Controls.Add(this.chkFarOverHo);
            this.grpFar.Controls.Add(this.chkFarWeak);
            this.grpFar.Location = new System.Drawing.Point(346, 21);
            this.grpFar.Name = "grpFar";
            this.grpFar.Size = new System.Drawing.Size(328, 70);
            this.grpFar.TabIndex = 0;
            this.grpFar.TabStop = false;
            this.grpFar.Text = "超远站";
            // 
            // chkFarFar
            // 
            this.chkFarFar.AutoSize = true;
            this.chkFarFar.Location = new System.Drawing.Point(236, 21);
            this.chkFarFar.Name = "chkFarFar";
            this.chkFarFar.Size = new System.Drawing.Size(74, 18);
            this.chkFarFar.TabIndex = 0;
            this.chkFarFar.Text = "过远覆盖";
            this.chkFarFar.UseVisualStyleBackColor = true;
            // 
            // chkFarMulti
            // 
            this.chkFarMulti.AutoSize = true;
            this.chkFarMulti.Location = new System.Drawing.Point(156, 21);
            this.chkFarMulti.Name = "chkFarMulti";
            this.chkFarMulti.Size = new System.Drawing.Size(74, 18);
            this.chkFarMulti.TabIndex = 0;
            this.chkFarMulti.Text = "重叠覆盖";
            this.chkFarMulti.UseVisualStyleBackColor = true;
            // 
            // chkFarOverCvr
            // 
            this.chkFarOverCvr.AutoSize = true;
            this.chkFarOverCvr.Checked = true;
            this.chkFarOverCvr.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFarOverCvr.Location = new System.Drawing.Point(88, 21);
            this.chkFarOverCvr.Name = "chkFarOverCvr";
            this.chkFarOverCvr.Size = new System.Drawing.Size(62, 18);
            this.chkFarOverCvr.TabIndex = 0;
            this.chkFarOverCvr.Text = "过覆盖";
            this.chkFarOverCvr.UseVisualStyleBackColor = true;
            // 
            // chkFarSINR
            // 
            this.chkFarSINR.AutoSize = true;
            this.chkFarSINR.Location = new System.Drawing.Point(156, 46);
            this.chkFarSINR.Name = "chkFarSINR";
            this.chkFarSINR.Size = new System.Drawing.Size(74, 18);
            this.chkFarSINR.TabIndex = 0;
            this.chkFarSINR.Text = "质差路段";
            this.chkFarSINR.UseVisualStyleBackColor = true;
            // 
            // chkFarOverHo
            // 
            this.chkFarOverHo.AutoSize = true;
            this.chkFarOverHo.Location = new System.Drawing.Point(20, 45);
            this.chkFarOverHo.Name = "chkFarOverHo";
            this.chkFarOverHo.Size = new System.Drawing.Size(74, 18);
            this.chkFarOverHo.TabIndex = 0;
            this.chkFarOverHo.Text = "切换频繁";
            this.chkFarOverHo.UseVisualStyleBackColor = true;
            // 
            // chkFarWeak
            // 
            this.chkFarWeak.AutoSize = true;
            this.chkFarWeak.Location = new System.Drawing.Point(20, 21);
            this.chkFarWeak.Name = "chkFarWeak";
            this.chkFarWeak.Size = new System.Drawing.Size(62, 18);
            this.chkFarWeak.TabIndex = 0;
            this.chkFarWeak.Text = "弱覆盖";
            this.chkFarWeak.UseVisualStyleBackColor = true;
            // 
            // grpHigh
            // 
            this.grpHigh.Controls.Add(this.chkHighFar);
            this.grpHigh.Controls.Add(this.chkHighMulti);
            this.grpHigh.Controls.Add(this.chkHighOverCvr);
            this.grpHigh.Controls.Add(this.chkHighSINR);
            this.grpHigh.Controls.Add(this.chkHighOverHo);
            this.grpHigh.Controls.Add(this.chkHighWeak);
            this.grpHigh.Location = new System.Drawing.Point(12, 21);
            this.grpHigh.Name = "grpHigh";
            this.grpHigh.Size = new System.Drawing.Size(328, 70);
            this.grpHigh.TabIndex = 0;
            this.grpHigh.TabStop = false;
            this.grpHigh.Text = "超高站";
            // 
            // chkHighFar
            // 
            this.chkHighFar.AutoSize = true;
            this.chkHighFar.Location = new System.Drawing.Point(236, 21);
            this.chkHighFar.Name = "chkHighFar";
            this.chkHighFar.Size = new System.Drawing.Size(74, 18);
            this.chkHighFar.TabIndex = 0;
            this.chkHighFar.Text = "过远覆盖";
            this.chkHighFar.UseVisualStyleBackColor = true;
            // 
            // chkHighMulti
            // 
            this.chkHighMulti.AutoSize = true;
            this.chkHighMulti.Location = new System.Drawing.Point(156, 21);
            this.chkHighMulti.Name = "chkHighMulti";
            this.chkHighMulti.Size = new System.Drawing.Size(74, 18);
            this.chkHighMulti.TabIndex = 0;
            this.chkHighMulti.Text = "重叠覆盖";
            this.chkHighMulti.UseVisualStyleBackColor = true;
            // 
            // chkHighOverCvr
            // 
            this.chkHighOverCvr.AutoSize = true;
            this.chkHighOverCvr.Location = new System.Drawing.Point(88, 21);
            this.chkHighOverCvr.Name = "chkHighOverCvr";
            this.chkHighOverCvr.Size = new System.Drawing.Size(62, 18);
            this.chkHighOverCvr.TabIndex = 0;
            this.chkHighOverCvr.Text = "过覆盖";
            this.chkHighOverCvr.UseVisualStyleBackColor = true;
            // 
            // chkHighSINR
            // 
            this.chkHighSINR.AutoSize = true;
            this.chkHighSINR.Location = new System.Drawing.Point(156, 46);
            this.chkHighSINR.Name = "chkHighSINR";
            this.chkHighSINR.Size = new System.Drawing.Size(74, 18);
            this.chkHighSINR.TabIndex = 0;
            this.chkHighSINR.Text = "质差路段";
            this.chkHighSINR.UseVisualStyleBackColor = true;
            // 
            // chkHighOverHo
            // 
            this.chkHighOverHo.AutoSize = true;
            this.chkHighOverHo.Location = new System.Drawing.Point(20, 45);
            this.chkHighOverHo.Name = "chkHighOverHo";
            this.chkHighOverHo.Size = new System.Drawing.Size(74, 18);
            this.chkHighOverHo.TabIndex = 0;
            this.chkHighOverHo.Text = "切换频繁";
            this.chkHighOverHo.UseVisualStyleBackColor = true;
            // 
            // chkHighWeak
            // 
            this.chkHighWeak.AutoSize = true;
            this.chkHighWeak.Checked = true;
            this.chkHighWeak.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkHighWeak.Location = new System.Drawing.Point(20, 21);
            this.chkHighWeak.Name = "chkHighWeak";
            this.chkHighWeak.Size = new System.Drawing.Size(62, 18);
            this.chkHighWeak.TabIndex = 0;
            this.chkHighWeak.Text = "弱覆盖";
            this.chkHighWeak.UseVisualStyleBackColor = true;
            // 
            // ResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1017, 499);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "ResultForm";
            this.Text = "小区体检";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.grpNear.ResumeLayout(false);
            this.grpNear.PerformLayout();
            this.grpFar.ResumeLayout(false);
            this.grpFar.PerformLayout();
            this.grpHigh.ResumeLayout(false);
            this.grpHigh.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExport;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox grpHigh;
        private System.Windows.Forms.CheckBox chkHighFar;
        private System.Windows.Forms.CheckBox chkHighMulti;
        private System.Windows.Forms.CheckBox chkHighOverCvr;
        private System.Windows.Forms.CheckBox chkHighSINR;
        private System.Windows.Forms.CheckBox chkHighOverHo;
        private System.Windows.Forms.CheckBox chkHighWeak;
        private System.Windows.Forms.GroupBox grpNear;
        private System.Windows.Forms.CheckBox chkNearFar;
        private System.Windows.Forms.CheckBox chkNearMulti;
        private System.Windows.Forms.CheckBox chkNearOverCvr;
        private System.Windows.Forms.CheckBox chkNearSINR;
        private System.Windows.Forms.CheckBox chkNearOverHo;
        private System.Windows.Forms.CheckBox chkNearWeak;
        private System.Windows.Forms.GroupBox grpFar;
        private System.Windows.Forms.CheckBox chkFarFar;
        private System.Windows.Forms.CheckBox chkFarMulti;
        private System.Windows.Forms.CheckBox chkFarOverCvr;
        private System.Windows.Forms.CheckBox chkFarSINR;
        private System.Windows.Forms.CheckBox chkFarOverHo;
        private System.Windows.Forms.CheckBox chkFarWeak;
        private System.Windows.Forms.Button btnFilter;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
    }
}