﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDDownLoadAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.appSpeed_olv = new BrightIdeasSoftware.TreeListView();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEventSn = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEventStatus = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEventIPAddr = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleSn = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnStartTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEndTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpeedSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpeed500KSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpeed500KRate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpeed0KSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpeed0KRate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpeedAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPccpchRscpSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPccpchRscpF85Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPccpchRscpF85Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPccpchRscpAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPccpchCISample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPccpchCIF3Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPccpchCIF3Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPccpchCIAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDpchRscpSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDpchRscpF85Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDpchRscpF85Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDpchRscpAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDpchCISample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDpchCIF3Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDpchCIF3Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDpchCIAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSScchCISample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSScchCIF3Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSScchCIF3Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSScchCI5Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSScchCI5Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSScchCIAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschCISample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschCIF3Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschCIF3Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschCI5Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschCI5Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschCIAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBlerSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBler5Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBler5Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBlerAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschBlerSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschBler5Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschBler5Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPdschBlerAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTxPowerSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTxPower24Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTxPower24Rate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTxPowerAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSCqiMaxAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSCqiMinAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSCqiAvgAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn16QAMRateAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnQPSKRateAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTimeSlotUsedAvg = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHORequest = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHOSuccess = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRAURequest = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRAUSuccess = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellUpdate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellUpdateConfirm = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHSPASample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnR4Sample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnGSMSample = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPolluteNum = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMidLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMidLat = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.appSpeed_olv)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // appSpeed_olv
            // 
            this.appSpeed_olv.AllColumns.Add(this.olvColumnFileName);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnEventSn);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnEventStatus);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnEventIPAddr);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnSampleSn);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnStartTime);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnEndTime);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnSpeedSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnSpeed500KSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnSpeed500KRate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnSpeed0KSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnSpeed0KRate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnSpeedAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnPccpchRscpSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnPccpchRscpF85Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnPccpchRscpF85Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnPccpchRscpAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnPccpchCISample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnPccpchCIF3Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnPccpchCIF3Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnPccpchCIAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnDpchRscpSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnDpchRscpF85Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnDpchRscpF85Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnDpchRscpAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnDpchCISample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnDpchCIF3Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnDpchCIF3Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnDpchCIAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSScchCISample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSScchCIF3Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSScchCIF3Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSScchCI5Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSScchCI5Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSScchCIAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschCISample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschCIF3Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschCIF3Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschCI5Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschCI5Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschCIAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnBlerSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnBler5Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnBler5Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnBlerAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschBlerSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschBler5Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschBler5Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPdschBlerAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnTxPowerSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnTxPower24Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnTxPower24Rate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnTxPowerAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSCqiMaxAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSCqiMinAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSCqiAvgAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumn16QAMRateAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnQPSKRateAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnTimeSlotUsedAvg);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHORequest);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHOSuccess);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnRAURequest);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnRAUSuccess);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnCellUpdate);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnCellUpdateConfirm);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnHSPASample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnR4Sample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnGSMSample);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnPolluteNum);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnMidLong);
            this.appSpeed_olv.AllColumns.Add(this.olvColumnMidLat);
            this.appSpeed_olv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnFileName,
            this.olvColumnEventSn,
            this.olvColumnEventStatus,
            this.olvColumnEventIPAddr,
            this.olvColumnSampleSn,
            this.olvColumnStartTime,
            this.olvColumnEndTime,
            this.olvColumnSpeedSample,
            this.olvColumnSpeed500KSample,
            this.olvColumnSpeed500KRate,
            this.olvColumnSpeed0KSample,
            this.olvColumnSpeed0KRate,
            this.olvColumnSpeedAvg,
            this.olvColumnSample,
            this.olvColumnPccpchRscpSample,
            this.olvColumnPccpchRscpF85Sample,
            this.olvColumnPccpchRscpF85Rate,
            this.olvColumnPccpchRscpAvg,
            this.olvColumnPccpchCISample,
            this.olvColumnPccpchCIF3Sample,
            this.olvColumnPccpchCIF3Rate,
            this.olvColumnPccpchCIAvg,
            this.olvColumnDpchRscpSample,
            this.olvColumnDpchRscpF85Sample,
            this.olvColumnDpchRscpF85Rate,
            this.olvColumnDpchRscpAvg,
            this.olvColumnDpchCISample,
            this.olvColumnDpchCIF3Sample,
            this.olvColumnDpchCIF3Rate,
            this.olvColumnDpchCIAvg,
            this.olvColumnHSScchCISample,
            this.olvColumnHSScchCIF3Sample,
            this.olvColumnHSScchCIF3Rate,
            this.olvColumnHSScchCI5Sample,
            this.olvColumnHSScchCI5Rate,
            this.olvColumnHSScchCIAvg,
            this.olvColumnHSPdschCISample,
            this.olvColumnHSPdschCIF3Sample,
            this.olvColumnHSPdschCIF3Rate,
            this.olvColumnHSPdschCI5Sample,
            this.olvColumnHSPdschCI5Rate,
            this.olvColumnHSPdschCIAvg,
            this.olvColumnBlerSample,
            this.olvColumnBler5Sample,
            this.olvColumnBler5Rate,
            this.olvColumnBlerAvg,
            this.olvColumnHSPdschBlerSample,
            this.olvColumnHSPdschBler5Sample,
            this.olvColumnHSPdschBler5Rate,
            this.olvColumnHSPdschBlerAvg,
            this.olvColumnTxPowerSample,
            this.olvColumnTxPower24Sample,
            this.olvColumnTxPower24Rate,
            this.olvColumnTxPowerAvg,
            this.olvColumnHSCqiMaxAvg,
            this.olvColumnHSCqiMinAvg,
            this.olvColumnHSCqiAvgAvg,
            this.olvColumn16QAMRateAvg,
            this.olvColumnQPSKRateAvg,
            this.olvColumnTimeSlotUsedAvg,
            this.olvColumnHORequest,
            this.olvColumnHOSuccess,
            this.olvColumnRAURequest,
            this.olvColumnRAUSuccess,
            this.olvColumnCellUpdate,
            this.olvColumnCellUpdateConfirm,
            this.olvColumnHSPASample,
            this.olvColumnR4Sample,
            this.olvColumnGSMSample,
            this.olvColumnPolluteNum,
            this.olvColumnMidLong,
            this.olvColumnMidLat});
            this.appSpeed_olv.ContextMenuStrip = this.contextMenuStrip1;
            this.appSpeed_olv.Cursor = System.Windows.Forms.Cursors.Default;
            this.appSpeed_olv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.appSpeed_olv.FullRowSelect = true;
            this.appSpeed_olv.GridLines = true;
            this.appSpeed_olv.IsNeedShowOverlay = false;
            this.appSpeed_olv.Location = new System.Drawing.Point(0, 0);
            this.appSpeed_olv.Name = "appSpeed_olv";
            this.appSpeed_olv.OwnerDraw = true;
            this.appSpeed_olv.ShowGroups = false;
            this.appSpeed_olv.Size = new System.Drawing.Size(1136, 521);
            this.appSpeed_olv.Sorting = System.Windows.Forms.SortOrder.Descending;
            this.appSpeed_olv.TabIndex = 3;
            this.appSpeed_olv.UseCompatibleStateImageBehavior = false;
            this.appSpeed_olv.UseHotItem = true;
            this.appSpeed_olv.UseTranslucentHotItem = true;
            this.appSpeed_olv.UseTranslucentSelection = true;
            this.appSpeed_olv.View = System.Windows.Forms.View.Details;
            this.appSpeed_olv.VirtualMode = true;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.AspectName = "";
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 100;
            // 
            // olvColumnEventSn
            // 
            this.olvColumnEventSn.AspectName = "";
            this.olvColumnEventSn.HeaderFont = null;
            this.olvColumnEventSn.Text = "事件序号";
            // 
            // olvColumnEventStatus
            // 
            this.olvColumnEventStatus.HeaderFont = null;
            this.olvColumnEventStatus.Text = "事件结果";
            // 
            // olvColumnEventIPAddr
            // 
            this.olvColumnEventIPAddr.HeaderFont = null;
            this.olvColumnEventIPAddr.Text = "IP地址";
            // 
            // olvColumnSampleSn
            // 
            this.olvColumnSampleSn.HeaderFont = null;
            this.olvColumnSampleSn.Text = "样本序号";
            // 
            // olvColumnStartTime
            // 
            this.olvColumnStartTime.HeaderFont = null;
            this.olvColumnStartTime.Text = "开始时间";
            this.olvColumnStartTime.Width = 70;
            // 
            // olvColumnEndTime
            // 
            this.olvColumnEndTime.HeaderFont = null;
            this.olvColumnEndTime.Text = "结束时间";
            this.olvColumnEndTime.Width = 70;
            // 
            // olvColumnSpeedSample
            // 
            this.olvColumnSpeedSample.HeaderFont = null;
            this.olvColumnSpeedSample.Text = "下载总采样点数";
            // 
            // olvColumnSpeed500KSample
            // 
            this.olvColumnSpeed500KSample.HeaderFont = null;
            this.olvColumnSpeed500KSample.Text = ">=500K采样点数";
            // 
            // olvColumnSpeed500KRate
            // 
            this.olvColumnSpeed500KRate.HeaderFont = null;
            this.olvColumnSpeed500KRate.Text = ">=500K比例";
            // 
            // olvColumnSpeed0KSample
            // 
            this.olvColumnSpeed0KSample.HeaderFont = null;
            this.olvColumnSpeed0KSample.Text = "0K采样点数";
            // 
            // olvColumnSpeed0KRate
            // 
            this.olvColumnSpeed0KRate.HeaderFont = null;
            this.olvColumnSpeed0KRate.Text = "0K比例";
            // 
            // olvColumnSpeedAvg
            // 
            this.olvColumnSpeedAvg.HeaderFont = null;
            this.olvColumnSpeedAvg.Text = "平均速率(Kbps)";
            this.olvColumnSpeedAvg.Width = 100;
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "总采样点数";
            // 
            // olvColumnPccpchRscpSample
            // 
            this.olvColumnPccpchRscpSample.HeaderFont = null;
            this.olvColumnPccpchRscpSample.Text = "PCCPCH_RSCP总采样点数";
            // 
            // olvColumnPccpchRscpF85Sample
            // 
            this.olvColumnPccpchRscpF85Sample.HeaderFont = null;
            this.olvColumnPccpchRscpF85Sample.Text = "PCCPCH_RSCP>=-85采样点数";
            // 
            // olvColumnPccpchRscpF85Rate
            // 
            this.olvColumnPccpchRscpF85Rate.HeaderFont = null;
            this.olvColumnPccpchRscpF85Rate.Text = "PCCPCH_RSCP>=-85比例";
            // 
            // olvColumnPccpchRscpAvg
            // 
            this.olvColumnPccpchRscpAvg.HeaderFont = null;
            this.olvColumnPccpchRscpAvg.Text = "PCCPCH_RSCP平均值";
            // 
            // olvColumnPccpchCISample
            // 
            this.olvColumnPccpchCISample.HeaderFont = null;
            this.olvColumnPccpchCISample.Text = "PCCPCH_C/I总采样点数";
            // 
            // olvColumnPccpchCIF3Sample
            // 
            this.olvColumnPccpchCIF3Sample.HeaderFont = null;
            this.olvColumnPccpchCIF3Sample.Text = "PCCPCH_C/I >=-3 采样点数";
            // 
            // olvColumnPccpchCIF3Rate
            // 
            this.olvColumnPccpchCIF3Rate.HeaderFont = null;
            this.olvColumnPccpchCIF3Rate.Text = "PCCPCH_C/I >=-3比例";
            // 
            // olvColumnPccpchCIAvg
            // 
            this.olvColumnPccpchCIAvg.HeaderFont = null;
            this.olvColumnPccpchCIAvg.Text = "PCCPCH_C/I平均值";
            // 
            // olvColumnDpchRscpSample
            // 
            this.olvColumnDpchRscpSample.HeaderFont = null;
            this.olvColumnDpchRscpSample.Text = "DPCH_RSCP总采样点数";
            // 
            // olvColumnDpchRscpF85Sample
            // 
            this.olvColumnDpchRscpF85Sample.HeaderFont = null;
            this.olvColumnDpchRscpF85Sample.Text = "DPCH_RSCP>=-85采样点数";
            // 
            // olvColumnDpchRscpF85Rate
            // 
            this.olvColumnDpchRscpF85Rate.HeaderFont = null;
            this.olvColumnDpchRscpF85Rate.Text = "DPCH_RSCP>=-85比例";
            // 
            // olvColumnDpchRscpAvg
            // 
            this.olvColumnDpchRscpAvg.HeaderFont = null;
            this.olvColumnDpchRscpAvg.Text = "DPCH_RSCP平均值";
            // 
            // olvColumnDpchCISample
            // 
            this.olvColumnDpchCISample.HeaderFont = null;
            this.olvColumnDpchCISample.Text = "DPCH_C/I总采样点数";
            // 
            // olvColumnDpchCIF3Sample
            // 
            this.olvColumnDpchCIF3Sample.HeaderFont = null;
            this.olvColumnDpchCIF3Sample.Text = "DPCH_C/I >=-3 采样点数";
            // 
            // olvColumnDpchCIF3Rate
            // 
            this.olvColumnDpchCIF3Rate.HeaderFont = null;
            this.olvColumnDpchCIF3Rate.Text = "DPCH_C/I >=-3比例";
            // 
            // olvColumnDpchCIAvg
            // 
            this.olvColumnDpchCIAvg.HeaderFont = null;
            this.olvColumnDpchCIAvg.Text = "DPCH_C/I平均值";
            // 
            // olvColumnHSScchCISample
            // 
            this.olvColumnHSScchCISample.HeaderFont = null;
            this.olvColumnHSScchCISample.Text = "HSSCCH_C/I总采样点数";
            // 
            // olvColumnHSScchCIF3Sample
            // 
            this.olvColumnHSScchCIF3Sample.HeaderFont = null;
            this.olvColumnHSScchCIF3Sample.Text = "HSSCCH_C/I >=-3 采样点数";
            // 
            // olvColumnHSScchCIF3Rate
            // 
            this.olvColumnHSScchCIF3Rate.HeaderFont = null;
            this.olvColumnHSScchCIF3Rate.Text = "HSSCCH_C/I >=-3比例";
            // 
            // olvColumnHSScchCI5Sample
            // 
            this.olvColumnHSScchCI5Sample.HeaderFont = null;
            this.olvColumnHSScchCI5Sample.Text = "HSSCCH_C/I >=5 采样点数";
            // 
            // olvColumnHSScchCI5Rate
            // 
            this.olvColumnHSScchCI5Rate.HeaderFont = null;
            this.olvColumnHSScchCI5Rate.Text = "HSSCCH_C/I >=5比例";
            // 
            // olvColumnHSScchCIAvg
            // 
            this.olvColumnHSScchCIAvg.HeaderFont = null;
            this.olvColumnHSScchCIAvg.Text = "HSSCCH_C/I平均值";
            // 
            // olvColumnHSPdschCISample
            // 
            this.olvColumnHSPdschCISample.HeaderFont = null;
            this.olvColumnHSPdschCISample.Text = "HSPDSCH_C/I总采样点数";
            // 
            // olvColumnHSPdschCIF3Sample
            // 
            this.olvColumnHSPdschCIF3Sample.HeaderFont = null;
            this.olvColumnHSPdschCIF3Sample.Text = "HSPDSCH_C/I >=-3 采样点数";
            // 
            // olvColumnHSPdschCIF3Rate
            // 
            this.olvColumnHSPdschCIF3Rate.HeaderFont = null;
            this.olvColumnHSPdschCIF3Rate.Text = "HSPDSCH_C/I >=-3比例";
            // 
            // olvColumnHSPdschCI5Sample
            // 
            this.olvColumnHSPdschCI5Sample.HeaderFont = null;
            this.olvColumnHSPdschCI5Sample.Text = "HSPDSCH_C/I >=5 采样点数";
            // 
            // olvColumnHSPdschCI5Rate
            // 
            this.olvColumnHSPdschCI5Rate.HeaderFont = null;
            this.olvColumnHSPdschCI5Rate.Text = "HSPDSCH_C/I >=5比例";
            // 
            // olvColumnHSPdschCIAvg
            // 
            this.olvColumnHSPdschCIAvg.HeaderFont = null;
            this.olvColumnHSPdschCIAvg.Text = "HSPDSCH_C/I平均值";
            // 
            // olvColumnBlerSample
            // 
            this.olvColumnBlerSample.HeaderFont = null;
            this.olvColumnBlerSample.Text = "BLER总采样点数";
            // 
            // olvColumnBler5Sample
            // 
            this.olvColumnBler5Sample.HeaderFont = null;
            this.olvColumnBler5Sample.Text = "BLER >=5 采样点数";
            // 
            // olvColumnBler5Rate
            // 
            this.olvColumnBler5Rate.HeaderFont = null;
            this.olvColumnBler5Rate.Text = "BLER >=5比例";
            // 
            // olvColumnBlerAvg
            // 
            this.olvColumnBlerAvg.HeaderFont = null;
            this.olvColumnBlerAvg.Text = "BLER平均值";
            // 
            // olvColumnHSPdschBlerSample
            // 
            this.olvColumnHSPdschBlerSample.HeaderFont = null;
            this.olvColumnHSPdschBlerSample.Text = "HSPDSCH_BLER总采样点数";
            // 
            // olvColumnHSPdschBler5Sample
            // 
            this.olvColumnHSPdschBler5Sample.HeaderFont = null;
            this.olvColumnHSPdschBler5Sample.Text = "HSPDSCH_BLER >=5 采样点数";
            // 
            // olvColumnHSPdschBler5Rate
            // 
            this.olvColumnHSPdschBler5Rate.HeaderFont = null;
            this.olvColumnHSPdschBler5Rate.Text = "HSPDSCH_BLER >=5比例";
            // 
            // olvColumnHSPdschBlerAvg
            // 
            this.olvColumnHSPdschBlerAvg.HeaderFont = null;
            this.olvColumnHSPdschBlerAvg.Text = "HSPDSCH_BLER平均值";
            // 
            // olvColumnTxPowerSample
            // 
            this.olvColumnTxPowerSample.HeaderFont = null;
            this.olvColumnTxPowerSample.Text = "TxPower总采样点数";
            // 
            // olvColumnTxPower24Sample
            // 
            this.olvColumnTxPower24Sample.HeaderFont = null;
            this.olvColumnTxPower24Sample.Text = "TxPower >=24 采样点数";
            // 
            // olvColumnTxPower24Rate
            // 
            this.olvColumnTxPower24Rate.HeaderFont = null;
            this.olvColumnTxPower24Rate.Text = "TxPower >=24比例";
            // 
            // olvColumnTxPowerAvg
            // 
            this.olvColumnTxPowerAvg.HeaderFont = null;
            this.olvColumnTxPowerAvg.Text = "TxPower平均值";
            // 
            // olvColumnHSCqiMaxAvg
            // 
            this.olvColumnHSCqiMaxAvg.HeaderFont = null;
            this.olvColumnHSCqiMaxAvg.Text = "HS_CQI最大值的平均值";
            // 
            // olvColumnHSCqiMinAvg
            // 
            this.olvColumnHSCqiMinAvg.HeaderFont = null;
            this.olvColumnHSCqiMinAvg.Text = "HS_CQI最小值的平均值";
            // 
            // olvColumnHSCqiAvgAvg
            // 
            this.olvColumnHSCqiAvgAvg.HeaderFont = null;
            this.olvColumnHSCqiAvgAvg.Text = "HS_CQI平均值的平均值";
            // 
            // olvColumn16QAMRateAvg
            // 
            this.olvColumn16QAMRateAvg.HeaderFont = null;
            this.olvColumn16QAMRateAvg.Text = "16QAM比例平均值";
            // 
            // olvColumnQPSKRateAvg
            // 
            this.olvColumnQPSKRateAvg.HeaderFont = null;
            this.olvColumnQPSKRateAvg.Text = "QPSK比例平均值";
            // 
            // olvColumnTimeSlotUsedAvg
            // 
            this.olvColumnTimeSlotUsedAvg.HeaderFont = null;
            this.olvColumnTimeSlotUsedAvg.Text = "HS占用时隙平均值";
            // 
            // olvColumnHORequest
            // 
            this.olvColumnHORequest.HeaderFont = null;
            this.olvColumnHORequest.Text = "切换请求次数";
            // 
            // olvColumnHOSuccess
            // 
            this.olvColumnHOSuccess.HeaderFont = null;
            this.olvColumnHOSuccess.Text = "切换成功次数";
            // 
            // olvColumnRAURequest
            // 
            this.olvColumnRAURequest.HeaderFont = null;
            this.olvColumnRAURequest.Text = "路由更新请求次数";
            // 
            // olvColumnRAUSuccess
            // 
            this.olvColumnRAUSuccess.HeaderFont = null;
            this.olvColumnRAUSuccess.Text = "路由更新成功次数";
            // 
            // olvColumnCellUpdate
            // 
            this.olvColumnCellUpdate.HeaderFont = null;
            this.olvColumnCellUpdate.Text = "小区更新请求次数";
            // 
            // olvColumnCellUpdateConfirm
            // 
            this.olvColumnCellUpdateConfirm.HeaderFont = null;
            this.olvColumnCellUpdateConfirm.Text = "小区更新成功次数";
            // 
            // olvColumnHSPASample
            // 
            this.olvColumnHSPASample.HeaderFont = null;
            this.olvColumnHSPASample.Text = "HSPA采样点数";
            // 
            // olvColumnR4Sample
            // 
            this.olvColumnR4Sample.HeaderFont = null;
            this.olvColumnR4Sample.Text = "R4采样点数";
            // 
            // olvColumnGSMSample
            // 
            this.olvColumnGSMSample.HeaderFont = null;
            this.olvColumnGSMSample.Text = "GSM采样点数";
            // 
            // olvColumnPolluteNum
            // 
            this.olvColumnPolluteNum.HeaderFont = null;
            this.olvColumnPolluteNum.Text = "导频污染采样点数";
            // 
            // olvColumnMidLong
            // 
            this.olvColumnMidLong.HeaderFont = null;
            this.olvColumnMidLong.Text = "中心点经度";
            // 
            // olvColumnMidLat
            // 
            this.olvColumnMidLat.HeaderFont = null;
            this.olvColumnMidLat.Text = "中心点纬度";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(137, 26);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemExport.Text = "导出到xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // TDDownLoadAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1136, 521);
            this.Controls.Add(this.appSpeed_olv);
            this.Name = "TDDownLoadAnaListForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "速率分析列表";
            ((System.ComponentModel.ISupportInitialize)(this.appSpeed_olv)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView appSpeed_olv;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnEventSn;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleSn;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeedAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeedSample;
        private BrightIdeasSoftware.OLVColumn olvColumnPccpchRscpAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnPccpchCIAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnBlerAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnHOSuccess;
        private BrightIdeasSoftware.OLVColumn olvColumnRAUSuccess;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPASample;
        private BrightIdeasSoftware.OLVColumn olvColumnPolluteNum;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private BrightIdeasSoftware.OLVColumn olvColumnStartTime;
        private BrightIdeasSoftware.OLVColumn olvColumnEndTime;
        private BrightIdeasSoftware.OLVColumn olvColumnMidLong;
        private BrightIdeasSoftware.OLVColumn olvColumnMidLat;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchRscpAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchCIAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnHSScchCIAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschCIAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnR4Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnGSMSample;
        private BrightIdeasSoftware.OLVColumn olvColumnEventStatus;
        private BrightIdeasSoftware.OLVColumn olvColumnPccpchRscpSample;
        private BrightIdeasSoftware.OLVColumn olvColumnPccpchRscpF85Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnPccpchCISample;
        private BrightIdeasSoftware.OLVColumn olvColumnPccpchCIF3Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchRscpSample;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchRscpF85Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchCISample;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchCIF3Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnHSScchCISample;
        private BrightIdeasSoftware.OLVColumn olvColumnHSScchCIF3Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnBlerSample;
        private BrightIdeasSoftware.OLVColumn olvColumnBler5Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschCISample;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschCIF3Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschBlerSample;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschBler5Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschBlerAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnHSCqiMaxAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnHSCqiMinAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnHSCqiAvgAvg;
        private BrightIdeasSoftware.OLVColumn olvColumn16QAMRateAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnQPSKRateAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnTimeSlotUsedAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeed500KSample;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnPccpchRscpF85Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnPccpchCIF3Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchRscpF85Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnDpchCIF3Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnHSScchCIF3Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschCIF3Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnBler5Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschBler5Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnHORequest;
        private BrightIdeasSoftware.OLVColumn olvColumnRAURequest;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeed500KRate;
        private BrightIdeasSoftware.OLVColumn olvColumnHSScchCI5Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnHSScchCI5Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnTxPowerSample;
        private BrightIdeasSoftware.OLVColumn olvColumnTxPower24Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnTxPower24Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnTxPowerAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeed0KSample;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeed0KRate;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschCI5Sample;
        private BrightIdeasSoftware.OLVColumn olvColumnHSPdschCI5Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnEventIPAddr;
        private BrightIdeasSoftware.OLVColumn olvColumnCellUpdate;
        private BrightIdeasSoftware.OLVColumn olvColumnCellUpdateConfirm;
    }
}