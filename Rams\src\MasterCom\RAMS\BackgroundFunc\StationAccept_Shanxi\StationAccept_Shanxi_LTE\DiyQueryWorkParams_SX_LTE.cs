﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class UpLoadWorkParams_SX : DiySqlMultiNonQuery
    {
        public int ValidCount { get; set; }
        public bool SaveInDBSuccess { get; set; } = false;
        protected StationAcceptWorkParams workParams;

        protected virtual string tableName { get { return "tb_btscheck_SX_cfg_cell"; } }

        public UpLoadWorkParams_SX(StationAcceptWorkParams workParams)
            : base()
        {
            MainDB = true;
            this.workParams = workParams;
        }

        protected override string getSqlTextString()
        {
            ValidCount = 0;
            var cellParamInfoDic = ((StationAcceptWorkParams_SX)workParams).WorkParamSumDic;
            StringBuilder strb = new StringBuilder();
            //地市
            foreach (var paramInfo in cellParamInfoDic.Values)
            {
                //基站
                foreach (var btsInfo in paramInfo.Values)
                {
                    strb.Append($"delete from {tableName} where BtsName = '{btsInfo.BtsNameFull}';");
                    //小区
                    foreach (var info in btsInfo.CellWorkParams)
                    {
                        CellAcceptWorkParam_SX cellInfo = info as CellAcceptWorkParam_SX;
                        strb.Append($@"insert into [{tableName}] ([DistrictName],[BtsName],[ENodeBID],[CellName],[CellID],[SectorID],[Tac],[Eci],[Earfcn],[Pci],[Longitude],[Latitude],[CoverTypeDes],[Altitude],[Direction],[Downward],[AnalysedType],[UpdateTime],[Remark],[ImportTime]) values ('{cellInfo.DistrictName}','{cellInfo.BtsNameFull}',{cellInfo.ENodeBID},'{cellInfo.CellNameFull}',{cellInfo.CellID},{cellInfo.SectorID},{cellInfo.Tac},{cellInfo.Eci},{cellInfo.Earfcn},{cellInfo.Pci},{cellInfo.Longitude * 10000000},{cellInfo.Latitude * 10000000},'{cellInfo.CoverTypeDes}',{cellInfo.Altitude},{cellInfo.Direction},{cellInfo.Downward},{(int)btsInfo.AnalysedType},'{cellInfo.UpdateTime}','{cellInfo.Remark}','{cellInfo.ImportTime}');");
                        ValidCount++;
                    }
                }
            }
            return strb.ToString();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    SaveInDBSuccess = true;
                    log.Debug("success");
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    log.Error("fail");
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class WorkParamsQuery_SX : DIYSQLBase
    {
        readonly StationAcceptCondition_SX acceptCondition;
        public StationAcceptWorkParams_SX WorkParams { get; set; }

        public WorkParamsQuery_SX(StationAcceptCondition_SX condition)
        {
            MainDB = true;
            acceptCondition = condition;
            WorkParams = new StationAcceptWorkParams_SX();
        }

        protected override string getSqlTextString()
        {
            string type;
            if (acceptCondition.AcceptType == StationAcceptType.NotAccept)
            {
                type = ((int)AnalyseType.NeedAnalyse).ToString();
            }
            else if (acceptCondition.AcceptType == StationAcceptType.FailedAccept)
            {
                type = ((int)AnalyseType.FailedAnalyse).ToString();
            }
            else if (acceptCondition.AcceptType == StationAcceptType.NotAndFailedAccept)
            {
                type = ((int)AnalyseType.FailedAnalyse).ToString() + "," + ((int)AnalyseType.NeedAnalyse).ToString();
            }
            else
            {
                throw (new Exception("缺少对应的单验工参类型"));
            }

            string sql = string.Format(@"select [DistrictName],[BtsName],[ENodeBID],[CellName],[CellID],[SectorID],[Tac],[Eci],[Earfcn],[Pci],[Longitude],[Latitude],[CoverTypeDes],[Altitude],[Direction],[Downward] from tb_btscheck_SX_cfg_cell where AnalysedType in ({0})", type);

            if (acceptCondition.IsAnalysedNearest)
            {
                DateTime dt = DateTime.Now.Date.AddDays(-acceptCondition.NearestDay);
                sql += string.Format(@"and ImportTime >= '{0}'", dt);
            }

            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[16];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i] = E_VType.E_Int;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellAcceptWorkParam_SX cellInfo = new CellAcceptWorkParam_SX();
                    cellInfo.FillDataByDB(package);
                    WorkParams.AddWorkParamSum(cellInfo);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }

    public class UpdateWorkParamDes_SX : DIYSQLBase
    {
        readonly string name;
        readonly string errorInfo;
        readonly string tableName;
        readonly bool isPassAccept = false;

        public UpdateWorkParamDes_SX(string name, string errorInfo, bool isPassAccept, NetType type)
        {
            MainDB = true;
            this.name = name;
            this.errorInfo = errorInfo;
            this.isPassAccept = isPassAccept;
            tableName = ParamsHelper.GetWorkParamsTableName(type);
        }

        protected override string getSqlTextString()
        {
            int analysedType = (int)AnalyseType.SuccessAnalyse;
            if (!isPassAccept)
            {
                analysedType = (int)AnalyseType.FailedAnalyse;
            }

            string sql = $"update {tableName} set AnalysedType = {analysedType},Remark = '{errorInfo}',UpdateTime = '{DateTime.Now}' where BtsName = '{name}';";

            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
        }
    }
}
