﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.BaseInfo;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System.Text.RegularExpressions;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WorkloadCountAndQueryDlg_HN : BaseForm
    {
        public WorkloadCountAndQueryDlg_HN()
            : base()
        {
            InitializeComponent();
            if (MainModel.User.DBID == -1)
            {
                this.Size = new System.Drawing.Size(545, 268);
                this.groupBox2.Visible = true;
            }
            else
            {
                this.Size = new System.Drawing.Size(545, 180);
                this.groupBox2.Visible = false;
            }
        }

        private void simpleBtnSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Multiselect = false;
            dlg.Filter = FilterHelper.Excel;
            if (dlg.ShowDialog() != DialogResult.OK) return;
            textBoxLoadFilePath.Text = dlg.FileName;
        }

        private void simpleBtnSearch_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
        public WorkloadType_HN GetType_HN()
        {
            if (radioButtonTest.Checked)
            {
                return WorkloadType_HN.测试厂家;
            }
            else
            {
                return WorkloadType_HN.分析厂家;
            }
        }
        private void simpleBtnLoadFile_Click(object sender, EventArgs e)
        {
            if (!System.IO.File.Exists(textBoxLoadFilePath.Text))
            {
                System.Windows.Forms.MessageBox.Show("此xls文件不存在", "提示");
                return;
            }
            LoadFileForWorkload query = new LoadFileForWorkload(textBoxLoadFilePath.Text,GetType_HN());
            query.Query();
        }
    }
}
