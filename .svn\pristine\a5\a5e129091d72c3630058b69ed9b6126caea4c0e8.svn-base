using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    public delegate void CallBackMethod();

    public delegate void CallBackMethodWithParams(object o);

    public static class ThreadUtil
    {
        public static void BeginInvoke(CallBackMethod d)
        {
            d.BeginInvoke(null, null);
        }

        public static void BeginInvoke(CallBackMethodWithParams d, object o)
        {
            d.BeginInvoke(o, null, null);
        }

        public static void Invoke(System.Windows.Forms.Control owner, CallBackMethod d)
        {
            if (owner.InvokeRequired)
            {
                owner.Invoke(d);
            }
            else
            {
                d();
            }
        }

        public static void Invoke(System.Windows.Forms.Control owner, CallBackMethodWithParams d, object o)
        {
            if (owner.InvokeRequired)
            {
                owner.Invoke(d, o);
            }
            else
            {
                d(o);
            }
        }

        public static void BeginInvoke(System.Windows.Forms.Control owner, CallBackMethod d)
        {
            if (owner.InvokeRequired)
            {
                owner.BeginInvoke(d);
            }
            else
            {
                d();
            }
        }

        public static void BeginInvoke(System.Windows.Forms.Control owner, CallBackMethodWithParams d, object o)
        {
            if (owner.InvokeRequired)
            {
                owner.BeginInvoke(d, o);
            }
            else
            {
                d(o);
            }
        }

        public static void BeginInvokeInUIThread(System.Windows.Forms.Control owner, CallBackMethod d)
        {
            BeginInvoke(callBackInUIThread, new object[] { owner, d });
        }

        public static void BeginInvokeInUIThread(System.Windows.Forms.Control owner, CallBackMethodWithParams d, object o)
        {
            BeginInvoke(callBackInUIThread, new object[] { owner, d, o });
        }

        private static void callBackInUIThread(object o)
        {
            System.Threading.Thread.Sleep(100);
            object[] args = (object[])o;
            System.Windows.Forms.Control owner = (System.Windows.Forms.Control)args[0];
            if (args.Length == 2)
            {
                CallBackMethod d = (CallBackMethod)args[1];
                BeginInvoke(owner, d);
            }
            if (args.Length == 3)
            {
                CallBackMethodWithParams d = (CallBackMethodWithParams)args[1];
                object obj = args[2];
                BeginInvoke(owner, d, obj);
            }
        }
    }
}
