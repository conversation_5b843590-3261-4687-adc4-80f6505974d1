﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteHandOverFreqBandAnaResult
    {
        public LteHandOverFreqBandAnaResult(string freqBandName)
        {
            FreqBandName = freqBandName;
        }

        public string FreqBandName { get; private set; }
        public int SameBandHandOverNum { get; set; }
        public int DiffBandHandOverNum { get; set; }
        public List<BandHandOverInfo> SameBandHandOver { get; set; } = new List<BandHandOverInfo>();
        public List<BandHandOverInfo> DiffBandHandOver { get; set; } = new List<BandHandOverInfo>();

        public DataInfo SameBandSrcRsrpInfo { get; set; } = new DataInfo();
        public List<BandHandOverInfo> FreqBandHandOverList { get; set; } = new List<BandHandOverInfo>();

        public void Calculate()
        {
            SameBandHandOverNum = SameBandHandOver.Count;
            DiffBandHandOverNum = DiffBandHandOver.Count;

            foreach (var item in SameBandHandOver)
            {
                SameBandSrcRsrpInfo.Add(item.SrcHandOverInfo.RsrpInfo);
                item.Calculate();
                FreqBandHandOverList.Add(item);
            }
            foreach (var item in DiffBandHandOver)
            {
                item.Calculate();
                FreqBandHandOverList.Add(item);
            }

            SameBandSrcRsrpInfo.Calculate();
        }

        public class BandHandOverInfo : ICloneable
        {
            public BandHandOverInfo(Event evt)
            {
                Evt = evt;
            }
            public Event Evt { get; private set; }
            public string HandOverTime { get; private set; }
            public string HandOverType { get; set; }
            public string GridName { get; private set; }

            public HandOverInfo SrcHandOverInfo { get; set; } = new HandOverInfo();
            public HandOverInfo TarHandOverInfo { get; set; } = new HandOverInfo();

            public void Calculate()
            {
                HandOverTime = Evt.DateTimeStringWithMillisecond;
                SrcHandOverInfo.Calculate();
                TarHandOverInfo.Calculate();
                GridName = GISManager.GetInstance().GetGridDesc(Evt.Longitude, Evt.Latitude);
            }

            public object Clone()
            {
                BandHandOverInfo info = new BandHandOverInfo(Evt);
                info.HandOverTime = HandOverTime;
                info.HandOverType = HandOverType;
                info.SrcHandOverInfo = (HandOverInfo)SrcHandOverInfo.Clone();
                info.TarHandOverInfo = (HandOverInfo)TarHandOverInfo.Clone();
                return info;
            }
        }

        public class HandOverInfo : ICloneable
        {
            public int Earfcn { get; set; }
            public string FreqBand { get; set; }
            public DataInfo RsrpInfo { get; set; } = new DataInfo();

            public void Fill(HandOverInfo info)
            {
                RsrpInfo = info.RsrpInfo;
            }

            public void Calculate()
            {
                RsrpInfo.Calculate();
            }

            public object Clone()
            {
                HandOverInfo info = new HandOverInfo();
                info.Earfcn = Earfcn;
                info.FreqBand = FreqBand;
                info.RsrpInfo = (DataInfo)RsrpInfo.Clone();
                return info;
            }
        }

        public class DataInfo : ICloneable
        {
            public int Count { get; set; }
            public float Sum { get; set; }
            public string Avg { get; set; }

            public void Add(float data)
            {
                Count++;
                Sum += data;
            }

            public void Add(DataInfo info)
            {
                Count += info.Count;
                Sum += info.Sum;
            }

            public void Calculate()
            {
                if (Count == 0)
                {
                    Avg = "";
                }
                else
                {
                    Avg = Math.Round(Sum / Count, 2).ToString();
                }
            }

            public object Clone()
            {
                return this.MemberwiseClone();
            }
        }
    }
}
