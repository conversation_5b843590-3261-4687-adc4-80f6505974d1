using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYCoverDistanceByRegion_TD : ZTDIYCoverDistanceByRegion
    {
        public ZTDIYCoverDistanceByRegion_TD(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }
        
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12004, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = MainModel.TD_SCell_LAC;
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = MainModel.TD_SCell_CI;
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = MainModel.TD_SCell_UARFCN;
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = MainModel.TD_SCell_CPI;
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = MainModel.TD_GSM_SCell_LAC;
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = MainModel.TD_GSM_SCell_CI;
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = MainModel.TD_GSM_SCell_ARFCN;
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = MainModel.TD_GSM_SCell_BSIC;
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"CoverDistance");
            tmpDic.Add("themeName", (object)"TD_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void clearDataBeforeQuery()
        {
            cellCoverDistanceDic.Clear();
            tdCellCoverDistanceDic.Clear();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                Cell mainCell = null;
                TDCell mainTDCell = tp.GetMainCell_TD_TDCell();
                if (mainTDCell == null)
                {
                    int? rxlevSub = (int?)tp["TD_GSM_RxlevSub"];
                    if (rxlevSub == null || rxlevSub < -120 || rxlevSub > -10)
                    {
                        return;
                    }
                    mainCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp[MainModel.TD_GSM_SCell_LAC], 
                        (ushort?)(int?)tp[MainModel.TD_GSM_SCell_CI], (short?)(int?)tp[MainModel.TD_GSM_SCell_ARFCN], 
                        (byte?)tp[MainModel.TD_GSM_SCell_BSIC], tp.Longitude, tp.Latitude);
                    if (mainCell != null)
                    {
                        saveTestPoint(tp, mainCell, (float)rxlevSub);
                    }
                }
                else
                {
                    float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
                    if (rscp == null || rscp < -120 || rscp > -10)
                    {
                        return;
                    }
                    saveTestPoint(tp, mainTDCell, (float)rscp);
                }
            }
            catch
            {
                //continue
            }
        }

        protected void saveTestPoint(TestPoint tp, TDCell mainTDCell, float rscp)
        {
            double distance = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, mainTDCell.Longitude, mainTDCell.Latitude);
            if (!tdCellCoverDistanceDic.ContainsKey(mainTDCell))
            {
                tdCellCoverDistanceDic[mainTDCell] = new CellCoverDistance(mainTDCell);
            }
            tdCellCoverDistanceDic[mainTDCell].distanceList.Add(distance);
            tdCellCoverDistanceDic[mainTDCell].rxLevList.Add(rscp);
        }

        protected override void getResultAfterQuery()
        {
            MainModel.CellCoverDistanceList = new List<CellCoverDistance>(tdCellCoverDistanceDic.Values);
            if (cellCoverDistanceDic.Count > 0)
            {
                MainModel.CellCoverDistanceList.AddRange(cellCoverDistanceDic.Values);
            }
        }

        protected Dictionary<TDCell, CellCoverDistance> tdCellCoverDistanceDic = new Dictionary<TDCell, CellCoverDistance>();
    }
}
