﻿
using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.ZTLastWeakMosAna;

namespace MasterCom.RAMS.ZTFunc
{
    public class LastWeakMosAna : DIYAnalyseByFileBackgroundBase
    {
        readonly List<int> handOverEventIds = new List<int> { 851, 899, 1039, 1146, 3138, 3156, 3159, 3632 };
        protected EWeakMosEventType eWeakMosEventType = EWeakMosEventType.MosUnder2dot8Last2Pnt;
        protected List<int> weakMosEventIds;
        private int tpNum = 2;
        private List<LastWeakMosResult> listResult = null;
        private LastWeakMosAnaResultForm resultForm = new LastWeakMosAnaResultForm();
        public LastWeakMosAna()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "持续弱MOS事件"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27011, this.Name);
        }

        protected override bool getCondition()
        {
            LastWeakMosAnaSettingForm setForm = new LastWeakMosAnaSettingForm();
            setForm.SetValue(eWeakMosEventType, tpNum);
            if (setForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            setForm.GetResult(out eWeakMosEventType, out this.tpNum);

            if (eWeakMosEventType == EWeakMosEventType.MosUnder2dot8Last2Pnt)
            {
                weakMosEventIds = new List<int> { 1180 , 3706 };
            }
            else if (eWeakMosEventType == EWeakMosEventType.MosUnder3dot0Last2Pnt)
            {
                weakMosEventIds = new List<int> { 1181, 3707 };
            }

            return true;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            this.listResult = new List<LastWeakMosResult>();
        }

        protected virtual string eventName
        {
            get { return "TDD_LTE_弱MOS"; }
        }
        protected override void doStatWithQuery()
        {
            try
            {
                int testpointNum = 0;
                DateTime bT, eT;
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                { 
                    foreach (Event ev in file.Events)
                    {
                        if (!weakMosEventIds.Contains(ev.ID))
                        {
                            continue;
                        }
                        testpointNum = (int)(System.Int64)ev["Value4"];
                        if (testpointNum < this.tpNum)
                        {
                            continue;
                        }
                        MiddleValue mv = new MiddleValue();

                        bT = JavaDate.GetDateTimeFromMilliseconds((System.Int64)ev["Value1"]);
                        eT = JavaDate.GetDateTimeFromMilliseconds((System.Int64)ev["Value2"]);
                        mv = dealTP(bT, eT, file, mv);
                        dealEvt(bT, eT, file, mv);
                        LastWeakMosResult re = new LastWeakMosResult();
                        re.SN = this.listResult.Count + 1;
                        re.EventName = eventName;
                        re.FileName = file.FileName;
                        re.DateTime = ev.DateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                        re.TpNum = testpointNum;
                        re.AvgRSRP = getValidData(mv.RsrpSum, mv.SinrCount);
                        re.AvgSINR = getValidData(mv.SinrSum, mv.SinrCount);
                        re.AvgUpBLER = getValidData(mv.UpBlerSum, mv.UpBlerCount);
                        re.AvgDnBLER = getValidData(mv.DnBlerSum, mv.DnBlerCount);
                        re.Earfcn = mv.Earfcn;
                        re.PCI = mv.PCI;
                        re.AvgLossRate = getValidData(mv.LossRateSum, mv.LossRateCount);
                        re.AvgMOS = getValidData(mv.MOSSum, mv.MOSCount);
                        re.Distance = Math.Round(mv.Distance, 2);

                        re.HandOverCount = mv.HandOverCount;
                        re.Ev = ev;
                        this.listResult.Add(re);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private float getValidData(double sum, int count)
        {
            if (count > 0)
            {
                float res = (float)Math.Round(sum / count, 2 );
                return res;
            }
            else
            {
                return 0;
            }
        }

        private MiddleValue dealTP(DateTime bT, DateTime eT, DTFileDataManager file, MiddleValue mv)
        {
            double distance = 0;
            TestPoint lastTP = null;
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp.DateTime < bT)
                {
                    continue;
                }
                else if (tp.DateTime > eT)
                {
                    break;
                }
                if (lastTP != null)
                {
                    distance += tp.Distance2(lastTP);
                }
                lastTP = tp;
                this.getMiddleValue(ref mv, tp);
            }

            mv.Distance = distance;
            return mv;
        }

        private void dealEvt(DateTime bT, DateTime eT, DTFileDataManager file, MiddleValue mv)
        {
            foreach (Event eve in file.Events)
            {
                if (eve.DateTime < bT)
                {
                    continue;
                }
                if (eve.DateTime > eT)
                {
                    break;
                }
                if (handOverEventIds.Contains(eve.ID))
                {
                    mv.HandOverCount++;
                }
            }
        }

        protected virtual void getMiddleValue(ref MiddleValue mv, TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            if (rsrp != null && (float)rsrp != -10000000)
            {
                mv.RsrpSum += (float)rsrp;
                mv.RsrpCount++;
            }
            float? sinr = (float?)tp["lte_SINR"];
            if (sinr != null && (float)sinr != -10000000)
            {
                mv.SinrSum += (float)sinr;
                mv.SinrCount++;
            }
            float? ub = (float?)tp["lte_PUSCH_BLER"];
            if (ub != null && (float)ub != -10000000)
            {
                mv.UpBlerSum += (float)ub;
                mv.UpBlerCount++;
            }
            float? db = (float?)tp["lte_PDSCH_BLER"];
            if (db != null && (float)db != -10000000)
            {
                mv.DnBlerSum += (float)db;
                mv.DnBlerCount++;
            }
            setEarfcnPci(mv, tp);
            float? lossRate = (float?)tp["lte_volte_RTP_Loss_Rate"];
            if (lossRate != null && (float)lossRate != -10000000)
            {
                mv.LossRateSum += (float)lossRate;
                mv.LossRateCount++;
            }

            float? pesq = (float?)tp["lte_PESQLQ"];
            float? polqa = (float?)tp["lte_POLQA_Score_SWB"];
            if (pesq != null && pesq > 0 && pesq <= 5)
            {
                mv.MOSSum += (float)pesq;
                mv.MOSCount++;
            }
            else if (polqa != null && polqa > 0 && polqa <= 5)
            {
                mv.MOSSum += (float)polqa;
                mv.MOSCount++;
            }
        }

        private static void setEarfcnPci(MiddleValue mv, TestPoint tp)
        {
            int? earfcn = (int?)tp["lte_EARFCN"];
            if (mv.Earfcn == 0 && earfcn != null && -10000000 != (int)earfcn)
            {
                mv.Earfcn = (int)earfcn;
            }
            short? pci = (short?)tp["lte_PCI"];
            if (mv.PCI == 0 && pci != null && (short)pci != -255)
            {
                mv.PCI = (int)pci;
            }
        }

        protected override void fireShowForm()
        {
            if (this.resultForm == null || this.resultForm.IsDisposed)
            {
                this.resultForm = new LastWeakMosAnaResultForm();
            }
            this.resultForm.FillData(this.listResult);
            this.resultForm.Owner = MainModel.MainForm;
            this.resultForm.Visible = false;
            this.resultForm.Show();
            this.resultForm.Focus();
        }
    }

    public class LastWeakMosAna_FDD : LastWeakMosAna
    {
        public LastWeakMosAna_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            carrierID = CarrierType.ChinaUnicom;
        }
        public override string Name
        {
            get { return "VOLTE_FDD持续弱MOS事件"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30026, this.Name);
        }
        protected override string eventName
        {
            get { return "FDD_LTE_弱MOS"; }
        }
        protected override void getMiddleValue(ref MiddleValue mv, TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_fdd_RSRP"];
            if (rsrp != null && (float)rsrp != -10000000)
            {
                mv.RsrpSum += (float)rsrp;
                mv.RsrpCount++;
            }
            float? sinr = (float?)tp["lte_fdd_SINR"];
            if (sinr != null && (float)sinr != -10000000)
            {
                mv.SinrSum += (float)sinr;
                mv.SinrCount++;
            }
            float? ub = (float?)tp["lte_fdd_PUSCH_BLER"];
            if (ub != null && (float)ub != -10000000)
            {
                mv.UpBlerSum += (float)ub;
                mv.UpBlerCount++;
            }
            float? db = (float?)tp["lte_fdd_PDSCH_BLER"];
            if (db != null && (float)db != -10000000)
            {
                mv.DnBlerSum += (float)db;
                mv.DnBlerCount++;
            }
            setEarfcnPci(mv, tp);
            float? lossRate = (float?)tp["lte_fdd_volte_RTP_Loss_Rate"];
            if (lossRate != null && (float)lossRate != -10000000)
            {
                mv.LossRateSum += (float)lossRate;
                mv.LossRateCount++;
            }

            float? pesq = (float?)tp["lte_fdd_PESQLQ"];
            float? polqa = (float?)tp["lte_fdd_POLQA_Score_SWB"];
            if (pesq != null && pesq > 0 && pesq <= 5)
            {
                mv.MOSSum += (float)pesq;
                mv.MOSCount++;
            }
            else if (polqa != null && polqa > 0 && polqa <= 5)
            {
                mv.MOSSum += (float)polqa;
                mv.MOSCount++;
            }
        }

        private static void setEarfcnPci(MiddleValue mv, TestPoint tp)
        {
            int? earfcn = (int?)tp["lte_fdd_EARFCN"];
            if (mv.Earfcn == 0 && earfcn != null && -10000000 != (int)earfcn)
            {
                mv.Earfcn = (int)earfcn;
            }
            short? pci = (short?)tp["lte_fdd_PCI"];
            if (mv.PCI == 0 && pci != null && (short)pci != -255)
            {
                mv.PCI = (int)pci;
            }
        }
    }

    public enum EWeakMosEventType
    {
        MosUnder2dot8Last2Pnt = 0,
        MosUnder3dot0Last2Pnt = 1
    }
}
