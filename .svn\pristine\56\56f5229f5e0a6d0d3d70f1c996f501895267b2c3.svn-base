﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class DCSBTSVoiCoverManager : GSMBTSVoiCoverManager
    {
        public new static DCSBTSVoiCoverManager GetInstance()
        {
            if (instance == null)
            {
                instance = new DCSBTSVoiCoverManager();
            }
            return instance;
        }

        protected override bool ValidFilter(BTS bts, MapOperation2 mop2)
        {
            if (bts.BandType != BTSBandType.DSC1800 || bts.Type != Condition.GsmType)
            {
                return false;
            }
            return mop2.CheckPointInRegion(bts.VertexX, bts.VertexY);
        }

        protected DCSBTSVoiCoverManager()
        {
        }

        private static DCSBTSVoiCoverManager instance;
    }
}
