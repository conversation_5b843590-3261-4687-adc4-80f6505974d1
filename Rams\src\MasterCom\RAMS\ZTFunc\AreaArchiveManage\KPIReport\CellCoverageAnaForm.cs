﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage.KPIReport;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class CellCoverageAnaForm : MasterCom.RAMS.Func.MinCloseForm
    {
        public CellCoverageAnaForm()
        {
            InitializeComponent();
        }

        public void FillData(CAreaCellCoverageInfo areaCellInfo)
        {
            List<CAreaCellInfo> cells = new List<CAreaCellInfo>(areaCellInfo.AreaCellMap.Values);
            cells.Sort();
            gridControlResult.DataSource = cells;
            gridControlResult.RefreshDataSource();

            addPnts(cells);
        }

        private void addPnts(List<CAreaCellInfo> cells)
        {
            Dictionary<Type, string> typeSerialDic = new Dictionary<Type, string>();
            typeSerialDic.Add(typeof(TDTestPointDetail), "TD_PCCPCH_RSCP");
            typeSerialDic.Add(typeof(ScanTestPoint_TD), "TDSCAN_PCCPCH_RSCP");
            typeSerialDic.Add(typeof(WCDMATestPointDetail), "W_Reference_RSCP");
            typeSerialDic.Add(typeof(ScanTestPoint_LTE), "LTESCAN_TopN_PSS_RP");
            typeSerialDic.Add(typeof(ScanTestPoint_G), "GSM_SCAN_RxLev");
            typeSerialDic.Add(typeof(TestPointDetail), "GSM RxLevSub");

            List<string> serialVec = new List<string>();

            MainModel.ClearDTData();

            foreach (CAreaCellInfo cell in cells)
            {
                foreach (TestPoint tp in cell.TestPntVec)
                {
                    MainModel.DTDataManager.Add(tp);

                    Type type = tp.GetType();

                    string serial;
                    if (!typeSerialDic.TryGetValue(type, out serial))
                        continue;

                    if (!serialVec.Contains(serial))
                    {
                        serialVec.Add(serial);
                        MasterCom.RAMS.Func.DTLayerSerialManager.Instance.SetDefaultSerial(serial);
                        if (MasterCom.RAMS.Func.DTLayerSerialManager.Instance.SelectedSerials.Count > 0)
                            MasterCom.RAMS.Func.DTLayerSerialManager.Instance.FlyLineSerial = MasterCom.RAMS.Func.DTLayerSerialManager.Instance.SelectedSerials[0];
                    }
                }
            }

            mModel.DrawFlyLines = true;
            MainModel.FireDTDataChanged(this);
        }

        private void gridControlResult_DoubleClick(object sender, EventArgs e)
        {
            mModel.SelectedCell = null;
            mModel.SelectedTDCell = null;
            mModel.SelectedWCell = null;
            mModel.SelectedLTECell = null;

            int[] hRow = gridViewResult.GetSelectedRows();
            if (hRow.Length == 0) return;

            CAreaCellInfo cell = gridViewResult.GetRow(hRow[0]) as CAreaCellInfo;

            drawFlyLines(cell);
        }

        private void drawFlyLines(CAreaCellInfo cell)
        {
            if (cell.AreaCell is TDCell)
            {
                mModel.SelectedTDCell = cell.AreaCell as TDCell;
            }
            else if (cell.AreaCell is WCell)
            {
                mModel.SelectedWCell = cell.AreaCell as WCell;
            }
            else if (cell.AreaCell is LTECell)
            {
                mModel.SelectedLTECell = cell.AreaCell as LTECell;
            }
            else
            {
                mModel.SelectedCell = cell.AreaCell as Cell;
            }

            GoToBound.GoToViewCellsBounds(cell.AreaCell, cell.TestPntVec);
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridViewResult);
            }
            catch
            {
                MessageBox.Show("导出到Excel...失败!");
            }
        }
    }

    public static class GoToBound
    {
        public static void GoToViewCellsBounds(ICell cell, List<TestPoint> pntLst)
        {
            double maxLongitude = 0;
            double minLongitude = 99999;
            double maxLatitude = 0;
            double minLatitude = 99999;
            double cellLongitude = 0;
            double cellLatitude = 0;
            if (cell != null)
            {
                cellLongitude = cell.Longitude;
                cellLatitude = cell.Latitude;
            }

            if (maxLongitude < cellLongitude)
            {
                maxLongitude = cellLongitude;
            }
            if (minLongitude > cellLongitude)
            {
                minLongitude = cellLongitude;
            }
            if (maxLatitude < cellLatitude)
            {
                maxLatitude = cellLatitude;
            }
            if (minLatitude > cellLatitude)
            {
                minLatitude = cellLatitude;
            }

            foreach (TestPoint tp in pntLst)
            {
                if (maxLongitude < tp.Longitude)
                {
                    maxLongitude = tp.Longitude;
                }
                if (minLongitude > tp.Longitude)
                {
                    minLongitude = tp.Longitude;
                }
                if (maxLatitude < tp.Latitude)
                {
                    maxLatitude = tp.Latitude;
                }
                if (minLatitude > tp.Latitude)
                {
                    minLatitude = tp.Latitude;
                }
            }

            MasterCom.MTGis.DbRect rect = new MasterCom.MTGis.DbRect(minLongitude, minLatitude, maxLongitude, maxLatitude);
            MainModel.GetInstance().MainForm.GetMapForm().GoToViewCellsBounds(rect);
        }
    }
}
