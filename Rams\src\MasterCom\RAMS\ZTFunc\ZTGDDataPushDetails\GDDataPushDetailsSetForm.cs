﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GDDataPushDetailsSetForm : MinCloseForm
    {
        public GDDataPushDetailsSetForm()
        {
            InitializeComponent();
            date推送开始L.Value = DateTime.Now.AddDays(-5);
            date推送结束L.Value = DateTime.Now.AddDays(-5);
            date加入记录L.Value = DateTime.Now.AddDays(-5);
            com推送状态.SelectedIndex = 2;
        }

        private void chk数据编号_CheckedChanged(object sender, EventArgs e)
        {
            num数据编号.Enabled = chk数据编号.Checked;
        }

        private void chk设备ID_CheckedChanged(object sender, EventArgs e)
        {
            txt设备ID.Enabled = chk设备ID.Checked;
        }

        private void chk推送状态_CheckedChanged(object sender, EventArgs e)
        {
            com推送状态.Enabled = chk推送状态.Checked;
        }

        private void chk数据名称_CheckedChanged(object sender, EventArgs e)
        {
            txt数据名称.Enabled = chk数据名称.Checked;
        }

        private void chk推送开始时间_CheckedChanged(object sender, EventArgs e)
        {
            date推送开始L.Enabled = date推送开始R.Enabled = chk推送开始时间.Checked;
        }

        private void chk推送结束时间_CheckedChanged(object sender, EventArgs e)
        {
            date推送结束L.Enabled = date推送结束R.Enabled = chk推送结束时间.Checked;
        }

        private void chk数据加入记录_CheckedChanged(object sender, EventArgs e)
        {
            date加入记录L.Enabled = date加入记录R.Enabled = chk数据加入记录.Checked;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        public string StrSQL
        {
            get
            {
                string sql = "";
                if (chk数据编号.Checked)
                {
                    sql += " and pd.SourceId = " + num数据编号.Value;
                }
                if (chk设备ID.Checked)
                {
                    sql += " and pd.DeviceGuid like '%" + txt设备ID.Text.Trim() + "%'";
                }
                if (chk推送状态.Checked)
                {
                    sql += " and pd.Status = " + (com推送状态.SelectedIndex + 1).ToString();
                }
                if (chk数据名称.Checked)
                {
                    sql += " and pd.FileName like '%" + txt数据名称.Text.Trim() + "%'";
                }
                if (chk推送开始时间.Checked)
                {
                    sql += " and pd.PushStartTime BETWEEN '" + date推送开始L.Value.ToString("yyyy-MM-dd 00:00:00.000")
                        + "' AND '" + date推送开始R.Value.ToString("yyyy-MM-dd 23:59:59.999") + "'";
                }
                if (chk推送结束时间.Checked)
                {
                    sql += " and pd.PushDoneTime BETWEEN '" + date推送结束L.Value.ToString("yyyy-MM-dd 00:00:00.000")
                        + "' AND '" + date推送结束R.Value.ToString("yyyy-MM-dd 23:59:59.999") + "'";
                }
                if (chk数据加入记录.Checked)
                {
                    sql += " and pd.InsertTime BETWEEN '" + date加入记录L.Value.ToString("yyyy-MM-dd 00:00:00.000")
                        + "' AND '" + date加入记录R.Value.ToString("yyyy-MM-dd 23:59:59.999") + "'";
                }
                if (sql != "")
                {
                    sql = " where 1 > 0 " + sql;
                }
               return sql;
            }
        }

        public bool isQueryDownload
        {
            get
            {
                return chk下载状态.Checked;
            }
        }
    }
}
