using System;
using System.Collections.Generic;
using System.Text;
using DBDataViewer;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Stat.Data
{
    /// <summary>
    /// GSM Voice或PS参数(新16个image结构)
    /// </summary>
    public class DataGSM_NewImg : PartialData
    {
        public void addStatData(DataGSM_NewImg data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
        }

        internal void copyFrom(DataGSM_NewImg data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Mx_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        return false;
                        //StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef("grid", dicKey);
                        //if (sidef != null)
                        //{
                        //    if (sidef.gatherMethod == GatherMethod.E_MIN)
                        //    {
                        //        return 999999;
                        //    }
                        //    else if (sidef.gatherMethod == GatherMethod.E_MAX)
                        //    {
                        //        return -999999;
                        //    }
                        //}
                        //return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng =  temp/10000000;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = temp/10000000;
            }
            return true;
        }
    }
    public class DataMTR_GSM : PartialData
    {
        public static DataMTR_GSM FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataMTR_GSM data = new DataMTR_GSM();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataMTR_GSM data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
        }

        internal void copyFrom(DataMTR_GSM data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Ux_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }
    /// <summary>
    /// 数据TD PS参数(新16个image结构)
    /// </summary>
    public class DataTDSCDMA_NewImg : PartialData
    {
        public static DataTDSCDMA_NewImg FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataTDSCDMA_NewImg data = new DataTDSCDMA_NewImg();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataTDSCDMA_NewImg data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
        }

        internal void copyFrom(DataTDSCDMA_NewImg data)
        {

            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Tx_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = temp / 10000000.0f;//0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = temp / 10000000.0f;//0.0000001 * temp;
            }
            return true;
        }

    }

    /// <summary>
    /// GSM Scan (新16个image结构)
    /// </summary>
    public class DataScan_GSM : PartialData
    {
        public const string BCCHID = "0846";
        public const string BSICID = "0847";
        public const string RxlevMeanValueID = "5F0C0104";
        public const string RxlevSampleCountID = "5F0C0101";
        public const string RxlevMaxID = "5F0C0102";
        public const string RxlevMinID = "5F0C0103";

        public const string RxqualMeanValueID = "5F0C0204";
        public const string RxqualMaxValueID = "5F0C0202";
        public const string RxqualSampleCountID = "5F0C0201";

        public const string C_IMeanValueID = "5F0C0304";

        public const string BERMeanValueID = "5F0C0404";

        public int CellID = 0;
        public int BCCH = 0;
        public int BSIC = 0;
        public int fileTime = 0;

        //按照小区ID栅格聚合
        private Dictionary<int, Dictionary<string, double>> wCellInfoDic = new Dictionary<int, Dictionary<string, double>>();
        public Dictionary<int, Dictionary<string, double>> WCellInfoDic
        {
            get
            {
                return wCellInfoDic;
            }
        }

        public double this[int cellID, string typeID]
        {
            get
            {
                if (wCellInfoDic.ContainsKey(cellID))
                {
                    if (wCellInfoDic[cellID].ContainsKey(typeID))
                    {
                        return wCellInfoDic[cellID][typeID];
                    }
                }
                return -999999;
            }
        }

        public void addStatData(DataScan_GSM data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);

            if (data.CellID != 0)
            {
                if (!this.WCellInfoDic.ContainsKey(data.CellID))
                {
                    this.WCellInfoDic[data.CellID] = new Dictionary<string, double>();
                }
                StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.WCellInfoDic[data.CellID]);
            }
        }

        internal void copyFrom(DataScan_GSM data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }

            wCellInfoDic.Clear();
            foreach (int str in data.wCellInfoDic.Keys)
            {
                wCellInfoDic.Add(str, data.wCellInfoDic[str]);
            }
        }

        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Gc_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }

    }
    /// <summary>
    /// WLAN (新16个image结构)
    /// </summary>
    public class DataWLAN : PartialData
    {
        public static DataWLAN FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataWLAN data = new DataWLAN();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataWLAN data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
        }

        internal void copyFrom(DataWLAN data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Wl_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }

    public class DataLTE : PartialData
    {
        public static DataLTE FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataLTE data = new DataLTE();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataLTE data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
        }

        internal void copyFrom(DataLTE data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 4).Equals("Lte_"))
            {
                if (fieldName.Length > 4)
                {
                    string dicKey = fieldName.Substring(4, fieldName.Length - 4);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }

    public class DataLTE_FDD : PartialData
    {
        public static DataLTE_FDD FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataLTE_FDD data = new DataLTE_FDD();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataLTE_FDD data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
        }

        internal void copyFrom(DataLTE_FDD data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Lf_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }

    public class DataLTE_Signal : PartialData
    {
        public static DataLTE_Signal FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataLTE_Signal data = new DataLTE_Signal();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataLTE_Signal data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
        }

        internal void copyFrom(DataLTE_Signal data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Sn_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }

    /// <summary>
    /// TD Scan (新16个image结构)
    /// </summary>
    public class DataScan_TD : PartialData
    {
        public const string CellNameID = "0848";
        public const string CellIDID = "0849";
        public const string ChannelID = "084A";
        public const string CPIID = "084B";
        public const string fileTimeID = "0804";

        public const string PCCPCHRSCPMeanValueID = "5F13030D";
        public const string PCCPCHRSCPSampleNum = "5F13030A";
        public const string PCCPCHRSCPMaxID = "5F13030B";
        public const string PCCPCHRSCPMinID = "5F13030C";

        public int CellID = 0;
        public int Channel = 0;
        public int CPI = 0;
        public int fileTime = 0;
        //按照小区栅格聚合
        private Dictionary<int, Dictionary<string, double>> wCellInfoDic = new Dictionary<int, Dictionary<string, double>>();
        public Dictionary<int, Dictionary<string, double>> WCellInfoDic
        {
            get
            {
                return wCellInfoDic;
            }
        }

        public double this[int cellID, string typeID]
        {
            get
            {
                if (wCellInfoDic.ContainsKey(cellID))
                {
                    if (wCellInfoDic[cellID].ContainsKey(typeID))
                    {
                        return wCellInfoDic[cellID][typeID];
                    }
                }
                return -999999;
            }
        }

        public static DataScan_TD FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataScan_TD data = new DataScan_TD();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataScan_TD data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);

            if (data.CellID != 0)
            {
                if (!this.WCellInfoDic.ContainsKey(data.CellID))
                {
                    this.WCellInfoDic[data.CellID] = new Dictionary<string, double>();
                }
                StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.WCellInfoDic[data.CellID]);
            }
        }

        internal void copyFrom(DataScan_TD data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }

            wCellInfoDic.Clear();
            foreach (int str in data.wCellInfoDic.Keys)
            {
                wCellInfoDic.Add(str, data.wCellInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Tc_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }

    /// <summary>
    /// LTE Scan
    /// </summary>
    public class DataScan_LTE : PartialData
    {
        public const string PSSRPSampleNum = "5F230101";
        public const string PSSRPMeanValue = "5F230102";
        public const string PSSRPMaxValue = "5F230103";
        public const string PSSRPMinValue = "5F230104";

        public int CellID
        {
            get;
            set;
        }
        public int EARFCN
        {
            get;
            set;
        }
        public int PCI
        {
            get;
            set;
        }
        public int FileTime
        {
            get;
            set;
        }

        ////按照小区栅格聚合
        private Dictionary<int, Dictionary<string, double>> cellInfoDic = new Dictionary<int, Dictionary<string, double>>();
        public Dictionary<int, Dictionary<string, double>> CellInfoDic
        {
            get
            {
                return cellInfoDic;
            }
        }

        public double this[int cellID, string typeID]
        {
            get
            {
                if (cellInfoDic.ContainsKey(cellID))
                {
                    if (cellInfoDic[cellID].ContainsKey(typeID))
                    {
                        return cellInfoDic[cellID][typeID];
                    }
                }
                return -999999;
            }
        }

        public static DataScan_LTE FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataScan_LTE data = new DataScan_LTE();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void AddStatData(DataScan_LTE data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
            if (data.CellID != 0)
            {
                if (!this.CellInfoDic.ContainsKey(data.CellID))
                {
                    this.CellInfoDic[data.CellID] = new Dictionary<string, double>();
                }
                StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.CellInfoDic[data.CellID]);
            }
        }

        internal void CopyFrom(DataScan_LTE data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }

            cellInfoDic.Clear();
            foreach (int str in data.cellInfoDic.Keys)
            {
                cellInfoDic.Add(str, data.cellInfoDic[str]);
            }
        }

        //not perfect
        public object GetFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Lc_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }

    /// <summary>
    /// W Scan (新16个image结构)
    /// </summary>
    public class DataScan_WCDMA : PartialData
    {
        public const string CellNameID = "0848";
        public const string CellIDID = "0849";
        public const string ChannelID = "084A";
        public const string CPIID = "084B";
        public const string fileTimeID = "0804";

        public const string RSCPMeanValueID = "5F153F";
        public const string RSCPSampleNumID = "5F153C";
        public const string RSCPMaxID = "5F153D";
        public const string RSCPMinID = "5F153E";

        public int CellID = 0;
        public int Channel = 0;
        public int CPI = 0;
        public int fileTime = 0;

        //按照小区栅格聚合
        private Dictionary<int, Dictionary<string, double>> wCellInfoDic = new Dictionary<int, Dictionary<string, double>>();
        public Dictionary<int, Dictionary<string, double>> WCellInfoDic
        {
            get
            {
                return wCellInfoDic;
            }
        }

        public double this[int cellID, string typeID]
        {
            get
            {
                if (wCellInfoDic.ContainsKey(cellID))
                {
                    if (wCellInfoDic[cellID].ContainsKey(typeID))
                    {
                        return wCellInfoDic[cellID][typeID];
                    }
                }
                return -999999;
            }
        }

        public static DataScan_WCDMA FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataScan_WCDMA data = new DataScan_WCDMA();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataScan_WCDMA data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);

            if (data.CellID != 0)
            {
                if (!this.WCellInfoDic.ContainsKey(data.CellID))
                {
                    this.WCellInfoDic[data.CellID] = new Dictionary<string, double>();
                }
                StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.WCellInfoDic[data.CellID]);
            }
        }

        internal void copyFrom(DataScan_WCDMA data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }

            wCellInfoDic.Clear();
            foreach (int str in data.wCellInfoDic.Keys)
            {
                wCellInfoDic.Add(str, data.wCellInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Wc_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }
    /// <summary>
    /// W Scan (新16个image结构)
    /// </summary>
    public class DataScan_CDMA : PartialData
    {
        public static DataScan_CDMA FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataScan_CDMA data = new DataScan_CDMA();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataScan_CDMA data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
        }

        internal void copyFrom(DataScan_CDMA data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Cc_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("0801", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("0808", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("0809", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }

    public class DataNR : PartialData
    {
        public static DataNR FillFrom(MasterCom.RAMS.Net.Content c)
        {
            DataNR data = new DataNR();
            List<byte[]> imgList = new List<byte[]>();
            for (int i = 0; i < 16; i++)
            {
                imgList.Add(c.GetParamBytes());
            }
            for (int i = 0; i < 16; i++)
            {
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgList[i]);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            return data;
        }

        public void addStatData(DataNR data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
        }

        internal void copyFrom(DataNR data)
        {
            wInfoDic.Clear();
            foreach (string str in data.wInfoDic.Keys)
            {
                wInfoDic.Add(str, data.wInfoDic[str]);
            }
        }
        public object getFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Nr_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        internal bool ApplyGisHeader(MasterCom.RAMS.Net.GridPartParam gpp)
        {
            double temp = 0;
            if (WInfoDic.TryGetValue("80040001", out temp))
            {
                gpp.filebase = new FileBase();
                gpp.filebase.fileId = (int)temp;
            }
            if (WInfoDic.TryGetValue("80040008", out temp))
            {
                gpp.LTLng = 0.0000001 * temp;
            }
            if (WInfoDic.TryGetValue("80040009", out temp))
            {
                gpp.LTLat = 0.0000001 * temp;
            }
            return true;
        }
    }


    /// <summary>
    /// NR Scan
    /// </summary>
    public class DataScan_NR : PartialData
    {
        public const string PSSRPSampleNumKey = "BB040008";
        public const string PSSRPMeanValueKey = "BB040009";

        public int CellID
        {
            get;
            set;
        }
        public int EARFCN
        {
            get;
            set;
        }
        public int PCI
        {
            get;
            set;
        }
        public int FileTime
        {
            get;
            set;
        }

        ////按照小区栅格聚合
        private Dictionary<int, Dictionary<string, double>> cellInfoDic = new Dictionary<int, Dictionary<string, double>>();
        public Dictionary<int, Dictionary<string, double>> CellInfoDic
        {
            get
            {
                return cellInfoDic;
            }
        }

        public double this[int cellID, string typeID]
        {
            get
            {
                if (cellInfoDic.ContainsKey(cellID))
                {
                    if (cellInfoDic[cellID].ContainsKey(typeID))
                    {
                        return cellInfoDic[cellID][typeID];
                    }
                }
                return -999999;
            }
        }

        public void AddStatData(DataScan_NR data)
        {
            StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.wInfoDic);
            if (data.CellID != 0)
            {
                if (!this.CellInfoDic.ContainsKey(data.CellID))
                {
                    this.CellInfoDic[data.CellID] = new Dictionary<string, double>();
                }
                StatDataConverter.gatherStatImgInfo(data.wInfoDic, this.CellInfoDic[data.CellID]);
            }
        }

        //not perfect
        public object GetFieldValue(string fieldName, int arg)
        {
            if (fieldName.Substring(0, 3).Equals("Nc_"))
            {
                if (fieldName.Length > 3)
                {
                    string dicKey = fieldName.Substring(3, fieldName.Length - 3);
                    if (this.WInfoDic.ContainsKey(dicKey))
                    {
                        return this.WInfoDic[dicKey];
                    }
                    else
                    {
                        StatImgDefItem sidef = InterfaceManager.GetInstance().GetStatImgDef(dicKey);
                        if (sidef != null)
                        {
                            if (sidef.gatherMethod == GatherMethod.E_MIN)
                            {
                                return 999999;
                            }
                            else if (sidef.gatherMethod == GatherMethod.E_MAX)
                            {
                                return -999999;
                            }
                        }
                        return 0;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
    }
}