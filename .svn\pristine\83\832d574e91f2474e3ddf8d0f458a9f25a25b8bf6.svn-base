﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRScanHighCoverateRoadForm : MinCloseForm
    {
        public NRScanHighCoverateRoadForm()
        {
            InitializeComponent();
        }

        private List<NRScanHighCoverateRoadInfo> relRoadCoverageList = null;
        private List<NRScanHighCoverateRoadInfo> absRoadCoverageList = null;

        public void FillData(List<NRScanHighCoverateRoadInfo> relRoadCoverageList,
            List<NRScanHighCoverateRoadInfo> absRoadCoverageList)
        {
            this.relRoadCoverageList = relRoadCoverageList;
            this.absRoadCoverageList = absRoadCoverageList;

            gcRel.DataSource = relRoadCoverageList;
            gcRel.RefreshDataSource();

            gcAbs.DataSource = absRoadCoverageList;
            gcAbs.RefreshDataSource();

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void miExportSummaryRel_Click(object sender, EventArgs e)
        {
            exportSummaryToExcel(relRoadCoverageList);
        }

        private void miExportSummaryAbs_Click(object sender, EventArgs e)
        {
            exportSummaryToExcel(absRoadCoverageList);
        }

        private void exportSummaryToExcel(List<NRScanHighCoverateRoadInfo> roadCoverageList)
        {
            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("序号");
            title.Add("道路名称");
            title.Add("距离(米)");
            title.Add("高重叠覆盖点占比(%)");
            title.Add("采样点数");
            title.Add("第一强最大值");
            title.Add("第一强最小值");
            title.Add("第一强平均值");
            title.Add("经度");
            title.Add("纬度");
            title.Add("文件名");
            title.Add("开始时间");
            title.Add("结束时间");
            content.Add(title);

            foreach (var roadInfo in roadCoverageList)
            {
                List<object> roadPart = new List<object>();
                roadPart.Add(roadInfo.SN);
                roadPart.Add(roadInfo.RoadName);
                roadPart.Add(roadInfo.Distance);
                roadPart.Add(roadInfo.Percent);
                roadPart.Add(roadInfo.SampleCount);
                roadPart.Add(roadInfo.Rsrp.Max);
                roadPart.Add(roadInfo.Rsrp.Min);
                roadPart.Add(roadInfo.Rsrp.Avg);
                roadPart.Add(roadInfo.LongitudeMid);
                roadPart.Add(roadInfo.LatitudeMid);
                roadPart.Add(roadInfo.FileName);
                roadPart.Add(roadInfo.FirstTime);
                roadPart.Add(roadInfo.LastTime);
                content.Add(roadPart);
            }
            ExcelNPOIManager.ExportToExcel(content);
        }

        private void miExportRel_Click(object sender, EventArgs e)
        {
            exportToExcel(relRoadCoverageList);
        }

        private void miExportAbs_Click(object sender, EventArgs e)
        {
            exportToExcel(absRoadCoverageList);
        }

        private void exportToExcel(List<NRScanHighCoverateRoadInfo> roadCoverageList)
        {
            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("序号");
            title.Add("道路名称");
            title.Add("距离(米)");
            title.Add("高重叠覆盖点占比(%)");
            title.Add("采样点数");
            title.Add("第一强最大值");
            title.Add("第一强最小值");
            title.Add("第一强平均值");
            title.Add("路段中心经度");
            title.Add("路段中心纬度");
            title.Add("文件名");
            title.Add("开始时间");
            title.Add("结束时间");

            title.Add("采样点编号");
            title.Add("采样点经度");
            title.Add("采样点纬度");

            title.Add("小区名称");
            //title.Add("小区状态");
            title.Add("TAC");
            title.Add("ECI");
            title.Add("EARFCN");
            title.Add("PCI");
            title.Add("RSRP");
            title.Add("与采样点距离（米）");
            title.Add("小区经度");
            title.Add("小区纬度");
            content.Add(title);

            foreach (var roadInfo in roadCoverageList)
            {
                List<object> roadPart = new List<object>();
                roadPart.Add(roadInfo.SN);
                roadPart.Add(roadInfo.RoadName);
                roadPart.Add(roadInfo.Distance);
                roadPart.Add(roadInfo.Percent);
                roadPart.Add(roadInfo.SampleCount);
                roadPart.Add(roadInfo.Rsrp.Max);
                roadPart.Add(roadInfo.Rsrp.Min);
                roadPart.Add(roadInfo.Rsrp.Avg);
                roadPart.Add(roadInfo.LongitudeMid);
                roadPart.Add(roadInfo.LatitudeMid);
                roadPart.Add(roadInfo.FileName);
                roadPart.Add(roadInfo.FirstTime);
                roadPart.Add(roadInfo.LastTime);
                foreach (var ptInfo in roadInfo.SampleList)
                {
                    List<object> pointPart = new List<object>(roadPart);
                    pointPart.Add(ptInfo.SN);
                    pointPart.Add(ptInfo.Tp.Longitude);
                    pointPart.Add(ptInfo.Tp.Latitude);
                    foreach (var cellInfo in ptInfo.CellList)
                    {
                        List<object> cellPart = new List<object>(pointPart);
                        cellPart.Add(cellInfo.CellName);
                        //cellPart.Add(cellInfo.CellTypeDesc);
                        cellPart.Add(cellInfo.TAC);
                        cellPart.Add(cellInfo.NCI);
                        cellPart.Add(cellInfo.ARFCN);
                        cellPart.Add(cellInfo.PCI);
                        cellPart.Add(cellInfo.Rsrp);
                        cellPart.Add(cellInfo.Distance);
                        cellPart.Add(cellInfo.Longitude);
                        cellPart.Add(cellInfo.Latitude);
                        content.Add(cellPart);
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(content);
        }

        private void gvRoadRel_DoubleClick(object sender, EventArgs e)
        {
            if (gvRoadRel.SelectedRowsCount > 0)
            {
                var info = gvRoadRel.GetRow(gvRoadRel.GetSelectedRows()[0]);
                switch (info)
                {
                    case NRScanHighCoverateRoadInfo roadInfo:
                        NRScanHighCoverateRoadLayerHelper.Instance.ShowRoadInfo(roadInfo);
                        break;
                    case NRScanHighCoverateRoadPointInfo pointInfo:
                        NRScanHighCoverateRoadLayerHelper.Instance.ShowPointInfo(pointInfo);
                        break;
                }
            }
        }

        private void gvRoadAbs_DoubleClick(object sender, EventArgs e)
        {
            if (gvRoadAbs.SelectedRowsCount > 0)
            {
                var info = gvRoadAbs.GetRow(gvRoadAbs.GetSelectedRows()[0]);
                switch (info)
                {
                    case NRScanHighCoverateRoadInfo roadInfo:
                        NRScanHighCoverateRoadLayerHelper.Instance.ShowRoadInfo(roadInfo);
                        break;
                    case NRScanHighCoverateRoadPointInfo pointInfo:
                        NRScanHighCoverateRoadLayerHelper.Instance.ShowPointInfo(pointInfo);
                        break;
                }
            }
        }
    }

    public class NRScanHighCoverateRoadLayerHelper
    {
        readonly MainModel mModel;
        private NRScanHighCoverateRoadLayerHelper()
        {
            mModel = MainModel.GetInstance();
        }

        private static NRScanHighCoverateRoadLayerHelper instance = null;
        public static NRScanHighCoverateRoadLayerHelper Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NRScanHighCoverateRoadLayerHelper();
                }
                return instance;
            }
        }

        public void ShowRoadInfo(NRScanHighCoverateRoadInfo info)
        {
            mModel.DTDataManager.Clear();
            lnglatLineDic = new Dictionary<string, Line>();
            DbPoint goViewPoint = null;

            foreach (NRScanHighCoverateRoadPointInfo pointInfo in info.SampleList)
            {
                mModel.DTDataManager.Add(pointInfo.Tp);
                this.addLineInPoint(pointInfo);
            }

            goViewPoint = new DbPoint(info.LongitudeMid, info.LatitudeMid);

            mModel.FireDTDataChanged(this);
            TempLayer.Instance.Draw(this.drawLine);
            mModel.MainForm.GetMapForm().GoToView(goViewPoint.x, goViewPoint.y);
        }

        public void ShowPointInfo(NRScanHighCoverateRoadPointInfo info)
        {
            mModel.DTDataManager.Clear();
            lnglatLineDic = new Dictionary<string, Line>();
            DbPoint goViewPoint = null;

            mModel.DTDataManager.Add(info.Tp);
            this.addLineInPoint(info);
            goViewPoint = new DbPoint(info.Tp.Longitude, info.Tp.Latitude);

            mModel.FireDTDataChanged(this);
            TempLayer.Instance.Draw(this.drawLine);
            mModel.MainForm.GetMapForm().GoToView(goViewPoint.x, goViewPoint.y);
        }

        private void addLineInPoint(NRScanHighCoverateRoadPointInfo ptInfo)
        {
            double ptLng = ptInfo.Tp.Longitude;
            double ptLat = ptInfo.Tp.Latitude;
            foreach (var cellInfo in ptInfo.CellList)
            {
                if (cellInfo.Cell == null)
                {
                    continue;
                }
                double cellLng = cellInfo.Cell.EndPointLongitude;
                double cellLat = cellInfo.Cell.EndPointLatitude;

                string key = string.Format("{0}_{1}_{2}_{3}", ptLng, ptLat, cellLng, cellLat);
                if (lnglatLineDic.ContainsKey(key))
                {
                    continue;
                }

                Line line = new Line();
                line.P1 = new DbPoint(ptLng, ptLat);
                line.P2 = new DbPoint(cellLng, cellLat);
                //if (cellInfo.CellType == LTEScanHighCoverageCellType.FarCover)
                //{
                //    line.Color = farCoverColor;
                //}
                //else if (cellInfo.CellType == LTEScanHighCoverageCellType.LeakOut)
                //{
                //    line.Color = Color.Chocolate;
                //}
                //else
                //{
                    line.Color = Color.Blue;
                //}
                lnglatLineDic.Add(key, line);
            }
        }

        private void drawLine(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            if (lnglatLineDic.Count == 0)
            {
                return;
            }

            foreach (Line line in lnglatLineDic.Values)
            {
                PointF p1, p2;
                mop.ToDisplay(line.P1, out p1);
                mop.ToDisplay(line.P2, out p2);
                Pen pen = new Pen(line.Color, 2);
                pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;

                graphics.DrawLine(pen, p1, p2);
            }
        }

        //private Color farCoverColor = Color.Red;
        //private Color leakOutColor = Color.Blue;
        //private Color normalColor = Color.Lime;
        private Dictionary<string, Line> lnglatLineDic = new Dictionary<string, Line>();

        private class Line
        {
            public DbPoint P1;
            public DbPoint P2;
            public Color Color;
        }
    }
}
