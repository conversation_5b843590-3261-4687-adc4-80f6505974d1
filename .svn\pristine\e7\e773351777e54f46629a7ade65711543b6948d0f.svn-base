﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// 有关程序集的常规信息通过下列属性集
// 控制。更改这些属性值可修改
// 与程序集关联的信息。
[assembly: AssemblyTitle("RAMS")]
[assembly: AssemblyDescription("路网通 － 路测数据综合分析应用平台")]
[assembly: AssemblyConfiguration("")]

[assembly: AssemblyCompany("MasterCom")]
[assembly: AssemblyProduct("RAMS")]

[assembly: AssemblyCopyright("版权所有© MasterCom 2008-2020")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// 将 ComVisible 设置为 false 使此程序集中的类型
// 对 COM 组件不可见。如果需要从 COM 访问此程序集中的类型，
// 则将该类型上的 ComVisible 属性设置为 true。
[assembly: ComVisible(false)]

// 如果此项目向 COM 公开，则下列 GUID 用于类型库的 ID
[assembly: Guid("aca73c5b-e97d-4c90-af01-7288cdcbd4db")]

// 程序集的版本信息由下面四个值组成:
//
//      主版本
//      次版本 
//      内部版本号
//      修订号
//
[assembly: AssemblyVersion("**********")]
[assembly: AssemblyFileVersion("**********")]
[assembly: log4net.Config.XmlConfigurator(Watch = true, ConfigFile = "log4net.config")]
