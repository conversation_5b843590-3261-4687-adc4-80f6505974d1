﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoLteESRVCCAnaByFile : VoLteESRVCCAnaByRegion
    {
        protected VoLteESRVCCAnaByFile()
            : base()
        {
        }

        private static VoLteESRVCCAnaByFile intance = null;
        public static new VoLteESRVCCAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new VoLteESRVCCAnaByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "eSRVCC无线性能分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class VoLteESRVCCAnaByFile_FDD : VoLteESRVCCAnaByFile
    {
        private static VoLteESRVCCAnaByFile_FDD instance = null;
        public static new VoLteESRVCCAnaByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLteESRVCCAnaByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected VoLteESRVCCAnaByFile_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            this.Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_gsm_SC_LAC");
            Columns.Add("lte_fdd_gsm_SC_CI");
            Columns.Add("lte_td_SC_LAC");
            Columns.Add("lte_td_SC_CellID");
            Columns.Add("lte_fdd_gsm_DM_RxLevBCCH");
            Columns.Add("lte_fdd_gsm_DM_RxLevSub");
            Columns.Add("C_I");
            Columns.Add("lte_td_DM_PCCPCH_RSCP");
        }
        public override string Name
        {
            get
            {
                return "VOLTE_FDD eSRVCC无线性能分析(按文件)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30022, this.Name);
        }
    }
}
