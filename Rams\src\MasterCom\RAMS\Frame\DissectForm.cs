﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Frame
{
    public partial class DissectForm : BaseFormStyle
    {
        ContextMenuStrip contextMenu = new ContextMenuStrip();
        ToolStripMenuItem itemCopyRow = null;

        public DissectForm()
        {
            InitializeComponent();
            this.itemCopyRow = new ToolStripMenuItem("复制文本");
            this.itemCopyRow.Click += itemCopyRow_Click;
            this.contextMenu.Items.Add(this.itemCopyRow);
            this.treeView.ContextMenuStrip = this.contextMenu;
            this.treeView.KeyDown += DissectForm_KeyDown;
        }

        void DissectForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Modifiers.CompareTo(Keys.Control) == 0 && e.KeyCode == Keys.C)
            {
                this.copyText();
            }
        }

        void itemCopyRow_Click(object sender, EventArgs e)
        {
            this.copyText();
        }

        string copyText()
        {
            TreeNode node = this.treeView.SelectedNode;
            if (node == null)
            {
                return string.Empty;
            }
            Clipboard.SetDataObject(node.Text);
            return node.Text;
        }

        public void Dissect(MessageWithSource msg)
        {
            treeView.BeginUpdate();
            try
            {
                srcTextBox.SetSrcCode(msg.Source);
                srcTextBox.DisplaySrcCode();
                if (msg.Source != null)
                {
                    MessageDecodeHelper.DissectToTree(msg.Source, msg.Source.Length, msg.ID, treeView);
                }
                else
                {
                    treeView.Nodes.Clear();
                }

                treeView.ExpandAll();
                if (treeView.Nodes.Count > 0)
                {
                    treeView.Nodes[0].EnsureVisible(); //added by wj 展开后滚动条停在最前面
                }
            }
            catch (Exception err)
            {
                XtraMessageBox.Show(err.Message);
                return;
            }
            treeView.EndUpdate();
        }

        private void Form1_SizeChanged(object sender, EventArgs e)
        {
            if (srcTextBox.Visible)
            {
                int nWidth = this.Size.Width;
                if (nWidth < 540)
                {
                    this.Width = 540;
                }
            }
        }

        private void treeView1_MouseUp(object sender, MouseEventArgs e)
        {
            TreeNode treeNode = treeView.GetNodeAt(e.X, e.Y);
            if (treeNode == null)
            {
                return;
            }
            try
            {
                MessageDecodeHelper.SetTextBoxHighLight(treeNode, srcTextBox);
            }
            catch
            {
                //continue
            }
        }

        private int nBack = 0;
        private System.IO.StreamWriter fileExport;

        void ExportNode(TreeNode node)
        {
            for (int i = 0; i < nBack; i++)
            {
                Console.Write("  ");
                fileExport.Write("  ");
            }
            Console.WriteLine(node.Text);
            fileExport.WriteLine(node.Text);
            foreach (TreeNode n in node.Nodes)
            {
                nBack++;
                ExportNode(n);
                nBack--;
            }
        }

        private void tsBtnSaveTxt_Click(object sender, EventArgs e)
        {
            System.String resultFile = "";
            SaveFileDialog openFileDialog = new SaveFileDialog();
            openFileDialog.Filter = "txt files (*.txt)|*.txt|All files (*.*)|*`.*";
            openFileDialog.RestoreDirectory = true;
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                resultFile = openFileDialog.FileName;
            }
            else
            {
                return;
            }

            try
            {
                fileExport = new System.IO.StreamWriter(resultFile, false);
            }
            catch (System.IO.IOException err)
            {
                XtraMessageBox.Show(err.Message);
                return;
            }

            foreach (TreeNode n in treeView.Nodes)
            {
                ExportNode(n);
            }

            fileExport.WriteLine("");
            fileExport.WriteLine("源码");
            String strTemp = srcTextBox.Text;
            strTemp = strTemp.Replace("\n", "\r\n");
            fileExport.Write(strTemp);
            fileExport.WriteLine("");

            fileExport.Flush();
            fileExport.Close();
        }

        private int hight = 0;
        private void tsBtnHide_Click(object sender, EventArgs e)
        {
            srcTextBox.Visible = !tsBtnHide.Checked;
            if (tsBtnHide.Checked)
            {
                splitContainer1.Panel2Collapsed = true;
                hight = splitContainer1.SplitterDistance;
                srcTextBox.Visible = false;
            }
            else
            {
                splitContainer1.Panel2Collapsed = false;
                splitContainer1.SplitterDistance = hight;
                srcTextBox.Visible = true;
            }
            Invalidate();
        }

        private void DissectForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                //e.Cancel = true
                //this.Visible = false
            }
        }

        private TreeNode searchNext(TreeNode startNode, string matchStr)
        {
            if (this.treeView.GetNodeCount(false) <= 0)
            {
                return null;
            }
            string str = matchStr;
            TreeNode node = startNode;
            for (int round = 0; round < 2; round++)
            {
                if (node == null)
                {
                    round = 1;
                    node = this.treeView.Nodes[0];
                    if (node.Text.ToUpper().IndexOf(str) >= 0)
                    {
                        return node;
                    }
                }
                TreeNode retNode = getValidNode(str, ref node);
                if (retNode != null)
                {
                    return retNode;
                }
            }
            return null;
        }

        private TreeNode getValidNode(string str, ref TreeNode node)
        {
            while (node != null)
            {
                TreeNode retNode = this.findNodeInChild(node, 0, str);
                if (retNode == null)
                {
                    node = this.getNextNode(node);
                    if (node == null)
                    {
                        break;
                    }
                    retNode = node;
                }
                if (retNode.Text.ToUpper().IndexOf(str) >= 0)
                {
                    return retNode;
                }
            }
            return null;
        }

        private TreeNode searchPre()
        {
            string str = this.tsTextBoxSearch.Text.ToUpper().Trim();
            TreeNode curNode = this.treeView.SelectedNode;
            TreeNode firstNode = this.searchNext(curNode, str);
            TreeNode lastNode = null;
            TreeNode temNode = null;

            curNode = curNode == null ? firstNode : curNode;
            if (firstNode == null)
            {
                return null;
            }
            temNode = firstNode;
            do
            {
                lastNode = temNode;
                temNode = this.searchNext(temNode, str);
            } while (temNode != curNode && temNode != firstNode);
            return lastNode;
        }
        private TreeNode getNextNode(TreeNode node)
        {
            int index = node.Index + 1;
            if (node.Level == 0)
            {
                if (index < this.treeView.GetNodeCount(false))
                {
                    return this.treeView.Nodes[index];
                }
                else
                {
                    return null;
                }
            }
            else if (node.Level > 0)
            {
                if (index < node.Parent.GetNodeCount(false))
                {
                    return node.Parent.Nodes[index];
                }
                return this.getNextNode(node.Parent);
            }
            return null;
        }
        private TreeNode findNodeInChild(TreeNode node, int startIndex, string str)
        {
            TreeNode retNode = null;
            for (int i = startIndex; i < node.GetNodeCount(false); i++)
            {
                TreeNode tn = node.Nodes[i];
                if (tn.Text.ToUpper().IndexOf(str) >= 0)
                {
                    return tn;
                }
                retNode = this.findNodeInChild(tn, 0, str);
                if (retNode != null)
                {
                    return retNode;
                }
            }
            return null;
        }

        private void toolStripButtonNext_Click(object sender, EventArgs e)
        {
            string str = this.tsTextBoxSearch.Text.ToUpper().Trim();
            this.treeView.SelectedNode = this.searchNext(this.treeView.SelectedNode, str);
            this.treeView.Focus();
        }

        private void toolStripButtonCopy_Click(object sender, EventArgs e)
        {
            this.tsTextBoxSearch.Text = this.copyText();
        }

        private void toolStripButtonPre_Click(object sender, EventArgs e)
        {
            this.treeView.SelectedNode = this.searchPre();
            this.treeView.Focus();
        }

        private void tsBtnFixed_Click(object sender, EventArgs e)
        {
            if (this.tsBtnFixed.Checked)
            {
                this.tsBtnFixed.Image = Properties.Resources.fixedPin;
                this.tsBtnFixed.ToolTipText = "取消冻结内容";
            }
            else
            {
                this.tsBtnFixed.Image = Properties.Resources.nonFixed;
                this.tsBtnFixed.ToolTipText = "冻结内容";
            }
        }

        public bool IsFixedContent
        {
            get { return this.tsBtnFixed.Checked; }
        }

    }
}