﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTGSMCellReselectAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public List<ZTGSMCellReselectAnaItem> resultList { get; set; } = new List<ZTGSMCellReselectAnaItem>();    //保存结果
        public ZTGSMCellReselectAnaCondition cellReselCondition { get; set; } = new ZTGSMCellReselectAnaCondition();   //查询条件

        public ZTGSMCellReselectAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;
        } 

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTGSMCellReselectAnaItem>();
        }

        ZTGSMCellReselectAnaSetForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTGSMCellReselectAnaSetForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                cellReselCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileMng.TestPoints;
                List<Event> eventList = fileMng.Events;

                for (int eLoop = 0; eLoop < eventList.Count; ++eLoop)
                {
                    Event e = eventList[eLoop];
                    if (e.ID != 40)
                    {
                        continue;
                    }

                    int index = -1;
                    if ((index = GetNearestTestPointIndex(e.SN, testPointList)) == -1)
                    {
                        continue;
                    }

                    ZTGSMCellReselectAnaItem item = new ZTGSMCellReselectAnaItem(e.FileName, e);

                    long tpTimeHead = e.Time * 1000L + e.Millisecond - cellReselCondition.BeforeSecond * 1000L;
                    long tpTimeTail = e.Time * 1000L + e.Millisecond + cellReselCondition.AfterSecond * 1000L;

                    tpTimeTail = getTpTimeTail(eventList, eLoop, item, tpTimeTail);

                    addTP(testPointList, index, item, tpTimeHead, tpTimeTail);

                    item.SN = resultList.Count + 1;
                    resultList.Add(item);
                }
            } 
        }

        private long getTpTimeTail(List<Event> eventList, int eLoop, ZTGSMCellReselectAnaItem item, long tpTimeTail)
        {
            //step1：向后看，是否存在起呼事件
            for (int i = eLoop + 1; i < eventList.Count; i++)
            {
                if (tpTimeTail < (eventList[i].Time * 1000L + eventList[i].Millisecond))
                {
                    break;
                }

                //存在起呼,比较起呼后看RxQuality的时间与重选后的时间，取最大值
                if (eventList[i].ID == 1 || eventList[i].ID == 2)
                {
                    item.SetAttemptEvt(eventList[i]);

                    long curTime = eventList[i].Time * 1000L + eventList[i].Millisecond + cellReselCondition.AfterAttemptSecond * 1000L;

                    if (curTime > tpTimeTail)
                    {
                        tpTimeTail = curTime;
                    }
                    break;
                }
            }

            return tpTimeTail;
        }

        private void addTP(List<TestPoint> testPointList, int index, ZTGSMCellReselectAnaItem item, long tpTimeHead, long tpTimeTail)
        {
            for (int i = index; i >= 0; --i)
            {
                TestPoint tp = testPointList[i];
                if ((tp.Time + cellReselCondition.BeforeSecond) * 1000L + tp.Millisecond < tpTimeHead)
                {
                    break;
                }
                item.AddBeforeTp(tp);
            }
            for (int i = index + 1; i < testPointList.Count; ++i)
            {
                TestPoint tp = testPointList[i];
                if (tpTimeTail < tp.Time * 1000L + tp.Millisecond)
                {
                    break;
                }
                item.AddAfterTp(tp);
            }
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTGSMCellReselectAnaListForm).FullName);
            ZTGSMCellReselectAnaListForm cellReselectAnaListForm = obj == null ? null : obj as ZTGSMCellReselectAnaListForm;
            if (cellReselectAnaListForm == null || cellReselectAnaListForm.IsDisposed)
            {
                cellReselectAnaListForm = new ZTGSMCellReselectAnaListForm(MainModel);
            }

            cellReselectAnaListForm.FillData(resultList);
            if (!cellReselectAnaListForm.Visible)
            {
                cellReselectAnaListForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        /// <summary>
        /// tpList升序，寻找最大的index使得tpList[index] <= eventSN
        /// </summary>
        /// <param name="eventSN"></param>
        /// <param name="tpList"></param>
        /// <returns></returns>
        protected int GetNearestTestPointIndex(int eventSN, List<TestPoint> tpList)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > eventSN)
                {
                    index = i - 1;
                    break;
                }
            }
            return index;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class ZTGSMCellReselectAnaItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }

        public ZTGSMCellReselectAnaCellItem SrcCellItem{ get; set; }
        public ZTGSMCellReselectAnaCellItem DestCellItem{ get; set; }

        public ZTGSMCellReselectAnaTpItem BeforeTpItem{ get; set; }
        public ZTGSMCellReselectAnaTpItem AfterTpItem{ get; set; }

        public Event ReselEvt { get; set; }
        public Event AttemptEvt { get; set; }

        public string IsAttemptEvt { get; set; }

        public ZTGSMCellReselectAnaItem(string fileName, Event reselEvt)
        {
            FileName = fileName;
            ReselEvt = reselEvt;
            SrcCellItem = new ZTGSMCellReselectAnaCellItem();
            DestCellItem = new ZTGSMCellReselectAnaCellItem();
            BeforeTpItem = new ZTGSMCellReselectAnaTpItem();
            AfterTpItem = new ZTGSMCellReselectAnaTpItem();

            SetCellItem(SrcCellItem, (int)reselEvt["LAC"], (int)reselEvt["CI"], reselEvt);
            SetCellItem(DestCellItem, (int)reselEvt["TargetLAC"], (int)reselEvt["TargetCI"], reselEvt);

            IsAttemptEvt = "否";
        }

        public void AddBeforeTp(TestPoint tp)
        {
            AddTpInfo(this.BeforeTpItem, tp);
        }

        public void AddAfterTp(TestPoint tp)
        {
            AddTpInfo(this.AfterTpItem, tp);
        }

        public void AddTpInfo(ZTGSMCellReselectAnaTpItem tpItem,TestPoint tp)
        {
            short? rxlevSub = (short?)tp["RxLevSub"];            
            if (rxlevSub != null)
            {
                tpItem.Rxlev += (float)rxlevSub;
                tpItem.RxlevCount++;
            }

            int? rxQual = (int?)(byte?)tp["RxQualSub"];            
            if (rxQual != null)
            {
                tpItem.RxQual += (float)rxQual;
                tpItem.RxQualCount++;
            }

            short? c2i = (short?)tp["C_I", 0];
            if (c2i != null && c2i <= 50)
            {
                tpItem.C2I += (float)c2i;
                tpItem.C2ICount++;
            }
        }

        public void SetCellItem(ZTGSMCellReselectAnaCellItem cellItem, int lac, int ci, Event reselEvt)
        {
            Cell cell = CellManager.GetInstance().GetCell(reselEvt.DateTime, (ushort)lac, (ushort)ci);

            if (cell != null)
            {
                cellItem.GSMCell = cell;
                cellItem.CellName = cell.Name;
                cellItem.BTSName = cell.BelongBTS.Name;
                cellItem.Distance = Math.Round(cell.GetDistance(reselEvt.Longitude, reselEvt.Latitude),2).ToString();
            }

            cellItem.LAC = lac.ToString();
            cellItem.CI = ci.ToString();
        }

        public void SetAttemptEvt(Event attemptEvt)
        {
            AttemptEvt = attemptEvt;
            IsAttemptEvt = "是";
        }

        #region 预处理
        public string BeforeRxlevAvg
        {
            get
            {
                if (BeforeTpItem.RxlevCount > 0)
                {
                    return Math.Round(BeforeTpItem.Rxlev/BeforeTpItem.RxlevCount,2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeRxQualAvg
        {
            get
            {
                if (BeforeTpItem.RxQualCount > 0)
                {
                    return Math.Round(BeforeTpItem.RxQual / BeforeTpItem.RxQualCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string BeforeC2IAvg
        {
            get
            {
                if (BeforeTpItem.C2ICount > 0)
                {
                    return Math.Round(BeforeTpItem.C2I / BeforeTpItem.C2ICount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterRxlevAvg
        {
            get
            {
                if (AfterTpItem.RxlevCount > 0)
                {
                    return Math.Round(AfterTpItem.Rxlev / AfterTpItem.RxlevCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterRxQualAvg
        {
            get
            {
                if (IsAttemptEvt == "否")    //避免采样点未清理，导致未发生起呼但是有Rxquality指标，重要！
                {
                    return "";
                }
                if (AfterTpItem.RxQualCount > 0)
                {
                    return Math.Round(AfterTpItem.RxQual / AfterTpItem.RxQualCount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string AfterC2IAvg
        {
            get
            {
                if (IsAttemptEvt == "否")    //避免采样点未清理，导致未发生起呼但是有C/I指标，重要！
                {
                    return "";
                }
                if (AfterTpItem.C2ICount > 0)
                {
                    return Math.Round(AfterTpItem.C2I / AfterTpItem.C2ICount, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }

        public string GridName
        {
            get
            {
                return GISManager.GetInstance().GetGridDesc(ReselEvt.Longitude, ReselEvt.Latitude);
            }
        }
        #endregion
    }

    public class ZTGSMCellReselectAnaCellItem
    {
        public Cell GSMCell { get; set; }
        public string CellName { get; set; }
        public string BTSName { get; set; }
        public string LAC { get; set; }
        public string CI { get; set; }
        public string Distance { get; set; }
    }

    public class ZTGSMCellReselectAnaTpItem
    {
        public float Rxlev { get; set; }
        public float RxlevCount { get; set; }
        public float RxQual { get; set; }
        public float RxQualCount { get; set; }
        public float C2I { get; set; }
        public float C2ICount { get; set; }
    }

    public class ZTGSMCellReselectAnaCondition
    {
        public int BeforeSecond { get; set; }           //重选前时长
        public int AfterSecond { get; set; }            //重选后时长
        public int AfterAttemptSecond { get; set; }     //如出现起呼，需要查看起呼后的质量，时长为AfterAttemptSecond

        public ZTGSMCellReselectAnaCondition()
        {
            BeforeSecond = 5;
            AfterSecond = 10;
            AfterAttemptSecond = 5;
        }
    }
}
