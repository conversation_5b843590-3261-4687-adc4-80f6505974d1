﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage.FewAreaKPI;

namespace MasterCom.RAMS.ZTFunc
{
    public class FewAreaKpiQuery : AreaKpiBaseQuery
    {
        protected ArchiveCondition archiveCondition { get; set; }
        private Dictionary<int, Dictionary<int, AreaBase>> areaTypeIDDic = null;

        protected void initAreaTypeIDDic(List<AreaBase> selAreas)
        {
            this.strType = string.Empty;
            areaTypeIDDic = new Dictionary<int, Dictionary<int, AreaBase>>();
            StringBuilder sb = new StringBuilder();
            foreach (AreaBase area in selAreas)
            {
                Dictionary<int, AreaBase> areaIdDic = null;
                if (!areaTypeIDDic.TryGetValue(area.AreaTypeID, out areaIdDic))
                {
                    sb.Append(area.AreaTypeID + ",");
                    areaIdDic = new Dictionary<int, AreaBase>();
                    areaTypeIDDic[area.AreaTypeID] = areaIdDic;
                }
                areaIdDic[area.AreaID] = area;
            }
            this.strType = sb.ToString().TrimEnd(',');
            rect = getRect();
        }

        private MasterCom.MTGis.DbRect getRect()
        {
            if (selAreas.Count == 0)
                return null;

            MasterCom.MTGis.DbRect rect = new MasterCom.MTGis.DbRect();
            rect.x1 = rect.y1 = 900;
            rect.x2 = rect.y2 = 0;

            foreach (AreaBase area in selAreas)
            {
                rect.x1 = Math.Min(area.Bounds.x1, rect.x1);
                rect.x2 = Math.Max(area.Bounds.x2, rect.x2);
                rect.y1 = Math.Min(area.Bounds.y1, rect.y1);
                rect.y2 = Math.Max(area.Bounds.y2, rect.y2);
            }
            return rect;
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            addRegion(package);
            AddDIYEndOpFlag(package);
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,25,1");
            package.Content.AddParam(strType);

            Dictionary<int, bool> areaIdDic = new Dictionary<int, bool>();
            foreach (Dictionary<int, AreaBase> areaDic in areaTypeIDDic.Values)
            {
                foreach (int id in areaDic.Keys)
                {
                    areaIdDic[id] = true;
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (int id in areaIdDic.Keys)
            {
                sb.Append(id.ToString() + ",");
            }
            if (sb.Length > 0 && sb.Length <= 5000)
            {
                package.Content.AddParam((byte)OpOptionDef.InSelect);
                package.Content.AddParam("0,26,1");
                package.Content.AddParam(sb.Remove(sb.Length - 1, 1).ToString());
            }
        }

        protected AreaReportTemplate curTemplate = null;
        private List<AreaBase> selAreas = null;
        protected override bool getConditionBeforeQuery()
        {
            archiveCondition = ArchiveSettingManager.GetInstance().Condition;

            if (archiveCondition.VillageCondition.RootLeafDic.Count == 0)
            {
                MessageBox.Show("请设置基础配置....", "提醒");
                return false;
            }
            FewAreaSettingDlg dlg = new FewAreaSettingDlg(TemplateMngr.Instance);
            dlg.SelectedAreas = selAreas;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curTemplate = dlg.SelTemplate;
            selAreas = dlg.SelectedAreas;
            IsShowResultForm = true;
            condition = archiveCondition.GetBaseConditionBackUp();
            initAreaTypeIDDic(selAreas);
            areaDataGrpDic = new Dictionary<AreaBase, Dictionary<DateTime, AreaKPIDataGroup<AreaBase>>>();
            return true;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> expSet = new List<string>();
            foreach (TemplateColumn col in curTemplate.Columns)
            {
                expSet.Add(col.Expression);
            }
            return getTriadIDIgnoreServiceType(expSet);
        }

        public override string Name
        {
            get { return "村庄指标统计（按天）"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31006, this.Name);
        }
        protected new AreaBase getArea(int areaTypeID, int areaSubID)
        {
            AreaBase area = null;
            Dictionary<int, AreaBase> leafDic = null;
            if (areaTypeIDDic.TryGetValue(areaTypeID, out leafDic))
            {
                leafDic.TryGetValue(areaSubID, out area);
            }
            return area;
        }

        private Dictionary<AreaBase, Dictionary<DateTime, AreaKPIDataGroup<AreaBase>>> areaDataGrpDic = null;
        protected override void recieveAndHandleSpecificStatData(Package package, List<StatImgDefItem> curImgColumnDef
         , KPIStatDataBase singleStatData)
        {
            int areaTypeID = package.Content.GetParamInt();
            int areaSubID = package.Content.GetParamInt();
            AreaBase area = getArea(areaTypeID, areaSubID);
            if (area == null)
            {
                return;
            }

            fillStatData(package, curImgColumnDef, singleStatData);
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            saveAreaData(area, fi, singleStatData);
        }

        private void saveAreaData(AreaBase area, FileInfo fi, KPIStatDataBase data)
        {
            DateTime date = DateTime.Parse(fi.BeginTimeString).Date;
            Dictionary<DateTime, AreaKPIDataGroup<AreaBase>> dateDataDic = null;
            if (!areaDataGrpDic.TryGetValue(area, out dateDataDic))
            {
                dateDataDic = new Dictionary<DateTime, AreaKPIDataGroup<AreaBase>>();
                areaDataGrpDic[area] = dateDataDic;
            }
            AreaKPIDataGroup<AreaBase> dataGrp;
            if (!dateDataDic.TryGetValue(date, out dataGrp))
            {
                dataGrp = new AreaKPIDataGroup<AreaBase>(area);
                dateDataDic[date] = dataGrp;
            }
            dataGrp.AddStatData(fi, data);
        }

        protected override void handleStatEvent(Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            AreaBase area = getArea(evt.AreaTypeID, evt.AreaID);
            if (area == null)
            {
                return;
            }
            saveAreaData(area, fi, eventData);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            MasterCom.Util.UiEx.WaitTextBox.Show("正在分析村庄指标...", makeSummary);
        }

        System.Data.DataSet dataSet = null;
        List<AreaBase> areasHaveData = null;
        protected virtual void makeSummary()
        {
            areasHaveData = new List<AreaBase>(areaDataGrpDic.Keys);
            try
            {
                foreach (AreaBase area in areaDataGrpDic.Keys)
                {
                    if (dataSet == null)
                    {
                        dataSet = new DataSet();
                    }
                    System.Data.DataTable table = createDataTable(area, this.curTemplate);
                    dataSet.Tables.Add(table);
                    Dictionary<DateTime, AreaKPIDataGroup<AreaBase>> dateDic = areaDataGrpDic[area];
                    AreaKPIDataGroup<AreaBase> sumGrp = null;
                    AreaFileInfo sumAreaFi = new AreaFileInfo();
                    sumAreaFi.Area = area;
                    if (dateDic.Count > 1)
                    {
                        sumGrp = new AreaKPIDataGroup<AreaBase>(area);
                    }
                    foreach (DateTime date in dateDic.Keys)
                    {
                        AreaKPIDataGroup<AreaBase> data = areaDataGrpDic[area][date];
                        data.FinalMtMoGroup();
                        AreaFileInfo areaFi = new AreaFileInfo(area, new List<FileInfo>(data.FileIDDic.Values));
                        if (sumGrp != null)
                        {
                            sumGrp.Merge(data);
                            sumAreaFi.Files.AddRange(areaFi.Files);
                        }
                        addRow(table, data, areaFi, date.ToString("yyyy-MM-dd"));
                    }
                    if (sumGrp != null)
                    {
                        addRow(table, sumGrp, sumAreaFi, "汇总");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                MasterCom.Util.UiEx.WaitTextBox.Close();
            }
        }

        private void addRow(DataTable table, AreaKPIDataGroup<AreaBase> data, AreaFileInfo areaFi, string desc)
        {
            DataRow row = table.NewRow();
            row["Tag"] = areaFi;
            row["测试日期"] = desc;
            foreach (TemplateColumn col in curTemplate.Columns)
            {
                double value = data.CalcFormula((CarrierType)col.CarrierID, col.MoMtFlag, col.Expression);
                if (double.IsNaN(value))
                {
                    row[col.Caption] = "-";
                }
                else
                {
                    row[col.Caption] = value;
                }
            }
            table.Rows.Add(row);
        }

        private DataTable createDataTable(AreaBase area, AreaReportTemplate template)
        {
            DataTable table = new DataTable(area.FullName);
            table.Columns.Add("测试日期", typeof(string));
            table.Columns.Add("Tag", typeof(AreaFileInfo));
            foreach (TemplateColumn col in template.Columns)
            {
                table.Columns.Add(col.Caption);
            }
            return table;
        }

        protected override void fireShowResult()
        {
            if (dataSet == null)
            {
                System.Windows.Forms.MessageBox.Show("所选村庄无测试数据");
                return;
            }
            LowestAreaKpiForm frm = MainModel.CreateResultForm(typeof(LowestAreaKpiForm)) as LowestAreaKpiForm;
            frm.FillData(dataSet, areasHaveData);
            frm.Visible = true;
            frm.BringToFront();
            areaDataGrpDic = null;
            areaTypeIDDic = null;
            dataSet = null;
        }

    }
}
