﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanCellWrongDirForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScanCellWrongDirForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBCCH = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.工参方向角 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.判断方位角 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.两角差值 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBadSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLevMean = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLevMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLevMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGoodSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBadSampleRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnSN);
            this.objectListView.AllColumns.Add(this.olvColumnCellName);
            this.objectListView.AllColumns.Add(this.olvColumnLAC);
            this.objectListView.AllColumns.Add(this.olvColumnCI);
            this.objectListView.AllColumns.Add(this.olvColumnBCCH);
            this.objectListView.AllColumns.Add(this.olvColumnBSIC);
            this.objectListView.AllColumns.Add(this.工参方向角);
            this.objectListView.AllColumns.Add(this.判断方位角);
            this.objectListView.AllColumns.Add(this.两角差值);
            this.objectListView.AllColumns.Add(this.olvColumnBadSampleCount);
            this.objectListView.AllColumns.Add(this.olvColumnRxLevMean);
            this.objectListView.AllColumns.Add(this.olvColumnRxLevMin);
            this.objectListView.AllColumns.Add(this.olvColumnRxLevMax);
            this.objectListView.AllColumns.Add(this.olvColumnGoodSampleCount);
            this.objectListView.AllColumns.Add(this.olvColumnBadSampleRate);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnBCCH,
            this.olvColumnBSIC,
            this.工参方向角,
            this.判断方位角,
            this.两角差值,
            this.olvColumnBadSampleCount,
            this.olvColumnRxLevMean,
            this.olvColumnRxLevMin,
            this.olvColumnRxLevMax,
            this.olvColumnGoodSampleCount,
            this.olvColumnBadSampleRate});
            this.objectListView.ContextMenuStrip = this.ctxMenu;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(976, 502);
            this.objectListView.TabIndex = 1;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            this.objectListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.AspectName = "CellName";
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名";
            this.olvColumnCellName.Width = 80;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.AspectName = "LAC";
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.AspectName = "CI";
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnBCCH
            // 
            this.olvColumnBCCH.AspectName = "BCCH";
            this.olvColumnBCCH.HeaderFont = null;
            this.olvColumnBCCH.Text = "频点";
            // 
            // olvColumnBSIC
            // 
            this.olvColumnBSIC.AspectName = "BSIC";
            this.olvColumnBSIC.HeaderFont = null;
            this.olvColumnBSIC.Text = "扰码";
            // 
            // 工参方向角
            // 
            this.工参方向角.AspectName = "DirectionCfg";
            this.工参方向角.HeaderFont = null;
            this.工参方向角.Text = "工参方向角";
            // 
            // 判断方位角
            // 
            this.判断方位角.AspectName = "WrongDirMean";
            this.判断方位角.HeaderFont = null;
            this.判断方位角.Text = "判断方位角";
            // 
            // 两角差值
            // 
            this.两角差值.AspectName = "DirDiff";
            this.两角差值.HeaderFont = null;
            this.两角差值.Text = "两角差值";
            // 
            // olvColumnBadSampleCount
            // 
            this.olvColumnBadSampleCount.AspectName = "BadSampleCount";
            this.olvColumnBadSampleCount.HeaderFont = null;
            this.olvColumnBadSampleCount.Text = "异常采样点数";
            this.olvColumnBadSampleCount.Width = 90;
            // 
            // olvColumnRxLevMean
            // 
            this.olvColumnRxLevMean.AspectName = "RxLevMean";
            this.olvColumnRxLevMean.HeaderFont = null;
            this.olvColumnRxLevMean.Text = "异常平均场强";
            this.olvColumnRxLevMean.Width = 90;
            // 
            // olvColumnRxLevMin
            // 
            this.olvColumnRxLevMin.AspectName = "RxLevMin";
            this.olvColumnRxLevMin.HeaderFont = null;
            this.olvColumnRxLevMin.Text = "异常最小场强";
            this.olvColumnRxLevMin.Width = 90;
            // 
            // olvColumnRxLevMax
            // 
            this.olvColumnRxLevMax.AspectName = "RxLevMax";
            this.olvColumnRxLevMax.HeaderFont = null;
            this.olvColumnRxLevMax.Text = "异常最大场强";
            this.olvColumnRxLevMax.Width = 90;
            // 
            // olvColumnGoodSampleCount
            // 
            this.olvColumnGoodSampleCount.AspectName = "GoodSampleCount";
            this.olvColumnGoodSampleCount.HeaderFont = null;
            this.olvColumnGoodSampleCount.Text = "正常采样点数";
            this.olvColumnGoodSampleCount.Width = 90;
            // 
            // olvColumnBadSampleRate
            // 
            this.olvColumnBadSampleRate.AspectName = "BadSampleRateString";
            this.olvColumnBadSampleRate.HeaderFont = null;
            this.olvColumnBadSampleRate.Text = "异常采样点比例";
            this.olvColumnBadSampleRate.Width = 100;
            // 
            // ScanCellWrongDirForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(976, 502);
            this.Controls.Add(this.objectListView);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ScanCellWrongDirForm";
            this.Text = "覆盖方向异常";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnBadSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMean;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMin;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMax;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC;
        private BrightIdeasSoftware.OLVColumn olvColumnGoodSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnBadSampleRate;
        private BrightIdeasSoftware.OLVColumn 工参方向角;
        private BrightIdeasSoftware.OLVColumn 判断方位角;
        private BrightIdeasSoftware.OLVColumn 两角差值;

    }
}