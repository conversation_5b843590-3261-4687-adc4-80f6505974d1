﻿using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.UserMng;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryCellKPIByFiles : QueryKPIStatBase
    {
        public QueryCellKPIByFiles()
            : base() { }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.cell;
        }

        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override LogInfoItem getRecLogItem()
        {
            return new LogInfoItem(2, 11000, 11061, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override string Name
        {
            get
            {
                return "文件涉及到的小区统计(可过滤小区)";
            }
        }

        protected override bool isValidCondition()
        {
            if (condition.FileInfos == null || condition.FileInfos.Count == 0)
            { 
                return false; 
            }
            return true;
        }

        //存放"TAC", "ECI", "CellName"
        public Dictionary<string, string> dicTACECICellName { get; set; } = new Dictionary<string, string>();

        protected override bool getConditionBeforeQuery()
        {
            ReportPickerDlg dlg = new ReportPickerDlg();
            dlg.DisplayCellFilter(true);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curReportStyle = dlg.Report;
            isQueryAllParams = dlg.IsQueryAllParams;
            KpiDataManager = new KPIDataManager();
            dicTACECICellName = LoadFilterCellFile.GetInfosByFileName(dlg.ExcelFilePath);
            getFilesCondition();
            return true;
        }
        protected virtual void getFilesCondition()
        {
            DateTime bTime = DateTime.MaxValue;
            DateTime eTime = DateTime.MinValue;
            StringBuilder sb = new StringBuilder();
            foreach (FileInfo fi in condition.FileInfos)
            {
                DateTime fBTime = DateTime.Parse(fi.BeginTimeString);
                DateTime fETime = DateTime.Parse(fi.EndTimeString);
                if (fBTime < bTime)
                {
                    bTime = fBTime;
                }
                if (fETime > eTime)
                {
                    eTime = fETime;
                }
                if (!condition.CarrierTypes.Contains(fi.CarrierType))
                {
                    condition.CarrierTypes.Add(fi.CarrierType);
                }
                if (!condition.ServiceTypes.Contains(fi.ServiceType))
                {
                    condition.ServiceTypes.Add(fi.ServiceType);
                }
                if (!condition.Projects.Contains(fi.ProjectID))
                {
                    condition.Projects.Add(fi.ProjectID);
                }
                sb.Append(fi.ID);
                sb.Append(",");
            }
            TimePeriod p = new TimePeriod(bTime, eTime);
            condition.Periods.Clear();
            condition.Periods.Add(p);
            condition.FileName = sb.ToString().TrimEnd(',');
            condition.NameFilterType = FileFilterType.ByMark_ID;
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            #region
            int lac = package.Content.GetParamInt();
            //int ci = package.Content.GetParamInt();
            long ci = NRDTDataHelper.AnalyseNCI(package.Content);
            filterAddStatData(package, curImgColumnDef, singleStatData, lac, ci);
            #endregion
        }

        protected void filterAddStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData, int lac, long ci)
        {
            if (dicTACECICellName == null || dicTACECICellName.Count == 0)
            {
                ICell cell = getCell(package, lac, ci);
                fillStatData(package, curImgColumnDef, singleStatData);
                int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
                FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
                KpiDataManager.AddStatData(string.Empty, cell.Name, fi, singleStatData, false);
            }
            else
            {
                string cellKey = string.Format("{0}_{1}", lac, ci);
                string cellName;
                if (dicTACECICellName.TryGetValue(cellKey, out cellName))
                {
                    ICell cell = getCell(package, lac, ci);

                    string cellNameDes = getFilterCellNameDes(cell, cellName);
                    fillStatData(package, curImgColumnDef, singleStatData);
                    int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
                    FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
                    KpiDataManager.AddStatData(string.Empty, cellNameDes, fi, singleStatData, false);
                }
            }
        }

        protected ICell getCell(Package package, int lac, long ci)
        {
            ICell cell = null;
            switch (package.Content.Type)
            {
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_GSM:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_GPRS:
                    cell = MainModel.CellManager.GetCurrentCell(lac, (int)ci);
                    break;
                case ResponseType.KPI_LTE_AMR:
                    cell = MainModel.CellManager.GetCurrentLTECell(lac, ci);
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_GRID_LTE_FDD_AMR:
                    cell = MainModel.CellManager.GetCurrentLTECell(lac, ci);
                    break;
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_AMR:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_PS:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_VP:
                    cell = MainModel.CellManager.GetCurrentTDCell(lac, (int)ci);
                    if (cell == null)
                    {
                        cell = MainModel.CellManager.GetCurrentCell(lac, (int)ci);
                    }
                    break;
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_AMR:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PS:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_VP:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PSHS:
                    cell = MainModel.CellManager.GetCurrentWCell(lac, (int)ci);
                    break;
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA_V:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA_D:
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA2000_D:
                    cell = MainModel.CellManager.GetCurrentCDCell(lac, (int)ci);
                    break;
                case ResponseType.RESTYPE_DIY_CELL_COVER_GRID_NR:
                    //5G存在LTE锚点站,先匹配LTE小区
                    cell = MainModel.CellManager.GetCurrentLTECell(lac, ci);
                    if (cell == null)
                    {
                        cell = MainModel.CellManager.GetCurrentNRCell(lac, ci);
                    }
                    break;
            }

            if (cell == null)//未知小区_lac_ci
            {
                cell = new UnknowCell(string.Format("{0}_{1}", lac, ci));
            }
            return cell;
        }
        protected override void handleStatEvent(Event evt)
        {
            object Lac = evt["LAC"];
            object Ci = evt["CI"];
            int lac = Convert.ToInt32(Lac);
            long ci = Convert.ToInt64(Ci);

            eventFilterAddStatData(evt, Lac, Ci, lac, ci);
        }

        protected void eventFilterAddStatData(Event evt, object Lac, object Ci, int lac, long ci)
        {
            if (Lac != null && Ci != null)
            {
                if (dicTACECICellName == null || dicTACECICellName.Count == 0)
                {
                    ICell cell = getCell(lac, ci);
                    StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
                    FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
                    KpiDataManager.AddStatData(string.Empty, cell.Name, fi, eventData, false);
                }
                else
                {
                    string cellKey = string.Format("{0}_{1}", lac, ci);
                    string cellName;
                    if (dicTACECICellName.TryGetValue(cellKey, out cellName))
                    {
                        ICell cell = getCell(lac, ci);

                        string cellNameDes = getFilterCellNameDes(cell, cellName);
                        StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
                        FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
                        KpiDataManager.AddStatData(string.Empty, cellNameDes, fi, eventData, false);
                    }
                }
            }
        }

        private ICell getCell(int lac, long ci)
        {
            ICell cell = MainModel.CellManager.GetICellByLACCI(lac, ci);
            if (cell == null)//未知小区_lac_ci
            {
                cell = new UnknowCell(string.Format("{0}_{1}", lac, ci));
            }

            return cell;
        }

        protected string getFilterCellNameDes(ICell srcCell, string cellName)
        {
            if (cellName.Trim().ToUpper() == srcCell.Name.Trim().ToUpper())
            {
                return srcCell.Name;
            }
            else
            {
                return string.Format("(异常小区){0}-{1}(工参小区名：{2})", cellName, srcCell.Token, srcCell.Name);
            }
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.KPI_CELL;
                package.Content.PrepareAddParam();
            }
        }
        /// <summary>
        /// RequestType.KPI_CELL，需要一个结束标记。
        /// </summary>
        /// <param name="package"></param>
        /// <param name="reservedParams"></param>
        protected override void AddExtraCondition(Package package, params object[] reservedParams)//增加外条件(KPI_CELL)
        {
            if (!isQueringEvent)
            {
                AddDIYEndOpFlag(package);
            }
        }

    }
}
