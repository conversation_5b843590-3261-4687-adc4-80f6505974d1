﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Data.SqlClient;
using MasterCom;
using System.Data;
using System.Threading;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTECell_FSO
    {
        public int iid { get; set; }
        public int? cityid { get; set; }
        public string strcellname { get; set; }
        public string strbtsname { get; set; }
        public int? enodeb_id { get; set; }
        public int? tac { get; set; }
        public int? cellid { get; set; }
        public int? ipci { get; set; }
        public int? ifreq { get; set; }
        public int? ilongitude { get; set; }
        public int? ilatitude { get; set; }
        public string strsupplier { get; set; }
        public string strdoor { get; set; }
        public int? iangle_dir { get; set; }
        public int? ialtitude { get; set; }

        public void Fill(int iid, SqlDataReader reader)
        {
            int idx = 0;
            this.iid = iid;
            this.cityid = reader.IsDBNull(idx) ? null : (int?)reader[idx]; idx++;
            this.strcellname = Convert.ToString(reader[idx++]);
            this.strbtsname = Convert.ToString(reader[idx++]);
            this.enodeb_id = reader.IsDBNull(idx) ? null : (int?)reader[idx]; idx++;
            this.tac = reader.IsDBNull(idx) ? null : (int?)reader[idx]; idx++;
            this.cellid = reader.IsDBNull(idx) ? null : (int?)reader[idx]; idx++;
            this.ipci = reader.IsDBNull(idx) ? null : (int?)reader[idx]; idx++;
            this.ifreq = reader.IsDBNull(idx) ? null : (int?)reader[idx]; idx++;
            this.ilongitude = reader.IsDBNull(idx) ? null : (int?)reader[idx]; idx++;
            this.ilatitude = reader.IsDBNull(idx) ? null : (int?)reader[idx]; idx++;
            this.strsupplier = Convert.ToString(reader[idx++]);
            this.strdoor = Convert.ToString(reader[idx++]);
            this.iangle_dir = reader.IsDBNull(idx) ? null : (int?)reader[idx]; idx++;
            this.ialtitude = reader.IsDBNull(idx) ? null : (int?)reader[idx];
        }

        public bool Check(out StringBuilder infoSB)
        {
            bool bcheck = true;
            infoSB = new StringBuilder();
            if (enodeb_id == null)
            {
                bcheck = false;
                infoSB.Append("enodeb_id 为NULL");
                infoSB.Append("\t");
            }
            if (tac == null)
            {
                bcheck = false;
                infoSB.Append("tac 为NULL");
                infoSB.Append("\t");
            }
            if (cellid == null)
            {
                bcheck = false;
                infoSB.Append("cellid 为NULL");
                infoSB.Append("\t");
            }
            if (ipci == null)
            {
                bcheck = false;
                infoSB.Append("ipci 为NULL");
                infoSB.Append("\t");
            }
            if (ifreq == null)
            {
                bcheck = false;
                infoSB.Append("ifreq 为NULL");
                infoSB.Append("\t");
            }
            return bcheck;
        }
    }

    public class DiyQueryLTECellFixDB
    {
        readonly string strConnDB;
        public Dictionary<string, LTECell_FSO> NameLTECellDic { get; set; }

        public DiyQueryLTECellFixDB(CityInfo curCity)
        {
            if (curCity != null)
            {
                strConnDB = curCity.GetDBConn();
            }
            NameLTECellDic = new Dictionary<string, LTECell_FSO>();
        }

        public void Query()
        {
            WaitBox.Show("正在查询LTE小区信息...", query);
        }

        private void query()
        {
            try
            {
                string sql = "select cityid,strcellname,strbtsname,enodeb_id,tac,cellid,ipci,ifreq,ilongitude,ilatitude,strsupplier,strdoor,iangle_dir,ialtitude from tb_cfg_tdlte_cell";

                using (SqlDataReader reader = SqlHelper.ExecuteReader(strConnDB, CommandType.Text, sql))
                {
                    int iid = 1;
                    while (reader.Read())
                    {
                        LTECell_FSO cell = new LTECell_FSO();
                        cell.Fill(iid++, reader);
                        if (!NameLTECellDic.ContainsKey(cell.strcellname))
                            NameLTECellDic.Add(cell.strcellname, cell);
                    }
                }
            }
            finally
            {
                Thread.Sleep(10);
                WaitBox.Close();
            }
        }
    }
}
