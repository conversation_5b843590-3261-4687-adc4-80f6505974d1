﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;
namespace MasterCom.RAMS.CQT
{
    public class CQTProblemSummaryAnalysGSM : QueryBase
    {
        public CQTProblemSummaryAnalysGSM(MainModel mainModel,string netType)
        : base(mainModel)
        {
            if (netType == "GSM")
            {
                if (CQTProblemSummaryAnalysGSM.cityLevel(mainModel.DistrictID) != "")
                    iareatype = 24;
                else
                    iareatype = -100;
            }
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override string Name
        {
            get { return "2G问题点统计分析"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21038, this.Name);
        }
        #region 全局变量
        public static List<ProblemSummaryPont2G> problemPointSummarryList { get; set; } = new List<ProblemSummaryPont2G>();
        public static List<ProblemPont2G> problemPointList { get; set; } = new List<ProblemPont2G>();     
        readonly List<ProblemItem> result = new List<ProblemItem>();
        readonly Dictionary<int, FileInfoItem> CQTFileDic = new Dictionary<int, FileInfoItem>();
        readonly Dictionary<int, FileInfoItem> CQTFileDicCount = new Dictionary<int, FileInfoItem>();
        SumarryDdataInfoGSM sumarryInfo;
        private bool pointNum = false;
        readonly int iareatype = 0;
        int strcityid = 0;
        int newpoint = 0;
        int iNo = 1;
        string strCqtProblemType ="";
        #endregion
        protected override void query()
        {
            int cityIDTem = 0;
            if (MainModel.User.DBID == -1)
            {
                cityIDTem = MainModel.DistrictID;
                MainModel.DistrictID = 2;
            }
            DIYQueryTBTypeAll diyTem = new DIYQueryTBTypeAll(MainModel, "tb_func_cqt%");
            diyTem.Query();
            List<string> dBName = new List<string>();
            if (diyTem.dbNameList.Count > 0)
                dBName.Add("通报问题点");
            diyTem = new DIYQueryTBTypeAll(MainModel, "tb_case_cqt%");
            diyTem.Query();
            if (diyTem.dbNameList.Count > 0)
                dBName.Add("派单问题点");
            if (MainModel.User.DBID == -1)
                MainModel.DistrictID = cityIDTem;
            CQTProblemType cqtType = new CQTProblemType(dBName);
            if (cqtType.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            strCqtProblemType = cqtType.strCQTProblemType;
            try
            {
                WaitBox.Show("准备获取CQT点...", seachProblemData);
            }
            finally
            {
                //continue
            }
            showData();
        }
        /// <summary>
        /// 准备查询数据的条件
        /// </summary>
        private void seachProblemData()
        {
            problemPointSummarryList.Clear();
            problemPointList.Clear();
            sumarryInfo = new SumarryDdataInfoGSM();
            WaitBox.ProgressPercent = 20;
            iNo = 1;         
            DateTime sDtime = condition.Periods[0].BeginTime;
            DateTime eDtime = condition.Periods[0].EndTime.AddSeconds(-1);
            string project = "";
            for (int i = 0; i < condition.Projects.Count; i++)
            {
                project = string.Format(project + condition.Projects[i].ToString() + ",");
            }
            project = project.TrimEnd(',');
            List<string> logList = getLogFileNameList(sDtime, eDtime);       
            if (MainModel.User.DBID == -1)
            {
                WaitBox.ProgressPercent = 30;
                List<int> disid = new List<int>();
                //按特定顺序排列查询各是数据
                int[] zhidingID = { 1, 2, 3, 12, 6, 5, 4, 10, 13, 14, 16, 17, 21, 7, 8, 9, 11, 15, 18, 19, 20, 22, 23 };
                for (int idk = 0; idk < zhidingID.Length; idk++)
                {
                    if (condition.DistrictIDs.Contains(zhidingID[idk]))
                    {
                        disid.Add(zhidingID[idk]);
                    }
                }              
               for (int chdistrid = 0; chdistrid < disid.Count; chdistrid++)
               {
                   CQTFileDic.Clear();
                   CQTFileDicCount.Clear();
                   MainModel.DistrictID = disid[chdistrid];
                   strcityid = disid[chdistrid];
                   seachProblemPointData(strcityid, sDtime, eDtime, project, logList);
               }
            }
            else
            {
                CQTFileDic.Clear();
                CQTFileDicCount.Clear();
                strcityid = MainModel.DistrictID;
                seachProblemPointData(MainModel.DistrictID, sDtime, eDtime, project, logList);
            }
            WaitBox.ProgressPercent = 80;
            if (problemPointSummarryList.Count != 0)
            {
                WaitBox.Text = "正在进行汇总处理...";
                CQTProblemSummaryAnalysGSM.problemPointSummarryList.Add(ProblemSummaryPont2G.fillDataTotal(sumarryInfo));
            }
            WaitBox.Close();
        }
        /// <summary>
        /// 真正查询问题点记录的过程
        /// </summary>
        private void seachProblemPointData(int cityId, DateTime sDtime, DateTime eDtime, string project, List<string> logList)
        {
            WaitBox.Text = "正在获取 " + DistrictManager.GetInstance().getDistrictName(cityId) + " 市的CQT点...";

            foreach (string log in logList)
            {
                DIYQueryCQTFileInfo diyFileInfo = new DIYQueryCQTFileInfo(MainModel, log, iareatype, project, true, sDtime, eDtime);
                diyFileInfo.Query();//查询测试地点的文件列表
                foreach (int idk in diyFileInfo.CQTFileDic.Keys)
                {
                    if (!CQTFileDic.ContainsKey(idk))
                        CQTFileDic.Add(idk, diyFileInfo.CQTFileDic[idk]);
                }
                DIYQueryCQTFileInfo diyTestCont = new DIYQueryCQTFileInfo(MainModel, log, iareatype, project, false, sDtime, eDtime);
                diyTestCont.Query();//查询测试总数
                foreach (int idk in diyTestCont.CQTFileCount.Keys)
                {
                    if (!CQTFileDicCount.ContainsKey(idk))
                        CQTFileDicCount.Add(idk, diyTestCont.CQTFileCount[idk]);
                }
            }
            DIYQueryEventLoLaInfo diyProblemLoLa = new DIYQueryEventLoLaInfo(MainModel, iareatype);
            diyProblemLoLa.Query();//查询问题点地点的经纬度
            DIYQueryCQTProblemInfo diyProblemInfo = new DIYQueryCQTProblemInfo(MainModel, iareatype, sDtime, eDtime, project, strCqtProblemType);
            diyProblemInfo.Query();//查测试地点问题点各字段
            int iareatypeid = 0;
            if (diyProblemInfo.CQTPromblemDic.Count == 0)
            {
                Dictionary<int, CQTPointTem> CQTPointDic = new Dictionary<int, CQTPointTem>();
                Dictionary<int, string> areaStrCoverDic = new Dictionary<int, string>();
                Dictionary<int, ProblemLoLaInfo> CQTPromblemLoLaDic = new Dictionary<int, ProblemLoLaInfo>();
                dealWithDataXQ(CQTPointDic, diyProblemInfo.CQTPromblemDic, areaStrCoverDic, CQTPromblemLoLaDic);
                dealWithSummarryData();
                return;
            }

            foreach (CQTProjectAreaIDKey proArea in diyProblemInfo.CQTPromblemDic.Keys)
            {
                iareatypeid = diyProblemInfo.CQTPromblemDic[proArea].IAreaType;
                if (true)
                {
                    break;
                }
            }
            DIYQueryCQTPointInfo dqcpi = new DIYQueryCQTPointInfo(MainModel, iareatypeid);
            dqcpi.Query();//查测试地点属性与地点名称
            WaitBox.ProgressPercent = WaitBox.ProgressPercent + 1;

            DIYQueryCQTCoverInfo dCoverInfo = new DIYQueryCQTCoverInfo(MainModel, iareatypeid);
            dCoverInfo.Query();//查询覆盖属性，因为点的属性查询类关联太广，而且覆盖属性表只有广东有
                               //所以，分开来查询
            dealWithDataXQ(dqcpi.CQTPointDic, diyProblemInfo.CQTPromblemDic, dCoverInfo.areaStrCover,diyProblemLoLa.CQTPromblemLoLaDic);
            dealWithSummarryData();
        }
        /// <summary>
        /// 获取tb_log表名
        /// </summary>
        private List<string> getLogFileNameList(DateTime tmpDate, DateTime eDtime)
        {
            List<string> logList = new List<string>();
            while (tmpDate <= eDtime)
            {
                string strLogName = string.Format("tb_log_file_{0:yyyy}_{0:MM}", tmpDate);
                if (!logList.Contains(strLogName))
                {
                    logList.Add(strLogName);
                }
                tmpDate = tmpDate.AddDays(1);
            }
            return logList;
        }
        /// <summary>
        /// 处理数据
        /// </summary>
        private void dealWithSummarryData()
        {
            XQDataInfoGSM xqInfo = new XQDataInfoGSM();
            if (pointNum)
            {
                for (int id = problemPointList.Count - newpoint; id < problemPointList.Count; id++)
                {
#if GDProjectOrder
                    if (isMergeProject(problemPointList.Count - newpoint, id, ref xqInfo))
                    {
                        continue;
                    }
#endif
                    addProblemCount(xqInfo, id);
                }
            }
            ProblemSummaryPont2G lisFTem = ProblemSummaryPont2G.fillXQData(xqInfo, CQTFileDicCount.Count, strcityid);
            CQTProblemSummaryAnalysGSM.problemPointSummarryList.Add(lisFTem);
            SumarryDdataInfoGSM.fillSummarryData(ref sumarryInfo, xqInfo, CQTFileDicCount.Count, strcityid);
        }

        private static void addProblemCount(XQDataInfoGSM xqInfo, int id)
        {
            if (problemPointList[id].SMainType == "弱覆盖")
                xqInfo.iweakcoverage++;
            else if (problemPointList[id].SMainType == "话质差")
                xqInfo.ipoorvoice++;
            else if (problemPointList[id].SMainType == "未接通")
                xqInfo.inotconnected++;
            else if (problemPointList[id].SMainType == "掉话")
                xqInfo.idroppedcalls++;
            else if (problemPointList[id].SMainType == "感知掉话")
                xqInfo.ifillDropedCall++;
            else if (problemPointList[id].SMainType == "下载速率低")
                xqInfo.idownless++;
            else if (problemPointList[id].SMainType == "深度覆盖不足"
                || problemPointList[id].SMainType == "覆盖不足")
                xqInfo.ilessthendeep++;
            else if (problemPointList[id].SMainType == "下载掉线")
                xqInfo.idowndrop++;
            else if (problemPointList[id].SMainType == "下载超时")
                xqInfo.idowntimeout++;
            else if (problemPointList[id].SMainType == "GSM深度覆盖不达标")
                xqInfo.ilessthendeepGSM++;
            else if (problemPointList[id].SMainType == "TD深度覆盖不达标")
                xqInfo.ilessthendeepTD++;
            else if (problemPointList[id].SMainType == "G劣")
                xqInfo.ilessGHG++;
            else if (problemPointList[id].SMainType == "G劣1")
                xqInfo.ilessG1++;
            else if (problemPointList[id].SMainType == "G劣2")
                xqInfo.ilessG2++;
        }

        /// <summary>
        /// 广东地市类别
        /// </summary>
        public static string cityLevel(int cityID)
        {
            string strCityLeve = "";
            string cityName = DistrictManager.GetInstance().getDistrictName(cityID);
            if (cityName.Equals("广东"))
                strCityLeve = "省ID";
            else if (cityName.Equals("广州") || cityName.Equals("深圳") || cityName.Equals("东莞") || cityName.Equals("佛山"))
                strCityLeve = "一类";
            else if (cityName.Equals("惠州") || cityName.Equals("珠海") || cityName.Equals("江门") || cityName.Equals("中山")
                || cityName.Equals("汕头") || cityName.Equals("湛江") || cityName.Equals("揭阳") || cityName.Equals("茂名"))
                strCityLeve = "二类";
            else if (cityName.Equals("梅州") || cityName.Equals("清远") || cityName.Equals("汕尾") || cityName.Equals("韶关")
                || cityName.Equals("阳江") || cityName.Equals("云浮") || cityName.Equals("肇庆") || cityName.Equals("河源")
                || cityName.Equals("潮州"))
                strCityLeve = "三类";
            else
                strCityLeve = "";
            return strCityLeve;
        }
        /// <summary>
        /// 截取字段
        /// </summary>
         public static string getsub(string strlis, int num)
        {
            string[] newstr = strlis.Split('|');
            if (newstr.Length > 1)
            {
                if (num == 1)
                    return newstr[0];
                else if (num == 2)
                    return newstr[1];
                else if (num == 3)
                    return newstr[2];
                else
                    return "";
            }
            else
                return newstr[0];

        }
        /// <summary>
        /// 对事前信息进行处理，以便对应原来的统计接口
        /// </summary>
         private void dealWithDataXQ(Dictionary<int, CQTPointTem> cqtNameDic, Dictionary<CQTProjectAreaIDKey, ProblemItem> CQTPromblemDic,
            Dictionary<int, string> areaStrCoverDic, Dictionary<int, ProblemLoLaInfo> CQTPromblemLoLaDic)
        {
            result.Clear();
            foreach (CQTProjectAreaIDKey proArea in CQTPromblemDic.Keys)
            {
                ProblemItem pT = CQTPromblemDic[proArea];
                pT.StrCoverType = "非室分";
                if (cqtNameDic.ContainsKey(proArea.IAreaID))
                {
                    pT.Strcqtname = cqtNameDic[proArea.IAreaID].Strareaname;
                    pT.Strcqttype = cqtNameDic[proArea.IAreaID].Strcomment;
                }
                if (CQTPromblemLoLaDic.ContainsKey(proArea.IAreaID))
                {
                    pT.DLongitude = CQTPromblemLoLaDic[proArea.IAreaID].DLongitude;
                    pT.DLatitude = CQTPromblemLoLaDic[proArea.IAreaID].DLatitude;
                }
                if (areaStrCoverDic.ContainsKey(proArea.IAreaID))
                {
                    pT.StrCoverType = areaStrCoverDic[proArea.IAreaID];
                }
                pT.probFileList = getFileNameList(pT);
                pT.probCellInfoList = getProbCellInfo(pT.StrCauseInfo);
                if (!string.IsNullOrEmpty(pT.Strcqtname) && pT.Strcqttype != null)
                {
                    result.Add(pT);
                }
            }
            if (result.Count > 0)
            {
                pointNum = true;
            }
            else
            {
                pointNum = false;
            }
            addpPoblemPointList();
        }

        private void addpPoblemPointList()
        {
            int file = 0;
            newpoint = 0;
            int mainProblemID = 0;
            for (int id = 0; id < result.Count; id++)
            {
                if (result[id].probFileList.Count != 0 && result[id].probFileList[0].ToString() != "")
                {
                    newpoint++;
                    ProblemPont2G lis1 = new ProblemPont2G();
                    mainProblemID = setMainProblemID(mainProblemID, id, lis1);

                    setSSecondType(mainProblemID, id, lis1);

                    string strFile = "", strCell = ""; int ilac = 0, ici = 0;
                    lis1.IID = iNo++;
                    lis1.SCity = DistrictManager.GetInstance().getDistrictName(result[id].Icity);
                    lis1.STestTime = result[id].Dtime;
                    lis1.SProject = CQTProblemSummaryAnalysGSM.GetProjectNameByProjectID(result[id].IProject);
                    lis1.STestPoint = result[id].Strcqtname;
                    lis1.DLongitude = result[id].DLongitude;
                    lis1.DLatitude = result[id].DLatitude;
                    lis1.SSitePro = result[id].Strcqttype;
                    lis1.StrCoverType = result[id].StrCoverType;
                    lis1.StrValue8 = result[id].StrValue8;
                    lis1.SImportLeve = "-";
                    for (file = 0; file < result[id].probFileList.Count; file++)
                    {
                        strFile = string.Format(strFile + result[id].probFileList[file].ToString() + "；");
                    }
                    lis1.STestFilePosition = strFile;
                    for (file = 0; file < result[id].probCellInfoList.Count; file++)
                    {
                        ilac = result[id].probCellInfoList[file].Ilac;
                        ici = result[id].probCellInfoList[file].Ici;
                        if (result[id].probCellInfoList[file].Strcellname == "")
                            strCell = string.Format(strCell + "(" + ilac + "_" + ici + "),采样点共" + result[id].probCellInfoList[file].Isampleid + "个,场强为" + result[id].probCellInfoList[file].Irxlev + "dBm" + ";");
                        else
                            strCell = string.Format(strCell + result[id].probCellInfoList[file].Strcellname + "(" + ilac + "_" + ici + "),采样点共" + result[id].probCellInfoList[file].Isampleid + "个,场强为" + result[id].probCellInfoList[file].Irxlev + "dBm" + ";");
                    }
                    lis1.SReasonAna = strCell;
                    lis1.SReasonType = result[id].StrCauseType;
                    lis1.SPreliminaryAna = CQTProblemSummaryAnalysGSM.getSummaryDesc(result[id], "GSM", lis1.SReasonAna);

                    problemPointList.Add(lis1);
                }
            }
        }

        private void setSSecondType(int mainProblemID, int id, ProblemPont2G lis1)
        {
            if (mainProblemID == 0)
            {
                lis1.SSecondType = "";
                setSSecondTypeByProblem(lis1, result[id].StrMainProblem3, false);
                setSSecondTypeByProblem(lis1, result[id].StrMainProblem1, false);
                setSSecondTypeByProblem(lis1, result[id].StrSecProblem1, true);
            }
            else if (mainProblemID == 1)
            {
                lis1.SSecondType = "";
                setSSecondTypeByProblem(lis1, result[id].StrMainProblem1, false);
                setSSecondTypeByProblem(lis1, result[id].StrSecProblem1, true);
            }
            else if (mainProblemID == 2)
            {
                lis1.SSecondType = "";
                setSSecondTypeByProblem(lis1, result[id].StrSecProblem1, true);
            }
            else if (mainProblemID == 3)
            {
                lis1.SSecondType = "";
            }
        }

        private void setSSecondTypeByProblem(ProblemPont2G lis1, string problem, bool needTrim)
        {
            if (problem != "")
            {
                lis1.SSecondType = string.Format(lis1.SSecondType + problem + ",");
            }
            if (needTrim)
            {
                lis1.SSecondType = lis1.SSecondType.TrimEnd(',');
            }
        }

        private int setMainProblemID(int mainProblemID, int id, ProblemPont2G lis1)
        {
            if (result[id].StrMainProblem2 != "")
            {
                lis1.SMainType = result[id].StrMainProblem2;
                lis1.SPointPosition = getsub(result[id].StrTestSite, 2);
                mainProblemID = 0;
            }
            else if (result[id].StrMainProblem3 != "")
            {
                lis1.SMainType = result[id].StrMainProblem3;
                lis1.SPointPosition = getsub(result[id].StrTestSite, 3);
                mainProblemID = 1;
            }
            else if (result[id].StrMainProblem1 != "")
            {
                lis1.SMainType = result[id].StrMainProblem1;
                lis1.SPointPosition = getsub(result[id].StrTestSite, 1);
                mainProblemID = 2;
            }
            else if (result[id].StrSecProblem1 != "")
            {
                lis1.SMainType = result[id].StrSecProblem1;
                mainProblemID = 3;
            }

            return mainProblemID;
        }
#if GDProjectOrder
        /// <summary>
        /// 合并不同项目
        /// </summary>
        private bool isMergeProject(int iPerIndex, int iCurIndex, ref XQDataInfoGSM xqInfo)
         {
             bool isMergeProject = false;
             for (int id = iPerIndex; id < iCurIndex; id++)
             {
                 if (problemPointList[id].STestPoint.Equals(problemPointList[iCurIndex].STestPoint))
                 {
                     isMergeProject = true;
                     if (isChangeIndex(problemPointList[id].SMainType, problemPointList[iCurIndex].SMainType))
                     {
                         isMergeProject = false;
                         if (problemPointList[id].SMainType.Contains("G劣"))
                         {
                             xqInfo.ilessGHG -= 1;
                         } 
                         else if (problemPointList[id].SMainType.Contains("掉话"))
                         {
                             xqInfo.idroppedcalls -= 1;
                         }
                         else if (problemPointList[id].SMainType.Contains("未接通"))
                         {
                             xqInfo.inotconnected -= 1;
                         }
                         else if (problemPointList[id].SMainType.Contains("覆盖不足"))
                         {
                             xqInfo.ilessthendeep -= 1;
                         }
                         else if (problemPointList[id].SMainType.Contains("下载速率低"))
                         {
                             xqInfo.idownless -= 1;
                         }
                         else if (problemPointList[id].SMainType.Contains("下载掉线"))
                         {
                             xqInfo.idowndrop -= 1;
                         }
                     }
                 }
             }
             return isMergeProject;
         }
        /// <summary>
        /// 判断优先级
        /// </summary>
         private bool isChangeIndex(string strPerProblem,string strCurProblem)
         {
             string[] problem = {"G劣", "掉话", "未接通", "覆盖不足", "下载速率低" };
             string[] problemOrder = { "掉话", "未接通", "下载掉线", "下载速率低" };
             if (strCqtProblemType.Contains("派单"))
             {
                 problem = problemOrder;
             }
             int iPerProblem = -1;
             int iCurProblem = -1;
             for (int i = 0; i < problem.Length;i++ )
             {
                 if (strPerProblem.Contains(problem[i]))
                 {
                     iPerProblem = i;
                 }

                 if (strCurProblem.Contains(problem[i]))
                 {
                     iCurProblem = i;
                 }
             }
             if (iPerProblem == -1 || iCurProblem == -1)
             {
                 return false;
             } 
             else
             {
                 return iCurProblem < iPerProblem;
             } 
         }
#endif
        /// <summary>
        /// 通过项目ID获取项目名称
        /// </summary>
        /// <returns></returns>
        public static string GetProjectNameByProjectID(int iProjectID)
        {
            string strProName = "";
            CategoryEnumItem[] projItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
            foreach (CategoryEnumItem cei in projItems)
            {
                if (cei.ID == iProjectID)
                {
                    strProName = cei.Name;
                }
            }
            return strProName;
        }
        /// <summary>
        /// 获取对应文件名列表
        /// </summary>
        private List<string> getFileNameList(ProblemItem pT)
        {
            List<String> fileNameList = new List<String>();

            string strFilePart = "";
            string[] filePart = pT.StrLogInfo.Split('|');
             if (pT.StrMainProblem2 != "" && filePart.Length > 1)
                strFilePart = filePart[1];
            else if (pT.StrMainProblem3 != "" && filePart.Length > 2)
                strFilePart = filePart[2];
            else
                strFilePart = filePart[0];
            string[] fileids = strFilePart.Split(',');
            foreach (string strfileid in fileids)
            {
                try
                {
                    int ifileid = Convert.ToInt32(strfileid);
                    if (CQTFileDic.ContainsKey(ifileid))
                        fileNameList.Add(CQTFileDic[ifileid].StrfileName);
                }
                catch(Exception e)
                {
                    log.Error(e.Message);
                }
            }
            return fileNameList;
        }
        /// <summary>
        /// 问题点信息详情列表
        /// </summary>
        private List<ProbCellInfo> getProbCellInfo(string probInfo)
        {
            List<ProbCellInfo> pcInfo = new List<ProbCellInfo>();
            string[] probCell = probInfo.Split('|');
            foreach (string prob in probCell)
            {
                try
                {
                    string[] info = prob.Split(',');
                    ProbCellInfo cpi = new ProbCellInfo();
                    cpi.Strcellname = info[0].ToString();
                    cpi.Ilac = Convert.ToInt32(info[1]);
                    cpi.Ici = Convert.ToInt32(info[2]);
                    cpi.Isampleid = Convert.ToInt32(info[3]);
                    cpi.Irxlev = Convert.ToInt32(info[4]);
                    pcInfo.Add(cpi);
                }
                catch (Exception e)
                {
                    log.Error(e.Message);
                }
            }
            return pcInfo;
        }
        /// <summary>
        /// 获取初步分析
        /// </summary>
        public static string getSummaryDesc(ProblemItem pItem, string strNet, string strCellInfo)
        {
            string strResult = "";
            string[] cellArray = strCellInfo.Split(';');
            string strTmpCell = "[未知]";
            if (cellArray.Length >= 1)
                strTmpCell = cellArray[0].Split(',')[0];
            string[] sampleArray = pItem.StrValue7.Split('|');
            string strRxlev = "";
            string strC2I = "";
            if (sampleArray.Length > 0)
                strRxlev = sampleArray[0];
            if (sampleArray.Length > 1)
                strC2I = sampleArray[1];

            if (strNet == "TD")
            {
                strResult = string.Format("问题点主要占用{0}等小区的信号，测试点的场强在{1}dBm左右，C/I在{2}dB左右，初判是{3}导致。",
                                           strTmpCell, strRxlev, strC2I, pItem.StrCauseType);
            }
            else
            {
                strResult = string.Format("问题点主要占用{0}等小区的信号，测试点的场强在{1}dBm左右，初判为{2}导致。",
                                           strTmpCell, strRxlev, pItem.StrCauseType);
            }
            return strResult;
        }
        /// <summary>
        /// 显示处理结果窗体
        /// </summary>
        private void showData()
        {
            CQTProblemSummary2G cqtproblemShowForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(CQTProblemSummary2G).FullName);
            cqtproblemShowForm = obj == null ? null : obj as CQTProblemSummary2G;
            if (cqtproblemShowForm == null || cqtproblemShowForm.IsDisposed)
            {
                cqtproblemShowForm = new CQTProblemSummary2G(MainModel, strCqtProblemType);
            }
            cqtproblemShowForm.filldataSummary2G(1);
            cqtproblemShowForm.Show(MainModel.MainForm);
        }
    }
    /// <summary>
    /// 查询各地市问题点记录
    /// </summary>
    public class DIYQueryCQTProblemInfo : DIYSQLBase
    {
        readonly int iAreaTypeId = 0;
        readonly string strProject;
        readonly DateTime sDate;
        readonly DateTime eDate;
        readonly string strType;
        public DIYQueryCQTProblemInfo(MainModel mm,int areaTypeId, DateTime sDate_l,
            DateTime eDate_l,string strPor,string strProblemType)
            : base(mm)
        {
            mainModel = mm;
            iAreaTypeId = areaTypeId;
            sDate = sDate_l;
            eDate = eDate_l;
            strProject = strPor;
            strType = strProblemType;
            CQTPromblemDic = new Dictionary<CQTProjectAreaIDKey, ProblemItem>();
        }
        public override string Name
        {
            get { return "查询CQT地点属性"; }
        }
        protected override string getSqlTextString()
        {
            string strSQL = "";
            string strTable = " tb_func_cqt_problem_gsm ";
            if (strType == "派单问题点")
                strTable = " tb_case_cqt_problem_gsm ";
            string strIareatype = " and  iareatype in (" + iAreaTypeId + ")";
            if (iAreaTypeId != -100 && iAreaTypeId != -200)
            {
                if (iAreaTypeId == 25)
                {
                    strTable = " tb_func_cqt_problem_td ";
                    if (strType == "派单问题点")
                        strTable = " tb_case_cqt_problem_td ";
                }
                strSQL = "SELECT convert(varchar(20),dstime,20),icity,iprojecttype,iareatype,iareaid,strMainProblem1,strSecProblem1,strMainProblem2,"
                            + "strSecProblem2,strMainProblem3,strSecProblem3,strTestSite,strLogInfo,strCauseInfo,strCauseType,strValue7,strValue8  from "
                            + strTable + " where  dstime >= '" + sDate + "' and detime <= '" + eDate
                            + "' and iprojecttype in (" + strProject + ") " + strIareatype;
            }
            else
            {
                if (iAreaTypeId == -200)
                {
                    strTable = " tb_func_cqt_problem_td ";
                    if (strType == "派单问题点")
                        strTable = " tb_case_cqt_problem_td ";
                }
                strSQL = "SELECT convert(varchar(20),dstime,20),icity,iprojecttype,iareatype,iareaid,strMainProblem1,strSecProblem1,strMainProblem2,"
                            + "strSecProblem2,strMainProblem3,strSecProblem3,strTestSite,strLogInfo,strCauseInfo,strCauseType,strValue7,strValue8  from "
                            + strTable + " where  dstime >= '" + sDate + "' and detime <= '" + eDate
                            + "' and iprojecttype in (" + strProject + ") ";
            }
            if (iAreaTypeId == 28)
            {
                strSQL = strSQL.Replace("_cqt_problem_td", "_cqt_problem_lte").Replace("_cqt_problem_gsm", "_cqt_problem_lte");
            }
            return strSQL;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[17];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_String;
            rType[10] = E_VType.E_String;
            rType[11] = E_VType.E_String;
            rType[12] = E_VType.E_String;
            rType[13] = E_VType.E_String;
            rType[14] = E_VType.E_String;
            rType[15] = E_VType.E_String;
            rType[16] = E_VType.E_String;
            return rType;
        }
        public Dictionary<CQTProjectAreaIDKey, ProblemItem> CQTPromblemDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CQTPromblemDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ProblemItem cqtTem = new ProblemItem();
                    cqtTem.Dtime = DateTime.Parse(package.Content.GetParamString());
                    cqtTem.Icity = package.Content.GetParamInt();
                    cqtTem.IProject = package.Content.GetParamInt();
                    cqtTem.IAreaType = package.Content.GetParamInt();
                    cqtTem.IAreaID = package.Content.GetParamInt();
                    cqtTem.StrMainProblem1 = package.Content.GetParamString();
                    cqtTem.StrSecProblem1 = package.Content.GetParamString();
                    cqtTem.StrMainProblem2 = package.Content.GetParamString();
                    cqtTem.StrSecProblem2 = package.Content.GetParamString();
                    cqtTem.StrMainProblem3 = package.Content.GetParamString();
                    cqtTem.StrSecProblem3 = package.Content.GetParamString();
                    cqtTem.StrTestSite = package.Content.GetParamString();
                    cqtTem.StrLogInfo = package.Content.GetParamString();
                    cqtTem.StrCauseInfo = package.Content.GetParamString();
                    cqtTem.StrCauseType = package.Content.GetParamString();
                    cqtTem.StrValue7 = package.Content.GetParamString();
                    cqtTem.StrValue8 = package.Content.GetParamString();

                    CQTProjectAreaIDKey projAreaKey = new CQTProjectAreaIDKey();
                    projAreaKey.Dtime = cqtTem.Dtime;
                    projAreaKey.IProj = cqtTem.IProject;
                    projAreaKey.IAreaID = cqtTem.IAreaID;

                    if (!CQTPromblemDic.ContainsKey(projAreaKey))
                        CQTPromblemDic.Add(projAreaKey, cqtTem);

                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
    /// <summary>
    /// 查询覆盖属性
    /// </summary>
    public class DIYQueryCQTCoverInfo : DIYSQLBase
    {
        readonly int iAreaTypeId = 0;
        public DIYQueryCQTCoverInfo(MainModel mm, int areaTypeId)
            : base(mm)
        {
            mainModel = mm;
            iAreaTypeId = areaTypeId;
            areaStrCover = new Dictionary<int, string>();
        }
        public override string Name
        {
            get { return "查询CQT地点属性"; }
        }
        protected override string getSqlTextString()
        {
            string Sql = "select iareaid,strcover from tb_cfg_static_areainfo  where iareatypeid in (" + iAreaTypeId + ")";
            return Sql;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;

            return rType;
        }
        public Dictionary<int, string> areaStrCover { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            areaStrCover.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int iAreaID = package.Content.GetParamInt();
                    string strCover = package.Content.GetParamString();

                    if (!areaStrCover.ContainsKey(iAreaID))
                        areaStrCover.Add(iAreaID, strCover);

                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
    /// <summary>
    /// 查询测试地点的文件列表
    /// </summary>
    public class DIYQueryCQTFileInfo : DIYSQLBase
    {
        readonly string tbName;
        readonly int iAreaTypeId = 0;
        readonly string strProject;
        readonly DateTime sDate;
        readonly DateTime eDate;
        readonly bool isGetTestCount = false;
        public DIYQueryCQTFileInfo(MainModel mm,string tblogName,int areaTypeId,string strPro,
            bool isGetCount,DateTime stime,DateTime etime)
            : base(mm)
        {
            mainModel = mm;
            tbName = tblogName;
            this.iAreaTypeId = areaTypeId;
            this.strProject = strPro;
            this.isGetTestCount = isGetCount;
            this.sDate = stime;
            this.eDate = etime;

            CQTFileDic = new Dictionary<int, FileInfoItem>();
            CQTFileCount = new Dictionary<int, FileInfoItem>();
            CQTFileCountByServ = new Dictionary<string, FileInfoItem>();
        }
        public override string Name
        {
            get { return "查询CQT地点属性文件列表"; }
        }
        protected override string getSqlTextString()
        {
            string strSql = "";
            strSql = "select distinct ifileid,strfilename,iareatype,iareaid from " + tbName + " where dateadd(ss,istime,'1970-1-1 8:00:00') >= '"
                + sDate + "' and dateadd(ss,ietime,'1970-1-1 8:00:00') <= '" + eDate +
                        "' and iprojecttype in (" + strProject + ") and statstatus >= 3";
            if (iAreaTypeId != -100 && iAreaTypeId != -200)
            {
                strSql += " and iareatype in (" + iAreaTypeId + ")";
            }
            if (iAreaTypeId == 28)
            {
                strSql = strSql.Replace("ifileid,strfilename,iareatype,iareaid", "0 as A, case when (iservicetype = 34) then '数据' else '语音' end as 业务,iareatype ,iareaid");
                strSql += " and( iservicetype in (1,4,33) or (iservicetype in (34)and strfilename like '%定点测试%' ))";
            }
            return strSql;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[4];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            return rType;
            
        }
        public Dictionary<int, FileInfoItem> CQTFileDic { get; set; }
        public Dictionary<int, FileInfoItem> CQTFileCount { get; set; }
        public Dictionary<string, FileInfoItem> CQTFileCountByServ { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CQTFileDic.Clear();
            CQTFileCount.Clear();
            CQTFileCountByServ.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            FileInfoItem ft = new FileInfoItem();
            ft.Ifileid = package.Content.GetParamInt();
            ft.StrfileName = package.Content.GetParamString();
            ft.Iareatype = package.Content.GetParamInt();
            ft.Iareaid = package.Content.GetParamInt();
            if (isGetTestCount)
            {
                if (!CQTFileDic.ContainsKey(ft.Ifileid))
                    CQTFileDic.Add(ft.Ifileid, ft);
            }
            else
            {
                if (iAreaTypeId != 28)
                {
                    if (!CQTFileCount.ContainsKey(ft.Iareaid))
                        CQTFileCount.Add(ft.Iareaid, ft);
                }
                else
                {
                    if (!CQTFileCountByServ.ContainsKey(ft.StrfileName + "|" + ft.Iareaid))
                        CQTFileCountByServ.Add(ft.StrfileName + "|" + ft.Iareaid, ft);
                }
            }
        }
    }
    /// <summary>
    /// 问题点记录
    /// </summary>
    public class ProblemItem
    {
        public ProblemItem()
        {
            probFileList = new List<String>();
            probCellInfoList = new List<ProbCellInfo>();
        }
        /// <summary>
        /// AreaType
        /// </summary>
        public int IAreaType { get; set; }
        /// <summary>
        /// AreaID
        /// </summary>
        public int IAreaID { get; set; }
        /// <summary>
        /// 文件ID列表
        /// </summary>
        public string StrLogInfo { get; set; }
        /// <summary>
        /// 测试时间
        /// </summary>
        public DateTime Dtime { get; set; }
        /// <summary>
        /// 城市ID
        /// </summary>
        public int Icity { get; set; }
        /// <summary>
        /// 项目ID
        /// </summary>
        public int IProject { get; set; }
        /// <summary>
        /// 测试点名称
        /// </summary>
        public string Strcqtname { get; set; }
        /// <summary>
        /// 地点经度
        /// </summary>
        public double DLongitude { get; set; }
        /// <summary>
        /// 地点纬度
        /// </summary>
        public double DLatitude { get; set; }
        /// <summary>
        /// 场所属性
        /// </summary>
        public string Strcqttype { get; set; }
        /// <summary>
        /// 覆盖属性
        /// </summary>
        public string StrCoverType { get; set; }
        /// <summary>
        /// 主问题类别
        /// </summary>
        public string StrMainProblem1 { get; set; }
        /// <summary>
        /// 次问题类别
        /// </summary>
        public string StrSecProblem1 { get; set; }
        /// <summary>
        /// 不达标点问题类别
        /// </summary>
        public string StrMainProblem2 { get; set; }
        /// <summary>
        /// 备份字段
        /// </summary>
        public string StrSecProblem2 { get; set; }
        /// <summary>
        /// 劣点类别(语音)
        /// </summary>
        public string StrMainProblem3 { get; set; }
        /// <summary>
        /// 劣点类别(数据)
        /// </summary>
        public string StrSecProblem3 { get; set; }
        /// <summary>
        /// 问题点位置
        /// </summary>
        public string StrTestSite { get; set; }
        /// <summary>
        /// 原因类别
        /// </summary>
        public string StrCauseType { get; set; }
        /// <summary>
        ///问题点类型
        /// </summary>
        public string StrValue7 { get; set; }
        /// <summary>
        /// 匹配站点
        /// </summary>
        public string StrValue8 { get; set; }
        /// <summary>
        /// strCauseInfo信息
        /// </summary>
        public string StrCauseInfo { get; set; }
        /// <summary>
        /// 测试文件
        /// </summary>
        public List<String> probFileList { get; set; }
        /// <summary>
        /// 小区原因详情信息
        /// </summary>
        public List<ProbCellInfo> probCellInfoList { get; set; }
    }
    public class ProbCellInfo
    {
        /// <summary>
        /// 小区名称
        /// </summary>
        public string Strcellname { get; set; }
        /// <summary>
        /// LAC
        /// </summary>
        public int Ilac { get; set; }
        /// <summary>
        /// CI
        /// </summary>
        public int Ici { get; set; }
        /// <summary>
        /// 采样点数目
        /// </summary>
        public int Isampleid { get; set; }
        /// <summary>
        /// 场强值
        /// </summary>
        public int Irxlev { get; set; }
    }
    public class FileInfoItem
    {
        public int Ifileid { get; set; }
        public string StrfileName { get; set; }
        public int Iareatype { get; set; }
        public int Iareaid { get; set; }
    }
    public class SumarryDdataInfoGSM
    {
        public int sumiWeakCoverage{ get; set; }
        public int sumiPoorVoice{ get; set; }
        public int sumiNotConnected{ get; set; }
        public int sumiDroppedCalls{ get; set; }
        public int sumiFillDropedCall{ get; set; }
        public int sumiDownLess{ get; set; }
        public int sumiLessthendeep{ get; set; }
        public int sumiDownDrop{ get; set; }
        public int sumiDownTimeOut{ get; set; }
        public int sumiLessThenDeepGSM{ get; set; }
        public int sumiLessThenDeepTD{ get; set; }
        public int sumiLessGHG{ get; set; }
        public int sumiLessG1{ get; set; }
        public int sumiLessG2{ get; set; }
        public int sumiLessThenCole{ get; set; }
        public int sumiLessThenDeepCole{ get; set; }
        public int sumiLessG{ get; set; }
        public int sumiCityColePoint{ get; set; }
        public int sumiTestCole{ get; set; }
        public SumarryDdataInfoGSM()
        { 
            sumiWeakCoverage = 0;
            sumiPoorVoice = 0;
            sumiNotConnected = 0;
            sumiDroppedCalls = 0;
            sumiFillDropedCall = 0;
            sumiDownLess = 0;
            sumiLessthendeep = 0;
            sumiDownDrop = 0;
            sumiDownTimeOut = 0;
            sumiLessThenDeepGSM = 0;
            sumiLessThenDeepTD = 0;
            sumiLessGHG = 0;
            sumiLessG1 = 0;
            sumiLessG2 = 0; 
            sumiLessThenCole = 0;
            sumiLessThenDeepCole = 0;
            sumiLessG = 0;
            sumiCityColePoint = 0; 
            sumiTestCole = 0;
        }
        public static void fillSummarryData(ref SumarryDdataInfoGSM sumarryInfo,XQDataInfoGSM xqInfo,
            int iTestDicCount, int strcityid)
        {
            sumarryInfo.sumiTestCole = sumarryInfo.sumiTestCole + iTestDicCount;
            sumarryInfo.sumiLessThenCole = sumarryInfo.sumiLessThenCole + xqInfo.ilessthenCole;
            sumarryInfo.sumiLessThenDeepCole = sumarryInfo.sumiLessThenDeepCole + xqInfo.ilessthenDeepCole;
            sumarryInfo.sumiLessG = sumarryInfo.sumiLessG + xqInfo.ilessG;
            sumarryInfo.sumiCityColePoint = sumarryInfo.sumiCityColePoint + xqInfo.ilessthenCole + xqInfo.ilessthenDeepCole + xqInfo.ilessG;
            sumarryInfo.sumiWeakCoverage = sumarryInfo.sumiWeakCoverage + xqInfo.iweakcoverage;
            sumarryInfo.sumiPoorVoice = sumarryInfo.sumiPoorVoice + xqInfo.ipoorvoice;
            sumarryInfo.sumiDroppedCalls = sumarryInfo.sumiDroppedCalls + xqInfo.idroppedcalls;
            sumarryInfo.sumiFillDropedCall = sumarryInfo.sumiFillDropedCall + xqInfo.ifillDropedCall;
            sumarryInfo.sumiNotConnected = sumarryInfo.sumiNotConnected + xqInfo.inotconnected;
            sumarryInfo.sumiDownLess = sumarryInfo.sumiDownLess + xqInfo.idownless;
            sumarryInfo.sumiLessthendeep = sumarryInfo.sumiLessthendeep + xqInfo.ilessthendeep;
            sumarryInfo.sumiDownDrop = sumarryInfo.sumiDownDrop + xqInfo.idowndrop;
            sumarryInfo.sumiDownTimeOut = sumarryInfo.sumiDownTimeOut + xqInfo.idowntimeout;
            sumarryInfo.sumiLessThenDeepGSM = sumarryInfo.sumiLessThenDeepGSM + xqInfo.ilessthendeepGSM;
            sumarryInfo.sumiLessThenDeepTD = sumarryInfo.sumiLessThenDeepTD + xqInfo.ilessthendeepTD;
            sumarryInfo.sumiLessGHG = sumarryInfo.sumiLessGHG + xqInfo.ilessGHG;
            sumarryInfo.sumiLessG1 = sumarryInfo.sumiLessG1 + xqInfo.ilessG1;
            sumarryInfo.sumiLessG2 = sumarryInfo.sumiLessG2 + xqInfo.ilessG2;                        
        }
    }
    public class SumarryDdataInfoTD
    {
        public int sumiWeakCoverage{ get; set; }
        public int sumiDroppedCalls{ get; set; }
        public int sumiNotConnected{ get; set; }
        public int sumiDownLessOther{ get; set; }
        public int sumiLessDownTDN{ get; set; }
        public int sumiSingleDownLess{ get; set; }
        public int sumiMostDownLess{ get; set; }
        public int sumiDownDrop{ get; set; }
        public int sumiDownTimeOut{ get; set; }
        public int sumiFallTDToGSM{ get; set; }
        public int sumiLessThenCole{ get; set; }
        public int sumiLessThenDeep{ get; set; }
        public int sumiLessThenDeepCole{ get; set; }
        public int sumiCallSuccessLess{ get; set; }
        public int sumiTlessHG{ get; set; }
        public int sumiCompareWeak{ get; set; }
        public int sumiCompareDownLess{ get; set; }
        public int sumiSingleDownLess1M{ get; set; }
        public int sumiTLessCole{ get; set; }
        public int sumiCityColePoint{ get; set; }
        public int sumiTestCole{ get; set; }
        public SumarryDdataInfoTD()
        {
            sumiWeakCoverage = 0;
            sumiDroppedCalls = 0;
            sumiNotConnected = 0;
            sumiDownLessOther = 0;
            sumiSingleDownLess = 0;
            sumiMostDownLess = 0;
            sumiDownDrop = 0;
            sumiDownTimeOut = 0;
            sumiFallTDToGSM = 0;
            sumiLessThenCole = 0;
            sumiLessThenDeep = 0;
            sumiLessThenDeepCole = 0;
            sumiCallSuccessLess = 0;
            sumiTlessHG = 0;
            sumiCompareWeak = 0;
            sumiCompareDownLess = 0;
            sumiSingleDownLess1M = 0;
            sumiTLessCole = 0;
            sumiCityColePoint = 0;
            sumiTestCole = 0;
        }
        public static void fillSummarryData(ref SumarryDdataInfoTD sumarryInfo, XQDataInfoTD xqInfo,
            int iTestDicCount, int strcityid)
        {
            sumarryInfo.sumiWeakCoverage = sumarryInfo.sumiWeakCoverage + xqInfo.iweakcoverage;
            sumarryInfo.sumiDroppedCalls = sumarryInfo.sumiDroppedCalls + xqInfo.idroppedcalls;
            sumarryInfo.sumiNotConnected = sumarryInfo.sumiNotConnected + xqInfo.inotconnected;
            sumarryInfo.sumiDownLessOther = sumarryInfo.sumiDownLessOther + xqInfo.idownlessother;
            sumarryInfo.sumiLessDownTDN = sumarryInfo.sumiLessDownTDN + xqInfo.ilessdowntdn;
            sumarryInfo.sumiSingleDownLess = sumarryInfo.sumiSingleDownLess + xqInfo.isingleDownLess;
            sumarryInfo.sumiMostDownLess = sumarryInfo.sumiMostDownLess + xqInfo.imostDownLess;
            sumarryInfo.sumiDownDrop = sumarryInfo.sumiDownDrop + xqInfo.idowndrop;
            sumarryInfo.sumiDownTimeOut = sumarryInfo.sumiDownTimeOut + xqInfo.idowntimeout;
            sumarryInfo.sumiFallTDToGSM = sumarryInfo.sumiFallTDToGSM + xqInfo.ifallTDToGSM;
            sumarryInfo.sumiLessThenCole = sumarryInfo.sumiLessThenCole + xqInfo.ilessthenCole;
            sumarryInfo.sumiLessThenDeep = sumarryInfo.sumiLessThenDeep + xqInfo.ilessthendeepTD;
            sumarryInfo.sumiLessThenDeepCole = sumarryInfo.sumiLessThenDeepCole + xqInfo.ilessthendeepTD;
            sumarryInfo.sumiCallSuccessLess = sumarryInfo.sumiCallSuccessLess + xqInfo.icallSuccessLess;
            sumarryInfo.sumiTlessHG = sumarryInfo.sumiTlessHG + xqInfo.itlessHG;
            sumarryInfo.sumiCompareWeak = sumarryInfo.sumiCompareWeak + xqInfo.icompareWeak;
            sumarryInfo.sumiCompareDownLess = sumarryInfo.sumiCompareDownLess + xqInfo.icompareDownLess;
            sumarryInfo.sumiTLessCole = sumarryInfo.sumiTLessCole + xqInfo.itlessCole;
            sumarryInfo.sumiCityColePoint = sumarryInfo.sumiCityColePoint + xqInfo.ilessthenCole 
                + xqInfo.itlessCole + xqInfo.ilessthendeepTD;
            sumarryInfo.sumiTestCole = sumarryInfo.sumiTestCole + iTestDicCount;
        }
    }
    public class XQDataInfoGSM
    {
        public int iweakcoverage{ get; set; }
        public int ipoorvoice{ get; set; }
        public int idroppedcalls{ get; set; }
        public int ifillDropedCall{ get; set; }
        public int inotconnected{ get; set; }
        public int idownless{ get; set; }
        public int ilessthendeep{ get; set; }
        public int idowndrop{ get; set; }
        public int idowntimeout{ get; set; }
        public int ilessthendeepGSM{ get; set; }
        public int ilessthendeepTD{ get; set; }
        public int ilessGHG{ get; set; }
        public int ilessG1{ get; set; }
        public int ilessG2{ get; set; }
        public int ilessthenCole
        {
            get
            {
                return iweakcoverage + ipoorvoice + inotconnected + ifillDropedCall
                + idroppedcalls + idownless + ilessthendeep + idowndrop + idowntimeout;
            } 
        }
        public int ilessthenDeepCole
        {
            get
            {
                return ilessthendeepGSM + ilessthendeepTD;
            }
        }
        public int ilessG
        {
            get
            {
                return ilessG1 + ilessG2 + ilessGHG;
            }
        }
        public XQDataInfoGSM()
        {
            iweakcoverage = 0;
            ipoorvoice = 0;
            idroppedcalls = 0;
            ifillDropedCall = 0;
            inotconnected = 0;
            idownless = 0;
            ilessthendeep = 0;
            idowndrop = 0;
            idowntimeout = 0;
            ilessthendeepGSM = 0;
            ilessthendeepTD = 0;
            ilessGHG = 0;
            ilessG1 = 0;
            ilessG2 = 0;
        }
    }
    public class XQDataInfoTD
    {
        public int iweakcoverage{ get; set; }      
        public int idroppedcalls{ get; set; }
        public int inotconnected{ get; set; }
        public int idownlessother{ get; set; }
        public int ilessdowntdn{ get; set; }
        public int isingleDownLess{ get; set; }
        public int imostDownLess{ get; set; }
        public int idowndrop{ get; set; }
        public int idowntimeout{ get; set; }
        public int ifallTDToGSM{ get; set; }
        public int ilessthendeepTD{ get; set; }
        public int icallSuccessLess{ get; set; }
        public int itlessHG{ get; set; }
        public int icompareWeak{ get; set; }
        public int icompareDownLess{ get; set; }
        public int isingleDownLess1M{ get; set; }

        public int ilessthenCole
        {
            get
            {
                return iweakcoverage + inotconnected + idroppedcalls + idownlessother
                    + isingleDownLess + imostDownLess + idowndrop + idowntimeout + ilessdowntdn
                    + ifallTDToGSM;
            }
        }
        public int itlessCole
        {
            get
            {
                return icallSuccessLess + itlessHG + icompareWeak + icompareDownLess;
            }
        }
        public XQDataInfoTD()
        {
            iweakcoverage = 0;
            idroppedcalls = 0;
            inotconnected = 0;
            idownlessother = 0;
            ilessdowntdn = 0;
            isingleDownLess = 0;
            imostDownLess = 0;
            idowndrop = 0;
            idowntimeout = 0;
            ifallTDToGSM = 0;
            ilessthendeepTD = 0;
            icallSuccessLess = 0;
            itlessHG = 0;
            icompareWeak = 0;
            icompareDownLess = 0;
            isingleDownLess1M = 0;
        }
    }
    public class ProblemPont2G
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int IID { get; set; }
        /// <summary>
        /// 轮次
        /// </summary>
        public string STurn { get; set; }
        /// <summary>
        /// 城市
        /// </summary>
        public string SCity { get; set; }
        /// <summary>
        /// 测试日期
        /// </summary>
        public DateTime STestTime { get; set; }
        /// <summary>
        /// 项目类型
        /// </summary>
        public string SProject { get; set; }
        /// <summary>
        /// 测试点名称
        /// </summary>
        public string STestPoint { get; set; }
        /// <summary>
        /// 地点经度
        /// </summary>
        public double DLongitude { get; set; }
        /// <summary>
        /// 地点纬度
        /// </summary>
        public double DLatitude { get; set; }
        /// <summary>
        /// 测试点属性
        /// </summary>
        public string SSitePro { get; set; }
        /// <summary>
        /// 覆盖属性
        /// </summary>
        public string StrCoverType { get; set; }
        /// <summary>
        /// 规划站点
        /// </summary>
        public string StrValue8 { get; set; }
        /// <summary>
        /// 重要等级
        /// </summary>
        public string SImportLeve { get; set; }
        /// <summary>
        /// 主问题类型
        /// </summary>
        public string SMainType { get; set; }
        /// <summary>
        /// 次问题类别
        /// </summary>
        public string SSecondType { get; set; }
        /// <summary>
        /// 问题的位置
        /// </summary>
        public string SPointPosition { get; set; }
        /// <summary>
        /// 测试文件存放路径
        /// </summary>
        public string STestFilePosition { get; set; }
        /// <summary>
        /// 原因分析
        /// </summary>
        public string SReasonAna { get; set; }
        /// <summary>
        /// 原因类别
        /// </summary>
        public string SReasonType { get; set; }
        /// <summary>
        /// 初步分析
        /// </summary>
        public string SPreliminaryAna { get; set; }
        /// <summary>
        /// 是否已解决
        /// </summary>
        public string SSolve { get; set; }
        /// <summary>
        /// 主要问题点原因分析
        /// </summary>
        public string SPointAna { get; set; }
        /// <summary>
        /// 问题点类型
        /// </summary>
        public string SPointType { get; set; }
        /// <summary>
        /// 解决方案
        /// </summary>
        public string SSolveScheme { get; set; }
        /// <summary>
        /// 预计解决时间
        /// </summary>
        public string SExceptSolveTime { get; set; }
        /// <summary>
        /// 场所属性
        /// </summary>
        public string Strcssx { get; set; }
        /// <summary>
        /// 次要问题点原因分析
        /// </summary>
        public string Strcywtyyfx { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Strbz { get; set; }
        public ProblemPont2G()
        {
            IID = 0;
            STurn = "-";
            SCity = "";
            STestTime = DateTime.Now;
            SProject = "";
            STestPoint = "";
            DLongitude = -1;
            DLatitude = -1;
            SSitePro = "";
            SImportLeve = "";
            SMainType = "";
            SSecondType = "";
            SPointPosition = "";
            STestFilePosition = "";
            SReasonAna = "";
            SReasonType = "";
            SPreliminaryAna = "";
            SSolve = "-";
            SPointAna = "-";
            SPointType = "-";
            SSolveScheme = "-";
            SExceptSolveTime = "-";
            Strcssx = "-";
            Strcywtyyfx = "-";
            Strbz = "-";
        }
    }
    public class ProblemSummaryPont2G
    {
        /// <summary>
        /// 项目
        /// </summary>
        public string SProject { get; set; }
        /// <summary>
        /// 城市类别
        /// </summary>
        public string SCityType { get; set; }
        /// <summary>
        /// 城市
        /// </summary>
        public string SCity { get; set; }
        /// <summary>
        /// 弱覆盖
        /// </summary>
        public int IWeakCoverage { get; set; }
        /// <summary>
        /// 语音质量差
        /// </summary>
        public int IPoorVoice { get; set; }
        /// <summary>
        /// 未接通
        /// </summary>
        public int INotConnected { get; set; }
        /// <summary>
        /// 掉话
        /// </summary>
        public int IDroppedCalls { get; set; }
        /// <summary>
        /// 感知掉话
        /// </summary>
        public int IFillDropedCall { get; set; }
        /// <summary>
        /// 下载速率低
        /// </summary>
        public int IDownLess { get; set; }
        /// <summary>
        /// 深度覆盖不足
        /// </summary>
        public int ILessthendeep { get; set; }
        /// <summary>
        /// 下载掉线
        /// </summary>
        public int IDownDrop { get; set; }
        /// <summary>
        /// 下载超时
        /// </summary>
        public int IDownTimeOut { get; set; }
        /// <summary>
        /// 指标未达标点汇总
        /// </summary>
        public int ILessThenCole { get; set; }
        /// <summary>
        /// GSM深度覆盖不达标
        /// </summary>
        public int ILessThenDeepGSM { get; set; }
        /// <summary>
        /// TD深度覆盖不达标
        /// </summary>
        public int ILessThenDeepTD { get; set; }
        /// <summary>
        /// G不达标点汇总
        /// </summary>
        public int ILessThenDeepCole { get; set; }
        /// <summary>
        /// G劣（使用广东、湖北版本）
        /// </summary>
        public int ILessGHG { get; set; }
        /// <summary>
        /// G劣1
        /// </summary>
        public int ILessG1 { get; set; }
        /// <summary>
        ///G劣2
        /// </summary>
        public int ILessG2 { get; set; }
        /// <summary>
        /// G劣点
        /// </summary>
        public int ILessG { get; set; }
        /// <summary>
        /// 地市总问题点数
        /// </summary>
        public int ICityColePoint { get; set; }
        /// <summary>
        /// 测试总数
        /// </summary>
        public int ITestCole { get; set; }
        /// <summary>
        /// 测试通过率
        /// </summary>
        public string ITestSucess { get; set; }

        public ProblemSummaryPont2G()
        {
            SProject = "";
            SCityType = "";
            SCity = "";
            IWeakCoverage = 0;
            IPoorVoice = 0;
            INotConnected = 0;
            IDroppedCalls = 0;
            IFillDropedCall = 0;
            ILessthendeep = 0;
            IDownLess = 0;
            IDownDrop = 0;
            IDownTimeOut = 0;
            ILessThenCole = 0;
            ILessThenDeepGSM = 0;
            ILessThenDeepTD = 0;
            ILessThenDeepCole = 0;
            ILessGHG = 0;
            ILessG1 = 0;
            ILessG2 = 0;
            ILessG = 0;
            ICityColePoint = 0;
            ITestCole = 0;
            ITestSucess = "";
        }
        public static ProblemSummaryPont2G fillXQData(XQDataInfoGSM xqInfo, int iTestDicCount, int strcityid)
        {
            ProblemSummaryPont2G lisFTem = new ProblemSummaryPont2G();
            lisFTem.SProject = "GSMCQT";
            lisFTem.SCityType = CQTProblemSummaryAnalysGSM.cityLevel(strcityid);
            lisFTem.SCity = DistrictManager.GetInstance().getDistrictName(strcityid);
            lisFTem.IWeakCoverage = xqInfo.iweakcoverage;
            lisFTem.IPoorVoice = xqInfo.ipoorvoice;
            lisFTem.IDroppedCalls = xqInfo.idroppedcalls;
            lisFTem.IFillDropedCall = xqInfo.ifillDropedCall;
            lisFTem.INotConnected = xqInfo.inotconnected;
            lisFTem.IDownLess = xqInfo.idownless;
            lisFTem.ILessthendeep = xqInfo.ilessthendeep;
            lisFTem.IDownDrop = xqInfo.idowndrop;
            lisFTem.IDownTimeOut = xqInfo.idowntimeout;
            lisFTem.ILessThenCole = xqInfo.ilessthenCole;
            lisFTem.ILessThenDeepGSM = xqInfo.ilessthendeepGSM;
            lisFTem.ILessThenDeepTD = xqInfo.ilessthendeepTD;
            lisFTem.ILessThenDeepCole = xqInfo.ilessthenDeepCole;
            lisFTem.ILessGHG = xqInfo.ilessGHG;
            lisFTem.ILessG1 = xqInfo.ilessG1;
            lisFTem.ILessG2 = xqInfo.ilessG2;
            lisFTem.ILessG = xqInfo.ilessG;
            lisFTem.ICityColePoint = xqInfo.ilessthenCole + xqInfo.ilessthenDeepCole + xqInfo.ilessG;
            lisFTem.ITestCole = iTestDicCount;
            if (lisFTem.ITestCole != 0)
                lisFTem.ITestSucess = (100 - Math.Round((lisFTem.ICityColePoint * 100.0 / lisFTem.ITestCole), 2)).ToString("0.00") + "%";
            else
                lisFTem.ITestSucess = "-";
            return lisFTem;
        }
        public static ProblemSummaryPont2G fillDataTotal(SumarryDdataInfoGSM sumarryInfo)
        {
            ProblemSummaryPont2G toa = new ProblemSummaryPont2G();
            toa.SProject = "----";
            toa.SCityType = "汇总统计";
            toa.SCity = "----";
            toa.IWeakCoverage = sumarryInfo.sumiWeakCoverage;
            toa.IPoorVoice = sumarryInfo.sumiPoorVoice;
            toa.INotConnected = sumarryInfo.sumiNotConnected;
            toa.IDroppedCalls = sumarryInfo.sumiDroppedCalls;
            toa.IFillDropedCall = sumarryInfo.sumiFillDropedCall;
            toa.ILessthendeep = sumarryInfo.sumiLessthendeep;
            toa.IDownLess = sumarryInfo.sumiDownLess;
            toa.IDownDrop = sumarryInfo.sumiDownDrop;
            toa.IDownTimeOut = sumarryInfo.sumiDownTimeOut;
            toa.ILessThenCole = sumarryInfo.sumiLessThenCole;
            toa.ILessThenDeepGSM = sumarryInfo.sumiLessThenDeepGSM;
            toa.ILessThenDeepTD = sumarryInfo.sumiLessThenDeepTD;
            toa.ILessThenDeepCole = sumarryInfo.sumiLessThenDeepCole;
            toa.ILessGHG = sumarryInfo.sumiLessGHG;
            toa.ILessG1 = sumarryInfo.sumiLessG1;
            toa.ILessG2 = sumarryInfo.sumiLessG2;
            toa.ILessG = sumarryInfo.sumiLessG;
            toa.ICityColePoint = sumarryInfo.sumiCityColePoint;
            toa.ITestCole = sumarryInfo.sumiTestCole;
            if (sumarryInfo.sumiTestCole != 0)
                toa.ITestSucess = (100 - Math.Round(sumarryInfo.sumiCityColePoint * 100.0 / sumarryInfo.sumiTestCole, 2)).ToString("0.00") + "%";
            else
                toa.ITestSucess = "-";
            return toa;
        }
    }
    public class ProblemPont3G
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int IID { get; set; }
        /// <summary>
        /// 轮次
        /// </summary>
        public string STurn { get; set; }
        /// <summary>
        /// 城市
        /// </summary>
        public string SCity { get; set; }
        /// <summary>
        /// 测试日期
        /// </summary>
        public DateTime STestTime { get; set; }
        /// <summary>
        /// 项目类型
        /// </summary>
        public string SProj { get; set; }

        /// <summary>
        /// 测试点名称
        /// </summary>
        public string STestPoint { get; set; }
        /// <summary>
        /// 地点经度
        /// </summary>
        public double DLongitude { get; set; }
        /// <summary>
        /// 地点纬度
        /// </summary>
        public double DLatitude { get; set; }
        /// <summary>
        /// 测试点属性
        /// </summary>
        public string SSitePro { get; set; }
        /// <summary>
        /// 覆盖属性
        /// </summary>
        public string StrCoverType { get; set; }
        /// <summary>
        /// 规划站点
        /// </summary>
        public string StrValue8 { get; set; }
        /// <summary>
        /// 重要等级
        /// </summary>
        public string SImportLeve { get; set; }
        /// <summary>
        /// 主问题类型
        /// </summary>
        public string SMainType { get; set; }
        /// <summary>
        /// 次问题类别
        /// </summary>
        public string SSecondType { get; set; }
        /// <summary>
        /// 问题的位置
        /// </summary>
        public string SPointPosition { get; set; }
        /// <summary>
        /// 测试文件存放路径
        /// </summary>
        public string STestFilePosition { get; set; }
        /// <summary>
        /// 原因分析
        /// </summary>
        public string SReasonAna { get; set; }
        /// <summary>
        /// 原因类别
        /// </summary>
        public string SReasonType { get; set; }
        /// <summary>
        /// 初步分析
        /// </summary>
        public string SPreliminaryAna { get; set; }
        /// <summary>
        /// 是否已解决
        /// </summary>
        public string SSolve { get; set; }
        /// <summary>
        /// 主要问题点原因分析
        /// </summary>
        public string SPointAna { get; set; }
        /// <summary>
        /// 问题点类型
        /// </summary>
        public string SPointType { get; set; }
        /// <summary>
        /// 解决方案
        /// </summary>
        public string SSolveScheme { get; set; }
        /// <summary>
        /// 预计解决时间
        /// </summary>
        public string SExceptSolveTime { get; set; }
        /// <summary>
        /// 场所属性
        /// </summary>
        public string Strcssx { get; set; }
        /// <summary>
        /// 次要问题点原因分析
        /// </summary>
        public string Strcywtyyfx { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Strbz { get; set; }
        public ProblemPont3G()
        {
            this.IID = 0;
            this.STurn = "-";
            this.SCity = "";
            this.STestTime = DateTime.Now;
            this.SProj = "";
            this.STestPoint = "";
            this.DLongitude = -1;
            this.DLatitude = -1;
            this.SSitePro = "";
            this.SImportLeve = "";
            this.SMainType = "";
            this.SSecondType = "";
            this.SPointPosition = "";
            this.STestFilePosition = "";
            this.SReasonAna = "";
            this.SReasonType = "";
            this.SPreliminaryAna = "";
            this.SSolve = "-";
            this.SPointAna = "-";
            this.SPointType = "-";
            this.SSolveScheme = "-";
            this.SExceptSolveTime = "-";
            this.Strcssx = "-";
            this.Strcywtyyfx = "-";
            this.Strbz = "-";

        }
    }
    public class ProblemSummaryPont3G
    {
        /// <summary>
        /// 项目
        /// </summary>
        public string SProject { get; set; }
        /// <summary>
        /// 城市类别
        /// </summary>
        public string SCityType { get; set; }
        /// <summary>
        /// 城市
        /// </summary>
        public string SCity { get; set; }
        /// <summary>
        /// 弱覆盖
        /// </summary>
        public int IWeakCoverage { get; set; }
        /// <summary>
        /// 掉话
        /// </summary>
        public int IDroppedCalls { get; set; }
        /// <summary>
        /// 未接通
        /// </summary>
        public int INotConnected { get; set; }
        /// <summary>
        /// 下载速率低（非广东版本）
        /// </summary>
        public int IDownLessOther { get; set; }
        /// <summary>
        /// TD深度覆盖不足
        /// </summary>
        public int ILessDownTDN { get; set; }
        /// <summary>
        /// 单用户下载速率低
        /// </summary>
        public int ISingleDownLess { get; set; }
        /// <summary>
        /// 多用户下载速率低
        /// </summary>
        public int IMostDownLess { get; set; }
        /// <summary>
        /// 下载掉线
        /// </summary>
        public int IDownDrop { get; set; }
        /// <summary>
        /// 下载超时
        /// </summary>
        public int IDownTimeOut { get; set; }
        /// <summary>
        /// 异系统切换失败
        /// </summary>
        public int IFallTDToGSM { get; set; }
        /// <summary>
        /// 指标未达标点汇总
        /// </summary>
        public int ILessThenCole { get; set; }
        /// <summary>
        /// TD深度覆盖不达标
        /// </summary>
        public int ILessThenDeep { get; set; }
        /// <summary>
        /// T不达标点汇总
        /// </summary>
        public int ILessThenDeepCole { get; set; }
        /// <summary>
        /// 全程呼叫成功率比竞争对手差
        /// </summary>
        public int ICallSuccessLess { get; set; }
        /// <summary>
        /// 单用户下载速率低于1Mbps
        /// </summary>
        public int ISingleDownLess1M { get; set; }
        /// <summary>
        /// T劣（广东）
        /// </summary>
        public int ITlessHG { get; set; }
        /// <summary>
        /// 竞对弱覆盖
        /// </summary>
        public int ICompareWeak { get; set; }
        /// <summary>
        /// 竞对下载速率低
        /// </summary>
        public int ICompareDownLess { get; set; }
        /// <summary>
        /// T劣点汇总
        /// </summary>
        public int ITLessCole { get; set; }
        /// <summary>
        /// 地市总问题点数
        /// </summary>
        public int ICityColePoint { get; set; }
        /// <summary>
        /// 测试总数
        /// </summary>
        public int ITestCole { get; set; }
        /// <summary>
        /// 测试通过率
        /// </summary>
        public string ITestSucess { get; set; }

        public ProblemSummaryPont3G()
        {
            this.SProject = "";
            this.SCityType = "";
            this.SCity = "";
            this.IWeakCoverage = 0;
            this.IDroppedCalls = 0;
            this.INotConnected = 0;
            this.IDownLessOther = 0;
            this.ILessDownTDN = 0;
            this.ISingleDownLess = 0;
            this.IMostDownLess = 0;
            this.IDownDrop = 0;
            this.IDownTimeOut = 0;
            this.IFallTDToGSM = 0;
            this.ILessThenCole = 0;
            this.ILessThenDeep = 0;
            this.ILessThenDeepCole = 0;
            this.ICallSuccessLess = 0;
            this.ITlessHG = 0;
            this.ICompareWeak = 0;
            this.ICompareDownLess = 0;
            this.ISingleDownLess1M = 0;
            this.ITLessCole = 0;
            this.ICityColePoint = 0;
            this.ITestCole = 0;
            this.ITestSucess = "";
        }
        public static ProblemSummaryPont3G fillXQDataTD(XQDataInfoTD xqInfo, int iTestDicCount, int strcityid)
        {
            ProblemSummaryPont3G lisSTem = new ProblemSummaryPont3G();
            lisSTem.SProject = "TDCQT";
            lisSTem.SCityType = CQTProblemSummaryAnalysGSM.cityLevel(strcityid);
            lisSTem.SCity = DistrictManager.GetInstance().getDistrictName(strcityid);
            lisSTem.IWeakCoverage = xqInfo.iweakcoverage;
            lisSTem.IDroppedCalls = xqInfo.idroppedcalls;           
            lisSTem.INotConnected = xqInfo.inotconnected;
            lisSTem.IDownLessOther = xqInfo.idownlessother;
            lisSTem.ILessDownTDN = xqInfo.ilessdowntdn;
            lisSTem.ISingleDownLess = xqInfo.isingleDownLess;
            lisSTem.IMostDownLess = xqInfo.imostDownLess;
            lisSTem.IDownDrop = xqInfo.idowndrop;
            lisSTem.IDownTimeOut = xqInfo.idowntimeout;
            lisSTem.IFallTDToGSM = xqInfo.ifallTDToGSM;
            lisSTem.ILessThenCole = xqInfo.ilessthenCole;
            lisSTem.ILessThenDeep = xqInfo.ilessthendeepTD;
            lisSTem.ILessThenDeepCole = xqInfo.ilessthendeepTD;
            lisSTem.ICallSuccessLess = xqInfo.icallSuccessLess;
            lisSTem.ITlessHG = xqInfo.itlessHG;
            lisSTem.ICompareWeak = xqInfo.icompareWeak;
            lisSTem.ICompareDownLess = xqInfo.icompareDownLess;
            lisSTem.ITLessCole = xqInfo.itlessCole;
            lisSTem.ICityColePoint = xqInfo.ilessthenCole + xqInfo.itlessCole + xqInfo.ilessthendeepTD;
            lisSTem.ITestCole = iTestDicCount;
            if (lisSTem.ITestCole != 0)
                lisSTem.ITestSucess = (100 - Math.Round((lisSTem.ICityColePoint * 100.0 / lisSTem.ITestCole), 2)).ToString("0.00") + "%";
            else
                lisSTem.ITestSucess = "-";
            return lisSTem;
        }
        public static ProblemSummaryPont3G fillDataTotal(SumarryDdataInfoTD sumarryInfo)
        {
            ProblemSummaryPont3G toa = new ProblemSummaryPont3G();
            toa.SProject = "----";
            toa.SCityType = "汇总统计";
            toa.SCity = "----";
            toa.IWeakCoverage = sumarryInfo.sumiWeakCoverage;
            toa.IDroppedCalls = sumarryInfo.sumiDroppedCalls;
            toa.INotConnected = sumarryInfo.sumiNotConnected;
            toa.IDownLessOther = sumarryInfo.sumiDownLessOther;
            toa.ILessDownTDN = sumarryInfo.sumiLessDownTDN;
            toa.ISingleDownLess = sumarryInfo.sumiSingleDownLess;
            toa.IMostDownLess = sumarryInfo.sumiMostDownLess;
            toa.IDownDrop = sumarryInfo.sumiDownDrop;
            toa.IDownTimeOut = sumarryInfo.sumiDownTimeOut;
            toa.IFallTDToGSM = sumarryInfo.sumiFallTDToGSM;
            toa.ILessThenCole = sumarryInfo.sumiLessThenCole;
            toa.ILessThenDeep = sumarryInfo.sumiLessThenDeep;
            toa.ILessThenDeepCole = sumarryInfo.sumiLessThenDeepCole;
            toa.ICallSuccessLess = sumarryInfo.sumiCallSuccessLess;
            toa.ITlessHG = sumarryInfo.sumiTlessHG;
            toa.ICompareWeak = sumarryInfo.sumiCompareWeak;
            toa.ICompareDownLess = sumarryInfo.sumiCompareDownLess;
            toa.ITLessCole = sumarryInfo.sumiTLessCole;
            toa.ICityColePoint = sumarryInfo.sumiCityColePoint;
            toa.ITestCole = sumarryInfo.sumiTestCole;
            if (sumarryInfo.sumiTestCole != 0)
                toa.ITestSucess = (100 - Math.Round((sumarryInfo.sumiCityColePoint * 100.0 / sumarryInfo.sumiTestCole), 2)).ToString("0.00") + "%";
            else
                toa.ITestSucess = "---";
            return toa;
        }
    }
    public class CQTProjectAreaIDKey
    {
        /// <summary>
        /// 测试时间
        /// </summary>
        public DateTime Dtime { get; set; }
        /// <summary>
        /// 项目ID
        /// </summary>
        public int IProj { get; set; }
        /// <summary>
        /// 地点ID
        /// </summary>
        public int IAreaID { get; set; }

        public CQTProjectAreaIDKey()
        {
            IProj = 0;
            IAreaID = 0;
            Dtime = DateTime.Now;
        }
        public override bool Equals(object obj)
        {
            CQTProjectAreaIDKey other = obj as CQTProjectAreaIDKey;
            if (other == null)
                return false;
            if (!base.GetType().Equals(obj.GetType()))
                return false;
            return (IProj.Equals(other.IProj)
                && IAreaID.Equals(other.IAreaID)
                && Dtime.Equals(other.Dtime));
        }
        public override int GetHashCode()
        {
            return (IProj + IAreaID + Dtime.ToString("yyyy-MM-dd")).GetHashCode();
        }
    }
}
