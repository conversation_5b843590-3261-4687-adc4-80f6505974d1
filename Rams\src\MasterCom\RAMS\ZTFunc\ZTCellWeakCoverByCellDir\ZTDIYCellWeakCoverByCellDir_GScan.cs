﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellWeakCoverByCellDir_GScan : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        public int rxLevMax { get; set; } = -85;
        public int cellCoverDistance { get; set; } = 300;
        public int weakGridCount { get; set; } = 2;
        public bool judgeByScale { get; set; } = false;
        public int judgeScale { get; set; } = 50;
        protected Dictionary<string, GridForCellWeakCover> gridDic = new Dictionary<string, GridForCellWeakCover>();
        protected List<CellWeakCoverByGridInfoBase> cellWeakCoverByGridList = new List<CellWeakCoverByGridInfoBase>();
        private static ZTDIYCellWeakCoverByCellDir_GScan intance = null;
        protected static readonly object lockObj = new object();
        public static ZTDIYCellWeakCoverByCellDir_GScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYCellWeakCoverByCellDir_GScan();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYCellWeakCoverByCellDir_GScan()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_SCAN);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖小区_GSM扫频"; }
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        protected override void getBackgroundData()
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(
                Condition.Periods[0].IBeginTime, Condition.Periods[0].IEndTime, GetSubFuncID(), Name
                , StatType, BackgroundFuncBaseSetting.GetInstance().projectType);
        }

        protected override List<TimePeriod> GetStatedTimePeriod()
        {
            return BackgroundFuncQueryManager.GetInstance().GetStatedTimePeriod_Cell(GetSubFuncID(), BackgroundFuncBaseSetting.GetInstance().projectType);
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15031, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);
            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        CellWeakCoverByCellDirConditionDlg conditionDlg;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (conditionDlg == null || conditionDlg.IsDisposed)
            {
                conditionDlg = new CellWeakCoverByCellDirConditionDlg();
            }
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                ZTDiyCellWeakCoverByCellDirCondition condition = new ZTDiyCellWeakCoverByCellDirCondition();
                conditionDlg.GetCondition(condition);
                rxLevMax = condition.rxLevMax;
                cellCoverDistance = condition.cellCoverDistance;
                weakGridCount = condition.weakGridCount;
                judgeByScale = condition.judgeByScale;
                judgeScale = condition.judgeScale;
                cellWeakCoverByGridList.Clear();    //数据清理，避免保留上次的结果。

                return true;
            }
            return false;
        }

        public class ZTDiyCellWeakCoverByCellDirCondition
        {
            public int rxLevMax { get; set; } = -85;
            public int cellCoverDistance { get; set; } = 300;
            public int weakGridCount { get; set; } = 2;
            public bool judgeByScale { get; set; } = false;
            public int judgeScale { get; set; } = 50;
        }

        protected override void doWithDTData(MasterCom.RAMS.Model.TestPoint tp)
        {
            double longitude = getGridTLLong(tp.Longitude);
            double latitude = getGridTLLat(tp.Latitude);
            string key = longitude + "|" + latitude;
            float? rxLev = getBestRxLev(tp);
            if (rxLev != null)
            {
                if (!gridDic.ContainsKey(key))
                {
                    GridForCellWeakCover grid = new GridForCellWeakCover(longitude, latitude, rxLevMax, judgeByScale, judgeScale);
                    gridDic[key] = grid;
                }
                gridDic[key].AddTestPoint((float)rxLev);
            }
        }

        protected override void getResultAfterQuery()
        {
            WaitBox.Text = "开始获取弱覆盖栅格...";
            List<GridForCellWeakCover> weakGridList = getWeakGridList();

            gridDic.Clear();
            if (weakGridList.Count > 0)
            {
                var weakCellDic = new Dictionary<ICell, CellWeakCoverByGridInfoBase>();
                double lonDiff = 0.0001 * cellCoverDistance / 10;
                double latDiff = 0.00008 * cellCoverDistance / 10;

                getWeakCellDic(weakGridList, weakCellDic, lonDiff, latDiff);
                cellWeakCoverByGridList.Clear();
                MainModel.ClearCellWeakCoverByGridData();
                foreach (var weakCell in weakCellDic.Values)
                {
                    weakCell.Calculate();
                    if (weakCell.GridCount >= weakGridCount)
                    {
                        cellWeakCoverByGridList.Add(weakCell);
                    }
                }
            }
        }

        protected virtual void getWeakCellDic(List<GridForCellWeakCover> weakGridList, Dictionary<ICell, CellWeakCoverByGridInfoBase> weakCellDic, 
            double lonDiff, double latDiff)
        {
            List<Cell> cellList = getCellsOfRegion();
            int iLoop = 1;
            WaitBox.Text = "开始获取弱覆盖小区...";
            foreach (Cell cell in cellList)
            {
                dealWeakCellByWeakGrid(weakGridList, weakCellDic, lonDiff, latDiff, cell);
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
        }

        protected void dealWeakCellByWeakGrid(List<GridForCellWeakCover> weakGridList
            , Dictionary<ICell, CellWeakCoverByGridInfoBase> weakCellDic
            , double lonDiff, double latDiff, ICell cell)
        {
            double lonMin = cell.Longitude - lonDiff;
            double lonMax = cell.Longitude + lonDiff;
            double latMin = cell.Latitude - latDiff;
            double latMax = cell.Latitude + latDiff;
            foreach (GridForCellWeakCover grid in weakGridList)
            {
                bool isValid = judgeValidGridCell(cell, lonMin, lonMax, latMin, latMax, grid);
                if (isValid)
                {
                    if (!weakCellDic.ContainsKey(cell))
                    {
                        weakCellDic[cell] = getCellWeakCoverByGrid(cell);
                    }
                    weakCellDic[cell].GridList.Add(grid);
                }
            }
        }

        protected virtual CellWeakCoverByGridInfoBase getCellWeakCoverByGrid(ICell cell)
        {
            if (cell is Cell)
            {
                Cell gsmCell = cell as Cell;
                CellWeakCoverByGridInfoGsm weakCell = new CellWeakCoverByGridInfoGsm();
                weakCell.FillCellData(gsmCell);
                //weakCell.Calculate();
                return weakCell;
            }
            return null;
        }

        protected bool judgeValidGridCell(ICell cell, double lonMin, double lonMax, double latMin, double latMax, GridForCellWeakCover grid)
        {
            if (grid.LongitudeMid < lonMin || grid.LongitudeMid > lonMax || grid.LatitudeMid < latMin || grid.LatitudeMid > latMax)
            {
                return false;
            }
            if (!MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, grid.LongitudeMid, grid.LatitudeMid, cell.Direction))
            {
                return false;
            }
            if (MathFuncs.GetDistance(cell.Longitude, cell.Latitude, grid.LongitudeMid, grid.LatitudeMid) >= cellCoverDistance)
            {
                return false;
            }
            return true;
        }

        protected List<GridForCellWeakCover> getWeakGridList()
        {
            List<GridForCellWeakCover> weakGridList = new List<GridForCellWeakCover>();
            int iLoop = 1;
            foreach (GridForCellWeakCover grid in gridDic.Values)
            {
                if (grid.IsWeakGrid())
                {
                    weakGridList.Add(grid);
                }
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / gridDic.Count);
            }

            return weakGridList;
        }

        private List<Cell> getCellsOfRegion()
        {
            List<Cell> cellList = new List<Cell>();
            int index = 1;
            List<Cell> curCells = MainModel.CellManager.GetCells(Condition.Periods[0].BeginTime);
            WaitBox.Text = "开始获取选择区域内小区...";
            foreach (Cell cell in curCells)
            {
                if (cell.Type == BTSType.Outdoor && condition.Geometorys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {
                    cellList.Add(cell);
                }
                WaitBox.ProgressPercent = (int)(100.0 * index++ / curCells.Count);
            }
            return cellList;
        }

        protected override void FireShowFormAfterQuery()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ScanCellWeakCoverByCellDirForm).FullName);
            ScanCellWeakCoverByCellDirForm scanCellWeakCoverByCellDirForm = obj == null ? null : obj as ScanCellWeakCoverByCellDirForm;
            if (scanCellWeakCoverByCellDirForm == null || scanCellWeakCoverByCellDirForm.IsDisposed)
            {
                scanCellWeakCoverByCellDirForm = new ScanCellWeakCoverByCellDirForm(MainModel);
            }
            scanCellWeakCoverByCellDirForm.FillData(cellWeakCoverByGridList);
            if (!scanCellWeakCoverByCellDirForm.Visible)
            {
                scanCellWeakCoverByCellDirForm.Show(MainModel.MainForm);
            }
            MainModel.FireGridCoverQueried(this);
        }

        protected virtual float? getBestRxLev(TestPoint tp)
        {
            if (tp is ScanTestPoint_G)
            {
                float? rxLev = (float?)tp["GSCAN_RxLev", 0];
                if (rxLev == null || rxLev < -120 || rxLev > -10)
                {
                    return null;
                }
                return rxLev;
            }
            return null;
        }

        protected double getGridTLLong(double longitude)
        {
            int tllongitude = (int)(longitude * 10000000) / 4000 * 4000;
            return tllongitude / 10000000.0D;
        }
        protected double getGridTLLat(double latitude)
        {
            int tllatitude = (int)(latitude * 10000000) / 3600 * 3600 + 3600;
            return tllatitude / 10000000.0D;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.GSM扫频; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RxLevMax"] = rxLevMax;
                param["CellCoverDistance"] = cellCoverDistance;
                param["WeakGridCount"] = weakGridCount;
                param["JudgeByScale"] = judgeByScale;
                param["JudgeScale"] = judgeScale;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("RxLevMax"))
                {
                    rxLevMax = int.Parse(param["RxLevMax"].ToString());
                }
                if (param.ContainsKey("CellCoverDistance"))
                {
                    cellCoverDistance = int.Parse(param["CellCoverDistance"].ToString());
                }
                if (param.ContainsKey("WeakGridCount"))
                {
                    weakGridCount = int.Parse(param["WeakGridCount"].ToString());
                }
                if (param.ContainsKey("JudgeByScale"))
                {
                    judgeByScale = (bool)param["JudgeByScale"];
                }
                if (param.ContainsKey("JudgeScale"))
                {
                    judgeScale = int.Parse(param["JudgeScale"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CellWeakCoverByCellProperties_GSCAN(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (var block in cellWeakCoverByGridList)
            {
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                result.ISTime = Condition.Periods[0].IBeginTime;
                result.IETime = Condition.Periods[0].IEndTime;
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
            cellWeakCoverByGridList.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int gridCount = bgResult.GetImageValueInt();
                StringBuilder sb = new StringBuilder();
                sb.Append("弱覆盖栅格数：");
                sb.Append(gridCount);
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }

    public class CellWeakCoverByGrid
    {
        public CellWeakCoverByGrid(Cell cell)
        {
            this.cell = cell;
        }

        public CellWeakCoverByGrid(TDCell tdCell)
        {
            this.tdCell = tdCell;
        }

        public CellWeakCoverByGrid(WCell wCell)
        {
            this.wCell = wCell;
        }
        public CellWeakCoverByGrid(LTECell lteCell)
        {
            this.lteCell = lteCell;
        }

        public Cell cell { get; set; }
        public TDCell tdCell { get; set; }
        public WCell wCell { get; set; }
        public LTECell lteCell { get; set; }
        public double LongitudeCell
        {
            get 
            {
                if (cell != null)
                {
                    return cell.Longitude;
                }
                else if(tdCell != null)
                {
                    return tdCell.Longitude;
                }
                else if(wCell != null)
                {
                    return wCell.Longitude;
                }
                else if (lteCell != null)
                {
                    return lteCell.Longitude;
                }
                else
                {
                    return 0;
                }
            }
        }
        public double LatitudeCell
        {
            get 
            {
                if (cell != null)
                {
                    return cell.Latitude;
                }
                else if(tdCell != null)
                {
                    return tdCell.Latitude;
                }
                else if (wCell != null)
                {
                    return wCell.Latitude;
                }
                else if (lteCell != null)
                {
                    return lteCell.Latitude;
                }
                else
                {
                    return 0;
                }
            }
        }
        public string CellName
        {
            get 
            {
                if (cell != null)
                {
                    return cell.Name;
                }
                else if (tdCell != null)
                {
                    return tdCell.Name;
                }
                else if (wCell != null)
                {
                    return wCell.Name;
                }
                else if (lteCell != null)
                {
                    return lteCell.Name;
                }
                else
                {
                    return "";
                }
            }
        }
        public int LAC
        {
            get
            {
                if (cell != null)
                {
                    return cell.LAC;
                }
                else if (tdCell != null)
                {
                    return tdCell.LAC;
                }
                else if (wCell != null)
                {
                    return wCell.LAC;
                }
                else if (lteCell != null)
                {
                    return lteCell.TAC;
                }
                else
                {
                    return -999;
                }
            }
        }
        public int CI
        {
            get
            {
                if (cell != null)
                {
                    return cell.CI;
                }
                else if (tdCell != null)
                {
                    return tdCell.CI;
                }
                else if (wCell != null)
                {
                    return wCell.CI;
                }
                else if (lteCell != null)
                {
                    return lteCell.ECI;
                }
                else
                {
                    return -999;
                }
            }
        }
        public int BCCH
        {
            get
            {
                if (cell != null)
                {
                    return cell.BCCH;
                }
                else if (tdCell != null)
                {
                    return tdCell.FREQ;
                }
                else if (wCell != null)
                {
                    return wCell.UARFCN;
                }
                else if (lteCell != null)
                {
                    return lteCell.EARFCN;
                }
                else
                {
                    return -999;
                }
            }
        }
        public int BSIC
        {
            get
            {
                if (cell != null)
                {
                    return cell.BSIC;
                }
                else if (tdCell != null)
                {
                    return tdCell.CPI;
                }
                else if (wCell != null)
                {
                    return wCell.PSC;
                }
                else if (lteCell != null)
                {
                    return lteCell.PCI;
                }
                else
                {
                    return -999;
                }
            }
        }
        public int GridCount
        {
            get { return gridList.Count; }
        }
        public List<GridForCellWeakCover> gridList { get; set; } = new List<GridForCellWeakCover>();

        public double RxLevMean
        {
            get 
            {
                double sum = 0;
                double count = 0;
                foreach (GridForCellWeakCover grid in gridList)
                {
                    sum += grid.rxLevSum;
                    count += grid.SampleCount;
                }
                return Math.Round(1.0 * sum / count, 2);
            }
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            if (cell != null)
            {
                bgResult.CellType = BackgroundCellType.GSM;
            }
            else if (tdCell != null)
            {
                bgResult.CellType = BackgroundCellType.TD;
            }
            bgResult.LAC = LAC;
            bgResult.CI = CI;
            bgResult.BCCH = BCCH;
            bgResult.BSIC = BSIC;
            bgResult.LongitudeMid = LongitudeCell;
            bgResult.LatitudeMid = LatitudeCell;
            bgResult.RxLevMean = (float)RxLevMean;
            bgResult.AddImageValue(GridCount);
            return bgResult;
        }
    }

    public class GridForCellWeakCover
    {
        public GridForCellWeakCover(double longitude, double latitude, int rxLevThreshold, bool judgeByScale, int judgeScale)
        {
            this.longitude = longitude;
            this.latitude = latitude;
            this.rxLevThreshold = rxLevThreshold;
            this.judgeByScale = judgeByScale;
            this.judgeScale = judgeScale / 100.0;
        }

        /// <summary>
        /// 弱覆盖判定门限
        /// </summary>
        private readonly int rxLevThreshold = 0;
        private readonly bool judgeByScale = false;
        /// <summary>
        /// 弱覆盖采样点比例门限
        /// </summary>
        private readonly double judgeScale = 0;
        public double longitude { get; set; }
        public double latitude { get; set; }

        public double LongitudeMid
        {
            get { return longitude + 0.0002; }
        }

        public double LatitudeMid
        {
            get { return latitude - 0.00018; }
        }

        public void AddTestPoint(float curRxLev)
        {
            if (curRxLev < rxLevThreshold)
            {
                badSampleCount++;
            }
            else
            {
                goodSampleCount++;
            }
            rxLevSum += curRxLev;
        }

        public bool IsWeakGrid()
        {
            if (judgeByScale)
            {
                return isWeakGridByScale();
            }
            return isWeakGridByRxLevMean();
        }

        /// <summary>
        /// 按平均电平判定
        /// </summary>
        /// <returns></returns>
        private bool isWeakGridByRxLevMean()
        {
            if (RxLevMean < rxLevThreshold)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 按弱覆盖采样点比例判定
        /// </summary>
        /// <returns></returns>
        private bool isWeakGridByScale()
        {
            if (badSampleCount * 1.0 / (badSampleCount + goodSampleCount) > judgeScale)
            {
                return true;
            }
            return false;
        }

        public double RxLevMean
        {
            get { return Math.Round(rxLevSum / (badSampleCount + goodSampleCount), 2); }
        }

        public double rxLevSum { get; set; } = 0;
        private int badSampleCount = 0;
        private int goodSampleCount = 0;
        public int SampleCount
        {
            get { return badSampleCount + goodSampleCount; }
        }
    }
}
