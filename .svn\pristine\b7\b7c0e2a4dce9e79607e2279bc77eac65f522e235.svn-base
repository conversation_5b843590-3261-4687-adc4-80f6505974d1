﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEMIMOSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupChancel = new DevExpress.XtraEditors.GroupControl();
            this.label6 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numChanel = new System.Windows.Forms.NumericUpDown();
            this.chkF = new System.Windows.Forms.CheckBox();
            this.chkE = new System.Windows.Forms.CheckBox();
            this.chkD = new System.Windows.Forms.CheckBox();
            this.groupSampleN = new DevExpress.XtraEditors.GroupControl();
            this.label5 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numSampleN = new System.Windows.Forms.NumericUpDown();
            this.groupRSRP0 = new DevExpress.XtraEditors.GroupControl();
            this.numRsrp0Min = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.numRsrp0Max = new System.Windows.Forms.NumericUpDown();
            this.chkChanel = new System.Windows.Forms.CheckBox();
            this.chkSampleN = new System.Windows.Forms.CheckBox();
            this.chkRsrp0 = new System.Windows.Forms.CheckBox();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.chkRsrp1 = new System.Windows.Forms.CheckBox();
            this.groupRSRP1 = new DevExpress.XtraEditors.GroupControl();
            this.numRsrp1Min = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.numRsrp1Max = new System.Windows.Forms.NumericUpDown();
            this.rbScan = new System.Windows.Forms.RadioButton();
            this.rbDT = new System.Windows.Forms.RadioButton();
            ((System.ComponentModel.ISupportInitialize)(this.groupChancel)).BeginInit();
            this.groupChancel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numChanel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupSampleN)).BeginInit();
            this.groupSampleN.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleN)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupRSRP0)).BeginInit();
            this.groupRSRP0.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp0Min)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp0Max)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupRSRP1)).BeginInit();
            this.groupRSRP1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp1Min)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp1Max)).BeginInit();
            this.SuspendLayout();
            // 
            // groupChancel
            // 
            this.groupChancel.Controls.Add(this.label6);
            this.groupChancel.Controls.Add(this.label1);
            this.groupChancel.Controls.Add(this.numChanel);
            this.groupChancel.Controls.Add(this.chkF);
            this.groupChancel.Controls.Add(this.chkE);
            this.groupChancel.Controls.Add(this.chkD);
            this.groupChancel.Location = new System.Drawing.Point(83, 41);
            this.groupChancel.Name = "groupChancel";
            this.groupChancel.Size = new System.Drawing.Size(391, 72);
            this.groupChancel.TabIndex = 0;
            this.groupChancel.Text = "按小区频段分析";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.ForeColor = System.Drawing.Color.Red;
            this.label6.Location = new System.Drawing.Point(238, 55);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(0, 12);
            this.label6.TabIndex = 7;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(216, 36);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(83, 12);
            this.label1.TabIndex = 5;
            this.label1.Text = "每频段前N强：";
            // 
            // numChanel
            // 
            this.numChanel.Location = new System.Drawing.Point(305, 31);
            this.numChanel.Name = "numChanel";
            this.numChanel.Size = new System.Drawing.Size(46, 21);
            this.numChanel.TabIndex = 4;
            this.numChanel.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // chkF
            // 
            this.chkF.AutoSize = true;
            this.chkF.Checked = true;
            this.chkF.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkF.Location = new System.Drawing.Point(145, 36);
            this.chkF.Name = "chkF";
            this.chkF.Size = new System.Drawing.Size(54, 16);
            this.chkF.TabIndex = 2;
            this.chkF.Text = "F频段";
            this.chkF.UseVisualStyleBackColor = true;
            // 
            // chkE
            // 
            this.chkE.AutoSize = true;
            this.chkE.Checked = true;
            this.chkE.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkE.Location = new System.Drawing.Point(85, 36);
            this.chkE.Name = "chkE";
            this.chkE.Size = new System.Drawing.Size(54, 16);
            this.chkE.TabIndex = 1;
            this.chkE.Text = "E频段";
            this.chkE.UseVisualStyleBackColor = true;
            // 
            // chkD
            // 
            this.chkD.AutoSize = true;
            this.chkD.Checked = true;
            this.chkD.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkD.Location = new System.Drawing.Point(25, 36);
            this.chkD.Name = "chkD";
            this.chkD.Size = new System.Drawing.Size(54, 16);
            this.chkD.TabIndex = 0;
            this.chkD.Text = "D频段";
            this.chkD.UseVisualStyleBackColor = true;
            // 
            // groupSampleN
            // 
            this.groupSampleN.Controls.Add(this.label5);
            this.groupSampleN.Controls.Add(this.label2);
            this.groupSampleN.Controls.Add(this.numSampleN);
            this.groupSampleN.Location = new System.Drawing.Point(83, 118);
            this.groupSampleN.Name = "groupSampleN";
            this.groupSampleN.Size = new System.Drawing.Size(391, 74);
            this.groupSampleN.TabIndex = 1;
            this.groupSampleN.Text = "按前N强";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.ForeColor = System.Drawing.Color.Red;
            this.label5.Location = new System.Drawing.Point(169, 39);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(0, 12);
            this.label5.TabIndex = 6;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(16, 40);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(95, 12);
            this.label2.TabIndex = 5;
            this.label2.Text = "每采样点前N强：";
            // 
            // numSampleN
            // 
            this.numSampleN.Enabled = false;
            this.numSampleN.Location = new System.Drawing.Point(117, 35);
            this.numSampleN.Name = "numSampleN";
            this.numSampleN.Size = new System.Drawing.Size(46, 21);
            this.numSampleN.TabIndex = 4;
            this.numSampleN.Value = new decimal(new int[] {
            9,
            0,
            0,
            0});
            // 
            // groupRSRP0
            // 
            this.groupRSRP0.Controls.Add(this.numRsrp0Min);
            this.groupRSRP0.Controls.Add(this.label3);
            this.groupRSRP0.Controls.Add(this.numRsrp0Max);
            this.groupRSRP0.Location = new System.Drawing.Point(83, 197);
            this.groupRSRP0.Name = "groupRSRP0";
            this.groupRSRP0.Size = new System.Drawing.Size(391, 72);
            this.groupRSRP0.TabIndex = 2;
            this.groupRSRP0.Text = "按RSRP0";
            // 
            // numRsrp0Min
            // 
            this.numRsrp0Min.Enabled = false;
            this.numRsrp0Min.Location = new System.Drawing.Point(18, 38);
            this.numRsrp0Min.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRsrp0Min.Name = "numRsrp0Min";
            this.numRsrp0Min.Size = new System.Drawing.Size(46, 21);
            this.numRsrp0Min.TabIndex = 6;
            this.numRsrp0Min.Value = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(70, 44);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(59, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "<=RSRP0<=";
            // 
            // numRsrp0Max
            // 
            this.numRsrp0Max.Enabled = false;
            this.numRsrp0Max.Location = new System.Drawing.Point(135, 38);
            this.numRsrp0Max.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRsrp0Max.Name = "numRsrp0Max";
            this.numRsrp0Max.Size = new System.Drawing.Size(46, 21);
            this.numRsrp0Max.TabIndex = 4;
            this.numRsrp0Max.Value = new decimal(new int[] {
            45,
            0,
            0,
            -2147483648});
            // 
            // chkChanel
            // 
            this.chkChanel.AutoSize = true;
            this.chkChanel.Checked = true;
            this.chkChanel.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkChanel.Location = new System.Drawing.Point(12, 77);
            this.chkChanel.Name = "chkChanel";
            this.chkChanel.Size = new System.Drawing.Size(60, 16);
            this.chkChanel.TabIndex = 4;
            this.chkChanel.Text = "按频段";
            this.chkChanel.UseVisualStyleBackColor = true;
            this.chkChanel.CheckedChanged += new System.EventHandler(this.chkChanel_CheckedChanged);
            // 
            // chkSampleN
            // 
            this.chkSampleN.AutoSize = true;
            this.chkSampleN.Location = new System.Drawing.Point(11, 157);
            this.chkSampleN.Name = "chkSampleN";
            this.chkSampleN.Size = new System.Drawing.Size(66, 16);
            this.chkSampleN.TabIndex = 5;
            this.chkSampleN.Text = "按前N强";
            this.chkSampleN.UseVisualStyleBackColor = true;
            this.chkSampleN.CheckedChanged += new System.EventHandler(this.chkSampleN_CheckedChanged);
            // 
            // chkRsrp0
            // 
            this.chkRsrp0.AutoSize = true;
            this.chkRsrp0.Location = new System.Drawing.Point(12, 233);
            this.chkRsrp0.Name = "chkRsrp0";
            this.chkRsrp0.Size = new System.Drawing.Size(66, 16);
            this.chkRsrp0.TabIndex = 6;
            this.chkRsrp0.Text = "按RSRP0";
            this.chkRsrp0.UseVisualStyleBackColor = true;
            this.chkRsrp0.CheckedChanged += new System.EventHandler(this.chkRsrp0_CheckedChanged);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(317, 351);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 7;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(398, 351);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 8;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // chkRsrp1
            // 
            this.chkRsrp1.AutoSize = true;
            this.chkRsrp1.Location = new System.Drawing.Point(11, 310);
            this.chkRsrp1.Name = "chkRsrp1";
            this.chkRsrp1.Size = new System.Drawing.Size(66, 16);
            this.chkRsrp1.TabIndex = 10;
            this.chkRsrp1.Text = "按RSRP1";
            this.chkRsrp1.UseVisualStyleBackColor = true;
            this.chkRsrp1.CheckedChanged += new System.EventHandler(this.chkRsrp1_CheckedChanged);
            // 
            // groupRSRP1
            // 
            this.groupRSRP1.Controls.Add(this.numRsrp1Min);
            this.groupRSRP1.Controls.Add(this.label4);
            this.groupRSRP1.Controls.Add(this.numRsrp1Max);
            this.groupRSRP1.Location = new System.Drawing.Point(82, 274);
            this.groupRSRP1.Name = "groupRSRP1";
            this.groupRSRP1.Size = new System.Drawing.Size(391, 72);
            this.groupRSRP1.TabIndex = 9;
            this.groupRSRP1.Text = "按RSRP1";
            // 
            // numRsrp1Min
            // 
            this.numRsrp1Min.Enabled = false;
            this.numRsrp1Min.Location = new System.Drawing.Point(18, 38);
            this.numRsrp1Min.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRsrp1Min.Name = "numRsrp1Min";
            this.numRsrp1Min.Size = new System.Drawing.Size(46, 21);
            this.numRsrp1Min.TabIndex = 6;
            this.numRsrp1Min.Value = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(70, 44);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "<=RSRP1<=";
            // 
            // numRsrp1Max
            // 
            this.numRsrp1Max.Enabled = false;
            this.numRsrp1Max.Location = new System.Drawing.Point(135, 38);
            this.numRsrp1Max.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRsrp1Max.Name = "numRsrp1Max";
            this.numRsrp1Max.Size = new System.Drawing.Size(46, 21);
            this.numRsrp1Max.TabIndex = 4;
            this.numRsrp1Max.Value = new decimal(new int[] {
            45,
            0,
            0,
            -2147483648});
            // 
            // rbScan
            // 
            this.rbScan.AutoSize = true;
            this.rbScan.Checked = true;
            this.rbScan.Location = new System.Drawing.Point(83, 13);
            this.rbScan.Name = "rbScan";
            this.rbScan.Size = new System.Drawing.Size(71, 16);
            this.rbScan.TabIndex = 11;
            this.rbScan.TabStop = true;
            this.rbScan.Text = "扫频数据";
            this.rbScan.UseVisualStyleBackColor = true;
            this.rbScan.CheckedChanged += new System.EventHandler(this.rbScan_CheckedChanged);
            // 
            // rbDT
            // 
            this.rbDT.AutoSize = true;
            this.rbDT.Location = new System.Drawing.Point(254, 12);
            this.rbDT.Name = "rbDT";
            this.rbDT.Size = new System.Drawing.Size(71, 16);
            this.rbDT.TabIndex = 12;
            this.rbDT.TabStop = true;
            this.rbDT.Text = "路测数据";
            this.rbDT.UseVisualStyleBackColor = true;
            // 
            // LTEMIMOSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(488, 381);
            this.Controls.Add(this.rbDT);
            this.Controls.Add(this.rbScan);
            this.Controls.Add(this.chkRsrp1);
            this.Controls.Add(this.groupRSRP1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.chkRsrp0);
            this.Controls.Add(this.chkSampleN);
            this.Controls.Add(this.chkChanel);
            this.Controls.Add(this.groupRSRP0);
            this.Controls.Add(this.groupSampleN);
            this.Controls.Add(this.groupChancel);
            this.Name = "LTEMIMOSetForm";
            this.Text = "LTE MIMO 设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupChancel)).EndInit();
            this.groupChancel.ResumeLayout(false);
            this.groupChancel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numChanel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupSampleN)).EndInit();
            this.groupSampleN.ResumeLayout(false);
            this.groupSampleN.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleN)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupRSRP0)).EndInit();
            this.groupRSRP0.ResumeLayout(false);
            this.groupRSRP0.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp0Min)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp0Max)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupRSRP1)).EndInit();
            this.groupRSRP1.ResumeLayout(false);
            this.groupRSRP1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp1Min)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrp1Max)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupChancel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numChanel;
        private System.Windows.Forms.CheckBox chkF;
        private System.Windows.Forms.CheckBox chkE;
        private System.Windows.Forms.CheckBox chkD;
        private DevExpress.XtraEditors.GroupControl groupSampleN;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numSampleN;
        private DevExpress.XtraEditors.GroupControl groupRSRP0;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRsrp0Max;
        private System.Windows.Forms.CheckBox chkChanel;
        private System.Windows.Forms.CheckBox chkSampleN;
        private System.Windows.Forms.CheckBox chkRsrp0;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.NumericUpDown numRsrp0Min;
        private System.Windows.Forms.CheckBox chkRsrp1;
        private DevExpress.XtraEditors.GroupControl groupRSRP1;
        private System.Windows.Forms.NumericUpDown numRsrp1Min;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numRsrp1Max;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.RadioButton rbScan;
        private System.Windows.Forms.RadioButton rbDT;
    }
}