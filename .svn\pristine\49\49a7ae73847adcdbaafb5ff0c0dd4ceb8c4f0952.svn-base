﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEHandOverEarfcnBase : ZTLTEHandOverAnaBase
    {
        List<HandOverDirectionInfo> handDirectionInfoList = new List<HandOverDirectionInfo>();
        public LTEHandOverEarfcnBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;
        }
        protected void init(bool isVoLTE)
        {
            valuedEvtId = new List<int> { 851, 870, 899, 1100 };
            ServiceTypes.Clear();
            if (isVoLTE)
            {
                ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            }
            else
            {
                ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
                ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
                ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
                ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
                ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22069, this.Name);//////
        }
        protected override void getResultsAfterQuery()
        {
            Dictionary<string, HandOverDirectionInfo> dicInfo = new Dictionary<string, HandOverDirectionInfo>();
            foreach(ZTLTEHandOverAnaItem item in resultList)
            {
                if (!dicInfo.ContainsKey(item.HnadOverDirection))
                {
                    dicInfo[item.HnadOverDirection] = new HandOverDirectionInfo(item.HnadOverDirection);
                }

                if (item.HandOverResult == "成功")
                {
                    dicInfo[item.HnadOverDirection].HnadOverSuccessTimes++;
                    dicInfo[item.HnadOverDirection].HnadOverTotalTimes++;
                }
                else if (item.HandOverResult == "失败")
                {
                    dicInfo[item.HnadOverDirection].HnadOverFailTimes++;
                    dicInfo[item.HnadOverDirection].HnadOverTotalTimes++;
                }
            }
            handDirectionInfoList = new List<HandOverDirectionInfo>(dicInfo.Values);
        }
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LTEHandOverEarfcnListForm frm = MainModel.CreateResultForm(typeof(LTEHandOverEarfcnListForm)) as LTEHandOverEarfcnListForm;
            frm.FillData(handDirectionInfoList, resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class LTEHandOverEarfcnBase_FDD : LTEHandOverEarfcnBase
    {
        public LTEHandOverEarfcnBase_FDD(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;
            carrierID = CarrierType.ChinaUnicom;
            valuedEvtId = new List<int> { 3156, 3157, 3159, 3160 };
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26044, this.Name);//////
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
    }

    public class VOLTEHandOverEarfcnBase_FDD : LTEHandOverEarfcnBase_FDD
    {
        public VOLTEHandOverEarfcnBase_FDD(MainModel mainModel)
            : base(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30031, this.Name);//////
        }
    }
    
    public class HandOverDirectionInfo
    {
        public HandOverDirectionInfo(string strDirection)
        {
            this.HnadOverDirection = strDirection;
        }
        public string HnadOverDirection { get; set; }
        public int HnadOverSuccessTimes { get; set; }
        public int HnadOverFailTimes { get; set; }
        public int HnadOverTotalTimes { get; set; }
    }
}
