﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model
{
    public class  QueryCellSignParam : QueryBase
    {
        private readonly List<CellSign> cells = null;
        protected List<CellParamColumn> paramCols = null;
        protected DateTime date;
        public QueryCellSignParam(MainModel mm,DateTime date,List<CellSign> cells,List<CellParamColumn> paramCols)
            : base(mm)
        {
            this.date = date;
            this.cells = cells;
            this.paramCols = paramCols;
        }

        public override string Name
        {
            get { return "查询单个时间点小区参数"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            if (cells == null || cells.Count == 0 || paramCols == null || paramCols.Count == 0)
            {
                return;
            }
            queryParam(cells);
        }

        Dictionary<CellSign, Dictionary<CellParamColumn, object>> cellParamValueDic = new Dictionary<CellSign, Dictionary<CellParamColumn, object>>();
        public Dictionary<CellSign, Dictionary<CellParamColumn, object>> CellParamValueDic
        {
            get { return cellParamValueDic; }
        }

        protected void queryParam(List<CellSign> cellSignList)
        {
            cellParamValueDic = new Dictionary<CellSign, Dictionary<CellParamColumn, object>>();
            StringBuilder cellSb = new StringBuilder();
            Dictionary<int, CellSign> cellDic = new Dictionary<int, CellSign>();
            foreach (CellSign cellSign in cellSignList)
            {
                cellDic.Add(cellSign.SignID,cellSign);
                cellSb.Append(cellSign.SignID);
                cellSb.Append(',');
            }
            if (cellSb.Length>0)
            {
                cellSb.Remove(cellSb.Length - 1, 1);
            }
            else
            {
                return;
            }
            Dictionary<CellParamTable, List<CellParamColumn>> tableColsDic = getTableColsDic(paramCols);
            Dictionary<CellSign, double> cellNearestDic = new Dictionary<CellSign, double>();
            foreach (KeyValuePair<CellParamTable, List<CellParamColumn>> pair in tableColsDic)
            {
                CellParamTable table = pair.Key;
                StringBuilder sb = new StringBuilder("select ");
                sb.Append(table.CheckDateFieldName);
                sb.Append(",");
                sb.Append(table.CellIDForeignKey);
                foreach (CellParamColumn col in pair.Value)
                {
                    sb.Append(",");
                    sb.Append(col.Name);
                }
                sb.Append(" from ");
                sb.Append(table.FullName);
                sb.Append(" where ");
                sb.Append(table.CellIDForeignKey);
                sb.Append(" in(");
                sb.Append(cellSb.ToString());
                sb.Append(");");
                using (SqlConnection conn = new SqlConnection(CellParamCfgManager.GetInstance().DBConnectionStr))
                {
                    try
                    {
                        doQuery(cellDic, cellNearestDic, pair, table, sb, conn);
                    }
                    catch
                    {
                        //continue
                    }
                }
            }
        }

        private void doQuery(Dictionary<int, CellSign> cellDic, Dictionary<CellSign, double> cellNearestDic, 
            KeyValuePair<CellParamTable, List<CellParamColumn>> pair, CellParamTable table, StringBuilder sb, SqlConnection conn)
        {
            SqlCommand command = new SqlCommand(sb.ToString(), conn);
            conn.Open();
            SqlDataReader reader = command.ExecuteReader();
            while (reader.Read())
            {
                try
                {
                    int cellKey = int.Parse(reader[table.CellIDForeignKey].ToString());
                    CellSign curCell = cellDic[cellKey];
                    DateTime checkDate = DateTime.Parse(reader[table.CheckDateFieldName].ToString());
                    double secDiff = Math.Abs((date - checkDate).TotalSeconds);
                    //只取最接近采样点时间的记录
                    if (cellNearestDic.ContainsKey(curCell) && secDiff >= cellNearestDic[curCell])
                    {
                        continue;
                    }
                    else
                    {
                        cellNearestDic[curCell] = secDiff;
                    }
                    addCellParamValueDic(pair, reader, curCell);
                }
                catch
                {
                    //continue
                }
            }
        }

        private void addCellParamValueDic(KeyValuePair<CellParamTable, List<CellParamColumn>> pair, SqlDataReader reader, CellSign curCell)
        {
            Dictionary<CellParamColumn, object> paramValueDic = null;
            if (!cellParamValueDic.TryGetValue(curCell, out paramValueDic))
            {
                paramValueDic = new Dictionary<CellParamColumn, object>();
                cellParamValueDic.Add(curCell, paramValueDic);
            }
            foreach (CellParamColumn col in pair.Value)
            {
                if (paramValueDic.ContainsKey(col))
                {
                    continue;
                }
                object objValue = reader[col.Name];
                if (objValue is DBNull)
                {
                    continue;
                }
                paramValueDic.Add(col, objValue);
            }
        }

        private Dictionary<CellParamTable, List<CellParamColumn>> getTableColsDic(List<CellParamColumn> cols)
        {
            Dictionary<CellParamTable, List<CellParamColumn>> dic = new Dictionary<CellParamTable, List<CellParamColumn>>();
            foreach (CellParamColumn col in cols)
            {
                if (dic.ContainsKey(col.Table))
                {
                    dic[col.Table].Add(col);
                }
                else
                {
                    List<CellParamColumn> list = new List<CellParamColumn>();
                    list.Add(col);
                    dic.Add(col.Table, list);
                }
            }
            return dic;
        }
    }
}
