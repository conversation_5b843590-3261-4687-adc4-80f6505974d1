﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LowSpeedRoadInfo_NR
    {
        public LowSpeedRoadInfo_NR()
        {
        }

        public List<TestPoint> TestPointList { get; set; } = new List<TestPoint>();

        public int SN { get; set; }
        public string NetType { get; set; }
        public string RoadAppType { get; set; }

        private double duration;
        public double Duration
        {
            get
            {
                return Math.Round(duration, 2);
            }
            set
            {
                duration = value;
            }
        }

        private double distance;
        public double Distance
        {
            get
            {
                return Math.Round(distance, 2);
            }
            set
            {
                distance = value;
            }
        }
        public int TpCount { get { return TestPointList.Count; } }
        public int TpCount_LowSpeed { get; set; }
        public double TpRate_LowSpeed { get; set; }

        public int TpCount_0Speed { get; set; }
        public double TpRate_0Speed { get { return Math.Round(100.0 * TpCount_0Speed / TpCount, 2); } }

        public string FileName { get; set; }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public string RoadDesc { get; set; }
        public string AreaName { get; set; }
        public string GridName { get; set; }
        public string Arfcns { get; set; }
        public string CellNames_NR { get; set; }
        public string CellNames_LTE { get; set; }

        protected DoubleKpiGroup speedKpi = new DoubleKpiGroup();
        public double? SpeedMax { get { return speedKpi.KpiMax; } }
        public double? SpeedMin { get { return speedKpi.KpiMin; } }
        public double? SpeedAvg { get { return speedKpi.KpiAvgValue; } }

        protected DoubleKpiGroup rsrpKpi = new DoubleKpiGroup();
        public double? RsrpMax { get { return rsrpKpi.KpiMax; } }
        public double? RsrpMin { get { return rsrpKpi.KpiMin; } }
        public double? RsrpAvg { get { return rsrpKpi.KpiAvgValue; } }

        protected DoubleKpiGroup sinrKpi = new DoubleKpiGroup();
        public double? SinrMax { get { return sinrKpi.KpiMax; } }
        public double? SinrMin { get { return sinrKpi.KpiMin; } }
        public double? SinrAvg { get { return sinrKpi.KpiAvgValue; } }

        protected DoubleKpiGroup rsrpKpi_NCell = new DoubleKpiGroup();
        public double? RsrpMax_NCell { get { return rsrpKpi_NCell.KpiMax; } }
        public double? RsrpMin_NCell { get { return rsrpKpi_NCell.KpiMin; } }
        public double? RsrpAvg_NCell { get { return rsrpKpi_NCell.KpiAvgValue; } }

        protected DoubleKpiGroup rsrpKpi_LTE = new DoubleKpiGroup();
        public double? RsrpMax_LTE { get { return rsrpKpi_LTE.KpiMax; } }
        public double? RsrpMin_LTE { get { return rsrpKpi_LTE.KpiMin; } }
        public double? RsrpAvg_LTE { get { return rsrpKpi_LTE.KpiAvgValue; } }

        protected DoubleKpiGroup sinrKpi_LTE = new DoubleKpiGroup();
        public double? SinrMax_LTE { get { return sinrKpi_LTE.KpiMax; } }
        public double? SinrMin_LTE { get { return sinrKpi_LTE.KpiMin; } }
        public double? SinrAvg_LTE { get { return sinrKpi_LTE.KpiAvgValue; } }

        public double? Rate_16QAM_DL { get; set; }
        public double? Rate_64QAM_DL { get; set; }
        public double? Rate_256QAM_DL { get; set; }
        public double? Rate_BPSK_DL { get; set; }
        public double? Rate_QPSK_DL { get; set; }

        public double PDSCH_BLER { get; set; }
        public double PUSCH_BLER { get; set; }
        public double? CqiAvg { get; set; }

        public delegate double? DelGetSpeed(TestPoint tp);
        public void SetTestPoints(List<TestPoint> testPoints, DelGetSpeed getSpeedFunc)
        {
            this.TestPointList = testPoints;
            if (testPoints == null || testPoints.Count <= 0)
            {
                return;
            }

            computeCommonInfo(testPoints, getSpeedFunc);
            computeModulation(testPoints);
        }
        private void computeCommonInfo(List<TestPoint> testPoints, DelGetSpeed getSpeedFunc)
        {
            TestPoint firstTp = testPoints[0];
            TestPoint midTp = testPoints[testPoints.Count / 2];
            TestPoint endTp = testPoints[testPoints.Count - 1];

            this.BeginTime = firstTp.DateTime;
            this.EndTime = endTp.DateTime;
            this.FileName = firstTp.FileName;
            this.Longitude = midTp.Longitude;
            this.Latitude = midTp.Latitude;

            speedKpi = new DoubleKpiGroup();
            rsrpKpi = new DoubleKpiGroup();
            sinrKpi = new DoubleKpiGroup();
            rsrpKpi_NCell = new DoubleKpiGroup();
            rsrpKpi_LTE = new DoubleKpiGroup();
            sinrKpi_LTE = new DoubleKpiGroup();
            List<string> arfcnList = new List<string>();
            List<string> cellNameList_NR = new List<string>();
            List<string> cellNameList_LTE = new List<string>();
            List<string> roadList = new List<string>();
            List<string> gridList = new List<string>();
            List<string> areaList = new List<string>();

            GISManager gis = GISManager.GetInstance();
            foreach (TestPoint tp in testPoints)
            {
                setRoadAppType(tp);
                statSpeedInfo(getSpeedFunc, tp);
                statCoverInfo(tp);
                statCellInfo(arfcnList, cellNameList_NR, cellNameList_LTE, tp);

                addStr(roadList, gis.GetRoadPlaceDesc(tp.Longitude, tp.Latitude));
                addStr(gridList, gis.GetGridDesc(tp.Longitude, tp.Latitude));
                addStr(areaList, gis.GetAreaPlaceDesc(tp.Longitude, tp.Latitude));
            }

            Arfcns = getStrDesc(arfcnList);
            CellNames_NR = getStrDesc(cellNameList_NR);
            CellNames_LTE = getStrDesc(cellNameList_LTE);
            RoadDesc = getStrDesc(roadList);
            GridName = getStrDesc(gridList);
            AreaName = getStrDesc(areaList);
        }
        private void statCellInfo(List<string> arfcnList, List<string> cellNameList_NR
            , List<string> cellNameList_LTE, TestPoint tp)
        {
            int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            if (arfcn != null)
            {
                addStr(arfcnList, arfcn.ToString());
            }

            NRCell nrCell = tp.GetMainCell_NR();
            if (nrCell != null)
            {
                addStr(cellNameList_NR, nrCell.Name);
            }

            LTECell lteCell = tp.GetMainCell_LTE();
            if (lteCell != null)
            {
                addStr(cellNameList_LTE, lteCell.Name);
            }
        }
        private void statSpeedInfo(DelGetSpeed getSpeedFunc, TestPoint tp)
        {
            double? speed = getSpeedFunc(tp);
            if (speed != null)
            {
                speedKpi.AddSinglePointKpi((double)speed);

                if (speed == 0)
                {
                    TpCount_0Speed++;
                }
            }
        }
        private void statCoverInfo(TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            if (rsrp != null && rsrp > -141)
            {
                rsrpKpi.AddSinglePointKpi((float)rsrp);
            }

            for (int i = 0; i < 16; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
                if (type == NRTpHelper.NRNCellType.NCELL)
                {
                    float? nRsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                    if (nRsrp != null && nRsrp >= -141)
                    {
                        rsrpKpi_NCell.AddSinglePointKpi((float)nRsrp);
                    }
                }
            }

            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            if (sinr != null && sinr > -20)
            {
                sinrKpi.AddSinglePointKpi((float)sinr);
            }

            float? rsrp_LTE = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
            if (rsrp_LTE != null && rsrp_LTE > -141)
            {
                rsrpKpi_LTE.AddSinglePointKpi((float)rsrp_LTE);
            }

            float? sinr_LTE = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
            if (sinr_LTE != null && sinr_LTE > -50)
            {
                sinrKpi_LTE.AddSinglePointKpi((float)sinr_LTE);
            }
        }
        private void setRoadAppType(TestPoint tp)
        {
            if (string.IsNullOrEmpty(RoadAppType))
            {
                int? appType = NRTpHelper.NrTpManager.GetAppType(tp);
                if (appType != null && appType != (int)AppType.NR_RACH)
                {
                    RoadAppType = ((AppType)appType).ToString();
                }
            }
        }

        private void computeModulation(List<TestPoint> testPoints)
        {
            int sum_16QAM_Count_DL = 0;
            int sum_64QAM_Count_DL = 0;
            int sum_256QAM_Count_DL = 0;
            int sum_BPSK_Count_DL = 0;
            int sum_QPSK_Count_DL = 0;
            float sum_PDSCH_BLER = 0;
            float sum_PUSCH_BLER = 0;
            int sum_CQI = 0;
            foreach (TestPoint tp in testPoints)
            {
                addIntValueToSum(tp, "NR_16QAM_Count_DL", ref sum_16QAM_Count_DL, 0);
                addIntValueToSum(tp, "NR_64QAM_Count_DL", ref sum_64QAM_Count_DL, 0);
                addIntValueToSum(tp, "NR_256QAM_Count_DL", ref sum_256QAM_Count_DL, 0);
                addIntValueToSum(tp, "NR_BPSK_Count_DL", ref sum_BPSK_Count_DL, 0);
                addIntValueToSum(tp, "NR_QPSK_Count_DL", ref sum_QPSK_Count_DL, 0);

                addFloatValueToSum(tp, "NR_PDSCH_BLER", ref sum_PDSCH_BLER, 0);
                addFloatValueToSum(tp, "NR_PUSCH_BLER", ref sum_PUSCH_BLER, 0);
                addIntValueToSum(tp, "NR_Avg_CQI", ref sum_CQI, 0);
            }

            int sumDlCount = sum_16QAM_Count_DL + sum_64QAM_Count_DL + sum_256QAM_Count_DL + sum_BPSK_Count_DL + sum_QPSK_Count_DL;

            Rate_16QAM_DL = Math.Round(100.0 * sum_16QAM_Count_DL / sumDlCount, 2);
            Rate_64QAM_DL = Math.Round(100.0 * sum_64QAM_Count_DL / sumDlCount, 2);
            Rate_256QAM_DL = Math.Round(100.0 * sum_256QAM_Count_DL / sumDlCount, 2);
            Rate_BPSK_DL = Math.Round(100.0 * sum_BPSK_Count_DL / sumDlCount, 2);
            Rate_QPSK_DL = Math.Round(100.0 * sum_QPSK_Count_DL / sumDlCount, 2);

            PDSCH_BLER = Math.Round(sum_PDSCH_BLER / testPoints.Count, 2);
            PUSCH_BLER = Math.Round(sum_PUSCH_BLER / testPoints.Count, 2);
            CqiAvg = Math.Round((double)sum_CQI / testPoints.Count, 2);
        }
        private void addIntValueToSum(TestPoint tp, string paramName, ref int sumValue
            , int minValue)
        {
            int? curValue = (int?)tp[paramName];
            if (curValue != null && curValue >= minValue)
            {
                sumValue += (int)curValue;
            }
        }
        private void addFloatValueToSum(TestPoint tp, string paramName, ref float sumValue
            , float minValue)
        {
            float? curValue = (float?)tp[paramName];
            if (curValue != null && curValue >= minValue)
            {
                sumValue += (float)curValue;
            }
        }
        private void addStr(List<string> strList, string str)
        {
            if (!strList.Contains(str))
            {
                strList.Add(str);
            }
        }
        private string getStrDesc(List<string> strList)
        {
            char charSplit = '；';
            StringBuilder strb = new StringBuilder();
            foreach (string str in strList)
            {
                strb.Append(str + charSplit);
            }

            return strb.ToString().TrimEnd(charSplit);
        }
    }
}
