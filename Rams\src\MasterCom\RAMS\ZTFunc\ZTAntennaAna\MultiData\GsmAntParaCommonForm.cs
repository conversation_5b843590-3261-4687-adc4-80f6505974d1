﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GsmAntParaCommonForm : MinCloseForm
    {
        public GsmAntParaCommonForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            initAngleArgs();
            this.mapForm = MainModel.MainForm.GetMapForm();
        }
        MapForm mapForm;
        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        Dictionary<LaiKey, GsmAntParaItem> cellAntParaInfoDic = null;
        GsmAntParaItem gsmAntParaItem = null;
        public void FillData(Dictionary<LaiKey, GsmAntParaItem> cellAntParaInfoDic)
        {          
            try
            {
                this.cellAntParaInfoDic = cellAntParaInfoDic;
                labNum.Text = cellAntParaInfoDic.Count.ToString();
                int iPage = cellAntParaInfoDic.Count % 200 > 0 ? cellAntParaInfoDic.Count / 200 + 1 : cellAntParaInfoDic.Count / 200;
                labPage.Text = iPage.ToString();

                dataGridViewCell.Columns.Clear();
                dataGridViewCell.Rows.Clear();//小区级

                int rowCellAt = 0;
                foreach (NPOIRow data in nrDatasList[0])
                {
                    if (rowCellAt == 0)
                    {
                        intDataViewColumn(dataGridViewCell, data.cellValues);
                        rowCellAt++;
                        continue;
                    }
                    if (rowCellAt > 200)
                        break;
                    initDataRow(dataGridViewCell, data);
                    rowCellAt++;
                }

            }
            catch
            {
          	    //continue
            }
        }

        private void intDataViewColumn(DataGridView dataGridView, List<object> objs)
        {
            dataGridView.Columns.Clear();
            int idx = 1;
            foreach (object obj in objs)
            {
                dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        private void initDataRow(DataGridView datatGridView, NPOIRow nop)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = nop.cellValues[4] + "_" + nop.cellValues[5];//小区LAC_小区CI
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow data in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == data.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && data.cellValues[3].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;
                initDataRow(dataGridViewCell, data);
                rowCellAt++;
            }
        }

        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow data in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == data.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;
                initDataRow(dataGridViewCell, data);
                rowCellAt++;
            }
        }

        public void initAngleArgs()
        {
            for (int i = -179; i < 181; i++)
            {
                DataGridViewTextBoxColumn angelCol = new DataGridViewTextBoxColumn();
                angelCol.HeaderText = i + "°";
                angelCol.Width = 63;
                dataGridViewAngle.Columns.Add(angelCol);
            }

            cbbxSeries1.Items.Add("Rxlev_Scan");
            cbbxSeries1.Items.Add("平滑Rxlev_Scan");
            cbbxSeries1.Items.Add("C/I_Scan");
            cbbxSeries1.Items.Add("通信距离_Scan");       
            cbbxSeries1.SelectedIndex = 0;

            cbbxSeries2.Items.Add("Rxlev_Dt");
            cbbxSeries2.Items.Add("平滑Rxlev_Dt");
            cbbxSeries2.Items.Add("Rxqual(DL)_Dt");
            cbbxSeries2.Items.Add("过覆盖指数_Dt");
            cbbxSeries2.Items.Add("C/I_Dt");
            cbbxSeries2.Items.Add("通信距离_Dt");           
            cbbxSeries2.SelectedIndex = 0;

            Series series = chartControl1.Series[0];
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical; //设置x轴有固定间距
            SeriesPoint pt;
            pt = new SeriesPoint(-150, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(0, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(150, 50);
            series.Points.Add(pt);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
        }

        /// <summary>
        /// 显示天线辐射波形重建
        /// </summary>
        private void miShwoChart_Click(object sender, EventArgs e)
        {
            if (dataGridViewCell.SelectedRows.Count == 0)
            {
                return;
            }
            string cellLac_Ci = dataGridViewCell.SelectedRows[0].Tag as string;
            LaiKey cellKey = new LaiKey(Convert.ToInt32(cellLac_Ci.Split('_')[0]),Convert.ToInt32(cellLac_Ci.Split('_')[1]));
            if (!this.cellAntParaInfoDic.TryGetValue(cellKey, out gsmAntParaItem))
            {
                return;
            }
            if (gsmAntParaItem.iFunc == 2)
            {
                getCellInfoByLacCi(gsmAntParaItem);
            }
            setAngelTable(gsmAntParaItem);
            cbbxSeries1_SelectedIndexChanged(null, null);
            cbbxSeries2_SelectedIndexChanged(null, null);
            drawAntRadarSeries(gsmAntParaItem);
            xTabGSMAnt.SelectedTabPage = pageAngle;
        }

        public void getCellInfoByLacCi(GsmAntParaItem gsmAntParaItem)
        {
            LaiKey cellKey = new LaiKey(gsmAntParaItem.小区LAC, gsmAntParaItem.小区CI);
            DIYQueryGSMAntAngle gsmAntQuery = new DIYQueryGSMAntAngle(MainModel);
            gsmAntQuery.SetCondition(gsmAntParaItem.timeCfg.ISitme, gsmAntParaItem.timeCfg.IEitme, cellKey);
            gsmAntQuery.Query();
            gsmAntParaItem.dtCellInfoItem = new ZTGsmAntenna.CellInfoItem();
            gsmAntParaItem.scanCellInfoItem = new ZTGsmAntenna.CellInfoItem();
            if (gsmAntQuery.dtCellInfoDic.ContainsKey(cellKey))
            {
                gsmAntParaItem.dtCellInfoItem = gsmAntQuery.dtCellInfoDic[cellKey];
            }
            if (gsmAntQuery.scanCellInfoDic.ContainsKey(cellKey))
            {
                gsmAntParaItem.scanCellInfoItem = gsmAntQuery.scanCellInfoDic[cellKey];
            }
        }

        public void setAngelTable(GsmAntParaItem gsmAntParaItem)
        {
            dataGridViewAngle.Rows.Clear();
            int rowAt = 0;
            dataGridViewAngle.Rows.Add(1);
            int colAt = 0;

            ZTGsmAntenna.CellInfoItem scanCellInfoItem = gsmAntParaItem.scanCellInfoItem;
            if (scanCellInfoItem == null)
            {
                scanCellInfoItem = new ZTGsmAntenna.CellInfoItem();
                gsmAntParaItem.scanCellInfoItem = new ZTGsmAntenna.CellInfoItem();
            }
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = gsmAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "Rxlev_Scan";
            for (int i = 181; i < 360; i++) //181~359度（按正负算-179~-1）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (scanCellInfoItem.samplNumArray[i] == 0 ? -140 : scanCellInfoItem.relArray[i] / scanCellInfoItem.samplNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (scanCellInfoItem.samplNumArray[i] == 0 ? -140 : scanCellInfoItem.relArray[i] / scanCellInfoItem.samplNumArray[i]).ToString();
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = gsmAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "平滑Rxlev_Scan";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(scanCellInfoItem.rxlevNewArray[i], 2);
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(scanCellInfoItem.rxlevNewArray[i], 2);
            }

            addRowData(gsmAntParaItem, ref rowAt, scanCellInfoItem, "C/I_Scan", scanCellInfoItem.c2iArray, -25);
            addRowData(gsmAntParaItem, ref rowAt, scanCellInfoItem, "通信距离_Scan", scanCellInfoItem.samplArray, 0);

            //路测角度信息
            ZTGsmAntenna.CellInfoItem dtCellInfoItem = gsmAntParaItem.dtCellInfoItem;
            if (dtCellInfoItem == null)
            {
                dtCellInfoItem = new ZTGsmAntenna.CellInfoItem();
                gsmAntParaItem.dtCellInfoItem = new ZTGsmAntenna.CellInfoItem();
            }
            addRowData(gsmAntParaItem, ref rowAt, dtCellInfoItem, "Rxlev_Dt", dtCellInfoItem.relArray, -140);

            dataGridViewAngle.Rows.Add(1);
            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = gsmAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "平滑Rxlev_Dt";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(dtCellInfoItem.rxlevNewArray[i], 2);
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(dtCellInfoItem.rxlevNewArray[i], 2);
            }

            addRowData(gsmAntParaItem, ref rowAt, dtCellInfoItem, "Rxqual(DL)_Dt", dtCellInfoItem.rxqDlArray, -25);
            addRowData(gsmAntParaItem, ref rowAt, dtCellInfoItem, "过覆盖指数_Dt", dtCellInfoItem.coverArray, 0);
            addRowData(gsmAntParaItem, ref rowAt, dtCellInfoItem, "C/I_Dt", dtCellInfoItem.c2iArray, -25);
            addRowData(gsmAntParaItem, ref rowAt, dtCellInfoItem, "通信距离_Dt", dtCellInfoItem.samplArray, 0);
        }

        private void addRowData(GsmAntParaItem gsmAntParaItem, ref int rowAt, ZTGsmAntenna.CellInfoItem dtCellInfoItem, string desc, double[] array, int defaultValue)
        {
            int colAt;
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = gsmAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = desc;
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (Math.Round(dtCellInfoItem.samplNumArray[i] == 0 ? defaultValue : array[i] / dtCellInfoItem.samplNumArray[i], 2)).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (Math.Round(dtCellInfoItem.samplNumArray[i] == 0 ? defaultValue : array[i] / dtCellInfoItem.samplNumArray[i], 2)).ToString();
            }
        }

        private void addRowData(GsmAntParaItem gsmAntParaItem, ref int rowAt, ZTGsmAntenna.CellInfoItem dtCellInfoItem, string desc, int[] array, int defaultValue)
        {
            int colAt;
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = gsmAntParaItem.小区名称;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = desc;
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (dtCellInfoItem.samplNumArray[i] == 0 ? defaultValue : array[i] / dtCellInfoItem.samplNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value
                    = (dtCellInfoItem.samplNumArray[i] == 0 ? defaultValue : array[i] / dtCellInfoItem.samplNumArray[i]).ToString();
            }
        }

        private void cbbxSeries1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (gsmAntParaItem != null)
            {
                UpdateTableSeries();
            }
        }

        private void cbbxSeries2_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (gsmAntParaItem != null)
            {
                UpdateTableSeries();
            }
        }

        private void UpdateTableSeries()
        {
            chartControl1.Series.Clear();
            Cursor.Current = Cursors.WaitCursor;
            #region 图例1内容
            switch (this.cbbxSeries1.Text)
            {
                case "Rxlev_Scan":
                    {
                        DrawTable1Series(gsmAntParaItem.scanCellInfoItem.relArray);
                    }
                    break;
                case "平滑Rxlev_Scan":
                    {
                        DrawTable1Series(gsmAntParaItem.scanCellInfoItem.rxlevNewArray);
                    }
                    break;
                case "C/I_Scan":
                    {
                        DrawTable1Series(gsmAntParaItem.scanCellInfoItem.c2iArray);
                    }
                    break;
                case "通信距离_Scan":
                    {
                        DrawTable1Series(gsmAntParaItem.scanCellInfoItem.samplArray);
                    }
                    break;
                default:
                    break;
            }
            #endregion

            #region 图例2内容
            switch (this.cbbxSeries2.Text)
            {
                case "Rxlev_Dt":
                    {
                        drawTable2Series(gsmAntParaItem.dtCellInfoItem.relArray);
                    }
                    break;
                case "平滑Rxlev_Dt":
                    {
                        drawTable2Series(gsmAntParaItem.dtCellInfoItem.rxlevNewArray);
                    }
                    break;
                case "Rxqual(DL)_Dt":
                    {
                        drawTable2Series(gsmAntParaItem.dtCellInfoItem.rxqDlArray);
                    }
                    break;
                case "过覆盖指数_Dt":
                    {
                        drawTable2Series(gsmAntParaItem.dtCellInfoItem.coverArray);
                    }
                    break;
                case "C/I_Dt":
                    {
                        drawTable2Series(gsmAntParaItem.dtCellInfoItem.c2iArray);
                    }
                    break;
                case "通信距离_Dt":
                    {
                        drawTable2Series(gsmAntParaItem.dtCellInfoItem.samplArray);
                    }
                    break;
                default:
                    break;
            }
            #endregion
            checkReverse();
            Cursor.Current = Cursors.Default;
        }

        private void checkReverse()
        {
            if ((double)(((XYDiagram)chartControl1.Diagram).AxisY.Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = true;
            }

            if ((double)(((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = true;
            }
        }

        private void DrawTable1Series(int[] seriesValues)
        {
            int count = 0;
            SeriesPoint pt;
            chartControl1.Series.Clear();
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 181; i < 360; i++)
            {
                string arg = (-179 + count).ToString();
                double value = Math.Round(gsmAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 : 
                    seriesValues[i] * 1.0 / gsmAntParaItem.dtCellInfoItem.samplNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
                count++;
            }
            for (int i = 1; i < 181; i++)
            {
                string arg = i.ToString();
                double value = Math.Round(gsmAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 : 
                    seriesValues[i] * 1.0 / gsmAntParaItem.dtCellInfoItem.samplNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        private void DrawTable1Series(double[] seriesValues)
        {
            chartControl1.Series.Clear();
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            int count = 0;
            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(gsmAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] / gsmAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }
            for (int i = 1; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(gsmAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] / gsmAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        private void drawTable2Series(int[] seriesValues)
        {
            int count = 0;
            if (chartControl1.Series.Count > 1)
            {
                chartControl1.Series.RemoveAt(1);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(gsmAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] * 1.0 / gsmAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(gsmAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] * 1.0 / gsmAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        private void drawTable2Series(double[] seriesValues)
        {
            int count = 0;
            if (chartControl1.Series.Count > 1)
            {
                chartControl1.Series.RemoveAt(1);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(gsmAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] / gsmAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(gsmAntParaItem.dtCellInfoItem.samplNumArray[i] == 0 ? 0 :
                    seriesValues[i] / gsmAntParaItem.dtCellInfoItem.samplNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        private void drawAntRadarSeries(GsmAntParaItem gsmAntParaItem)
        {
            chartControl2.Series.Clear();
            int iNum = 0;

            #region 扫频测试数据
            if (gsmAntParaItem.scanCellInfoItem != null)
            {
                Series seriesScan = new Series();
                seriesScan.ShowInLegend = true;
                seriesScan.LegendText = "扫频Rxlev";
                seriesScan.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesScanView = new RadarLineSeriesView();
                lineSeriesScanView.Color = Color.Blue;
                lineSeriesScanView.LineMarkerOptions.Size = 2;

                seriesScan.View = lineSeriesScanView;
                seriesScan.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                seriesScan.Label.Visible = false;

                for (int i = 359; i >= 0; i--)
                {
                    seriesScan.Points.Add(new SeriesPoint(i.ToString(), Math.Round(gsmAntParaItem.scanCellInfoItem.rxlevNewArray[i], 2)));
                }
                chartControl2.Series.Insert(iNum++, seriesScan);
            }
            #endregion

            #region 路测测试数据

            if (gsmAntParaItem.dtCellInfoItem != null)
            {
                Series seriesDT = new Series();
                seriesDT.ShowInLegend = true;
                seriesDT.LegendText = "路测Rxlev";
                seriesDT.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesDTView = new RadarLineSeriesView();
                lineSeriesDTView.Color = Color.DarkViolet;
                lineSeriesDTView.LineMarkerOptions.Size = 2;

                seriesDT.View = lineSeriesDTView;
                seriesDT.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                seriesDT.Label.Visible = false;

                for (int i = 359; i >= 0; i--)
                {
                    seriesDT.Points.Add(new SeriesPoint(i.ToString(), Math.Round(gsmAntParaItem.dtCellInfoItem.rxlevNewArray[i], 2)));
                }
                chartControl2.Series.Insert(iNum, seriesDT);
            }
            #endregion

            int iMaxValue = -140;
            int iMinValue = 25;
            ZTAntFuncHelper.getMaxAndMinValue(gsmAntParaItem.dtCellInfoItem.rxlevNewArray, ref iMaxValue, ref iMinValue);
            ZTAntFuncHelper.getMaxAndMinValue(gsmAntParaItem.scanCellInfoItem.rxlevNewArray, ref iMaxValue, ref iMinValue);
            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MinValue = iMinValue;
            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MaxValue = iMaxValue;

            ((RadarDiagram)chartControl2.Diagram).AxisX.GridSpacing = 20;

            ((RadarDiagram)chartControl2.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControl2.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartControl2.Focus();
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = this.cellAntParaInfoDic.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = this.cellAntParaInfoDic.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }

        private void outPutCsv_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        /// <summary>
        /// GIS呈现
        /// </summary>
        private void miShowSimulation_Click(object sender, EventArgs e)
        {
            if (dataGridViewCell.SelectedRows.Count == 0)
            {
                return;
            }
            string cellLac_Ci = dataGridViewCell.SelectedRows[0].Tag as string;
            LaiKey cellKey = new LaiKey(Convert.ToInt32(cellLac_Ci.Split('_')[0]), Convert.ToInt32(cellLac_Ci.Split('_')[1]));
            if (!this.cellAntParaInfoDic.TryGetValue(cellKey, out gsmAntParaItem))
            {
                return;
            }
            ZTGsmAntenna.CellAngleData data = gsmAntParaItem.scanCellAngleData;
            if (gsmAntParaItem.天线经度 == 0 || gsmAntParaItem.天线纬度 == 0)
                return;
            if (gsmAntParaItem.iFunc == 2)
            {
                getCellInfoByLacCi(gsmAntParaItem);
            }
            if (gsmAntParaItem.scanCellInfoItem == null || gsmAntParaItem.dtCellInfoItem == null)
                return;
            CalcAntDetailValue();
            MainModel.SelectedCell = CellManager.GetInstance().GetCellByName(gsmAntParaItem.小区名称);
            MainModel.MainForm.GetMapForm().GoToView(gsmAntParaItem.天线经度, gsmAntParaItem.天线纬度);
            AntLineLayer antLayer = mapForm.GetLayerBase(typeof(AntLineLayer)) as AntLineLayer;
            if (antLayer != null)
            {
                antLayer.Invalidate();
                antLayer.dtLongLatList = data.longLatDTList;
                antLayer.scanLongLatList = gsmAntParaItem.dtCellAngleData.longLatDTList;
                antLayer.modelLongLatList = data.longLatModelList;
                antLayer.Invalidate();
            }
        }
        /// <summary>
        /// 计算权值
        /// </summary>
        private void CalcAntDetailValue()
        {
            ZTGsmAntenna.CellAngleData data = gsmAntParaItem.scanCellAngleData;
            AntParaItem antItem = new AntParaItem("D", 1, 1, 1, 1, "");
            antItem.Init(0, 90, 90, 0);

            double[] modelArray = antItem.getPowerArray();
            LongLat ll = new LongLat();
            ll.fLongitude = (float)(gsmAntParaItem.天线经度);
            ll.fLatitude = (float)(gsmAntParaItem.天线纬度);

            data.longLatModelList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, modelArray, 10, -20, gsmAntParaItem.方位角);
            int iMaxValue = -19;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(gsmAntParaItem.scanCellInfoItem.rxlevNewArray, ref iMaxValue, ref iMinValue);
            data.longLatDTList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, gsmAntParaItem.scanCellInfoItem.rxlevNewArray, iMaxValue, iMinValue, gsmAntParaItem.方位角);
            
            iMaxValue = -19;
            iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(gsmAntParaItem.dtCellInfoItem.rxlevNewArray, ref iMaxValue, ref iMinValue);
            gsmAntParaItem.dtCellAngleData.longLatDTList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, gsmAntParaItem.dtCellInfoItem.rxlevNewArray, iMaxValue, iMinValue, gsmAntParaItem.方位角);
       

        }
    }
}
