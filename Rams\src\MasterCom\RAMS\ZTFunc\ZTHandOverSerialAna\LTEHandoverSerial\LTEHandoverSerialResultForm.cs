﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEHandoverSerialResultForm : MinCloseForm
    {
        public LTEHandoverSerialResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
        }

        public void FillData(List<LTEHandoverSerialGroupView> viewList)
        {
            gridControl1.DataSource = viewList;
            gridControl1.RefreshDataSource();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                List<NPOIRow> rows = new List<NPOIRow>();
                NPOIRow row = new NPOIRow();
                foreach (GridColumn col in gridView1.Columns)
                {
                    row.AddCellValue(col.Caption);
                }
                foreach (GridColumn col in gridView2.Columns)
                {
                    row.AddCellValue(col.Caption);
                }
                rows.Add(row);
                for (int i = 0; i < gridView1.RowCount; i++)
                {
                    row = new NPOIRow();
                    rows.Add(row);
                    foreach (GridColumn col in gridView1.Columns)
                    {
                        row.AddCellValue(gridView1.GetRowCellDisplayText(i, col));
                    }
                    gridView1.ExpandMasterRow(i);
                    DevExpress.XtraGrid.Views.Grid.GridView view = gridView1.GetDetailView(i, 0) as DevExpress.XtraGrid.Views.Grid.GridView;
                    if (view != null)
                    {
                        for (int j = 0; j < view.RowCount; j++)
                        {
                            NPOIRow subRow = new NPOIRow();
                            row.AddSubRow(subRow);
                            foreach (GridColumn subCol in view.Columns)
                            {
                                subRow.AddCellValue(view.GetRowCellDisplayText(j, subCol));
                            }
                        }
                    }
                }
                ExcelNPOIManager.ExportToExcel(rows);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            } 
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object selectRow = gv.GetRow(gv.GetSelectedRows()[0]);

            if (selectRow is LTEHandoverSerialGroupView)
            {
                LTEHandoverSerialGroupView grpView = selectRow as LTEHandoverSerialGroupView;
                FillHandoverSeqByGroupView(grpView);
                MainModel.MainForm.GetMapForm().GoToView(grpView.Evt.Longitude, grpView.Evt.Latitude);
            }
            else if (selectRow is LTEhandoverSerialEventView)
            {
                LTEhandoverSerialEventView evtView = selectRow as LTEhandoverSerialEventView;
                FillHandoverSeqByGroupView(evtView.Parent);

                MainModel.ClearSelectedEvents();
                MainModel.SelectedEvents.Clear();
                evtView.Evt.Selected = true;
                MainModel.SelectedEvents.Add(evtView.Evt);
                MainModel.FireSelectedEventsChanged(MainModel.MainForm);

                MainModel.MainForm.GetMapForm().GoToView(evtView.Longitude, evtView.Latitude);
            }
        }

        private void FillHandoverSeqByGroupView(LTEHandoverSerialGroupView grpView)
        {
            MainModel.HandOverSeqEvents.Clear();
            MainModel.DrawHandoverSeq = false;

            if (grpView == null)
            {
                return;
            }

            LTEHandoverSerialEventPicker evtPicker = new LTEHandoverSerialEventPicker();
            foreach (LTEhandoverSerialEventView evtView in grpView.EventViews)
            {
                if (evtPicker.IsHandoverSuccess(evtView.Evt))
                {
                    MainModel.HandOverSeqEvents.Add(evtView.Evt);
                }
            }
            MainModel.DrawHandoverSeq = true;
        }
    }
}
