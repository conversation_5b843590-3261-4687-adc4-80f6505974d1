﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakSinrRoadGridCompare : DIYAnalyseByFileBackgroundBase
    {
        public NRWeakSinrRoadGridCompare()
            : base(MainModel.GetInstance())
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "NR质差路段对比"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35042, this.Name);
        }

        NRWeakSinrRoadGridCondtion curCondtion;
        protected override bool getCondition()
        {
            NRWeakSinrRoadGridDlg setForm = new NRWeakSinrRoadGridDlg();
            setForm.SetCondition(curCondtion);
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            curCondtion = setForm.GetCondition();
            return true;
        }

        protected override void getReadyBeforeQuery()
        {
            allSegGrids = new List<NRWeakSinrRoadGrid>();
            p1Grids = new List<NRWeakSinrRoadGrid>();
            p2Grids = new List<NRWeakSinrRoadGrid>();
            repeatGrids = new List<NRWeakSinrRoadGrid>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                FileInfo fi = file.GetFileInfo();
                NRWeakSinrRoadGrid weakSegGrid = null;
                TestPoint prePoint = null;
                for (int i = 0; i < file.TestPoints.Count; i++)
                {
                    TestPoint testPoint = file.TestPoints[i];
                    if (isValidTestPoint(testPoint))
                    {
                        weakSegGrid = addWeakSinrRoadGrid(fi, weakSegGrid, prePoint, testPoint);
                        prePoint = testPoint;
                    }
                    else
                    {
                        saveWeakRoadSeg(ref weakSegGrid);
                    }
                }
                saveWeakRoadSeg(ref weakSegGrid);
            }
        }

        private NRWeakSinrRoadGrid addWeakSinrRoadGrid(FileInfo fi, NRWeakSinrRoadGrid weakSegGrid, TestPoint prePoint, TestPoint testPoint)
        {
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(testPoint);
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(testPoint);
            if (!curCondtion.IsValidate(sinr))
            {
                saveWeakRoadSeg(ref weakSegGrid);
            }
            else
            {
                if (weakSegGrid == null) //弱覆盖开始
                {
                    weakSegGrid = new NRWeakSinrRoadGrid(fi);
                    weakSegGrid.Add((float)sinr, rsrp, 0, testPoint);
                }
                else
                { //上一点为弱覆盖点
                    double dis = prePoint.Distance2(testPoint.Longitude, testPoint.Latitude);
                    if (curCondtion.Match2TestpointsMaxDistance(dis))
                    { //符合两采样点之间的距离门限
                        weakSegGrid.Add((float)sinr, rsrp, dis, testPoint);
                    }
                    else
                    { //两采样点距离不符合，该点开始新的弱覆盖
                        saveWeakRoadSeg(ref weakSegGrid);
                        weakSegGrid = new NRWeakSinrRoadGrid(fi);
                        weakSegGrid.Add((float)sinr, rsrp, 0, testPoint);
                    }
                }
            }

            return weakSegGrid;
        }

        protected List<NRWeakSinrRoadGrid> allSegGrids = null;

        protected virtual void saveWeakRoadSeg(ref NRWeakSinrRoadGrid segGrid)
        {
            if (segGrid == null || !curCondtion.MatchMinWeakCoverDistance(segGrid.Distance))
            {
                segGrid = null;
                return;
            }
            if (!allSegGrids.Contains(segGrid))
            {
                allSegGrids.Add(segGrid);
            }
            segGrid = null;
        }

        private List<NRWeakSinrRoadGrid> p1Grids = null;
        private List<NRWeakSinrRoadGrid> p2Grids = null;
        private List<NRWeakSinrRoadGrid> repeatGrids = null;
        protected override void getResultsAfterQuery()
        {
            NRWeakSinrRoadGridCondtion compareCondition = curCondtion;
            foreach (NRWeakSinrRoadGrid grid in allSegGrids)
            {
                if (grid.FileInfo.EndTime >= compareCondition.Period1.IBeginTime
                   && grid.FileInfo.BeginTime <= compareCondition.Period1.IEndTime)
                { //时间段1
                    grid.MakeGrid(compareCondition.GridSpanDegree);
                    p1Grids.Add(grid);
                    grid.SN = p1Grids.Count;
                }
                else if (grid.FileInfo.EndTime >= compareCondition.Period2.IBeginTime
                        && grid.FileInfo.BeginTime <= compareCondition.Period2.IEndTime)
                { //时间段2
                    grid.MakeGrid(compareCondition.GridSpanDegree);
                    p2Grids.Add(grid);
                    grid.SN = p2Grids.Count;
                }
            }

            dealIntersect();

            dealRes(p1Grids);
            dealRes(p2Grids);
            dealRes(repeatGrids);
        }

        private void dealRes(List<NRWeakSinrRoadGrid> grids)
        {
            foreach (NRWeakSinrRoadGrid grid in grids)
            {
                grid.Calculate();
            }
        }

        private void dealIntersect()
        {
            foreach (NRWeakSinrRoadGrid grid2 in p2Grids)
            {
                foreach (NRWeakSinrRoadGrid grid1 in p1Grids)
                {
                    if (grid1.Intersect(grid2))
                    {
                        grid2.AddIntersectSeg(grid1);
                    }
                }
                if (grid2.IntersectSegNum > 0)
                {
                    repeatGrids.Add(grid2);
                }
            }
        }

        protected override void fireShowForm()
        {
            NRWeakSinrRoadGridForm frm = MainModel.GetObjectFromBlackboard(typeof(NRWeakSinrRoadGridForm)) as NRWeakSinrRoadGridForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new NRWeakSinrRoadGridForm();
            }
            frm.Owner = MainModel.MainForm;
            frm.FillData(p1Grids, p2Grids, repeatGrids);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class NRWeakSinrRoadGridCompareByFile : NRWeakSinrRoadGridCompare
    {
        private static NRWeakSinrRoadGridCompareByFile instance = null;
        public static NRWeakSinrRoadGridCompareByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new NRWeakSinrRoadGridCompareByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR质差路段对比(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
