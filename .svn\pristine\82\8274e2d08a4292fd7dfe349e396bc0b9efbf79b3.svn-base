﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class ReasonMod3 : ReasonBase
    {
        public ReasonMod3()
        {
            this.Name = "模3干扰";
        }
        
        public float RSRPDiffMax { get; set; } = 6;

        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            /*
             取工参的方式
             */
            //LTECell mainCell = tp.GetMainCell_LTE();
            //if (mainCell==null)
            //{
            //    return false;
            //}
            //for (int i = 0; i < 10; i++)
            //{
            //    LTECell nCell = tp.GetNBCell_LTE(i);
            //    if (nCell != null && mainCell.PCI % 3 == nCell.PCI % 3)
            //    {//模3干扰：如果主服务小区与邻区PCI模3值相同，即PCI/3余数相同
            //        return true;
            //    }
            //}
            //return false;

            int? lteEarfcn = (int?)GetEARFCN(tp);
            short? ltePci = (short?)GetPCI(tp);
            if (lteEarfcn == null || ltePci == null)
            {
                return false;
            }

            for (int i = 0; i < 10; i++)
            {
                int? lteNCellEarfcn = (int?)GetNEARFCN(tp, i);
                if (lteNCellEarfcn != null && lteEarfcn == lteNCellEarfcn)//同频时才进行判断
                {
                    bool isValid = judgeValid(tp, ltePci, i);
                    if (isValid)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool judgeValid(TestPoint tp, short? ltePci, int i)
        {
            short? lteNCellPci = (short?)GetNPCI(tp, i);
            if (lteNCellPci != null && ltePci % 3 == lteNCellPci % 3)
            {
                float? rsrp = (float?)GetRSRP(tp);
                float? nRsrp = (float?)GetNRSRP(tp, i);
                if (rsrp != null && nRsrp != null)
                {
                    float diff = Math.Abs((float)(rsrp - nRsrp));
                    if (diff <= 6)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        protected object GetEARFCN(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_EARFCN"];
            }
            else if (tp is Model.ScanTestPoint_NBIOT)
            {
                return tp["LTESCAN_TopN_EARFCN"];
            }
            return tp["lte_EARFCN"];
        }
        protected object GetPCI(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_PCI"];
            }
            else if (tp is Model.ScanTestPoint_NBIOT)
            {
                return tp["LTESCAN_TopN_PCI"];
            }
            return tp["lte_PCI"];
        }
        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            else if (tp is Model.ScanTestPoint_NBIOT)
            {
                return tp["LTESCAN_TopN_CELL_Specific_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNEARFCN(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_EARFCN", index];
            }
            else if (tp is Model.ScanTestPoint_NBIOT && index > 0)
            {
                return tp["LTESCAN_TopN_EARFCN", index];
            }
            return tp["lte_NCell_EARFCN", index];
        }
        protected object GetNPCI(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_PCI", index];
            }
            else if (tp is Model.ScanTestPoint_NBIOT && index > 0)
            {
                return tp["LTESCAN_TopN_PCI", index];
            }
            return tp["lte_NCell_PCI", index];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            else if (tp is Model.ScanTestPoint_NBIOT && index > 0)
            {
                return tp["LTESCAN_TopN_CELL_Specific_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["RSRPDiffMax"] = this.RSRPDiffMax;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("RSRPDiffMax"))
                {
                    this.RSRPDiffMax = (float)param["RSRPDiffMax"];
                }
            }
        }
    }
}
