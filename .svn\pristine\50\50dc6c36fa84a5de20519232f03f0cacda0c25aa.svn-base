﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellWrongDirQuery_LteUep : ZTCellWrongDirQuery_LTE
   {
        private static ZTCellWrongDirQuery_LteUep instance = null;
        public static  new ZTCellWrongDirQuery_LteUep GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellWrongDirQuery_LteUep();
                    }
                }
            }
            return instance;
        }

        private ZTCellWrongDirQuery_LteUep()
            : base()
        {
            init();
        }

        protected new void init()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "覆盖方向异常_LTE_UEP"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 24000, 24003, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_uep_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_uep_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_uep_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_uep_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_uep_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_UEP_RSRP");
            tmpDic.Add("themeName", (object)"LTE_UEP_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_uep_RSRP"];
        }
     
        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE感知; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RSCPMin"] = cellWrongCond.RxLevMin;
                param["DisMin"] = cellWrongCond.DistanceMin;
                param["AngleMin"] = cellWrongCond.AngleMin;
                param["WrongPerMin"] = cellWrongCond.WrongRateMin;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("RSCPMin"))
                {
                    cellWrongCond.RxLevMin = float.Parse(param["RSCPMin"].ToString());
                }
                if (param.ContainsKey("DisMin"))
                {
                    cellWrongCond.DistanceMin = double.Parse(param["DisMin"].ToString());
                }
                if (param.ContainsKey("AngleMin"))
                {
                    cellWrongCond.AngleMin = int.Parse(param["AngleMin"].ToString());
                }
                if (param.ContainsKey("WrongPerMin"))
                {
                    cellWrongCond.WrongRateMin = double.Parse(param["WrongPerMin"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return null;
            }
        }

        protected override void saveBackgroundData()
        {
            //
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int goodSampleCount = bgResult.GetImageValueInt();
                float wrongPercent = bgResult.GetImageValueFloat();
                StringBuilder sb = new StringBuilder();
                sb.Append("正常采样点数：");
                sb.Append(goodSampleCount);
                sb.Append("\r\n");
                sb.Append("异常采样点占比：");
                sb.Append(wrongPercent);
                sb.Append("%");
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }
}
