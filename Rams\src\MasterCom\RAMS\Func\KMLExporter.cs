﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using System.IO;
using System.Drawing;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    /************************************************************************/
    /* A help class for create&export to kml xml files                      */
    /************************************************************************/
    public class KMLExporter
    {
        private readonly XmlDocument doc = new XmlDocument();
        /// <summary>
        /// 重置Document导出对象，并得到带各个层导出的父节点,在每次执行前只调用一次
        /// </summary>
        /// <returns></returns>
        public XmlElement InitAndGetParentElementNode()
        {
            doc.RemoveAll();
            doc.AppendChild(doc.CreateXmlDeclaration("1.0", "utf-8", null));
            XmlElement kmlElem = doc.CreateElement("kml");
            doc.AppendChild(kmlElem);
            kmlElem.SetAttribute("xmlns", @"http://earth.google.com/kml/2.0");
            XmlElement docElem = doc.CreateElement("Document");
            kmlElem.AppendChild(docElem);
            XmlElement nameElem = doc.CreateElement("name");
            //nameElem.Value = "";
            nameElem.AppendChild(doc.CreateTextNode("dtdata"));
            docElem.AppendChild(nameElem);
            XmlElement folder = doc.CreateElement("Folder");
            docElem.AppendChild(folder);
            XmlElement open = doc.CreateElement("open");
            open.AppendChild(doc.CreateTextNode("0"));
            folder.AppendChild(open);
            XmlElement parentNameElem = doc.CreateElement("name");
            parentNameElem.AppendChild(doc.CreateTextNode("layers"));
            folder.AppendChild(parentNameElem);
            return folder;
        }

        public XmlElement InitAndGetParentElementNodeParamExport()
        {
            doc.RemoveAll();
            doc.AppendChild(doc.CreateXmlDeclaration("1.0", "utf-8", null));
            XmlElement kmlElem = doc.CreateElement("kml");
            doc.AppendChild(kmlElem);
            kmlElem.SetAttribute("xmlns", @"http://earth.google.com/kml/2.1");
            XmlElement docElem = doc.CreateElement("Document");
            kmlElem.AppendChild(docElem);
            XmlElement nameElem = doc.CreateElement("name");
            nameElem.AppendChild(doc.CreateTextNode("基站"));
            docElem.AppendChild(nameElem);
            return docElem;
        }
        public void MakeLookAt(XmlElement parentNode, double longitude, double latitude)
        {
            XmlElement lookAt = doc.CreateElement("LookAt");
            XmlElement longi = doc.CreateElement("longitude");
            longi.AppendChild(doc.CreateTextNode(longitude.ToString()));
            lookAt.AppendChild(longi);
            XmlElement lati = doc.CreateElement("latitude");
            lati.AppendChild(doc.CreateTextNode(latitude.ToString()));
            lookAt.AppendChild(lati);
            XmlElement range = doc.CreateElement("range");
            range.AppendChild(doc.CreateTextNode("1500"));
            lookAt.AppendChild(range);
            XmlElement tilt = doc.CreateElement("tilt");
            tilt.AppendChild(doc.CreateTextNode("50"));
            lookAt.AppendChild(tilt);
            XmlElement heading = doc.CreateElement("heading");
            heading.AppendChild(doc.CreateTextNode("0"));
            lookAt.AppendChild(heading);
            parentNode.AppendChild(lookAt);
        }
        public void MakeOverlayLogo(XmlElement parentNode, string logoname)
        {
            XmlElement overlay = doc.CreateElement("ScreenOverlay");
            XmlElement name = doc.CreateElement("name");
            name.AppendChild(doc.CreateTextNode("Title"));
            overlay.AppendChild(name);
            XmlElement icon = doc.CreateElement("Icon");
            XmlElement href = doc.CreateElement("href");
            href.AppendChild(doc.CreateTextNode(logoname));//"title.png"
            icon.AppendChild(href);
            XmlElement vs = doc.CreateElement("viewBoundScale");
            vs.AppendChild(doc.CreateTextNode("0.75"));
            icon.AppendChild(vs);
            overlay.AppendChild(icon);
            XmlElement oxy = doc.CreateElement("overlayXY");
            oxy.SetAttribute("x", "0.02");
            oxy.SetAttribute("y", "0.97");
            oxy.SetAttribute("xunits", "fraction");
            oxy.SetAttribute("yunits", "fraction");
            overlay.AppendChild(oxy);

            XmlElement sxy = doc.CreateElement("screenXY");
            sxy.SetAttribute("x", "0.02");
            sxy.SetAttribute("y", "0.97");
            sxy.SetAttribute("xunits", "fraction");
            sxy.SetAttribute("yunits", "fraction");
            overlay.AppendChild(sxy);

            parentNode.AppendChild(overlay);
        }
        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="filename">保存路径文件名</param>
        /// <returns>
        /// true success
        /// false failed
        /// </returns>
        public bool Save(string filename)
        {
            try
            {
                new FileInfo(filename).Directory.Create();
                doc.Save(filename);
            }
            catch
            {
                return false;
            }
            return true;
        }
        public XmlElement CreateGroundOverlay(string showname, string filename, int Alpha, double ltLong, double ltLat, double brLong, double brLat)
        {
            XmlElement groundOverlayElem = doc.CreateElement("GroundOverlay");
            groundOverlayElem.AppendChild(createSimpleNode("name", showname));
            groundOverlayElem.AppendChild(createSimpleNode("color", string.Format("{0:X}ffffff", Alpha)));
            XmlElement iconElem = doc.CreateElement("Icon");
            iconElem.AppendChild(createSimpleNode("href", filename));
            iconElem.AppendChild(createSimpleNode("viewBoundScale", "0.75"));
            groundOverlayElem.AppendChild(iconElem);
            XmlElement latLongElem = doc.CreateElement("LatLonBox");
            latLongElem.AppendChild(createSimpleNode("north", ltLat.ToString()));
            latLongElem.AppendChild(createSimpleNode("south", brLat.ToString()));
            latLongElem.AppendChild(createSimpleNode("east", brLong.ToString()));
            latLongElem.AppendChild(createSimpleNode("west", ltLong.ToString()));
            groundOverlayElem.AppendChild(latLongElem);
            return groundOverlayElem;
        }

        /// <summary>
        /// 生成KML标准格式的目录
        /// </summary>
        /// <param name="folderName">目录名称</param>
        /// <param name="open">该目录节点是否在载入时默认打开</param>
        /// <returns></returns>
        public XmlElement CreateFolder(string folderName, bool open)
        {
            XmlElement folder = doc.CreateElement("Folder");
            XmlElement nameNode = doc.CreateElement("name");
            nameNode.AppendChild(doc.CreateTextNode(folderName));
            folder.AppendChild(nameNode);
            XmlElement openNode = doc.CreateElement("open");
            openNode.AppendChild(doc.CreateTextNode(open ? "1" : "0"));
            folder.AppendChild(openNode);
            return folder;
        }
        private XmlElement createSimpleNode(string name, string v)
        {
            XmlElement elem = doc.CreateElement(name);
            elem.AppendChild(doc.CreateTextNode(v));
            return elem;
        }

        /**
        private XmlElement createColorStyleNode(Color clr)
        {
            XmlElement style = doc.CreateElement("Style");
            XmlElement polystyle = doc.CreateElement("PolyStyle");

            XmlElement color = doc.CreateElement("color");
            color.AppendChild(doc.CreateTextNode(makeColorABGRDes(clr)));
            polystyle.AppendChild(color);

            XmlElement outline = doc.CreateElement("outline");
            outline.AppendChild(doc.CreateTextNode("0"));
            polystyle.AppendChild(outline);
            style.AppendChild(polystyle);
            return style;
        }
        */

        private String makeColorABGRDes(Color color)
        {
            return String.Format("{0:X2}{1:X2}{2:X2}{3:X2}", (int)color.A, (int)color.B, (int)color.G, (int)color.R);

        }


        public void AddTestPoint(XmlElement serialInfoElement, Color color, DbPoint dPoint, DateTime time, string description, string nameDesc = "采样点")
        {
            List<DbPoint> dPoints = getCircleDPoint(dPoint, 0.00004, 16);
            DbPoint point = dPoints[0];
            dPoints.Add(point);
            AddLayerKMLElement(serialInfoElement, dPoints, color, 10.0, description, nameDesc);
        }

        /// <summary>
        /// 添加KML格式元素
        /// </summary>
        /// <param name="parentElement">根文件夹节点</param>
        /// <param name="dPoints">坐标点集合</param>
        /// <param name="color">图层颜色</param>
        /// <param name="height">图层高度</param>
        /// <param name="description">图层描述</param>
        /// <param name="pointName">图层名称</param>
        public void AddLayerKMLElement(XmlElement parentElement, List<DbPoint> dPoints, Color color, 
            double height, string description, string pointName)
        {
            //DbPoint[] dPoints = getCircleDPoint(dPoint, 0.00004, 16);

            XmlElement elementTestPoint = doc.CreateElement("Placemark");
            parentElement.AppendChild(elementTestPoint);
            XmlElement elementName = doc.CreateElement("name");
            elementTestPoint.AppendChild(elementName);
            elementName.AppendChild(doc.CreateTextNode(pointName));
            XmlElement elementDescription = doc.CreateElement("description");
            elementTestPoint.AppendChild(elementDescription);
            elementDescription.AppendChild(doc.CreateTextNode(description));
            //XmlElement elementTimeStamp = doc.CreateElement("TimeStamp");
            //elementTestPoint.AppendChild(elementTimeStamp);
            //XmlElement elementWhen = doc.CreateElement("when");
            //elementTimeStamp.AppendChild(elementWhen);
            //elementWhen.AppendChild(doc.CreateTextNode(time.ToString("yyyy-MM-ddTHH:mm:ss")));
            XmlElement elementStyle = doc.CreateElement("Style");
            elementTestPoint.AppendChild(elementStyle);
            XmlElement elementPolyStyle = doc.CreateElement("PolyStyle");
            elementStyle.AppendChild(elementPolyStyle);
            XmlElement elementColor = doc.CreateElement("color");
            elementPolyStyle.AppendChild(elementColor);
            elementColor.AppendChild(doc.CreateTextNode(makeColorABGRDes(color)));
            XmlElement elementOutline = doc.CreateElement("outline");
            elementPolyStyle.AppendChild(elementOutline);
            elementOutline.AppendChild(doc.CreateTextNode("0"));

            XmlElement elementPolygon = doc.CreateElement("Polygon");
            elementTestPoint.AppendChild(elementPolygon);
            XmlElement elementExtrude = doc.CreateElement("extrude");
            elementPolygon.AppendChild(elementExtrude);
            elementExtrude.AppendChild(doc.CreateTextNode("1"));
            XmlElement elementAltitudeMode = doc.CreateElement("altitudeMode");
            elementPolygon.AppendChild(elementAltitudeMode);
            elementAltitudeMode.AppendChild(doc.CreateTextNode("relativeToGround"));
            XmlElement elementOuterBoundaryIs = doc.CreateElement("outerBoundaryIs");
            elementPolygon.AppendChild(elementOuterBoundaryIs);
            XmlElement elementLinearRing = doc.CreateElement("LinearRing");
            elementOuterBoundaryIs.AppendChild(elementLinearRing);
            XmlElement elementCoordinates = doc.CreateElement("coordinates");
            elementLinearRing.AppendChild(elementCoordinates);

            StringBuilder sbPoints = new StringBuilder();
            foreach (DbPoint item in dPoints)
            {
                sbPoints.AppendFormat("{0:g},{1:g},{2} ", item.x, item.y, height);
            }

            elementCoordinates.AppendChild(doc.CreateTextNode(sbPoints.ToString()));
        }

        private List<DbPoint> getCircleDPoint(DbPoint centerDPoint, double radius, int partCount)
        {
            List<DbPoint> dPoints = new List<DbPoint>();
            if (partCount > 0)
            {
                double anglePerPart = 2 * Math.PI / partCount;
                for (int i = 0; i < partCount; i++)
                {
                    double deltaY = radius * Math.Sin(anglePerPart * i);
                    double deltaX = radius * Math.Cos(anglePerPart * i);
                    DbPoint dPoint = new DbPoint(centerDPoint.x + deltaX, centerDPoint.y + deltaY);
                    dPoints.Add(dPoint);
                }
            }
            return dPoints;
        }

        public void AddEventStyle(XmlElement eventInfoElement, String imageName)
        {
            XmlElement elementStyle = doc.CreateElement("Style");
            eventInfoElement.AppendChild(elementStyle);
            XmlAttribute attribute = doc.CreateAttribute("id");
            attribute.Value = imageName;
            elementStyle.Attributes.Append(attribute);
            XmlElement elementIconStyle = doc.CreateElement("IconStyle");
            elementStyle.AppendChild(elementIconStyle);
            XmlElement elementScale = doc.CreateElement("scale");
            elementIconStyle.AppendChild(elementScale);
            elementScale.AppendChild(doc.CreateTextNode("0.5"));
            XmlElement elementIcon = doc.CreateElement("Icon");
            elementIconStyle.AppendChild(elementIcon);
            XmlElement elementHref = doc.CreateElement("href");
            elementIcon.AppendChild(elementHref);
            elementHref.AppendChild(doc.CreateTextNode(imageName));
        }

        public void AddEvent(XmlElement eventInfoElement, DbPoint dPoint, object imageName, DateTime time, string description)
        {
            XmlElement elementEvent = doc.CreateElement("Placemark");
            eventInfoElement.AppendChild(elementEvent);
            XmlElement elementDescription = doc.CreateElement("description");
            elementEvent.AppendChild(elementDescription);
            elementDescription.AppendChild(doc.CreateTextNode(description));
            //XmlElement elementTimeStamp = doc.CreateElement("TimeStamp");
            //elementEvent.AppendChild(elementTimeStamp);
            //XmlElement elementWhen = doc.CreateElement("when");
            //elementTimeStamp.AppendChild(elementWhen);
            //elementWhen.AppendChild(doc.CreateTextNode(time.ToString("yyyy-MM-ddTHH:mm:ss")));
            XmlElement elementStyleUrl = doc.CreateElement("styleUrl");
            elementEvent.AppendChild(elementStyleUrl);
            elementStyleUrl.AppendChild(doc.CreateTextNode("#" + imageName));
            XmlElement elementPoint = doc.CreateElement("Point");
            elementEvent.AppendChild(elementPoint);
            XmlElement elementCoordinates = doc.CreateElement("coordinates");
            elementPoint.AppendChild(elementCoordinates);
            elementCoordinates.AppendChild(doc.CreateTextNode(String.Format("{0:g},{1:g},10", dPoint.x, dPoint.y)));
        }

        public XmlDocument getRootNode()
        {
            return doc;
        }


        internal XmlElement CreateRectangle(Color color, double ltlong, double ltlati, double brlong, double brlati)
        {
            XmlElement elem = doc.CreateElement("Placemark");
            XmlElement elementStyle = doc.CreateElement("Style");

            elem.AppendChild(elementStyle);
            XmlElement elementPolyStyle = doc.CreateElement("PolyStyle");
            elementStyle.AppendChild(elementPolyStyle);
            XmlElement elementColor = doc.CreateElement("color");
            elementPolyStyle.AppendChild(elementColor);
            elementColor.AppendChild(doc.CreateTextNode(makeColorABGRDes(color)));
            XmlElement elementOutline = doc.CreateElement("outline");
            elementPolyStyle.AppendChild(elementOutline);
            elementOutline.AppendChild(doc.CreateTextNode("0"));

            XmlElement elementPolygon = doc.CreateElement("Polygon");
            elem.AppendChild(elementPolygon);
            XmlElement elementExtrude = doc.CreateElement("extrude");
            elementPolygon.AppendChild(elementExtrude);
            elementExtrude.AppendChild(doc.CreateTextNode("1"));
            XmlElement elementAltitudeMode = doc.CreateElement("altitudeMode");
            elementPolygon.AppendChild(elementAltitudeMode);
            elementAltitudeMode.AppendChild(doc.CreateTextNode("relativeToGround"));
            XmlElement elementOuterBoundaryIs = doc.CreateElement("outerBoundaryIs");
            elementPolygon.AppendChild(elementOuterBoundaryIs);
            XmlElement elementLinearRing = doc.CreateElement("LinearRing");
            elementOuterBoundaryIs.AppendChild(elementLinearRing);
            XmlElement elementCoordinates = doc.CreateElement("coordinates");
            elementLinearRing.AppendChild(elementCoordinates);
            elementCoordinates.AppendChild(doc.CreateTextNode(String.Format("{0:g},{1:g},10 {2:g},{3:g},10 {4:g},{5:g},10 {6:g},{7:g},10",
                ltlong, ltlati,
                ltlong, brlati,
                brlong, brlati,
                brlong, ltlati


                )));
            return elem;
        }
    }
    /************************************************************************/
    /* An interface for layers to be fired to export operations, to be implemented by
     * layers
    /************************************************************************/
    public interface IKMLExport
    {
        void ExportKml(KMLExporter exporter, XmlElement parentElem);
    }
}
