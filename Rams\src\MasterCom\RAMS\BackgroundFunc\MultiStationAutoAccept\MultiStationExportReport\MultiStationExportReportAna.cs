﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class MultiStationExportReportAna : MultiStationAcceptAna
    {
        public StationAcceptAutoSet_XJ FuncSet { get; set; } = new StationAcceptAutoSet_XJ();
        public bool IsStarted { get; set; } = false;
        Dictionary<string, Dictionary<string, BtsWorkParam_XJ>> workParamSumDic = null;//Dictionary<地市, Dictionary<基站编号, BtsWorkParam_XJ>> 
        string fileNameKeyStr = "";
        string curDistrictName = "";

        private static MultiStationExportReportAna instance = null;
        public new static MultiStationExportReportAna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new MultiStationExportReportAna();
                    }
                }
            }
            return instance;
        }

        protected MultiStationExportReportAna()
            : base()
        {
            if (instance != null)
            {
                return;
            }
            this.isIgnoreExport = true;
        }
        public override string Name
        {
            get
            {
                return "新疆单站验收报告自动导出";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22114, "查询");
        }
        public override void DealBeforeBackgroundQueryByCity()
        {
            workParamSumDic = GetWorkParamsHelper_XJ.GetWorkParamsInfo(FuncSet);
        }
        protected override bool getCondition()
        {
            curDistrictName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID).Replace("市", "");
            if (workParamSumDic == null || workParamSumDic.Count <= 0 
                || !workParamSumDic.ContainsKey(curDistrictName))
            {
                reportBackgroundInfo("未读取到" + curDistrictName + "的待评估对象数据");
                return false;
            }
            MainModel.MainForm.GetMapForm().updateMap();

            return base.getCondition();
        }
        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            this.IsStarted = true;
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                clientProxy.Close();

                Dictionary<string, BtsWorkParam_XJ> curDistrictWorkParam = workParamSumDic[curDistrictName.Replace("市", "")];
                if (curDistrictWorkParam != null)
                {
                    reportBackgroundInfo("读取到" + curDistrictWorkParam.Count + "个站点的工参信息");
                    foreach (BtsWorkParam_XJ btsInfo in curDistrictWorkParam.Values)
                    {
                        if (MainModel.BackgroundStopRequest)
                        {
                            return;
                        }

                        string folderPath = AcpAutoCoverPicture.Instance.GetBtsPicFolder(btsInfo.BtsName);
                        if (System.IO.Directory.Exists(folderPath))//删除之前本站的覆盖截图
                        {
                            System.IO.Directory.Delete(folderPath, true);
                        }

                        reportBackgroundInfo(string.Format("开始读取基站 {0} 的待分析文件...", btsInfo.BtsName));
                        fileNameKeyStr = btsInfo.BtsName;
                        doBackgroundStatByFile(clientProxy);

                        reportBackgroundInfo(string.Format("开始读取基站 {0} 的预处理信息...", btsInfo.BtsName));
                        exportReportByBgData(btsInfo);

                        if (System.IO.Directory.Exists(folderPath))//删除之前本站的覆盖截图
                        {
                            System.IO.Directory.Delete(folderPath, true);
                        }
                    }
                }
            }
            finally
            {
                this.IsStarted = false;
            }
        }

        protected int getBgSubFuncID()
        {
            //与MultiStationAcceptAna共用一个功能ID(文件分析过程和结果相同)
            return MultiStationAcceptAna.GetInstance().GetSubFuncID();
        }
        protected override void getFilesForAnalyse()
        {
            int subFuncId = getBgSubFuncID();
            BackgroundFuncQueryManager.GetInstance().GetFilterFile_CellAccept(subFuncId, ServiceTypeString
                            , ((int)carrierID).ToString(), "strfilename", fileNameKeyStr);
        }

        protected void exportReportByBgData(BtsWorkParam_XJ btsWorkParamInfo)//查询预处理信息，然后导出报告
        {
            if (MainModel.BackgroundStopRequest)
            {
                return;
            }
            getBackgroundData();
            reportBackgroundInfo(string.Format("共读取到{0}条预处理信息", BackgroundResultList.Count));
            if (BackgroundResultList.Count <= 0)
            {
                return;
            }

            string strFileName = "";
            string isAccordStr = "";
            if (btsWorkParamInfo.IsOutDoorBts)
            {
                #region 导出宏站报告
                Dictionary<int, OutDoorBtsAcceptInfo> outDoorBtsAcceptInfoDic = new Dictionary<int, OutDoorBtsAcceptInfo>();
                MultiOutStaionAcceptResultQuery.AddBackgroundInfoToResults(BackgroundResultList, ref outDoorBtsAcceptInfoDic);

                OutDoorBtsAcceptInfo curBtsAcceptInfo;
                if (outDoorBtsAcceptInfoDic.TryGetValue(btsWorkParamInfo.ENodeBID, out curBtsAcceptInfo))
                {
                    getFusionInfo(curBtsAcceptInfo, btsWorkParamInfo);
                    strFileName = ExportOutdoorBtsReportHelper_XJ.ExportReports(curBtsAcceptInfo, btsWorkParamInfo);
                    isAccordStr = curBtsAcceptInfo.IsAccordAcceptStr;
                }
                else
                {
                    reportBackgroundInfo("未匹配到目标基站信息，请核查路网通工参和待评估工参信息是否一致");
                }
                #endregion
            }
            else
            {
                #region 导出室分站报告
                Dictionary<int, InDoorBtsAcceptInfo> inDoorBtsAcceptInfoDic = new Dictionary<int, InDoorBtsAcceptInfo>();
                MultiInStaionAcceptResultQuery.AddBackgroundInfoToResults(BackgroundResultList, ref inDoorBtsAcceptInfoDic);

                InDoorBtsAcceptInfo curBtsAcceptInfo;
                if (inDoorBtsAcceptInfoDic.TryGetValue(btsWorkParamInfo.ENodeBID, out curBtsAcceptInfo))
                {
                    getFusionInfo(curBtsAcceptInfo, btsWorkParamInfo);
                    List<IndoorCoverPicInfo> coverPicInfoList = getIndoorCoverPic(btsWorkParamInfo.BtsName);
                    strFileName = ExportIndoorBtsReportHelper_XJ.ExportReports(curBtsAcceptInfo, btsWorkParamInfo, coverPicInfoList);
                    isAccordStr = curBtsAcceptInfo.IsAccordAcceptStr;
                }
                else
                {
                    reportBackgroundInfo("未匹配到目标基站信息，请核查路网通工参和待评估工参信息是否一致");
                }
                #endregion
            }

            if (!string.IsNullOrEmpty(strFileName) && File.Exists(strFileName))
            {
                string pushResult = "未开启webservice自动推送";
                if (FuncSet.IsCheckWebservice)
                {
                    pushResult = pushRport(btsWorkParamInfo.ENodeBID, strFileName, FuncSet.ResultWebservicePath);
                    reportBackgroundInfo(string.Format("发送站点[{0}]的验收报告：{1}", btsWorkParamInfo.BtsName, pushResult));
                }

                string strUpdateSql = string.Format(@"update tb_xinjiang_btsInfo_accept set reportFilePath = '{0}' 
,isAccordAccept = '{1}', strdes = '{2}' where enodebid = {3} and workOrderNum = '{4}'"
                    , strFileName, isAccordStr, DateTime.Now.ToString() + ":" + pushResult
                    , btsWorkParamInfo.ENodeBID, btsWorkParamInfo.WorkOrderNum);

                DiySqlNonQueryMainDB sqlUpdate = new DiySqlNonQueryMainDB(mainModel, strUpdateSql);
                sqlUpdate.Query();
            }
        }
        protected string webserviceTest(string webservicePath)
        {
            try
            {
                object result = WebServiceHelper.InvokeWebService(webservicePath, "isAlive", null);

                if (result != null)
                {
                    return "WebService接口连接测试返回结果：" + result.ToString();
                }
                else
                {
                    return "WebService接口连接测试失败！";
                }
            }
            catch (Exception ex)
            {
                return "接口连接测试出错:" + ex.Message + ex.StackTrace + ex.Source;
            }
        }
        protected string pushRport(int btsId, string strFileName, string webservicePath)
        {
            if (string.IsNullOrEmpty(webservicePath))
            {
                return "webservice地址不能为空";
            }

            string resultDes = "推送失败";
            try
            {
                string fileStr = getXlsString(strFileName);
                if (string.IsNullOrEmpty(fileStr))
                {
                    return "文件信息为空";
                }

                object[] args = new object[1];
                args[0] = string.Format(@"<root>
    <btsId>{0}</btsId>
    <fileName>{1}</fileName>
    <fileStr>{2}</fileStr>
    <isappend>{3}</isappend>
</root>", btsId, Path.GetFileName(strFileName), fileStr, 0);

                reportBackgroundInfo(string.Format("开始调用接口发送验收报告:{0}", strFileName));

                object objResult = WebServiceHelper.InvokeWebService(webservicePath, "uploadFile", args);
                if (objResult != null)
                {
                    XmlDocument xml = new XmlDocument();
                    xml.LoadXml(objResult.ToString());

                    string strFlag = xml.DocumentElement["returnFlag"].InnerText;
                    string strMsg = xml.DocumentElement["returnMsg"].InnerText;
                    if (strFlag == "1" || strFlag == "true")
                    {
                        resultDes = "推送成功";
                    }
                    else if (!string.IsNullOrEmpty(strMsg))
                    {
                        resultDes = "推送失败:" + strMsg;
                    }
                }
            }
            catch (Exception ex)
            {
                reportBackgroundInfo("调用接口发送验收报告出错:" + ex.Message + ex.StackTrace + ex.Source);
            }

            return resultDes;
        }
        protected string getXlsString(string strFileName)
        {
            byte[] content = null;
            using (FileStream fsRead = new FileStream(strFileName, FileMode.Open))
            {
                content = new byte[(int)fsRead.Length];
                fsRead.Read(content, 0, content.Length);
            }

            return Convert.ToBase64String(content);
        }

        protected void getFusionInfo(BtsAcceptInfoBase btsInfo, BtsWorkParam_XJ btsWorkParamInfo)
        {
            btsInfo.FusionInfo = null;
            if (FuncSet.IsAnaFusionDatas)
            {
                reportBackgroundInfo(string.Format("开始关联 {0} 站点的性能、告警、MR数据信息...", btsInfo.BtsName));
                btsInfo.FusionInfo = BtsFusionDataQuery_XJ.GetFusionInfos(btsWorkParamInfo, FuncSet.FusionBeginTime
                    , FuncSet.FusionEndTime);

                btsInfo.FusionInfo.CheckIsAccordAccept(FuncSet);
                if (!btsInfo.FusionInfo.IsAccord)
                {
                    btsInfo.IsAccordAccept = false;
                    btsInfo.NotAccordKpiDes += btsInfo.FusionInfo.NotAccordKpiDes;//汇总不达标的指标信息
                }
            }
        }
        protected List<IndoorCoverPicInfo> getIndoorCoverPic(string btsName)
        {
            List<IndoorCoverPicInfo> coverPicInfoList = new List<IndoorCoverPicInfo>();
            if (Directory.Exists(FuncSet.IndoorCoverPicFolderPath))
            {
                reportBackgroundInfo(string.Format("开始加载 {0} 站点的性能覆盖效果图...", btsName));
                BackgroundFuncConfigManager bgConfigManager = BackgroundFuncConfigManager.GetInstance();
                QueryIndoorCoverPic_XJ coverPicQuery = new QueryIndoorCoverPic_XJ(btsName
                    , bgConfigManager.StartTime, bgConfigManager.EndTime);

                coverPicQuery.Query();
                coverPicInfoList.AddRange(coverPicQuery.Floor_CoverPicDic.Values);
            }
            return coverPicInfoList;
        }
        protected override void getBackgroundData()
        {
            BackgroundFuncConfigManager bgConfigManager = BackgroundFuncConfigManager.GetInstance();
            int subFuncId = getBgSubFuncID();
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetFilterResult_CellAccept(new BackgroundFuncQueryManager.CellAcceptCondition(bgConfigManager.ISTime, bgConfigManager.IETime, subFuncId, bgConfigManager.ProjectType, "fileName", fileNameKeyStr), Name, StatType);
        }
        protected override void saveBackgroundData()
        {
            List<BackgroundResult> resultList = new List<BackgroundResult>();
            int subFuncId = getBgSubFuncID();
            if (manager.AcceptFileInfo != null && manager.AcceptFileInfo.AcceptKpiDic.Count > 0)
            {
                BackgroundResult result = manager.AcceptFileInfo.ConvertToBackgroundResult(subFuncId, BackgroundFuncConfigManager.GetInstance().ProjectType);
                resultList.Add(result);
                manager.AcceptFileInfo = null;

                //未匹配到目标小区或未获取到指标信息的文件信息暂不保留
                //（有可能是未更新工参信息导致的,或者DT上传下载文件是截取覆盖图用的，每次出报告都要重新查询回放）
                BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(subFuncId, curAnaFileInfo, resultList);
            }
        }
        protected override void initBackgroundImageDesc()
        { 
            //
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Clear();
                ignoreParamKeys.Add("ExportReportSet");

                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["ExportReportSet"] = FuncSet.Params;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("ExportReportSet"))
                {
                    FuncSet.Params = param["ExportReportSet"] as Dictionary<string, object>;
                }
            }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new StationAcceptPropertiesXJ_LTE(this, FuncSet, webserviceTest, pushRport);
            }
        }
        public override void DealAfterBackgroundQueryByCity()
        {
            if (workParamSumDic != null)
            {
                workParamSumDic.Clear();
            }
        }
    }
}
