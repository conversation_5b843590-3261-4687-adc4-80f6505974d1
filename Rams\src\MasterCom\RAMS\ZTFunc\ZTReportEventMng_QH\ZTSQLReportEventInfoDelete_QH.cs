﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSQLReportEventInfoDelete_QH : DIYSQLBase
    {
        public ZTSQLReportEventInfoDelete_QH(MainModel mainModel, ZTReportEventInfo_QH reportEventInfo)
            : base(mainModel)
        {
            this.reportEventInfo = reportEventInfo;
        }

        private readonly ZTReportEventInfo_QH reportEventInfo;

        
        protected override string getSqlTextString()
        {
            return string.Format("delete from tb_beijing_report_event where ifileid = {0} and iseqid = {1};"
                  , reportEventInfo.FileID, reportEventInfo.SeqID);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[1];
            rType[index] = E_VType.E_Int;

            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int result = 0;
                    result = package.Content.GetParamInt();
                    if (result == 1)
                    {
                        //更新成功
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
        public override string Name
        {
            get { return "ZTSQLReportEventInfoDelete_QH"; }
        }
    }
}
