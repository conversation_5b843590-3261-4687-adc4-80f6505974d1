﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYLeakOutCellSetByRegion_LTE : ZTDIYLeakOutCellSetByRegion
    {
        protected LeakOutAsNCellDlg conditionDlg = null;

        protected LTELeakOutCellSetCondition cond = null;

        protected Dictionary<LTECell, LteLeakOutIndoorCell> indoorCellDic = new Dictionary<LTECell, LteLeakOutIndoorCell>();

        private static ZTDIYLeakOutCellSetByRegion_LTE instance;
        public new static ZTDIYLeakOutCellSetByRegion_LTE GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTDIYLeakOutCellSetByRegion_LTE();
            }
            return instance;
        }

        protected ZTDIYLeakOutCellSetByRegion_LTE()
        {
            isAddSampleToDTDataManager = false;
        }

        public ZTDIYLeakOutCellSetByRegion_LTE(ServiceName serviceName)
            : this()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "LTE外泄分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22042, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup sampleGroup = new DIYSampleGroup();
            sampleGroup.ThemeName = "---";
            addParameter(sampleGroup, "lte_EARFCN");
            addParameter(sampleGroup, "lte_PCI");
            addParameter(sampleGroup, "lte_RSRP");
            addParameter(sampleGroup, "lte_SINR");
            addParameter(sampleGroup, "lte_TAC");
            addParameter(sampleGroup, "lte_ECI");
            addParameter(sampleGroup, "lte_NCell_RSRP");
            addParameter(sampleGroup, "lte_NCell_SINR");
            addParameter(sampleGroup, "lte_NCell_EARFCN");
            addParameter(sampleGroup, "lte_NCell_PCI");
            addParameter(sampleGroup, "lte_gsm_DM_RxLevSub");
            addParameter(sampleGroup, "lte_gsm_DM_RxQualSub");
            addParameter(sampleGroup, "lte_gsm_NC_RxLev");
            addParameter(sampleGroup, "lte_gsm_SC_LAC");
            addParameter(sampleGroup, "lte_gsm_SC_CI");
            addParameter(sampleGroup, "lte_gsm_SC_BCCH");
            addParameter(sampleGroup, "lte_gsm_SC_BSIC");

            return sampleGroup;
        }

        private void addParameter(DIYSampleGroup sampleGroup, string name)
        {
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter(name);
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (conditionDlg == null)
            {
                conditionDlg = new LeakOutAsNCellDlg(true);
            }
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                this.cond = new LTELeakOutCellSetCondition(conditionDlg);
                if (!this.cond.LeakOutAsMainCell && !this.cond.LeakOutAsNCell)
                {
                    return false;
                }
                this.rxLevThreshold = cond.MinNCellRxlev;
                this.rxLevDValue = cond.DiffRxlev;
                GSMCellStaterMap = new Dictionary<Cell, LteGSMLeakOutCell>();
                GSMSN = 0;
                return true;
            }
            return false;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            //
        }

        protected override void getResultAfterQuery()
        {
            int sn = 0;
            foreach (LteLeakOutIndoorCell indoorCell in this.indoorCellDic.Values)
            {
                indoorCell.SN = ++sn;
                indoorCell.GetResult(this.cond.IsGetRoadDesc);
                indoorCell.SetTop5FileName();
            }
            foreach (Cell cell in GSMCellStaterMap.Keys)
            {
                GSMCellStaterMap[cell].GetResult();
                GSMCellStaterMap[cell].SetTop5FileName();
                getGSMRoadDesc(cell.Longitude, cell.Latitude, GSMCellStaterMap[cell]);
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            if (indoorCellDic.Values.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LTELeakOutCellSetResultForm resultForm = MainModel.CreateResultForm(typeof(LTELeakOutCellSetResultForm)) as LTELeakOutCellSetResultForm;
            resultForm.FillData(new List<LteLeakOutIndoorCell>(indoorCellDic.Values), new List<LteGSMLeakOutCell>(GSMCellStaterMap.Values));
            resultForm.Visible = true;
            indoorCellDic = new Dictionary<LTECell, LteLeakOutIndoorCell>();
        }

        protected override void doWithDTData(TestPoint testPoint)
        {
            bool isBreak = false;
            if (cond.IsGetGSMCell)
                isBreak = dowithCSFBDataSC(testPoint);
            if (cond.IsGetGSMCell && cond.LeakOutAsNCell && !isBreak)
                dowithCSFBDataNC(testPoint);
            float? sRsrp = (float?)testPoint["lte_RSRP"];
            float? sSinr = (float?)testPoint["lte_SINR"];
            LTECell sLteCell = testPoint.GetMainCell_LTE();//getMainCell(tp);//原 
            if (sRsrp == null)
            {
                return;
            }

            bool isLeakOutBySCell = false;
            if (this.cond.LeakOutAsMainCell && sLteCell != null && sLteCell.Type == LTEBTSType.Indoor)
            {
                LteLeakOutIndoorCell indoorCell = null;
                if (!indoorCellDic.TryGetValue(sLteCell, out indoorCell))
                {
                    indoorCell = new LteLeakOutIndoorCell(sLteCell);
                    indoorCellDic.Add(sLteCell, indoorCell);
                }
                indoorCell.AddTestPoint(testPoint, (float)sRsrp, sSinr, true);
                isLeakOutBySCell = true;
            }

            if (this.cond.LeakOutAsNCell && !isLeakOutBySCell)
            {
                addNCell(testPoint, sRsrp);
            }
        }

        private void addNCell(TestPoint testPoint, float? sRsrp)
        {
            for (int i = 0; i < 12; ++i)
            {
                float? nRsrp = (float?)testPoint["lte_NCell_RSRP"];
                if (nRsrp == null || nRsrp < this.cond.MinNCellRxlev || sRsrp - nRsrp > this.cond.DiffRxlev)
                {
                    break;
                }

                float? nSinr = (float?)testPoint["lte_NCell_SINR"];
                LTECell nLteCell = testPoint.GetNBCell_LTE(i);//getNBCell(tp, i);//原 
                if (nLteCell == null || nLteCell.Type != LTEBTSType.Indoor)
                {
                    continue;
                }

                LteLeakOutIndoorCell indoorCell = null;
                if (!indoorCellDic.TryGetValue(nLteCell, out indoorCell))
                {
                    indoorCell = new LteLeakOutIndoorCell(nLteCell);
                    indoorCellDic.Add(nLteCell, indoorCell);
                }
                indoorCell.AddTestPoint(testPoint, (float)nRsrp, nSinr, false);
            }
        }

        /// <summary>
        /// /分析采样点的主服是否是GSM室内小区
        /// </summary>
        protected bool dowithCSFBDataSC(TestPoint testPoint)
        {
            short? rxlev = (short?)testPoint["lte_gsm_DM_RxLevSub"];
            if (rxlev == null || rxlev < -120 || rxlev > -10)
                return false;

            Cell mainCell = testPoint.GetMainCell_LTE_GSM();
            if (mainCell != null && mainCell.Type == BTSType.Indoor)
            {
                addLeakoutGSMCell(mainCell, testPoint, false, -1);
                return true;
            }
            else
                return false;
        }

        /// <summary>
        /// 分析采样点的邻区是否是GSM室内小区
        /// </summary>
        protected void dowithCSFBDataNC(TestPoint testPoint)
        {
            int? mainRxLev = (int?)(short?)testPoint["lte_gsm_DM_RxLevSub"];
            if (mainRxLev == null || mainRxLev < -120 || mainRxLev > -10)
                return;

            for (int i = 0; i < 6; i++)
            {
                int? rxlev = (int?)(short?)testPoint["lte_gsm_NC_RxLev", i];
                if (rxlev == null || rxlev < rxLevThreshold || mainRxLev - rxlev > rxLevDValue)
                {
                    break;
                }
                Cell cell = CellManager.GetInstance().GetNearestCell(testPoint.DateTime, (short?)testPoint["lte_gsm_NC_BCCH", i],
                    (byte?)testPoint["lte_gsm_NC_BSIC", i], testPoint.Longitude, testPoint.Latitude, (ushort?)(int?)testPoint["lte_gsm_SC_LAC"],
                    (ushort?)(int?)testPoint["lte_gsm_SC_CI"], (short?)testPoint["lte_gsm_SC_BCCH"], (byte?)testPoint["lte_gsm_SC_BSIC"]);
                if (cell != null && cell.Type == BTSType.Indoor)
                {
                    addLeakoutGSMCell(cell, testPoint, true, i);
                }
            }

        }

        Dictionary<Cell, LteGSMLeakOutCell> GSMCellStaterMap = new Dictionary<Cell, LteGSMLeakOutCell>();
        private int GSMSN = 0;
        protected void addLeakoutGSMCell(Cell cell, TestPoint testPoint, bool bNBCell, int indexNB)
        {
            LteGSMLeakOutCell stater = null;
            if (GSMCellStaterMap.ContainsKey(cell))
            {
                stater = GSMCellStaterMap[cell];
            }
            else
            {
                stater = new LteGSMLeakOutCell(cell);
                GSMCellStaterMap[cell] = stater;
                stater.SN = ++GSMSN;
            }
            stater.AddGSMTestPoint(testPoint);
            if (bNBCell)
                stater.AddRxLevSub((float?)(short?)testPoint["lte_gsm_NC_RxLev", indexNB], false);
            else
            {
                stater.AddRxLevSub((float?)(short?)testPoint["lte_gsm_DM_RxLevSub"], true);
                byte? rxQual = (byte?)testPoint["lte_gsm_DM_RxQualSub"];
                if (rxQual != null && rxQual >= 0 && rxQual <= 7)
                    stater.AddQualVal((float)rxQual);
            }
            stater.AddDistance(cell.GetDistance(testPoint.Longitude, testPoint.Latitude));
        }

        protected void getGSMRoadDesc(double longitude, double latitude, LteGSMLeakOutCell leakOut)
        {
            string area = GISManager.GetInstance().GetAreaPlaceDesc(longitude, latitude);
            leakOut.AreaDesc = area;

            if (cond.IsGetRoadDesc)
            {
                List<string> multiRoads = new List<string>();
                foreach (TestPoint tp in leakOut.testPointList)
                {
                    addRoads(multiRoads, tp);
                }
                setRoadDesc(leakOut, multiRoads);
            }
        }

        private static void addRoads(List<string> multiRoads, TestPoint tp)
        {
            string road = GISManager.GetInstance().GetRoadPlaceDesc(tp.Longitude, tp.Latitude);
            if (!string.IsNullOrEmpty(road))
            {
                string[] roads = road.Split(';');
                foreach (string item in roads)
                {
                    if (!string.IsNullOrEmpty(item) && !multiRoads.Contains(item))
                    {
                        multiRoads.Add(item);
                    }
                }
            }
        }

        private void setRoadDesc(LteGSMLeakOutCell leakOut, List<string> multiRoads)
        {
            StringBuilder roadDesc = new StringBuilder();
            foreach (string road in multiRoads)
            {
                if (roadDesc.Length != 0)
                {
                    roadDesc.Append(";");
                }
                roadDesc.Append(road);
            }
            leakOut.RoadDesc = roadDesc.ToString();
        }
    }

    public class LTELeakOutCellSetCondition
    {
        public bool LeakOutAsMainCell { get; set; }

        public bool LeakOutAsNCell { get; set; }

        public bool IsGetRoadDesc { get; set; }

        public bool IsGetGSMCell { get; set; }

        public int MinNCellRxlev { get; set; } = -85;

        public int DiffRxlev { get; set; } = 10;
        public LTELeakOutCellSetCondition()
        {
        }
        public LTELeakOutCellSetCondition(LeakOutAsNCellDlg condDlg)
        {
            LeakOutAsMainCell = condDlg.LeakOutAsMainCell;
            LeakOutAsNCell = condDlg.LeakOutAsNBCell;
            MinNCellRxlev = condDlg.RxLevThreshold;
            DiffRxlev = condDlg.RxLevDValue;
            IsGetRoadDesc = condDlg.getRoadDesc;
            IsGetGSMCell = condDlg.IsGetGSMCell;
        }
    }

    /// <summary>
    /// 为了重写方法内由于小区类型不同所需要改变的方法或key
    /// </summary>
    public class LteGSMLeakOutCell : LeakOutCell
    {
        public LteGSMLeakOutCell(ICell cell)
        {
            this.cell = cell;
            TpFiles = new Dictionary<string, int>();
        }

        public string Top5FileName
        {
            get;
            private set;
        }

        public Dictionary<string, int> TpFiles
        {
            get;
            private set;
        }

        public void AddGSMTestPoint(TestPoint tp)
        {
            testPointList.Add(tp);
            if (TpFiles.ContainsKey(tp.FileName))
                TpFiles[tp.FileName]++;
            else
                TpFiles.Add(tp.FileName, 0);

            istime = Math.Min(tp.Time, istime);
            ietime = Math.Max(tp.Time, ietime);
            if (longitude == 0)
            {
                longitude = tp.Longitude;
                latitude = tp.Latitude;
            }
        }

        public void SetTop5FileName()
        {
            Dictionary<string, int> nTpFiles = TpFiles;
            int fileCount = 0;

            if (TpFiles.Count <= 5)
                fileCount = TpFiles.Count;
            else
                fileCount = 5;

            for (int i = 0; i < fileCount; i++)
            {
                string fileName = findMax(nTpFiles);
                Top5FileName = string.Format(Top5FileName + fileName + "; ");
                nTpFiles.Remove(fileName);
            }
        }

        private string findMax(Dictionary<string, int> dic)
        {
            string MaxKey = string.Empty;
            foreach (KeyValuePair<string, int> item in dic)
            {
                if (MaxKey == string.Empty)
                    MaxKey = item.Key;
                else
                {
                    if (dic[MaxKey] < item.Value)
                        MaxKey = item.Key;
                }
            }
            return MaxKey;
        }

        protected override void getOutdoorMainCell(TestPoint tp, out ICell cell)
        {
            cell = null;
            Cell gsmCell = tp.GetMainCell_LTE_GSM();
            if (gsmCell != null && gsmCell.Type == BTSType.Outdoor)
            {
                cell = gsmCell;
            }
        }

        protected override void getOutdoorNCell(TestPoint tp, int idx, out ICell cell)
        {
            cell = null;
            Cell gsmCell = tp.GetNBCell_LTE_GSM(idx);
            if (gsmCell != null && gsmCell.Type == BTSType.Outdoor)
            {
                cell = gsmCell;
            }
        }

        protected override bool tryGetMainRxLev(TestPoint tp, out int rxlev)
        {
            rxlev = 0;
            short? value = (short?)tp["lte_gsm_DM_RxLevSub"];
            if (value != null && value >= -120 && value <= -10)
            {
                rxlev = (int)value;
                return true;
            }
            return false;
        }

        protected override bool tryGetNCellRxLev(TestPoint tp, int idx, out int rxlev)
        {
            rxlev = 0;
            short? value = (short?)tp["lte_gsm_NC_RxLev", idx];
            if (value != null && value >= -120 && value <= -10)
            {
                rxlev = (int)value;
                return true;
            }
            return false;
        }
    }

    public class LteLeakOutCell
    {
        public LTECell LteCell
        {
            get;
            protected set;
        }

        public string CellName
        {
            get { return LteCell.Name; }
        }

        public int Tac
        {
            get;
            protected set;
        }

        public int Eci
        {
            get;
            protected set;
        }

        public int Earfcn
        {
            get;
            protected set;
        }

        public int Pci
        {
            get;
            protected set;
        }

        public double AvgRsrp
        {
            get;
            protected set;
        }

        public double MinRsrp
        {
            get;
            protected set;
        }

        public double MaxRsrp
        {
            get;
            protected set;
        }

        public string AvgSinrStr
        {
            get;
            protected set;
        }

        public string MinSinrStr
        {
            get;
            protected set;
        }

        public string MaxSinrStr
        {
            get;
            protected set;
        }

        public double AvgDistance
        {
            get;
            protected set;
        }

        public string RoadDesc
        {
            get;
            protected set;
        }

        public int SampleCount
        {
            get;
            protected set;
        }

        public List<TestPoint> TestPoints
        {
            get;
            protected set;
        }

        public LteLeakOutCell(LTECell lteCell)
        {
            this.LteCell = lteCell;
            this.Tac = lteCell.TAC;
            this.Eci = lteCell.ECI;
            this.Earfcn = lteCell.EARFCN;
            this.Pci = lteCell.PCI;
            this.TestPoints = new List<TestPoint>();
            this.MinRsrp = double.MaxValue;
            this.MaxRsrp = double.MinValue;
        }

        public void AddTestPoint(TestPoint tp, float rsrp, float? sinr)
        {
            TestPoints.Add(tp);
            ++SampleCount;

            MinRsrp = Math.Min(MinRsrp, rsrp);
            MaxRsrp = Math.Max(MaxRsrp, rsrp);
            this.sumRsrp += rsrp;

            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                float sinrValue = (float)sinr;
                minSinr = Math.Min(minSinr, sinrValue);
                maxSinr = Math.Max(maxSinr, sinrValue);
                sumSinr += sinrValue;
                ++sinrCnt;
            }
        }

        public virtual void GetResult(bool isGetRoadInfo)
        {
            AvgRsrp = SampleCount == 0 ? 0 : sumRsrp / SampleCount;
            avgSinr = sinrCnt == 0 ? 0 : sumSinr / sinrCnt;
            AvgSinrStr = sinrCnt == 0 ? "" : Math.Round(avgSinr, 2).ToString();
            MinSinrStr = sinrCnt == 0 ? "" : Math.Round(minSinr, 2).ToString();
            MaxSinrStr = sinrCnt == 0 ? "" : Math.Round(maxSinr, 2).ToString();

            double distance = 0;
            foreach (TestPoint tp in TestPoints)
            {
                distance += MathFuncs.GetDistance(LteCell.Longitude, LteCell.Latitude, tp.Longitude, tp.Latitude);
            }
            AvgDistance = SampleCount == 0 ? 0 : distance / SampleCount;

            if (isGetRoadInfo && SampleCount != 0)
            {
                TestPoint midPoint = TestPoints[TestPoints.Count / 2];
                RoadDesc = GISManager.GetInstance().GetRoadPlaceDesc(midPoint.Longitude, midPoint.Latitude);
            }
        }

        protected double sumRsrp = 0;
        protected double sumSinr = 0;
        protected double minSinr = double.MaxValue;
        protected double maxSinr = double.MinValue;
        protected double avgSinr = 0;
        protected int sinrCnt = 0;
    }

    public class LteLeakOutIndoorCell : LteLeakOutCell
    {
        public LteLeakOutIndoorCell(LTECell lteCell)
            : base(lteCell)
        {
            OutdoorCells = new List<LteLeakOutOutdoorCell>();
            TpFiles = new Dictionary<string, int>();
        }

        public int SN
        {
            get;
            set;
        }

        public int SCellSampleCount
        {
            get;
            private set;
        }

        public int NCellSampleCount
        {
            get;
            private set;
        }

        public int SinrGT4SampleCount // sinr great than 4
        {
            get;
            private set;
        }

        public int SinrLT0SampleCount // sinr less than 0
        {
            get;
            private set;
        }

        public string Top5FileName
        {
            get;
            private set;
        }

        public List<LteLeakOutOutdoorCell> OutdoorCells
        {
            get;
            private set;
        }

        public Dictionary<string, int> TpFiles
        {
            get;
            private set;
        }

        public void SetTop5FileName()
        {
            Dictionary<string, int> nTpFiles = TpFiles;
            int fileCount = 0;

            if (TpFiles.Count <= 5)
                fileCount = TpFiles.Count;
            else
                fileCount = 5;

            for (int i = 0; i < fileCount; i++)
            {
                string fileName = findMax(nTpFiles);
                Top5FileName = string.Format(Top5FileName + fileName + "; ");
                nTpFiles.Remove(fileName);
            }
        }

        private string findMax(Dictionary<string, int> dic)
        {
            string MaxKey = string.Empty;
            foreach (KeyValuePair<string, int> item in dic)
            {
                if (MaxKey == string.Empty)
                    MaxKey = item.Key;
                else
                {
                    if (dic[MaxKey] < item.Value)
                        MaxKey = item.Key;
                }
            }
            return MaxKey;
        }

        public void AddTestPoint(TestPoint tp, float rsrp, float? sinr, bool isSCell)
        {
            base.AddTestPoint(tp, rsrp, sinr);

            if (TpFiles.ContainsKey(tp.FileName))
                TpFiles[tp.FileName]++;
            else
                TpFiles.Add(tp.FileName, 0);

            if (isSCell)
            {
                ++SCellSampleCount;
            }
            else
            {
                ++NCellSampleCount;
            }
            if (sinr != null)
            {
                if (sinr > 4)
                    ++SinrGT4SampleCount;
                if (sinr <= 0)
                    ++SinrLT0SampleCount;
            }
        }

        public override void GetResult(bool isGetRoadInfo)
        {
            base.GetResult(isGetRoadInfo);

            Dictionary<LTECell, LteLeakOutOutdoorCell> outCellDic = new Dictionary<LTECell, LteLeakOutOutdoorCell>();
            foreach (TestPoint tp in TestPoints)
            {
                LTECell sCell = tp.GetMainCell_LTE();
                float? sRsrp = (float?)tp["lte_RSRP"];
                float? sSinr = (float?)tp["lte_SINR"];
                addCellInfo(outCellDic, tp, sCell, sRsrp, sSinr);

                for (int i = 0; i < 12; ++i)
                {
                    LTECell nCell = tp.GetNBCell_LTE(i);
                    float? nRsrp = (float?)tp["lte_NCell_RSRP"];
                    float? nSinr = (float?)tp["lte_NCell_SINR"];
                    addCellInfo(outCellDic, tp, nCell, nRsrp, nSinr);
                }
            }

            OutdoorCells.AddRange(outCellDic.Values);
            OutdoorCells.Sort();
            int sn = 0;
            foreach (LteLeakOutOutdoorCell outCell in OutdoorCells)
            {
                outCell.GetResult(isGetRoadInfo);
                outCell.SN = ++sn;
            }
        }

        protected void addCellInfo(Dictionary<LTECell, LteLeakOutOutdoorCell> outCellDic, TestPoint tp, LTECell cell, float? rsrp, float? sinr)
        {
            if (rsrp != null && cell != null && cell.Type == LTEBTSType.Outdoor)
            {
                LteLeakOutOutdoorCell outCell = null;
                if (!outCellDic.TryGetValue(cell, out outCell))
                {
                    outCell = new LteLeakOutOutdoorCell(cell);
                    outCellDic.Add(cell, outCell);
                }
                outCell.AddTestPoint(tp, (float)rsrp, sinr);
            }
        }

        public BackgroundResult ConvertToBackgroundResult(FileInfo file)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
            bgResult.CellType = BackgroundCellType.LTE;
            if (file != null)
            {
                bgResult.FileID = file.ID;
                bgResult.FileName = file.Name;
                bgResult.ISTime = file.BeginTime;
                bgResult.IETime = file.EndTime;
            }
            if (LteCell != null)
            {
                bgResult.LAC = LteCell.TAC;
                bgResult.CI = LteCell.ECI;
                bgResult.BCCH = LteCell.EARFCN;
                bgResult.BSIC = LteCell.PCI;
                bgResult.LongitudeMid = LteCell.Longitude;
                bgResult.LatitudeMid = LteCell.Latitude;
            }
            if (TestPoints != null && TestPoints.Count > 0)
            {
                bgResult.ISTime = (int)(JavaDate.GetMilliseconds(TestPoints[0].DateTime) / 1000);
                bgResult.IETime = (int)(JavaDate.GetMilliseconds(TestPoints[TestPoints.Count - 1].DateTime) / 1000);
            }
            bgResult.SampleCount = SampleCount;
            bgResult.RxLevMax = (float)MaxRsrp;
            bgResult.RxLevMin = (float)MinRsrp;
            bgResult.RxLevMean = (float)AvgRsrp;
            bgResult.RxQualMax = (float)maxSinr;
            bgResult.RxQualMin = (float)minSinr;
            bgResult.RxQualMean = (float)avgSinr;
            bgResult.RoadDesc = RoadDesc;

            bgResult.AddImageValue(SCellSampleCount);
            bgResult.AddImageValue(NCellSampleCount);
            bgResult.AddImageValue(SinrLT0SampleCount);
            bgResult.AddImageValue((float)AvgDistance);

            if (OutdoorCells == null || OutdoorCells.Count <= 0)
            {
                bgResult.AddImageValue(0);
            }
            else
            {
                bgResult.AddImageValue(OutdoorCells.Count);
                foreach (LteLeakOutOutdoorCell outdoorCell in OutdoorCells)
                {
                    bgResult.AddImageValue(outdoorCell.CellName);
                    bgResult.AddImageValue(outdoorCell.Tac);
                    bgResult.AddImageValue(outdoorCell.Eci);
                    bgResult.AddImageValue((float)outdoorCell.AvgRsrp);
                    bgResult.AddImageValue(outdoorCell.AvgSinrStr);
                    bgResult.AddImageValue(outdoorCell.SampleCount);
                }
            }

            return bgResult;
        }

        /// <summary>
        /// 5G 室分外泄锚点分析用  add by youq  2020-08-10
        /// </summary>
        /// <param name="isGetRoadInfo"></param>
        public void GetNRLteResult(bool isGetRoadInfo)
        {
            base.GetResult(isGetRoadInfo);

            Dictionary<LTECell, LteLeakOutOutdoorCell> outCellDic = new Dictionary<LTECell, LteLeakOutOutdoorCell>();
            foreach (TestPoint tp in TestPoints)
            {
                LTECell sCell = tp.GetMainCell_LTE(true);
                //tp.GetMainCell_LTE();
                float? sRsrp = (float?)tp["NR_lte_RSRP"];
                float? sSinr = (float?)tp["NR_lte_SINR"];
                addCellInfo(outCellDic, tp, sCell, sRsrp, sSinr);

                for (int i = 0; i < 12; ++i)
                {
                    //GetNBCell_LTE(bool byTestTime, int index)
                    LTECell nCell = tp.GetNBCell_LTE(true, i);
                    float? nRsrp = (float?)tp["NR_lte_NCell_RSRP"];
                    float? nSinr = (float?)tp["NR_lte_NCell_SINR"];
                    addCellInfo(outCellDic, tp, nCell, nRsrp, nSinr);
                }
            }

            OutdoorCells.AddRange(outCellDic.Values);
            OutdoorCells.Sort();
            int sn = 0;
            foreach (LteLeakOutOutdoorCell outCell in OutdoorCells)
            {
                outCell.GetResult(isGetRoadInfo);
                outCell.SN = ++sn;
            }
        }
    }

    public class LteFddLeakOutIndoorCell : LteLeakOutCell
    {
        public LteFddLeakOutIndoorCell(LTECell lteCell)
            : base(lteCell)
        {
            OutdoorCells = new List<LteLeakOutOutdoorCell>();
        }
        public int SN
        {
            get;
            set;
        }

        public int SCellSampleCount
        {
            get;
            private set;
        }

        public int NCellSampleCount
        {
            get;
            private set;
        }

        public int SinrGT4SampleCount // sinr great than 4
        {
            get;
            private set;
        }

        public List<LteLeakOutOutdoorCell> OutdoorCells
        {
            get;
            private set;
        }

        public void AddTestPoint(TestPoint tp, float rsrp, float? sinr, bool isSCell)
        {
            base.AddTestPoint(tp, rsrp, sinr);

            if (isSCell)
            {
                ++SCellSampleCount;
            }
            else
            {
                ++NCellSampleCount;
            }
            if (sinr != null && sinr > 4)
            {
                ++SinrGT4SampleCount;
            }
        }

        public override void GetResult(bool isGetRoadInfo)
        {
            base.GetResult(isGetRoadInfo);

            Dictionary<LTECell, LteLeakOutOutdoorCell> outCellDic = new Dictionary<LTECell, LteLeakOutOutdoorCell>();
            foreach (TestPoint tp in TestPoints)
            {
                LTECell sCell = tp.GetMainCell_LTE_FDD();
                float? sRsrp = (float?)tp["lte_fdd_RSRP"];
                float? sSinr = (float?)tp["lte_fdd_SINR"];
                addCellInfo(outCellDic, tp, sCell, sRsrp, sSinr);

                for (int i = 0; i < 12; ++i)
                {
                    LTECell nCell = tp.GetNBCell_LTE_FDD(i);
                    float? nRsrp = (float?)tp["lte_fdd_NCell_RSRP"];
                    float? nSinr = (float?)tp["lte_fdd_NCell_SINR"];
                    addCellInfo(outCellDic, tp, nCell, nRsrp, nSinr);
                }
            }

            OutdoorCells.AddRange(outCellDic.Values);
            OutdoorCells.Sort();
            int sn = 0;
            foreach (LteLeakOutOutdoorCell outCell in OutdoorCells)
            {
                outCell.GetResult(isGetRoadInfo);
                outCell.SN = ++sn;
            }
        }

        protected void addCellInfo(Dictionary<LTECell, LteLeakOutOutdoorCell> outCellDic, TestPoint tp, LTECell cell, float? rsrp, float? sinr)
        {
            if (rsrp != null && cell != null && cell.Type == LTEBTSType.Outdoor)
            {
                LteLeakOutOutdoorCell outCell = null;
                if (!outCellDic.TryGetValue(cell, out outCell))
                {
                    outCell = new LteLeakOutOutdoorCell(cell);
                    outCellDic.Add(cell, outCell);
                }
                outCell.AddTestPoint(tp, (float)rsrp, sinr);
            }
        }
    }

    public class LteLeakOutOutdoorCell : LteLeakOutCell, IComparable<LteLeakOutOutdoorCell>
    {
        public LteLeakOutOutdoorCell(LTECell lteCell)
            : base(lteCell)
        {
        }

        public int SN
        {
            get;
            set;
        }

        public int CompareTo(LteLeakOutOutdoorCell other)
        {
            if (this == other || this.AvgRsrp == other.AvgRsrp) return 0;
            return this.AvgRsrp > other.AvgRsrp ? 1 : -1;
        }
    }

    //室分外泄*****************************************************************
    public class ZTDIYLeakOutCellSetByRegion_LteFdd : ZTDIYLeakOutCellSetByRegion_LTE
    {
        protected new Dictionary<LTECell, LteFddLeakOutIndoorCell> indoorCellDic = new Dictionary<LTECell, LteFddLeakOutIndoorCell>();

        private static ZTDIYLeakOutCellSetByRegion_LteFdd instance = null;
        public new static ZTDIYLeakOutCellSetByRegion_LteFdd GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTDIYLeakOutCellSetByRegion_LteFdd();
            }
            return instance;
        }

        protected ZTDIYLeakOutCellSetByRegion_LteFdd()
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "LTE_FDD_外泄分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26009, this.Name);
        }


        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup sampleGroup = new DIYSampleGroup();
            sampleGroup.ThemeName = "---";
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_SINR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_NCell_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_NCell_SINR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_NCell_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_NCell_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }

            return sampleGroup;
        }

        protected override void getResultAfterQuery()
        {
            int sn = 0;
            foreach (LteFddLeakOutIndoorCell indoorCell in this.indoorCellDic.Values)
            {
                indoorCell.SN = ++sn;
                indoorCell.GetResult(this.cond.IsGetRoadDesc);
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            if (indoorCellDic.Values.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LTELeakOutCellSetResultForm resultForm = MainModel.CreateResultForm(typeof(LTELeakOutCellSetResultForm)) as LTELeakOutCellSetResultForm;
            resultForm.FillData(new List<LteFddLeakOutIndoorCell>(indoorCellDic.Values));
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
            MainModel.FireSetDefaultMapSerialTheme("LTE_FDD:RSRP");

            indoorCellDic.Clear();
        }

        protected override void doWithDTData(TestPoint testPoint)
        {
            float? sRsrp = (float?)testPoint["lte_fdd_RSRP"];
            float? sSinr = (float?)testPoint["lte_fdd_SINR"];
            LTECell sLteCell = testPoint.GetMainCell_LTE_FDD();
            if (sRsrp == null)
            {
                return;
            }

            bool isLeakOutBySCell = false;
            if (this.cond.LeakOutAsMainCell && sLteCell != null && sLteCell.Type == LTEBTSType.Indoor)
            {
                LteFddLeakOutIndoorCell indoorCell = null;
                if (!indoorCellDic.TryGetValue(sLteCell, out indoorCell))
                {
                    indoorCell = new LteFddLeakOutIndoorCell(sLteCell);
                    indoorCellDic.Add(sLteCell, indoorCell);
                }
                indoorCell.AddTestPoint(testPoint, (float)sRsrp, sSinr, true);
                isLeakOutBySCell = true;
            }

            if (this.cond.LeakOutAsNCell && !isLeakOutBySCell)
            {
                addNCell(testPoint, sRsrp);
            }
        }

        private void addNCell(TestPoint testPoint, float? sRsrp)
        {
            for (int i = 0; i < 12; ++i)
            {
                float? nRsrp = (float?)testPoint["lte_fdd_NCell_RSRP"];
                if (nRsrp == null || nRsrp < this.cond.MinNCellRxlev || sRsrp - nRsrp > this.cond.DiffRxlev)
                {
                    break;
                }

                float? nSinr = (float?)testPoint["lte_fdd_NCell_SINR"];
                LTECell nLteCell = testPoint.GetNBCell_LTE_FDD(i);
                if (nLteCell == null || nLteCell.Type != LTEBTSType.Indoor)
                {
                    continue;
                }

                LteFddLeakOutIndoorCell indoorCell = null;
                if (!indoorCellDic.TryGetValue(nLteCell, out indoorCell))
                {
                    indoorCell = new LteFddLeakOutIndoorCell(nLteCell);
                    indoorCellDic.Add(nLteCell, indoorCell);
                }
                indoorCell.AddTestPoint(testPoint, (float)nRsrp, nSinr, false);
            }
        }
    }

    public class ZTDIYLeakOutCellSetByRegion_LteFdd_VOLTE : ZTDIYLeakOutCellSetByRegion_LteFdd
    {
        private static ZTDIYLeakOutCellSetByRegion_LteFdd_VOLTE instance = null;
        public new static ZTDIYLeakOutCellSetByRegion_LteFdd_VOLTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYLeakOutCellSetByRegion_LteFdd_VOLTE();
                    }
                }
            }
            return instance;
        }
        public ZTDIYLeakOutCellSetByRegion_LteFdd_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD外泄分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30011, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)tp.ServiceType)) return false;
            return true;
        }
    }
}
