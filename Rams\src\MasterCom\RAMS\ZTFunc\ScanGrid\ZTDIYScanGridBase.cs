﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyScanGridBase : DIYSQLBase
    {
        public ZTDiyScanGridBase(MainModel mainModel)
            : base(mainModel)
        {

        }

        protected override string getSqlTextString()
        {
            string sql = "";
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[0];
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
        }

        /// <summary>
        /// 判断栅格是否有效
        /// </summary>
        /// <param name="grid"></param>
        /// <returns></returns>
        protected virtual bool isValidGrid(ScanGridInfo grid)
        {
            return true;
        }
    }

    public class ZTDiyScanGridByFile : ZTDiyScanGridBase
    {
        public ZTDiyScanGridByFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        /// <summary>
        /// 已录入文件的栅格信息合集
        /// </summary>
        protected List<ScanGridInfo> scanGridInfoList = new List<ScanGridInfo>();

        /// <summary>
        /// 汇聚后的栅格信息合集
        /// </summary>
        protected List<ScanGridInfo> convergeScanGridInfoList = new List<ScanGridInfo>();

        public List<ScanGridInfo> GetScanGridInfos()
        {
            return scanGridInfoList;
        }

        protected FileInfo curFile;

        #region 基础数据重写
        public override string Name
        {
            get { return "扫频栅格(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33001, this.Name);
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                mainModel.FileInfos = Condition.FileInfos;
                return true;
            }
            return false;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        #endregion

        #region 查询流程
        protected override void query()
        {
            //根据选择的文件,查询已录入文件对应的栅格表
            if (mainModel.FileInfos.Count <= 0)
            {
                return;
            }

            //循环所有已录入文件,查询栅格信息并汇聚
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.Show("正在进行扫频栅格汇聚...", queryInThread, clientProxy);
                fireShowForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                scanGridInfoList = new List<ScanGridInfo>();
                foreach (var file in mainModel.FileInfos)
                {
                    curFile = file;
                    Package package = clientProxy.Package;
                    string strsql = getSqlTextString();
                    E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
                    package.Command = Command.DIYSearch;//枚举类型：DIY接口
                    package.SubCommand = SubCommand.Request;//枚举类型：请求
                    if (MainDB)
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                    }
                    else
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                    }
                    package.Content.PrepareAddParam();
                    package.Content.AddParam(strsql);
                    StringBuilder sb = new StringBuilder();
                    if (retArrDef != null)
                    {
                        for (int i = 0; i < retArrDef.Length; i++)
                        {
                            sb.Append((int)retArrDef[i]);
                            sb.Append(",");
                        }
                    }

                    package.Content.AddParam(sb.ToString().TrimEnd(','));
                    clientProxy.Send();
                    receiveRetData(clientProxy);
                }
                ConvergeScanGridInfo();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strbSql = new StringBuilder();
                string tableName = NbIotMgrsQueryFuncBase.ModelTableprefix + curFile.StrWeek;
                strbSql.AppendFormat(@"SELECT ifileid,bms,itime,wtimems,strMGRTIndex,itllongitude,itllatitude,
ibrlongitude,ibrlatitude,iGridSize,EARFCN,PCI,SSS_RSSI,SSS_RP,R0_RP,R0_RQ,R0_CINR,iSampleCount
from {0} where ifileid = {1} ", tableName, curFile.ID);
            
            string sql = strbSql.ToString();
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[18];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Byte;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Short;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_IntFloat;
            rType[13] = E_VType.E_IntFloat;
            rType[14] = E_VType.E_IntFloat;
            rType[15] = E_VType.E_IntFloat;
            rType[16] = E_VType.E_IntFloat;
            rType[17] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ScanGridInfo info = ScanGridInfo.Fill(package.Content);
                    scanGridInfoList.Add(info);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
        #endregion

        /// <summary>
        /// 数据结果汇聚
        /// </summary>
        protected virtual void ConvergeScanGridInfo()
        {
            Dictionary<string, Dictionary<int, ScanGridInfo>> convergeDic = ScanGridInfo.ConvergeScanGridInfo(scanGridInfoList);

            List<ScanGridInfo> list = new List<ScanGridInfo>();
            foreach (var grid in convergeDic.Values)
            {
                List<ScanGridInfo> cellGridList = new List<ScanGridInfo>();
                foreach (var gridInfo in grid.Values)
                {
                    if (isValidGrid(gridInfo))
                    {
                        cellGridList.Add(gridInfo);
                    }
                }
                //按小区场强排序
                cellGridList.Sort((x, y) => { return -x.R0_RP.CompareTo(y.R0_RP); });
                list.AddRange(cellGridList);
            }
            //按栅格序号排序
            list.Sort((x, y) => { return x.MGRTIndex.CompareTo(y.MGRTIndex); });
            convergeScanGridInfoList = list;
        }

        /// <summary>
        /// 显示结果窗体
        /// </summary>
        protected virtual void fireShowForm()
        {
            ZTDIYScanGridResultForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTDIYScanGridResultForm)) as ZTDIYScanGridResultForm;
            frm.FillData(convergeScanGridInfoList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class ZTDiyScanGridByRegion : ZTDiyScanGridByFile
    {
        public ZTDiyScanGridByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        /// <summary>
        /// 地图框选的区域
        /// </summary>
        protected DbRect selectRect;

        #region 基础数据重写
        public override string Name
        {
            get { return "扫频栅格(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33002, this.Name);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        #endregion

        protected override void query()
        {
            selectRect = condition.Geometorys.RegionBounds;
            if (selectRect == null)
            {
                return;
            }

            condition.ServiceTypes.Clear();
            //NBIOT
            condition.ServiceTypes.Add(55);

            //查询区域内文件
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();

            //循环所有已录入文件,查询栅格信息并汇聚
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在进行扫频栅格汇聚...", queryInThread, clientProxy);
                fireShowForm();
            }
            finally
            {
                clientProxy.Close();
            }

        }

        protected override bool isValidGrid(ScanGridInfo grid)
        {
            //过滤不在框选区域内的栅格
            if (grid.Within(selectRect))
            {
                return true;
            }
            return false;
        }
    }

    /// <summary>
    /// 小区栅格信息类
    /// </summary>
    public class ScanGridInfo
    {
        private int fileID = 0;
        public int FileID
        {
            get { return fileID; }
        }
        private short bms = 0;
        public short BMS
        {
            get { return bms; }
        }
        private int iTime = 0;
        public int Time
        {
            get { return iTime; }
        }
        private short wTimems = 0;
        public short WTimems
        {
            get { return wTimems; }
        }
        private string strMGRTIndex = "";
        public string MGRTIndex
        {
            get { return strMGRTIndex; }
        }
        private double itllongitude = 0;
        public double TLLongitude
        {
            get { return itllongitude; }
        }
        private double itllatitude = 0;
        public double TLLatitude
        {
            get { return itllatitude; }
        }
        private double ibrlongitude = 0;
        public double BRLongitude
        {
            get { return ibrlongitude; }
        }
        private double ibrlatitude = 0;
        public double BRLatitude
        {
            get { return ibrlatitude; }
        }
        private int iGridSize = 0;
        public int GridSize
        {
            get { return iGridSize; }
        }
        private int earfcn = 0;
        public int EARFCN
        {
            get { return earfcn; }
        }
        private int pci = 0;
        public int PCI
        {
            get { return pci; }
        }
        private float sss_rssi = 0;
        public float SSS_RSSI
        {
            get { return sss_rssi; }
        }
        private float sss_rp = 0;
        public float SSS_RP
        {
            get { return sss_rp; }
        }
        private float r0_rp = 0;
        public float R0_RP
        {
            get { return r0_rp; }
        }
        private float r0_rq = 0;
        public float R0_RQ
        {
            get { return r0_rq; }
        }
        private float r0_cinr = 0;
        public float R0_CINR
        {
            get { return r0_cinr; }
        }
        private int iSampleCount = 0;
        public int SampleCount
        {
            get { return iSampleCount; }
        }
        public double CentLng
        {
            get { return (TLLongitude + BRLongitude) / 2; }
        }
        public double CentLat
        {
            get { return (TLLatitude + BRLatitude) / 2; }
        }

        public string Token { get; private set; }

        public static ScanGridInfo Fill(Content content)
        {
            ScanGridInfo info = new ScanGridInfo();
            info.fileID = content.GetParamInt();
            info.bms = content.GetParamByte();
            info.iTime = content.GetParamInt();
            info.wTimems = content.GetParamShort();
            info.strMGRTIndex = content.GetParamString();
            info.itllongitude = (content.GetParamInt() / 10000000.0);
            info.itllatitude = (content.GetParamInt() / 10000000.0);
            info.ibrlongitude = (content.GetParamInt() / 10000000.0);
            info.ibrlatitude = (content.GetParamInt() / 10000000.0);
            info.iGridSize = content.GetParamInt();
            info.earfcn = content.GetParamInt();
            info.pci = content.GetParamInt();
            info.sss_rssi = content.GetParamFloat();
            info.sss_rp = content.GetParamFloat();
            info.r0_rp = content.GetParamFloat();
            info.r0_rq = content.GetParamFloat();
            info.r0_cinr = content.GetParamFloat();
            info.iSampleCount = content.GetParamInt();
            info.Token = info.earfcn + "_" + info.pci;
            return info;
        }

        /// <summary>
        /// 将从数据库查询到的多个文件的数据按栅格小区进行汇聚
        /// </summary>
        /// <param name="scanGridInfoList">数据源</param>
        /// <returns></returns>
        public static Dictionary<string, Dictionary<int, ScanGridInfo>> ConvergeScanGridInfo(List<ScanGridInfo> scanGridInfoList)
        {
            Dictionary<string, Dictionary<int, ScanGridInfo>> convergeDic = new Dictionary<string, Dictionary<int, ScanGridInfo>>();
            Dictionary<int, ScanGridInfo> gridInfoDic;

            foreach (ScanGridInfo scanGridInfo in scanGridInfoList)
            {
                if (!convergeDic.ContainsKey(scanGridInfo.MGRTIndex))
                {
                    gridInfoDic = new Dictionary<int, ScanGridInfo>();
                    int gridKey = scanGridInfo.earfcn * 1000 + scanGridInfo.pci;
                    gridInfoDic.Add(gridKey, scanGridInfo);
                    convergeDic.Add(scanGridInfo.MGRTIndex, gridInfoDic);
                }
                else
                {
                    gridInfoDic = convergeDic[scanGridInfo.MGRTIndex];
                    int gridKey = scanGridInfo.earfcn * 1000 + scanGridInfo.pci;
                    if (!gridInfoDic.ContainsKey(gridKey))
                    {
                        gridInfoDic.Add(gridKey, scanGridInfo);
                    }
                    else
                    {
                        int srcSampleCount = gridInfoDic[gridKey].iSampleCount;
                        int addSampleCount = scanGridInfo.iSampleCount;

                        gridInfoDic[gridKey].r0_cinr = (scanGridInfo.r0_cinr * addSampleCount + gridInfoDic[gridKey].r0_cinr * srcSampleCount) / (addSampleCount + srcSampleCount);
                        gridInfoDic[gridKey].r0_rp = (scanGridInfo.r0_rp * addSampleCount + gridInfoDic[gridKey].r0_rp * srcSampleCount) / (addSampleCount + srcSampleCount);
                        gridInfoDic[gridKey].r0_rq = (scanGridInfo.r0_rq * addSampleCount + gridInfoDic[gridKey].r0_rq * srcSampleCount) / (addSampleCount + srcSampleCount);
                        gridInfoDic[gridKey].sss_rp = (scanGridInfo.sss_rp * addSampleCount + gridInfoDic[gridKey].sss_rp * srcSampleCount) / (addSampleCount + srcSampleCount);
                        gridInfoDic[gridKey].sss_rssi = (scanGridInfo.sss_rssi * addSampleCount + gridInfoDic[gridKey].sss_rssi * srcSampleCount) / (addSampleCount + srcSampleCount);
                        gridInfoDic[gridKey].iSampleCount = addSampleCount + srcSampleCount;
                    }
                }
            }

            return convergeDic;
        }

        public static List<ScanGridInfo> ConvergeMaxGridInfo(List<ScanGridInfo> scanGridInfoList)
        {
            Dictionary<string, List<ScanGridInfo>> convergeDic = new Dictionary<string, List<ScanGridInfo>>();
            //按栅格汇聚
            foreach (ScanGridInfo scanGridInfo in scanGridInfoList)
            {
                if (!convergeDic.ContainsKey(scanGridInfo.MGRTIndex))
                {
                    List<ScanGridInfo> gridInfo = new List<ScanGridInfo>();
                    gridInfo.Add(scanGridInfo);
                    convergeDic.Add(scanGridInfo.MGRTIndex, gridInfo);
                }
                else
                {
                    convergeDic[scanGridInfo.MGRTIndex].Add(scanGridInfo);
                }
            }
            //保存栅格最强rsrp小区
            List<ScanGridInfo> maxRSRPGrid = new List<ScanGridInfo>();
            foreach (var item in convergeDic)
            {
                item.Value.Sort((x, y) => { return -x.R0_RP.CompareTo(y.R0_RP); });
                maxRSRPGrid.Add(item.Value[0]);
            }
            return maxRSRPGrid;
        }

        public bool Within(DbRect rect)
        {
            if (BRLongitude < rect.x1 || TLLongitude > rect.x2 || TLLatitude < rect.y1 || BRLatitude > rect.y2)
            {
                return false;
            }
            return true;
        }
    }
}
