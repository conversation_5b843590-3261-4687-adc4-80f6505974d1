﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCallEndConditionDlg : BaseDialog
    {
        public NRCallEndConditionDlg()
            : base()
        {
            InitializeComponent();
        }

        public void GetCondition(out float sec)
        {
            sec = (float)numMaxDelaySec.Value;
        }

        public void SetCondition(float sec)
        {
            numMaxDelaySec.Value = (decimal)sec;
        }

    }
}
