﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public enum LTEAntennaDirectionType
    {
        Omni = 1,
        Beam
    }

    public class LTEAntenna : Snapshot<LTEAntenna>
    {
        public LTEAntenna()
        {
            Value = this;
        }

        public double Longitude { get; set; }

        public double Latitude { get; set; }

        public double EndPointLongitude
        {
            get
            {
                double offset = 0;
                if (DirectionType == LTEAntennaDirectionType.Beam)
                {
                    offset = Math.Cos(-(Direction - 90) * Math.PI / 180) * 0.000024
                        * MasterCom.RAMS.Func.MapLTECellLayer.CellDefaultDisplayLength
                        * MasterCom.RAMS.Func.MapLTECellLayer.ShapeLengthScale
                        * (this.Cell != null ? this.Cell.LengthRatioByFreq : 1);
                }
                return Longitude + offset;
            }
        }

        public double EndPointLatitude
        {
            get
            {
                double offset = 0;
                if (DirectionType == LTEAntennaDirectionType.Beam)
                {
                    offset = Math.Sin(-(Direction - 90) * Math.PI / 180) * 0.000024
                        * MasterCom.RAMS.Func.MapLTECellLayer.CellDefaultDisplayLength
                        * MasterCom.RAMS.Func.MapLTECellLayer.ShapeLengthScale
                    * (this.Cell != null ? this.Cell.LengthRatioByFreq : 1);
                }
                return Latitude + offset;
            }
        }

        public LTEBTSType Type
        {
            get { return Cell.Type; }
        }

        public LTEAntennaDirectionType DirectionType
        {//应各地要求，360°时，显示为正北方向
            get
            {
                setDirectionType();
                return directionType;
            }
            set { directionType =value; }
        }

        private void setDirectionType()
        {
            if (Type == LTEBTSType.Indoor)
            {
                directionType = LTEAntennaDirectionType.Omni;
            }
            else
            {
                directionType = LTEAntennaDirectionType.Beam;
            }
        }

        public int DirectionTypeInt
        {
            get { return (int)directionType; }
            set
            {
                try { directionType = (LTEAntennaDirectionType)value; }
                catch
                {
                    //continue
                }
            }
        }
        /// <summary>
        /// 与正北方向，顺时针的夹角
        /// </summary>
        public short Direction { get; set; }

        public short Downward { get; set; }

        public int Altitude { get; set; }

        public string Description { get; set; }

        private LTECell cell;
        public LTECell Cell
        {
            get
            {
                return cell;
            }
            set
            {
                cell = value;
                value.Antennas.Add(this);
            }
        }

        public string SimpleInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();
                info.Append("DirectionType:").Append(DirectionType);
                info.Append(" Direction:").Append(Direction);
                info.Append(" Downward:").Append(Downward);
                info.Append(" Altitude:").Append(Altitude);
                return info.ToString();
            }
        }

        public string DetailInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();
                info.Append(Cell.DetailInfo);
                info.Append("\r\nLongitude:").Append(Longitude);
                info.Append("\r\nLatitude:").Append(Latitude);
                info.Append("\r\nDirectionType:").Append(DirectionType);
                info.Append("\r\nDirection:").Append(Direction);
                info.Append("\r\nDownward:").Append(Downward);
                info.Append("\r\nAltitude:").Append(Altitude);
                info.Append("\r\nDescription:").Append(Description);
                info.Append("\r\n");
                return info.ToString();
            }
        }

        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }

        public void Fill(MasterCom.RAMS.Net.Content content, CellManager cellManager)
        {
            base.Fill(content.GetParamInt(), content.GetParamInt(), content.GetParamInt());
            int cellID = content.GetParamInt();
            foreach (LTECell lteCell in cellManager.GetLTECells(cellID, ValidPeriod))
            {
                if (lteCell.ValidPeriod.Contains(ValidPeriod.BeginTime) || ValidPeriod.IsIntersect(lteCell.ValidPeriod))
                {
                    this.Cell = lteCell;
                }
            }
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            Direction = (short)content.GetParamInt();
            Downward = (short)content.GetParamInt();
            Altitude = content.GetParamInt();
            Description = content.GetParamString();
            if ((Longitude < 60 || Latitude < 10) && this.Cell != null)
            {
                Longitude = this.Cell.Longitude;
                Latitude = this.Cell.Latitude;
            }
        }
        

        private LTEAntennaDirectionType directionType;
    }
}
