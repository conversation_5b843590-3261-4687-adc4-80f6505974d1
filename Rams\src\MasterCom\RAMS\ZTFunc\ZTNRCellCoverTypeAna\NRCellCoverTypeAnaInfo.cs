﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRCellCoverTypeAnaInfo
    {
        public NRCellCoverTypeAnaInfo(NRCell cell, TestPoint testPoint)
        {
            Cell = cell;
            AddTestPoint(testPoint);
        }

        public NRCell Cell { get; set; }
        public string CellName { get; set; }
        public int CellID { get; set; }
        public int ARFCN { get; set; }
        public int PCI { get; set; }
        public int TAC { get; set; }
        public long NCI { get; set; }
        public int TestPointCount { get; set; }
        public int ServiceTimes { get; set; }
        public int Category { get; set; }

        public double ServiceSeconds { get; private set; }
        public string CategoryDesc { get; private set; }

        private TestPoint lastTP;

        public void AddTestPoint(TestPoint testPoint)
        {
            if (testPoint == null)
            {
                return;
            }
            TestPointCount++;
            if (lastTP != null)
            {
                TimeSpan ts = testPoint.DateTime - lastTP.DateTime;
                ServiceSeconds += ts.TotalSeconds;
            }
            lastTP = testPoint;
        }

        public void Calcluate()
        {
            CellName = Cell.Name;
            ARFCN = Cell.SSBARFCN;
            PCI = Cell.PCI;
            TAC = Cell.TAC;
            NCI = Cell.NCI;
            CellID = Cell.ID;
            CategoryDesc = getCategoryDesc();
        }

        private string getCategoryDesc()
        {
            switch (Category)
            {
                case 0:
                    return "A";
                case 1:
                    return "B";
                case 2:
                    return "C";
                default:
                    return "";
            }
        }
    }

    public class NRRegionCellCoverTypeAnaInfo
    {
        public NRRegionCellCoverTypeAnaInfo(string regionName, List<NRCellCoverTypeAnaInfo> cellList)
        {
            this.RegionName = regionName;
            CellServiceConditions = cellList;
        }

        public string RegionName { get; set; }
        public List<NRCellCoverTypeAnaInfo> CellServiceConditions { get; set; }
    }
}
