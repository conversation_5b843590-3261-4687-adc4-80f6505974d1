﻿using DevExpress.XtraCharts;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func.PopShow
{
    public static class KPIInfoPanelHelper
    {
        #region GIS
        public static void FreshShowChart_GIS(int colIndex, FreshShowChartControl control, AxisRange defaultRangeX, AxisRange defaultRangeY, int kpihowType, int sortAreaType)
        {
            control.Chart.Legend.AlignmentHorizontal = LegendAlignmentHorizontal.Right;
            control.Chart.Legend.AlignmentVertical = LegendAlignmentVertical.TopOutside;
            control.Chart.Legend.Direction = LegendDirection.LeftToRight;
            control.Chart.Legend.Visible = true;
            string selShowType = control.CbxShowType.SelectedItem as string;

            if (selShowType == "最近一周" || selShowType == "最近一月")
            {
                freshShowChartByLastWeekOrMoth(colIndex, control.Chart, control.Dgv, selShowType, kpihowType, sortAreaType);
            }
            else if (selShowType == "按周" || selShowType == "按天")
            {
                string showCont = control.CbxContentType.Text;
                if (showCont == "(全部)")//显示多个序列
                {
                    freshShowChartByWeekOrDay(colIndex, control.Chart, control.Dgv, selShowType);
                }
                else
                {
                    freshShowChartByWeekOrDayOther(colIndex, control.Chart, control.Dgv, selShowType);
                }
            }
            else if (selShowType == "按月")//柱形图，x轴label为地市名，series对应月份
            {
                freshShowChartByMonth(colIndex, control.Chart, control.Dgv, selShowType);
            }
            if (control.Chart.Diagram != null)
            {
                ((ISupportInitialize)control.Chart).BeginInit();
                ((XYDiagram)(control.Chart.Diagram)).AxisX.Range.Assign(defaultRangeX);
                ((XYDiagram)(control.Chart.Diagram)).AxisY.Range.Assign(defaultRangeY);
                ((ISupportInitialize)control.Chart).EndInit();
            }
        }

        private static void freshShowChartByLastWeekOrMoth(int colIndex, ChartControl chartControl, DataGridView dataGridView, string selShowType, int KPIShowType, int sortAreaType)
        {
            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = selShowType + " " + dataGridView.Columns[colIndex].HeaderText;
            chartControl.Titles.Add(title);

            Series series1 = new Series(dataGridView.Columns[colIndex].HeaderText, ViewType.Bar);
            double[] doubles;
            string[] labels;
            int lblCol = 1;
            if (KPIShowType == 2)
            {
                lblCol = sortAreaType == 1 ? 2 : 4;
            }
            bool isAllInt = extractLabValues(colIndex, lblCol, out doubles, out labels, dataGridView);
            for (int i = 0; i < labels.Length; i++)
            {
                series1.Points.Add(new SeriesPoint(labels[i], new double[] { doubles[i] }));
            }
            series1.PointOptions.ValueNumericOptions.Precision = isAllInt ? 0 : 2;
            series1.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
            series1.Label.ResolveOverlappingMode = ResolveOverlappingMode.HideOverlapped;
            series1.Visible = true;
            chartControl.Series.Clear();
            chartControl.Series.Add(series1);

            ScaleBreakOptions optionScale = ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisY.ScaleBreakOptions;
            optionScale.Style = ScaleBreakStyle.Waved;
            optionScale.SizeInPixels = 4;
            AutoScaleBreaks autoScaleBreaks = ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisY.AutoScaleBreaks;
            autoScaleBreaks.MaxCount = 1;
            autoScaleBreaks.Enabled = true;

            if (series1.Points.Count > 5)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 270;
            }
            else if (series1.Points.Count > 0)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 0;
            }
            series1.Label.Visible = true;
        }
        #endregion

        #region ng
        public static void FreshShowChart_ng(int colIndex, FreshShowChartControl control, AxisRange defaultRangeX, AxisRange defaultRangeY)
        {
            control.Chart.Legend.AlignmentHorizontal = LegendAlignmentHorizontal.Right;
            control.Chart.Legend.AlignmentVertical = LegendAlignmentVertical.TopOutside;
            control.Chart.Legend.Direction = LegendDirection.LeftToRight;
            control.Chart.Legend.Visible = true;
            string selShowType = control.CbxShowType.SelectedItem as string;
            try
            {
                if (selShowType == "最近一周" || selShowType == "最近一月")
                {
                    freshShowChartByLastWeekOrMoth(colIndex, control.Chart, control.Dgv, selShowType);
                }
                else if (selShowType == "按周" || selShowType == "按天")
                {
                    string showCont = control.CbxContentType.Text;
                    if (showCont == "(全部)")//显示多个序列
                    {
                        freshShowChartByWeekOrDay(colIndex, control.Chart, control.Dgv, selShowType);
                    }
                    else
                    {
                        freshShowChartByWeekOrDayOther(colIndex, control.Chart, control.Dgv, selShowType);
                    }
                }
                else if (selShowType == "按月")//柱形图，x轴label为地市名，series对应月份
                {
                    freshShowChartByMonth(colIndex, control.Chart, control.Dgv, selShowType);
                }
                if (control.Chart.Diagram != null)
                {
                    ((ISupportInitialize)control.Chart).BeginInit();
                    ((XYDiagram)(control.Chart.Diagram)).AxisX.Range.Assign(defaultRangeX);
                    ((XYDiagram)(control.Chart.Diagram)).AxisY.Range.Assign(defaultRangeY);
                    ((ISupportInitialize)control.Chart).EndInit();
                }
            }
            catch
            {
                //continue
            }
        }

        private static void freshShowChartByWeekOrDayOther(int colIndex, ChartControl chartControl, DataGridView dataGridView, string selShowType)
        {
            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = dataGridView.Columns[colIndex].HeaderText + " " + selShowType;
            chartControl.Titles.Add(title);

            Series series1 = new Series(dataGridView.Columns[colIndex].HeaderText, ViewType.Bar);

            double[] doubles;
            string[] labels;
            bool isAllInt = extractLabValues(colIndex, 0, out doubles, out labels, dataGridView);
            for (int i = 0; i < labels.Length; i++)
            {
                series1.Points.Add(new SeriesPoint(labels[i], new double[] { doubles[i] }));
            }
            series1.PointOptions.ValueNumericOptions.Precision = isAllInt ? 0 : 2;
            series1.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
            series1.Label.ResolveOverlappingMode = ResolveOverlappingMode.HideOverlapped;
            series1.Visible = true;
            chartControl.Series.Clear();
            chartControl.Series.Add(series1);
            if (labels.Length > 5)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 270;
                //((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Visible = false;
            }
            else if (labels.Length > 0)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 0;
                // ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Visible = true;
            }
        }
        #endregion

        #region ng_total
        public static bool FreshShowChart_ng_total(int colIndex, FreshShowChartControl control, AxisRange defaultRangeX, AxisRange defaultRangeY)
        {
            if (colIndex >= control.Dgv.Columns.Count)
            {
                return false;
            }
            if (control.Dgv.Rows.Count <= 30) //大于30行不显示
            {
                control.Chart.Legend.AlignmentHorizontal = LegendAlignmentHorizontal.Right;
                control.Chart.Legend.AlignmentVertical = LegendAlignmentVertical.TopOutside;
                control.Chart.Legend.Direction = LegendDirection.LeftToRight;
                control.Chart.Legend.Visible = true;
                string selShowType = control.CbxShowType.SelectedItem as string;
                if (selShowType == "最近一周" || selShowType == "最近一月" || selShowType == "本月" || selShowType == "本周" || selShowType == "今天" || selShowType == "前一周" || selShowType == "前一月" || selShowType == "前一天")
                {
                    freshShowChartByLastWeekOrMoth(colIndex, control.Chart, control.Dgv, selShowType);
                }
                else if (selShowType == "按周" || selShowType == "按天")
                {
                    freshShowChartByWeekOrDay(colIndex, control.Chart, control.Dgv, selShowType);
                }
                else if (selShowType == "按月")//柱形图，x轴label为地市名，series对应月份
                {
                    freshShowChartByMonth(colIndex, control.Chart, control.Dgv, selShowType);
                }
                if (control.Chart.Diagram != null)
                {
                    ((ISupportInitialize)control.Chart).BeginInit();
                    ((XYDiagram)(control.Chart.Diagram)).AxisX.Range.Assign(defaultRangeX);
                    ((XYDiagram)(control.Chart.Diagram)).AxisY.Range.Assign(defaultRangeY);
                    ((ISupportInitialize)control.Chart).EndInit();
                }
                return true;
            }
            else
            {
                control.Chart.Titles.Clear();
                control.Chart.Series.Clear();
                ChartTitle title = new ChartTitle();
                title.Text = "报表数据量大，不显示图表";
                control.Chart.Titles.Add(title);
                return false;
            }
        }
        #endregion

        private static void freshShowChartByLastWeekOrMoth(int colIndex, ChartControl chartControl, DataGridView dataGridView, string selShowType)
        {
            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = selShowType + " " + dataGridView.Columns[colIndex].HeaderText;
            chartControl.Titles.Add(title);

            Series series1 = new Series(dataGridView.Columns[colIndex].HeaderText, ViewType.Bar);
            double[] doubles;
            string[] labels;
            bool isAllInt = extractLabValues(colIndex, 1, out doubles, out labels, dataGridView);
            for (int i = 0; i < labels.Length; i++)
            {
                series1.Points.Add(new SeriesPoint(labels[i], new double[] { doubles[i] }));
            }
            series1.PointOptions.ValueNumericOptions.Precision = isAllInt ? 0 : 2;
            series1.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
            series1.Label.ResolveOverlappingMode = ResolveOverlappingMode.HideOverlapped;
            series1.Visible = true;
            chartControl.Series.Clear();
            chartControl.Series.Add(series1);

            ScaleBreakOptions optionScale = ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisY.ScaleBreakOptions;
            optionScale.Style = ScaleBreakStyle.Waved;
            optionScale.SizeInPixels = 4;
            AutoScaleBreaks autoScaleBreaks = ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisY.AutoScaleBreaks;
            autoScaleBreaks.MaxCount = 1;
            autoScaleBreaks.Enabled = true;

            if (series1.Points.Count > 5)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 270;
            }
            else if (series1.Points.Count > 0)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 0;
            }
            series1.Label.Visible = true;
        }

        private static void freshShowChartByWeekOrDay(int colIndex, ChartControl chartControl, DataGridView dataGridView, string selShowType)
        {
            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = dataGridView.Columns[colIndex].HeaderText + selShowType + "情况";
            chartControl.Titles.Add(title);
            chartControl.Series.Clear();
            Dictionary<string, Series> areaSeriesDic = new Dictionary<string, Series>();
            for (int r = 0; r < dataGridView.Rows.Count; r++)
            {
                string namelabel = dataGridView.Rows[r].Cells[1].Value.ToString();//名称列
                double vdouble = 0;
                string labelStr = "";
                bool isAllInt = extractLabValue(r, colIndex, 0, out vdouble, out labelStr, dataGridView);
                Series nmBar;
                if (!areaSeriesDic.TryGetValue(namelabel, out nmBar))
                {
                    nmBar = new Series(namelabel, ViewType.Bar);
                    nmBar.PointOptions.ValueNumericOptions.Precision = isAllInt ? 0 : 2;
                    nmBar.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
                    nmBar.Label.ResolveOverlappingMode = ResolveOverlappingMode.HideOverlapped;
                    chartControl.Series.Add(nmBar);
                    areaSeriesDic[namelabel] = nmBar;
                }
                nmBar.Points.Add(new SeriesPoint(labelStr, new double[] { vdouble }));
            }
            if (chartControl.Series.Count > 0)
            {
                ScaleBreakOptions optionScale = ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisY.ScaleBreakOptions;
                optionScale.Style = ScaleBreakStyle.Waved;
                optionScale.SizeInPixels = 4;
                AutoScaleBreaks autoScaleBreaks = ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisY.AutoScaleBreaks;
                autoScaleBreaks.MaxCount = 1;
                autoScaleBreaks.Enabled = true;
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).DefaultPane.Weight = 100;
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).EnableAxisXScrolling = true;
                if (dataGridView.Rows.Count > 5)
                {
                    ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 270;
                }
                else
                {
                    ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 0;
                }
            }
        }

        private static void freshShowChartByMonth(int colIndex, ChartControl chartControl, DataGridView dataGridView, string selShowType)
        {
            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = dataGridView.Columns[colIndex].HeaderText + selShowType + "情况";
            chartControl.Titles.Add(title);
            chartControl.Series.Clear();
            Dictionary<string, Series> timeSeriesDic = new Dictionary<string, Series>();
            for (int r = 0; r < dataGridView.Rows.Count; r++)
            {
                string namelabel = dataGridView.Rows[r].Cells[0].Value.ToString();//时间列
                string pointlabel = dataGridView.Rows[r].Cells[1].Value.ToString();//名称列
                double vdouble = 0;
                string labelStr = "";
                extractLabValue(r, colIndex, 0, out vdouble, out labelStr, dataGridView);
                Series nmBar;
                if (!timeSeriesDic.TryGetValue(namelabel, out nmBar))
                {
                    nmBar = new Series(namelabel, ViewType.Bar);//增加新的时间序列
                    nmBar.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
                    nmBar.Label.ResolveOverlappingMode = ResolveOverlappingMode.HideOverlapped;
                    chartControl.Series.Add(nmBar);
                    timeSeriesDic[namelabel] = nmBar;
                }
                nmBar.Points.Add(new SeriesPoint(pointlabel, new double[] { vdouble }));//各个地市的序列点
            }
            if (chartControl.Series.Count > 0)
            {
                ScaleBreakOptions optionScale = ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisY.ScaleBreakOptions;
                optionScale.Style = ScaleBreakStyle.Waved;
                optionScale.SizeInPixels = 4;
                AutoScaleBreaks autoScaleBreaks = ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisY.AutoScaleBreaks;
                autoScaleBreaks.MaxCount = 1;
                autoScaleBreaks.Enabled = true;
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).DefaultPane.Weight = 100;
                ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).EnableAxisXScrolling = true;
                if (dataGridView.Rows.Count > 5)
                {
                    ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 270;
                }
                else
                {
                    ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 0;
                }
            }
        }

        private static bool extractLabValue(int rowAt, int vColumn, int labelColumn, out double doublev, out string labelv, DataGridView dataGridView)
        {
            bool isAllInt = true;
            doublev = 0;
            object v = dataGridView.Rows[rowAt].Cells[vColumn].Value;
            if (v is int)
            {
                doublev = (int)v;
            }
            else if (v is double)
            {
                double db = (double)v;
                doublev = db;
                isAllInt = false;
            }
            else if (v is float)
            {
                float db = (float)v;
                doublev = db;
                isAllInt = false;
            }
            string vlabel = dataGridView.Rows[rowAt].Cells[labelColumn].Value.ToString();
            labelv = vlabel;
            return isAllInt;
        }

        private static bool extractLabValues(int vColumn, int labelColumn, out double[] doubles, out string[] labels, DataGridView dataGridView)
        {
            bool isAllInt = true;
            doubles = new double[dataGridView.Rows.Count];
            labels = new string[dataGridView.Rows.Count];
            for (int r = 0; r < dataGridView.Rows.Count; r++)
            {
                object v = dataGridView.Rows[r].Cells[vColumn].Value;
                if (v is int)
                {
                    doubles[r] = (int)v;
                }
                else if (v is double)
                {
                    double db = (double)v;
                    doubles[r] = db;
                    isAllInt = false;
                }
                else if (v is float)
                {
                    doubles[r] = (float)v;
                    isAllInt = false;
                }
                string vlabel = dataGridView.Rows[r].Cells[labelColumn].Value.ToString();
                labels[r] = vlabel;
            }
            return isAllInt;
        }
    }

    public class FreshShowChartControl
    {
        public ChartControl Chart { get; set; }
        public DataGridView Dgv { get; set; }
        public ComboBox CbxShowType { get; set; }
        public DevExpress.XtraEditors.CheckedComboBoxEdit CbxContentType { get; set; }

        public FreshShowChartControl(ChartControl chart, DataGridView dgv, ComboBox cbxShowType, DevExpress.XtraEditors.CheckedComboBoxEdit cbxContentType)
        {
            Chart = chart;
            Dgv = dgv;
            CbxShowType = cbxShowType;
            CbxContentType = cbxContentType;
        }
    }
}
