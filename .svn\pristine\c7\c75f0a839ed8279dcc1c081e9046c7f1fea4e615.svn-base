﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanHighCoverateRoadInfo
    {
        //public NRScanHighCoverateRoadInfo(ShowCoverage type)
        //{
        //    this.Type = type;
        //}
        //public ShowCoverage Type { get; set; }
        public int SN { get; set; }
        public int FileID { get; set; }
        public string FileName { get; set; } = "";
        public string RoadName { get; set; } = "";

        public List<NRScanHighCoverateRoadPointInfo> SampleList { get; set; } = new List<NRScanHighCoverateRoadPointInfo>();

        public int SampleCount { get; private set; }

        public double Distance { get; set; }
        public double Percent { get; set; }

        public double LongitudeMid { get; private set; }
        public double LatitudeMid { get; private set; }

        public AvgInfo Rsrp { get; set; } = new AvgInfo();

        public string FirstTime { get; private set; }
        public string LastTime { get; private set; }

        public void Calculate()
        {
            Rsrp.Calculate();
            SampleCount = SampleList.Count;
            if (SampleList.Count > 0)
            {
                FileID = SampleList[0].Tp.FileID;
                FileName = SampleList[0].Tp.FileName;

                FirstTime = SampleList[0].Tp.TimeStringWithMillisecond;
                LastTime = SampleList[SampleList.Count - 1].Tp.TimeStringWithMillisecond;

                int index = SampleList.Count / 2;
                LongitudeMid = SampleList[index].Tp.Longitude;
                LatitudeMid = SampleList[index].Tp.Latitude;
                RoadName = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(LongitudeMid, LatitudeMid);
            }
            Distance = Math.Round(Distance, 2);
            Percent = Math.Round(Percent, 2);
        }
    }

    public class NRScanHighCoverateRoadPointInfo
    {
        public int SN { get; set; }
        public TestPoint Tp { get; set; }
        public List<NRScanHighCoverateRoadCellInfo> CellList { get; set; }

        public NRScanHighCoverateRoadPointInfo(int SN, TestPoint tp
            , List<NRScanHighCoverateRoadCellInfo> cellList)
        {
            this.SN = SN;
            this.Tp = tp;
            this.CellList = cellList;
        }
    }

    public class NRScanHighCoverateRoadCellInfo
    {
        public int SN { get; set; }
        public NRCell Cell { get; set; }
        public string CellName { get; set; } = "";
        public string TAC { get; set; }
        public string NCI { get; set; }
        public int ARFCN { get; set; }
        public int PCI { get; set; }
        public string Longitude { get; set; }
        public string Latitude { get; set; }
        public float Rsrp { get; set; }
        public bool IsValid { get; private set; } = false;
        public string Distance { get; set; }

        //public string CellTypeDesc { get; set; }

        public NRScanHighCoverateRoadCellInfo(TestPoint tp, int index)
        {
            this.SN = index + 1;

            float? rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(tp, index);
            int? earfcn = (int?)NRTpHelper.NrScanTpManager.GetEARFCN(tp, index);
            int? pci = (int?)NRTpHelper.NrScanTpManager.GetPCI(tp, index);

            if (rsrp != null && earfcn != null && pci != null)
            {
                IsValid = true;
                NRCell curCell = CellManager.GetInstance().GetNearestNRCellByARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude);

                if (curCell != null)
                {
                    Cell = curCell;
                    CellName = curCell.Name;
                    TAC = curCell.TAC.ToString();
                    NCI = curCell.NCI.ToString();
                    Longitude = curCell.Longitude.ToString();
                    Latitude = curCell.Latitude.ToString();
                    Distance = Math.Round(curCell.GetDistance(tp.Longitude, tp.Latitude), 2).ToString();
                }
                else
                {
                    Cell = null;
                    CellName = earfcn.ToString() + "_" + pci.ToString();
                    TAC = "-";
                    NCI = "-";
                    Longitude = "-";
                    Latitude = "-";
                    Distance = "-";
                }
                this.ARFCN = (int)earfcn;
                this.PCI = (int)pci;
                this.Rsrp = (float)rsrp;
            }
        }
    }
}
