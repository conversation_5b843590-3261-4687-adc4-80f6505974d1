using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class WeakRxQualTCPDlg : BaseDialog
    {
        public WeakRxQualTCPDlg()
        {
            InitializeComponent();
            cbxHP.SelectedIndex = 0;
        }

        public void InitForLTE()
        {
            this.numCI.Enabled = false;
            this.sameProtect.Enabled = false;
            this.neiborProtect.Enabled = false;
            this.cbxHP.Enabled = false;
        }
        public int RxQualthreshold
        {
            get { return (int)numRxQual.Value; }
        }

        public int IsHP
        {
            get { return cbxHP.SelectedIndex - 1; }
        }

        public int RxLevThreshold
        {
            get { return (int)numRxlev.Value; }
        }

        public int C2IThreshold
        {
            get { return (int)numCI.Value; }
        }

        public int SameProtect
        {
            get { return (int)sameProtect.Value; }
        }

        public int NeiborProtect
        {
            get { return (int)neiborProtect.Value; }
        }
    }
}

