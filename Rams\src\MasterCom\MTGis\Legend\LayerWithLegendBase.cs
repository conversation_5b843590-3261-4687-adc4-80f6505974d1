﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.MTGis
{
    public abstract class LayerWithLegendBase<T> : CustomDrawLayer where T : IGis
    {
        protected LayerWithLegendBase(MapOperation oper, string name)
            : base(oper, name)
        {

        }

        /// <summary>
        /// 添加图例
        /// </summary>
        /// <param name="legend">图例实例</param>
        /// <param name="isSingle">是否为单例。若是单例，则根据图例的type来判断，是否要添加进来。</param>
        public void AddLegend(LegendGroup<T> legend,bool isSingle)
        {
            if (isSingle)
            {
                foreach (LegendGroup<T> item in Legends)
                {
                    if (item.GetType().FullName.Equals(legend.GetType().FullName))
                    {
                        return;
                    }
                }
                Legends.Add(legend);
            }
            else
            {
                Legends.Add(legend);
            }
        }

        private List<LegendGroup<T>> legends = null;

        /// <summary>
        ///该属性，应该是只读的。若要添加图例，请调用AddLegend
        /// </summary>
        public List<LegendGroup<T>> Legends
        {
            get
            {
                if (legends == null)
                {
                    legends = new List<LegendGroup<T>>();
                }
                return legends;
            }
            set { legends = value; }
        }

        public List<T> Entitys2Draw
        {
            get;
            set;
        }
        
        public virtual object Tag { get; set; }

        public virtual void Clear()
        {
            legends = null;
            Tag = null;
            Entitys2Draw = null;
        }
    }

}
