﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraTab;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsResultForm : MinCloseForm
    {
        private List<NbIotMgrsFuncItem> funcItems;

        public NbIotMgrsResultForm()
            : base()
        {
            InitializeComponent();
            xtraTabControl1.SelectedPageChanged += TabControl_SelectedPageChanged;
        }

        public List<NbIotMgrsFuncItem> BindingFuncs
        {
            get
            {
                return funcItems;
            }
        }

        public List<NbIotMgrsResultControlBase> BindingControls
        {
            get
            {
                List<NbIotMgrsResultControlBase> retList = new List<NbIotMgrsResultControlBase>();
                foreach (XtraTabPage tp in xtraTabControl1.TabPages)
                {
                    retList.Add(tp.Controls[0] as NbIotMgrsResultControlBase);
                }
                return retList;
            }
        }

        public NbIotMgrsResultControlBase GetControlByType(Type t)
        {
            foreach (XtraTabPage tp in xtraTabControl1.TabPages)
            {
                if (tp.Controls[0].GetType() == t)
                {
                    return tp.Controls[0] as NbIotMgrsResultControlBase;
                }
            }
            return null;
        }

        /// <summary>
        /// 不要在非UI线程调用这个函数
        /// </summary>
        /// <param name="t"></param>
        public void SelectedPageByType(Type t)
        {
            foreach (XtraTabPage tp in xtraTabControl1.TabPages)
            {
                if (tp.Controls[0].GetType() == t)
                {
                    if (xtraTabControl1.SelectedTabPage == tp)
                    {
                        TabControl_SelectedPageChanged(xtraTabControl1, new TabPageChangedEventArgs(tp, tp));
                    }
                    else
                    {
                        xtraTabControl1.SelectedTabPage = tp;
                    }
                }
            }
        }

        public void FillData(List<NbIotMgrsFuncItem> funcItems)
        {
            this.funcItems = funcItems;
            dealWithData();
        }

        private void dealWithData()
        {
            xtraTabControl1.TabPages.Clear();
            foreach (NbIotMgrsFuncItem item in funcItems)
            {
                List<NbIotMgrsResultControlBase> controls = item.Stater.GetResultControl();

                foreach (NbIotMgrsResultControlBase ctrl in controls)
                {
                    XtraTabPage tabPage = new XtraTabPage();
                    tabPage.Text = ctrl.Desc;
                    tabPage.Controls.Add(ctrl);
                    ctrl.Dock = DockStyle.Fill;
                    xtraTabControl1.TabPages.Add(tabPage);
                    //xtraTabControl1.SelectedTabPageIndex = xtraTabControl1.TabPages.Count - 1;
                }
                if (item.Stater is NbIotMgrsResultStatisticsStater &&
                    xtraTabControl1.TabPages.Count > 0)
                {
                    xtraTabControl1.SelectedTabPageIndex = xtraTabControl1.TabPages.Count - 1;
                }
            }
            if (xtraTabControl1.SelectedTabPage == null &&
                xtraTabControl1.TabPages.Count > 0)
            {
                xtraTabControl1.SelectedTabPageIndex = 0;
            }
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            foreach (NbIotMgrsFuncItem fItem in funcItems)
            {
                fItem.Clear();
            }

            foreach (XtraTabPage tabPage in xtraTabControl1.TabPages)
            {
                foreach (Control ctrl in tabPage.Controls)
                {
                    (ctrl as NbIotMgrsResultControlBase).Clear();
                    ctrl.Dispose();
                }
            }
            xtraTabControl1.TabPages.Clear();
            NbIotMgrsQueryFuncBase.Clear();
            ZTScanGridCoverageLayer.Ranges = new NbIotMgrsCoverageColorRange();
            ZTScanGridCoverageLayer.IsActived = false;
            MainModel.RefreshLegend();
            MainModel.MainForm.GetMapForm().RemoveTempBaseLayer(typeof(ZTScanGridLayer));
            MainModel.MainForm.GetMapForm().RemoveTempBaseLayer(typeof(ZTScanGridCoverageLayer));
            MainModel.MainForm.GetMapForm().updateMap();

            base.MinCloseForm_FormClosing(sender, e);
        }

        private void TabControl_SelectedPageChanged(object sender, TabPageChangedEventArgs e)
        {
            XtraTabPage prevTp = e.PrevPage;
            if (prevTp != null && prevTp.Controls.Count != 0)
            {
                NbIotMgrsResultControlBase prevCtrl = prevTp.Controls[0] as NbIotMgrsResultControlBase;
                prevCtrl.LayerDataClear();
            }

            XtraTabPage curTp = e.Page;
            if (curTp == null || curTp.Controls.Count == 0)
            {
                return;
            }
            NbIotMgrsResultControlBase ctrl = curTp.Controls[0] as NbIotMgrsResultControlBase;
            ctrl.DrawOnLayer();
        }
    }
}
