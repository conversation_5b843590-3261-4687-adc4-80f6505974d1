﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.BaseInfo
{
    /// <summary>
    /// 数据源权限组（该组包括Project，Service，Carrier，Agent等）
    /// </summary>
    public class DataSourceRole
    {
        private int districtID = -1;
        public int DistrictID
        {
            get
            {
                if (districtID == -1)
                {
                    districtID = MainModel.GetInstance().DistrictID;
                }
                return districtID;
            }
            set { districtID = value; }
        }

        public string DistrictName
        {
            get
            {
                return DistrictManager.GetInstance().getDistrictName(DistrictID);
            }
        }

        public bool HasAllAreaRightIncludeNew
        {
            get;
            set;
        }
        
        public int ID { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        private readonly Dictionary<string, List<int>> categoryNameDic = new Dictionary<string, List<int>>();
        public Dictionary<string, List<int>> CategoryNameDic
        {
            get { return categoryNameDic; }
        }

        private readonly Dictionary<string, Dictionary<int, bool>> categoryNameModifyDic = new Dictionary<string, Dictionary<int, bool>>();
        public Dictionary<string, Dictionary<int, bool>> CategoryNameModifyDic
        {
            get { return categoryNameModifyDic; }
        }
        public bool HasProjectRight(int projID)
        {
            return HasCategoryRight("Project", projID);
        }
        public bool HasServiceRight(int svrID)
        {
            return HasCategoryRight("Service", svrID);
        }
        public bool HasCarrierRight(int carrierID)
        {
            return HasCategoryRight("Carrier", carrierID);
        }
        public bool HasAgentRight(int agentID)
        {
            return HasCategoryRight("Agent", agentID);
        }

        public bool HasCategoryRight(string category,int id)
        {
            List<int> idSet = null;
            if (categoryNameDic.TryGetValue(category, out idSet))
            {
                return idSet.Contains(id);
            }
            return false;
        }

        private readonly Dictionary<int, List<int>> areaDic = new Dictionary<int, List<int>>();
        public Dictionary<int, List<int>> AreaDic
        {
            get { return areaDic; }
        }

        private readonly Dictionary<int, Dictionary<int, bool>> areaModifyDic = new Dictionary<int, Dictionary<int, bool>>();
        public Dictionary<int, Dictionary<int, bool>> AreaModifyDic
        {
            get { return areaModifyDic; }
        }
        public bool HasAreaRight(int areaTypeID)
        {
            if (HasAllAreaRightIncludeNew)
            {
                return true;
            }
            List<int> areaIDs = null;
            if (areaDic.TryGetValue(areaTypeID, out areaIDs))
            {
                return areaIDs.Count > 0;
            }
            return false;
        }

        public bool HasAreaRight(int areaTypeID, int areaID)
        {
            if (HasAllAreaRightIncludeNew)
            {
                return true;
            }
            List<int> areaIDs = null;
            if (areaDic.TryGetValue(areaTypeID, out areaIDs))
            {
                return areaIDs.Contains(areaID);
            }
            return false;
        }

        internal void UpdatePermission(string categoryName, int subID, bool hasRight, bool isModify)
        {
            List<int> idSet = null;
            if (categoryNameDic.TryGetValue(categoryName, out idSet))
            {
                if (hasRight)
                {
                    if (!idSet.Contains(subID))
                    {
                        idSet.Add(subID);
                    }
                }
                else
                {
                    idSet.Remove(subID);
                }
            }
            else
            {
                idSet = new List<int>();
                if (hasRight)
                {
                    idSet.Add(subID);
                }
                categoryNameDic.Add(categoryName,idSet);
            }

            if (isModify)
            {
                statModifyCategorys(categoryName, subID, hasRight); 
            }
        }
        private void statModifyCategorys(string categoryName, int subID, bool hasRight)
        {
            Dictionary<int, bool> idDic;
            if (!categoryNameModifyDic.TryGetValue(categoryName, out idDic))
            {
                idDic = new Dictionary<int, bool>();
                categoryNameModifyDic.Add(categoryName, idDic);
            }
            idDic[subID] = hasRight;
        }

        internal void UpdatePermission(int areaTypeID, int areaID, bool hasRight, bool isModify)
        {
            List<int> idSet = null;
            if (areaDic.TryGetValue(areaTypeID, out idSet))
            {
                if (hasRight)
                {
                    if (!idSet.Contains(areaID))
                    {
                        idSet.Add(areaID);
                    }
                }
                else
                {
                    idSet.Remove(areaID);
                }
            }
            else
            {
                idSet = new List<int>();
                if (hasRight)
                {
                    idSet.Add(areaID);
                }
                areaDic.Add(areaTypeID, idSet);
            }

            if (isModify)
            {
                statModifyAreas(areaTypeID, areaID, hasRight);
            }
        }
        private void statModifyAreas(int areaTypeID, int areaID, bool hasRight)
        {
            Dictionary<int, bool> idDic;
            if (!areaModifyDic.TryGetValue(areaTypeID, out idDic))
            {
                idDic = new Dictionary<int, bool>();
                areaModifyDic.Add(areaTypeID, idDic);
            }
            idDic[areaID] = hasRight;
        }

        internal static DataSourceRole Fill(Net.Content content)
        {
            DataSourceRole role = new DataSourceRole();
            role.ID = content.GetParamInt();
            role.Name = content.GetParamString();
            role.Description = content.GetParamString();
            return role;
        }
    }
}
