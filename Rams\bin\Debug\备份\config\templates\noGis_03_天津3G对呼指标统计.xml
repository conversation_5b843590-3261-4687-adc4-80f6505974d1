<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">03_天津3G对呼指标统计</Item>
          <Item typeName="Int32" key="KeyCount">2</Item>
          <Item typeName="Int32" key="TimeShowType">0</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试日期</Item>
              <Item typeName="String" key="Exp">{kTimeValue}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试网格</Item>
              <Item typeName="String" key="Exp">{kAreaId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">网格场景（本地）</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">维护区域</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试时长（小时)</Item>
              <Item typeName="String" key="Exp">{Wx_0862/(60*60*1000) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试里程（公里）</Item>
              <Item typeName="String" key="Exp">{Wx_0863/1000 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">厂家</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">LOG名称</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">覆盖采样点</Item>
              <Item typeName="String" key="Exp">{Wx_710A33+Wx_710A34+Wx_710A35+Wx_710A36+Wx_710B33+Wx_710B34+Wx_710B35+Wx_710B36+Wx_710F33+Wx_710F34+Wx_710F35+Wx_710F36+Wx_711C33+Wx_711C34+Wx_711C35+Wx_711C36 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">总采样点</Item>
              <Item typeName="String" key="Exp">{Wx_710A3C+Wx_710B3C+Wx_710F3C+Wx_711C3C }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RSCP（平均值）</Item>
              <Item typeName="String" key="Exp">{(Wx_710A3F*Wx_710A3C+Wx_710B3F*Wx_710B3C+Wx_710F3F*Wx_710F3C+Wx_711C3F*Wx_711C3C )/(Wx_710A3C+Wx_710B3C+Wx_710F3C+Wx_711C3C ) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TotalRSCP采样点比例（TotalRSCP≥-85dBm的采样点数/TotalRSCP总采样点数×100%）</Item>
              <Item typeName="String" key="Exp">{100*(Wx_710A33+Wx_710A34+Wx_710A35+Wx_710A36+Wx_710B33+Wx_710B34+Wx_710B35+Wx_710B36+Wx_710F33+Wx_710F34+Wx_710F35+Wx_710F36+Wx_711C33+Wx_711C34+Wx_711C35+Wx_711C36  )/(Wx_710A3C+Wx_710B3C+Wx_710F3C+Wx_711C3C ) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TotalEc/Io（平均值）</Item>
              <Item typeName="String" key="Exp">{(Wx_6A0A0A*Wx_6A0A09+Wx_710B3F*Wx_710B3C+Wx_710F3F*Wx_710F3C+Wx_711C3F*Wx_711C3C)/(Wx_6A0A09+Wx_710B3C+Wx_710F3C+Wx_711C3C) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TotalEc/Io采样点比例（TotalEc/Io≥-12dB的采样点数/TotalEc/Io总采样点数×100% ）</Item>
              <Item typeName="String" key="Exp">{100*(Wx_6A0A06+Wx_6A0A05+Wx_6A0A04+Wx_6A0A03)/(Wx_6A0A09) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">软切换比例</Item>
              <Item typeName="String" key="Exp">{100*(Wx_5D0A0402+Wx_5D0A0403+Wx_5D0A0404+Wx_5D0A0405+Wx_5D0A0406)/(Wx_5D0A0401+Wx_5D0A0402+Wx_5D0A0403+Wx_5D0A0404+Wx_5D0A0405+Wx_5D0A0406) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[500]+value9[500]+evtIdCount[506]+value9[506]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[500]+value9[500]+evtIdCount[506]+value9[506])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[505]+evtIdCount[517]+value9[505]+value9[517]+evtIdCount[511]+evtIdCount[523]+evtIdCount[598]+evtIdCount[599]+value9[511]+value9[523]+value9[598]+value9[599]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通率</Item>
              <Item typeName="String" key="Exp">{100*((evtIdCount[500]+value9[500]+evtIdCount[506]+value9[506])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]))/(evtIdCount[500]+value9[500]+evtIdCount[506]+value9[506])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话率</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[505]+evtIdCount[517]+value9[505]+value9[517]+evtIdCount[511]+evtIdCount[523]+evtIdCount[598]+evtIdCount[599]+value9[511]+value9[523]+value9[598]+value9[599])/((evtIdCount[500]+value9[500]+evtIdCount[512]+value9[512]+evtIdCount[506]+value9[506]+evtIdCount[518]+value9[518])-(evtIdCount[504]+value9[504]+evtIdCount[588]+value9[588]+evtIdCount[516]+value9[516]+evtIdCount[589]+value9[589]+evtIdCount[510]+value9[510]+evtIdCount[600]+value9[600]+evtIdCount[522]+value9[522]+evtIdCount[601]+value9[601]))}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">11</Item>
                <Item typeName="Int32">15</Item>
                <Item typeName="Int32">28</Item>
                <Item typeName="Int32">25</Item>
                <Item typeName="Int32">21</Item>
                <Item typeName="Int32">14</Item>
                <Item typeName="Int32">10</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试设备</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>