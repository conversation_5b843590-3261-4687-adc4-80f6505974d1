﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTEpsfbDelayAna;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class EpsfbFRCallStatQuery : DIYAnalyseByFileBackgroundBase
    {
        //主叫
        protected readonly List<int> MoCallAttemptEvtIdList = new List<int> { 9334, 9346 };
        //被叫
        protected readonly List<int> MtCallAttemptEvtIdList = new List<int> { 9335, 9347 };

        protected static readonly object lockObj = new object();
        private static EpsfbFRCallStatQuery instance = null;
        public static EpsfbFRCallStatQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new EpsfbFRCallStatQuery();
                    }
                }
            }
            return instance;
        }

        protected EpsfbFRCallStatQuery()
           : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();
        }

        public override string Name
        {
            get
            {
                return "EPSFB回落时延、返回时延统计(按文件)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22046, this.Name);
        }

        protected bool checkDelay = true;
        protected int maxDelaySec = 10;

        protected List<SingleEpsfbCallStatInfo> callStatList = null;

        protected override bool getCondition()
        {
            //var dlg = new CallConditionDlg();
            //dlg.SetCondition(checkDelay, maxDelaySec);
            //if (dlg.ShowDialog() != DialogResult.OK)
            //{
            //    return false;
            //}

            //dlg.GetCondition(out checkDelay, out maxDelaySec);

            callStatList = new List<SingleEpsfbCallStatInfo>();

            return callStatList != null;
        }

        protected override void fireShowForm()
        {
            if (callStatList == null || callStatList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }

            var frm = MainModel.GetObjectFromBlackboard(typeof(EpsfbCallStatListForm)) as EpsfbCallStatListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new EpsfbCallStatListForm();
            }
            frm.FillData(callStatList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            callStatList = null;
        }

        /// <summary>
        /// 先进行主被叫关联
        /// </summary>
        protected override void analyseFiles()
        {
            var moMtPair = getMoMtPair();

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;

                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }

                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }

                    replay();
                    condition.FileInfos.Clear();

                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }

                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private Dictionary<FileInfo, FileInfo> getMoMtPair()
        {
            var moMtPair = new Dictionary<FileInfo, FileInfo>();
            var fileAdded = new Dictionary<FileInfo, bool>();

            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }

                if (fileInfo.Momt == (int)MoMtFile.MoFlag)
                {
                    // 主叫关联被叫
                    FileInfo mtFile = MainModel.FileInfos.Find(delegate (FileInfo x)
                    {
                        return x.ID == fileInfo.EventCount;
                    });

                    moMtPair[fileInfo] = mtFile;
                    if (mtFile != null)
                    {
                        fileAdded[mtFile] = true;
                    }
                }
            }

            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (fileInfo.Momt == (int)MoMtFile.MtFlag && !fileAdded.ContainsKey(fileInfo))
                {
                    moMtPair[fileInfo] = null;
                }
            }

            return moMtPair;
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (var file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    moFile = file;
                }
                else
                {
                    mtFile = file;
                }
            }

            if (moFile != null)
            {
                dealMoFile(moFile, mtFile);
            }
            else if (mtFile != null)
            {
                //dealMtFile(mtFile, ref lastMtTpIdx, ref lastMtMsgIdx);
            }
        }

        private void dealMoFile(DTFileDataManager moFile, DTFileDataManager mtFile)
        {
            try
            {
                EpsfbCallInfo singleCall = null;
                for (int i = 0; i < moFile.Events.Count; i++)
                {
                    Event evt = moFile.Events[i];
                    if (MoCallAttemptEvtIdList.Contains(evt.ID))
                    {
                        if (singleCall != null)
                        {
                            saveCallInfo(singleCall, null);
                            singleCall = null;
                        }

                        /* MO EPSFB Request
                         * 一次呼叫开始信息
                         */
                        singleCall = new EpsfbCallInfo(moFile.FileName);
                    }

                    if (singleCall != null && singleCall.AddEvent(evt))
                    {
                        // 一次呼叫完成；保存singleCall的事件

                        saveCallInfo(singleCall, null);
                        singleCall = null;
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
        }

        private void saveCallInfo(EpsfbCallInfo moCall, EpsfbCallInfo mtCall)
        {
            var info = new SingleEpsfbCallStatInfo(moCall, mtCall);
            info.Sn = callStatList.Count + 1;
            callStatList.Add(info);
        }

    }
}
