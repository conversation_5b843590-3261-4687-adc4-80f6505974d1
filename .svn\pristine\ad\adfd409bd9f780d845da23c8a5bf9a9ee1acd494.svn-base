using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
namespace MasterCom.RAMS.Func
{
    [System.ComponentModel.ToolboxItem(false)]
    public partial class MapWCellLayerBTSProperties : MTLayerPropUserControl
    {
        public MapWCellLayerBTSProperties()
        {
            InitializeComponent();
        }

        public override void Setup(Object obj)
        {
            if (obj == null)
            {
                return;
            }
            Text = "WCDMA NodeB";
            layer = (MapWCellLayer)obj;
            checkBoxDisplay.Checked = layer.DrawBTS;
            labelColorNodeB.BackColor = layer.ColorBTS;
            numericUpDownSize.Value = layer.SizeBTS;
            checkBoxDrawBTSLabel.Checked = layer.DrawBTSLabel;
            buttonFont.Enabled = checkBoxDrawBTSLabel.Checked;
            groupBox1.Enabled = checkBoxDrawBTSLabel.Checked;
            TrackBarOpacity.Value = labelColorNodeB.BackColor.A;

            cbxBTSName.Checked = layer.DrawBTSName;
            cbxBTSRNC.Checked = layer.DrawBTSRNC;
            cbxBTSLongitude.Checked = layer.DrawBTSLongitude;
            cbxBTSLatitude.Checked = layer.DrawBTSLatitude;
            cbxBTSType.Checked = layer.DrawBTSType;
            cbxBTSDescription.Checked = layer.DrawBTSDescription;
        }

        private void checkBoxDisplay_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTS = checkBoxDisplay.Checked;
        }

        private void labelColorNodeB_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorNodeB.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                labelColorNodeB.BackColor = Color.FromArgb(TrackBarOpacity.Value, colorDialog.Color);
                layer.ColorBTS = labelColorNodeB.BackColor;
                layer.RefreshBrushes();
            }
        }

        private void numericUpDownSize_ValueChanged(object sender, EventArgs e)
        {
            layer.SizeBTS = (int)numericUpDownSize.Value;
        }

        private void checkBoxDrawBTSLabel_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSLabel = checkBoxDrawBTSLabel.Checked;
            buttonFont.Enabled = checkBoxDrawBTSLabel.Checked;
            groupBox1.Enabled = checkBoxDrawBTSLabel.Checked;
        }

        private void buttonFont_Click(object sender, EventArgs e)
        {
            fontDialog.Font = layer.FontBTSLabel;
            if (fontDialog.ShowDialog(this) == DialogResult.OK)
            {
                layer.FontBTSLabel = fontDialog.Font;
            }
        }

        private void TrackBarOpacity_Scroll(object sender, EventArgs e)
        {
            labelColorNodeB.BackColor = Color.FromArgb(TrackBarOpacity.Value, labelColorNodeB.BackColor);
            layer.ColorBTS = labelColorNodeB.BackColor;
            layer.RefreshBrushes();
        }

        private void cbxBTSName_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSName = cbxBTSName.Checked;
        }

        private void cbxBTSCode_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSCode = cbxBTSCode.Checked;
        }

        private void cbxBTSMGW_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSMGW = cbxBTSMGW.Checked;
        }

        private void cbxBTSRNC_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSRNC = cbxBTSRNC.Checked;
        }

        private void cbxBTSLongitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSLongitude = cbxBTSLongitude.Checked;
        }

        private void cbxBTSLatitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSLatitude = cbxBTSLatitude.Checked;
        }

        private void cbxBTSType_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSType = cbxBTSType.Checked;
        }

        private void cbxBTSDescription_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSDescription = cbxBTSDescription.Checked;
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Label label1;
            this.labelColor = new System.Windows.Forms.Label();
            this.LabelOpacity = new System.Windows.Forms.Label();
            this.label0 = new System.Windows.Forms.Label();
            this.label100 = new System.Windows.Forms.Label();
            this.TrackBarOpacity = new System.Windows.Forms.TrackBar();
            this.labelColorNodeB = new System.Windows.Forms.Label();
            this.checkBoxDisplay = new System.Windows.Forms.CheckBox();
            this.checkBoxDrawBTSLabel = new System.Windows.Forms.CheckBox();
            this.buttonFont = new System.Windows.Forms.Button();
            this.numericUpDownSize = new System.Windows.Forms.NumericUpDown();
            this.cbxBTSName = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxBTSMGW = new System.Windows.Forms.CheckBox();
            this.cbxBTSCode = new System.Windows.Forms.CheckBox();
            this.cbxBTSDescription = new System.Windows.Forms.CheckBox();
            this.cbxBTSType = new System.Windows.Forms.CheckBox();
            this.cbxBTSLatitude = new System.Windows.Forms.CheckBox();
            this.cbxBTSLongitude = new System.Windows.Forms.CheckBox();
            this.cbxBTSRNC = new System.Windows.Forms.CheckBox();
            this.label2 = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSize)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(3, 77);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(35, 12);
            label1.TabIndex = 68;
            label1.Text = "Size:";
            // 
            // labelColor
            // 
            this.labelColor.Location = new System.Drawing.Point(3, 26);
            this.labelColor.Name = "labelColor";
            this.labelColor.Size = new System.Drawing.Size(37, 20);
            this.labelColor.TabIndex = 5;
            this.labelColor.Text = "Color:";
            this.labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // LabelOpacity
            // 
            this.LabelOpacity.Location = new System.Drawing.Point(3, 166);
            this.LabelOpacity.Name = "LabelOpacity";
            this.LabelOpacity.Size = new System.Drawing.Size(56, 16);
            this.LabelOpacity.TabIndex = 8;
            this.LabelOpacity.Text = "Opacity: ";
            // 
            // label0
            // 
            this.label0.AutoSize = true;
            this.label0.Location = new System.Drawing.Point(54, 187);
            this.label0.Name = "label0";
            this.label0.Size = new System.Drawing.Size(17, 12);
            this.label0.TabIndex = 40;
            this.label0.Text = "0%";
            this.label0.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label100
            // 
            this.label100.AutoSize = true;
            this.label100.Location = new System.Drawing.Point(138, 187);
            this.label100.Name = "label100";
            this.label100.Size = new System.Drawing.Size(29, 12);
            this.label100.TabIndex = 41;
            this.label100.Text = "100%";
            this.label100.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // TrackBarOpacity
            // 
            this.TrackBarOpacity.LargeChange = 32;
            this.TrackBarOpacity.Location = new System.Drawing.Point(49, 155);
            this.TrackBarOpacity.Maximum = 255;
            this.TrackBarOpacity.Name = "TrackBarOpacity";
            this.TrackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.TrackBarOpacity.Size = new System.Drawing.Size(115, 45);
            this.TrackBarOpacity.TabIndex = 9;
            this.TrackBarOpacity.TickFrequency = 32;
            this.TrackBarOpacity.Value = 255;
            this.TrackBarOpacity.Scroll += new System.EventHandler(this.TrackBarOpacity_Scroll);
            // 
            // labelColorNodeB
            // 
            this.labelColorNodeB.BackColor = System.Drawing.Color.Red;
            this.labelColorNodeB.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelColorNodeB.Location = new System.Drawing.Point(49, 47);
            this.labelColorNodeB.Name = "labelColorNodeB";
            this.labelColorNodeB.Size = new System.Drawing.Size(25, 25);
            this.labelColorNodeB.TabIndex = 35;
            this.labelColorNodeB.Click += new System.EventHandler(this.labelColorNodeB_Click);
            // 
            // checkBoxDisplay
            // 
            this.checkBoxDisplay.AutoSize = true;
            this.checkBoxDisplay.Location = new System.Drawing.Point(6, 6);
            this.checkBoxDisplay.Name = "checkBoxDisplay";
            this.checkBoxDisplay.Size = new System.Drawing.Size(66, 16);
            this.checkBoxDisplay.TabIndex = 42;
            this.checkBoxDisplay.Text = "Display";
            this.checkBoxDisplay.UseVisualStyleBackColor = true;
            this.checkBoxDisplay.CheckedChanged += new System.EventHandler(this.checkBoxDisplay_CheckedChanged);
            // 
            // checkBoxDrawBTSLabel
            // 
            this.checkBoxDrawBTSLabel.AutoSize = true;
            this.checkBoxDrawBTSLabel.Location = new System.Drawing.Point(6, 101);
            this.checkBoxDrawBTSLabel.Name = "checkBoxDrawBTSLabel";
            this.checkBoxDrawBTSLabel.Size = new System.Drawing.Size(102, 16);
            this.checkBoxDrawBTSLabel.TabIndex = 50;
            this.checkBoxDrawBTSLabel.Text = "Display Label";
            this.checkBoxDrawBTSLabel.UseVisualStyleBackColor = true;
            this.checkBoxDrawBTSLabel.CheckedChanged += new System.EventHandler(this.checkBoxDrawBTSLabel_CheckedChanged);
            // 
            // buttonFont
            // 
            this.buttonFont.Location = new System.Drawing.Point(6, 124);
            this.buttonFont.Name = "buttonFont";
            this.buttonFont.Size = new System.Drawing.Size(53, 23);
            this.buttonFont.TabIndex = 67;
            this.buttonFont.Text = "Font...";
            this.buttonFont.UseVisualStyleBackColor = true;
            this.buttonFont.Click += new System.EventHandler(this.buttonFont_Click);
            // 
            // numericUpDownSize
            // 
            this.numericUpDownSize.Location = new System.Drawing.Point(39, 75);
            this.numericUpDownSize.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numericUpDownSize.Minimum = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numericUpDownSize.Name = "numericUpDownSize";
            this.numericUpDownSize.Size = new System.Drawing.Size(34, 21);
            this.numericUpDownSize.TabIndex = 69;
            this.numericUpDownSize.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numericUpDownSize.ValueChanged += new System.EventHandler(this.numericUpDownSize_ValueChanged);
            // 
            // cbxBTSName
            // 
            this.cbxBTSName.AutoSize = true;
            this.cbxBTSName.Checked = true;
            this.cbxBTSName.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxBTSName.Location = new System.Drawing.Point(26, 23);
            this.cbxBTSName.Name = "cbxBTSName";
            this.cbxBTSName.Size = new System.Drawing.Size(48, 16);
            this.cbxBTSName.TabIndex = 70;
            this.cbxBTSName.Text = "Name";
            this.cbxBTSName.UseVisualStyleBackColor = true;
            this.cbxBTSName.CheckedChanged += new System.EventHandler(this.cbxBTSName_CheckedChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxBTSMGW);
            this.groupBox1.Controls.Add(this.cbxBTSCode);
            this.groupBox1.Controls.Add(this.cbxBTSDescription);
            this.groupBox1.Controls.Add(this.cbxBTSType);
            this.groupBox1.Controls.Add(this.cbxBTSLatitude);
            this.groupBox1.Controls.Add(this.cbxBTSLongitude);
            this.groupBox1.Controls.Add(this.cbxBTSRNC);
            this.groupBox1.Controls.Add(this.cbxBTSName);
            this.groupBox1.Location = new System.Drawing.Point(179, 6);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(130, 193);
            this.groupBox1.TabIndex = 71;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Display Index";
            // 
            // cbxBTSMGW
            // 
            this.cbxBTSMGW.AutoSize = true;
            this.cbxBTSMGW.Location = new System.Drawing.Point(26, 63);
            this.cbxBTSMGW.Name = "cbxBTSMGW";
            this.cbxBTSMGW.Size = new System.Drawing.Size(42, 16);
            this.cbxBTSMGW.TabIndex = 78;
            this.cbxBTSMGW.Text = "MGW";
            this.cbxBTSMGW.UseVisualStyleBackColor = true;
            this.cbxBTSMGW.CheckedChanged += new System.EventHandler(this.cbxBTSMGW_CheckedChanged);
            // 
            // cbxBTSCode
            // 
            this.cbxBTSCode.AutoSize = true;
            this.cbxBTSCode.Location = new System.Drawing.Point(26, 43);
            this.cbxBTSCode.Name = "cbxBTSCode";
            this.cbxBTSCode.Size = new System.Drawing.Size(48, 16);
            this.cbxBTSCode.TabIndex = 77;
            this.cbxBTSCode.Text = "Code";
            this.cbxBTSCode.UseVisualStyleBackColor = true;
            this.cbxBTSCode.CheckedChanged += new System.EventHandler(this.cbxBTSCode_CheckedChanged);
            // 
            // cbxBTSDescription
            // 
            this.cbxBTSDescription.AutoSize = true;
            this.cbxBTSDescription.Location = new System.Drawing.Point(26, 163);
            this.cbxBTSDescription.Name = "cbxBTSDescription";
            this.cbxBTSDescription.Size = new System.Drawing.Size(90, 16);
            this.cbxBTSDescription.TabIndex = 76;
            this.cbxBTSDescription.Text = "Description";
            this.cbxBTSDescription.UseVisualStyleBackColor = true;
            this.cbxBTSDescription.CheckedChanged += new System.EventHandler(this.cbxBTSDescription_CheckedChanged);
            // 
            // cbxBTSType
            // 
            this.cbxBTSType.AutoSize = true;
            this.cbxBTSType.Location = new System.Drawing.Point(26, 143);
            this.cbxBTSType.Name = "cbxBTSType";
            this.cbxBTSType.Size = new System.Drawing.Size(48, 16);
            this.cbxBTSType.TabIndex = 75;
            this.cbxBTSType.Text = "Type";
            this.cbxBTSType.UseVisualStyleBackColor = true;
            this.cbxBTSType.CheckedChanged += new System.EventHandler(this.cbxBTSType_CheckedChanged);
            // 
            // cbxBTSLatitude
            // 
            this.cbxBTSLatitude.AutoSize = true;
            this.cbxBTSLatitude.Location = new System.Drawing.Point(26, 123);
            this.cbxBTSLatitude.Name = "cbxBTSLatitude";
            this.cbxBTSLatitude.Size = new System.Drawing.Size(72, 16);
            this.cbxBTSLatitude.TabIndex = 74;
            this.cbxBTSLatitude.Text = "Latitude";
            this.cbxBTSLatitude.UseVisualStyleBackColor = true;
            this.cbxBTSLatitude.CheckedChanged += new System.EventHandler(this.cbxBTSLatitude_CheckedChanged);
            // 
            // cbxBTSLongitude
            // 
            this.cbxBTSLongitude.AutoSize = true;
            this.cbxBTSLongitude.Location = new System.Drawing.Point(26, 103);
            this.cbxBTSLongitude.Name = "cbxBTSLongitude";
            this.cbxBTSLongitude.Size = new System.Drawing.Size(78, 16);
            this.cbxBTSLongitude.TabIndex = 73;
            this.cbxBTSLongitude.Text = "Longitude";
            this.cbxBTSLongitude.UseVisualStyleBackColor = true;
            this.cbxBTSLongitude.CheckedChanged += new System.EventHandler(this.cbxBTSLongitude_CheckedChanged);
            // 
            // cbxBTSRNC
            // 
            this.cbxBTSRNC.AutoSize = true;
            this.cbxBTSRNC.Location = new System.Drawing.Point(26, 83);
            this.cbxBTSRNC.Name = "cbxBTSRNC";
            this.cbxBTSRNC.Size = new System.Drawing.Size(42, 16);
            this.cbxBTSRNC.TabIndex = 71;
            this.cbxBTSRNC.Text = "RNC";
            this.cbxBTSRNC.UseVisualStyleBackColor = true;
            this.cbxBTSRNC.CheckedChanged += new System.EventHandler(this.cbxBTSRNC_CheckedChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(4, 52);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(35, 12);
            this.label2.TabIndex = 72;
            this.label2.Text = "NodeB";
            // 
            // MapWCellLayerBTSProperties
            // 
            this.Controls.Add(this.label2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.numericUpDownSize);
            this.Controls.Add(label1);
            this.Controls.Add(this.buttonFont);
            this.Controls.Add(this.checkBoxDrawBTSLabel);
            this.Controls.Add(this.checkBoxDisplay);
            this.Controls.Add(this.label100);
            this.Controls.Add(this.label0);
            this.Controls.Add(this.labelColorNodeB);
            this.Controls.Add(this.TrackBarOpacity);
            this.Controls.Add(this.LabelOpacity);
            this.Controls.Add(this.labelColor);
            this.Name = "MapWCellLayerBTSProperties";
            this.Size = new System.Drawing.Size(317, 204);
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSize)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private MapWCellLayer layer;

        private CheckBox checkBoxDisplay;

        private Label labelColorNodeB;

        private NumericUpDown numericUpDownSize;

        private CheckBox checkBoxDrawBTSLabel;

        private Button buttonFont;

        private TrackBar TrackBarOpacity;

        private readonly ColorDialog colorDialog = new ColorDialog();
        private Label labelColor;
        private Label LabelOpacity;
        private Label label0;
        private Label label100;
        private CheckBox cbxBTSName;
        private GroupBox groupBox1;
        private CheckBox cbxBTSDescription;
        private CheckBox cbxBTSType;
        private CheckBox cbxBTSLatitude;
        private CheckBox cbxBTSLongitude;
        private CheckBox cbxBTSRNC;
        private Label label2;
        private CheckBox cbxBTSCode;
        private CheckBox cbxBTSMGW;

        private readonly FontDialog fontDialog = new FontDialog();
    }
}
