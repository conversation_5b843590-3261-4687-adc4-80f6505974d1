﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCellCoverTypeAnaForm : MinCloseForm
    {
        public NRCellCoverTypeAnaForm()
        {
            InitializeComponent();
        }

        List<NRRegionCellCoverTypeAnaInfo> regionCells;

        public void FillData(List<NRRegionCellCoverTypeAnaInfo> regionCells)
        {
            this.regionCells = regionCells;
            gridControlResult.DataSource = regionCells;
            gridControlResult.Refresh();
        }

        private void gridControlResult_DoubleClick(object sender, EventArgs e)
        {
            if (this.gvCell.SelectedRowsCount <= 0)
            {
                return;
            }
            NRCellCoverTypeAnaInfo item = gvCell.GetRow(gvCell.GetSelectedRows()[0]) as NRCellCoverTypeAnaInfo;
            if (item == null)
            {
                return;
            }

            MainModel.MainForm.GetMapForm().GoToView(item.Cell.Longitude, item.Cell.Latitude);
        }

        private void ItemExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                List<NPOIRow> lstRows = getResultRows();
                ExcelNPOIManager.ExportToExcel(lstRows);
            }
            catch
            {
                MessageBox.Show("导出导Excel...失败！");
            }
        }

        private List<NPOIRow> getResultRows()
        {
            List<NPOIRow> lstRows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("区域");
            row.AddCellValue("小区名称");
            row.AddCellValue("小区ID");
            row.AddCellValue("TAC");
            row.AddCellValue("NCI");
            row.AddCellValue("频点");
            row.AddCellValue("PCI");
            row.AddCellValue("占用时长");
            row.AddCellValue("占用次数");
            row.AddCellValue("小区类别");
            lstRows.Add(row);

            for (int idx = 0; idx < regionCells.Count; idx++)
            {
                NRRegionCellCoverTypeAnaInfo result = regionCells[idx];

                row = new NPOIRow();
                row.AddCellValue(result.RegionName);
                lstRows.Add(row);

                foreach (NRCellCoverTypeAnaInfo ocResult in result.CellServiceConditions)
                {
                    row = new NPOIRow();
                    row.AddCellValue(ocResult.CellName);
                    row.AddCellValue(ocResult.CellID);
                    row.AddCellValue(ocResult.ARFCN);
                    row.AddCellValue(ocResult.PCI);
                    row.AddCellValue(ocResult.TAC);
                    row.AddCellValue(ocResult.NCI);
                    row.AddCellValue(ocResult.ServiceSeconds);
                    row.AddCellValue(ocResult.ServiceTimes);
                    row.AddCellValue(ocResult.CategoryDesc);
                    lstRows.Add(row);
                }
            }

            return lstRows;
        }
    }
}
