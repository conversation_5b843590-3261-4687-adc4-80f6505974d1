﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class GetWorkParamsHelper_XJ
    {
        protected GetWorkParamsHelper_XJ()
        {

        }

        public static Dictionary<string, Dictionary<string, BtsWorkParam_XJ>> GetWorkParamsInfo(
            StationAcceptAutoSet_XJ funcSet)
        {
            // Dictionary<地市, Dictionary<基站编号, BtsWorkParam_XJ>> 
            Dictionary<string, Dictionary<string, BtsWorkParam_XJ>> workParamSumDic = new Dictionary<string, Dictionary<string, BtsWorkParam_XJ>>();
            try
            {
                //Dictionary<CGI,AntennaWorkParam_XJ>
                Dictionary<string, AntennaWorkParam_XJ> antennaInfoDic = getAntennaInfo(funcSet.AntennaInfoFolderPath);//读取天资推送来的天线实测信息
                readAndUploadWorkParams(funcSet.CellParamFolderPath);//读取并上传资管推送来的待验收工参信息

                WorkParamsQuery_XJ queryParams = new WorkParamsQuery_XJ(funcSet.WorkParamTime_Begin
                    , funcSet.WorkParamTime_End, antennaInfoDic);
                queryParams.Query();//从服务端查询需要自动导出报告的站点工参
                workParamSumDic = queryParams.WorkParamSumDic;
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
            reportInfo(string.Format("查询到{0}个地市的待评估工参数据", workParamSumDic.Count));
            return workParamSumDic;
        }

        #region 读取并上传资管推送来的待验收工参信息
        protected static void readAndUploadWorkParams(string strCellParamFolderPath)
        {
            try
            {
                reportInfo("开始读取待评估对象数据...");

                List<string> fileNameList = new List<string>();
                if (System.IO.Directory.Exists(strCellParamFolderPath))
                {
                    System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(strCellParamFolderPath);

                    #region 处理压缩文件
                    UnZipClass unZip = new UnZipClass();
                    List<string> zipFiles = new List<string>();
                    foreach (System.IO.FileInfo file in dinfo.GetFiles("*.zip", System.IO.SearchOption.TopDirectoryOnly))
                    {
                        zipFiles.Add(file.FullName);
                        unZip.UnZip(file.FullName, strCellParamFolderPath);
                    }
                    GetWorkParamsHelper_QH.BcpFiles(strCellParamFolderPath, zipFiles);
                    #endregion

                    foreach (System.IO.FileInfo file in dinfo.GetFiles("*", System.IO.SearchOption.TopDirectoryOnly))
                    {
                        fileNameList.Add(file.FullName);
                    }
                }
                if (fileNameList.Count > 0)
                {
                    Dictionary<string, BtsWorkParam_XJ> btsParamInfoDic = new Dictionary<string, BtsWorkParam_XJ>();
                    Dictionary<string, CellWorkParam_XJ> cellParamInfoDic = new Dictionary<string, CellWorkParam_XJ>();

                    foreach (string fileName in fileNameList)//逐个读取待上传工参文件
                    {
                        getWorkParamFormExcle(fileName, ref btsParamInfoDic, ref cellParamInfoDic);
                    }

                    reportInfo("正在上传待评估对象数据...");
                    UpLoadWorkParams_XJ upLoadParams = new UpLoadWorkParams_XJ(btsParamInfoDic, cellParamInfoDic);
                    upLoadParams.Query();//上传工参

                    GetWorkParamsHelper_QH.BcpFiles(strCellParamFolderPath, fileNameList);
                }
                else
                {
                    reportInfo("未找到指定目录下的待上传工参文件");
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
        }

        protected static bool getWorkParamFormExcle(string fileName
            , ref Dictionary<string, BtsWorkParam_XJ> btsParamInfoDic
            , ref Dictionary<string, CellWorkParam_XJ> cellParamInfoDic)
        {
            try
            {
                reportInfo(string.Format("正在读取文件 {0} 信息...", fileName));
                int reReadCount = 0;
                while (reReadCount < 6 && FileStatus.FileIsOpen(fileName) == 1)
                {
                    System.Threading.Thread.Sleep(10000);
                    reReadCount++;
                }
                string fileName_Upper = fileName.ToUpper();
                if (fileName_Upper.EndsWith(".CSV"))
                {
                    CSVReader reader = new CSVReader(fileName);
                    List<string[]> listData = reader.GetAllData();
                    if (listData != null && listData.Count > 0)
                    {
                        string[] cols = listData[0];
                        listData.RemoveAt(0);
                        DataTable dtnew = getDtnew(listData, cols);

                        if (fileName_Upper.Contains("NODEB"))
                        {
                            getBtsInfoFromDataTable(fileName, dtnew, ref btsParamInfoDic);
                        }
                        else if (fileName_Upper.Contains("CELL"))
                        {
                            getCellsInfoFromDataTable(fileName, dtnew, ref cellParamInfoDic);
                        }
                    }
                }
                else
                {
                    DataSet dataSet = ExcelNPOIManager.ImportFromExcel(fileName);
                    if (dataSet == null || dataSet.Tables.Count <= 0)
                    {
                        return false;
                    }
                    getBtsInfoFromDataTable(fileName, dataSet.Tables["NODEB"], ref btsParamInfoDic);
                    getCellsInfoFromDataTable(fileName, dataSet.Tables["CELL"], ref cellParamInfoDic);
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
            return true;
        }

        private static DataTable getDtnew(List<string[]> listData, string[] cols)
        {
            DataTable dtnew = new DataTable();
            foreach (String c in cols)
            {
                DataColumn dcol = new DataColumn(c, typeof(String));
                dtnew.Columns.Add(dcol);
            }
            foreach (String[] rowDataVec in listData)
            {
                DataRow drow = dtnew.NewRow();
                for (int colAt = 0; colAt < rowDataVec.Length; colAt++)
                {
                    drow[colAt] = rowDataVec[colAt].ToString().Trim();
                }
                dtnew.Rows.Add(drow);
            }

            return dtnew;
        }

        protected static bool getBtsInfoFromDataTable(string fileName, System.Data.DataTable tb
            , ref Dictionary<string, BtsWorkParam_XJ> btsParamInfoDic)
        {
            if (tb == null || tb.Rows.Count <= 0)
            {
                return false;
            }

            StringBuilder strbErrorInfo = new StringBuilder();
            int index = 0;
            foreach (DataRow row in tb.Rows)
            {
                index++;
                try
                {
                    BtsWorkParam_XJ btsInfo = new BtsWorkParam_XJ();
                    btsInfo.WorkOrderNum = row["工单号"].ToString();
                    btsInfo.BtsName = row["ENODEB名称"].ToString();
                    btsInfo.ENodeBCode = row["ENODEB编码"].ToString();
                    btsInfo.ENodeBID = Convert.ToInt32(row["ENODEBID"]);
                    btsInfo.DistrictName = row["所属地市"].ToString().Replace("市", "");
                    btsInfo.Longitude = Convert.ToDouble(row["经度"]);
                    btsInfo.Latitude = Convert.ToDouble(row["纬度"]);

                    btsParamInfoDic[btsInfo.ENodeBCode] = btsInfo;
                }
                catch
                {
                    strbErrorInfo.AppendLine("第" + index + "行工参信息配置错误!   " + System.DateTime.Now.ToString());
                }
            }
            if (strbErrorInfo.Length > 0)
            {
                reportInfo(string.Format("{0}文件错误信息:{1}", fileName, strbErrorInfo.ToString()));
            }
            return true;
        }

        protected static bool getCellsInfoFromDataTable(string fileName, System.Data.DataTable tb
            , ref Dictionary<string, CellWorkParam_XJ> cellParamInfoDic)
        {
            if (tb == null || tb.Rows.Count <= 0)
            {
                return false;
            }

            StringBuilder strbErrorInfo = new StringBuilder();
            int index = 0;
            foreach (DataRow row in tb.Rows)
            {
                index++;
                try
                {
                    CellWorkParam_XJ cellInfo = new CellWorkParam_XJ();
                    cellInfo.CellName = row["扇区名称"].ToString();
                    if (string.IsNullOrEmpty(cellInfo.CellName))
                    {
                        continue;
                    }
                    cellInfo.SectorID = Convert.ToInt32(getStringValue(row, "扇区号"));
                    cellInfo.ENodeBCode = row["基站编号"].ToString();
                    cellInfo.BtsName = row["基站名称"].ToString();
                    cellInfo.DistrictName = row["所属地市"].ToString().Replace("市", "");
                    string strTac = getStringValue(row, "跟踪区码TAC");
                    cellInfo.Tac = string.IsNullOrEmpty(strTac) ? 0 : Convert.ToInt32(strTac);
                    cellInfo.CoverTypeDes = getStringValue(row, "覆盖类型");
                    cellInfo.CoverScene = getStringValue(row, "覆盖场景");
                    cellInfo.CGI = row["小区码ECGI"].ToString();

                    cellParamInfoDic[cellInfo.CGI] = cellInfo;
                }
                catch
                {
                    strbErrorInfo.AppendLine("第" + index + "行工参信息配置错误!   " + System.DateTime.Now.ToString());
                }
            }
            if (strbErrorInfo.Length > 0)
            {
                reportInfo(string.Format("{0}文件错误信息:{1}", fileName, strbErrorInfo.ToString()));
            }
            return true;
        }

        protected static string getStringValue(DataRow row, string colName)
        {
            string strValue = "";
            object objValue = row[colName];
            if (objValue != null)
            {
                return objValue.ToString().Trim();
            }
            return strValue;
        }
        #endregion

        #region 读取天资推送来的天线实测信息
        /// <summary>
        /// 从本地文件夹读取天资信息（由于天资数据为每天推送全新疆信息，数量太多，上传到数据库耗时太久，故只从本地读取）
        /// </summary>
        /// <param name="strAntennaInfoFolderPath"></param>
        /// <returns></returns>
        protected static Dictionary<string, AntennaWorkParam_XJ> getAntennaInfo(string strAntennaInfoFolderPath)
        {
            //Dictionary<CGI,AntennaWorkParam_XJ>
            Dictionary<string, AntennaWorkParam_XJ> antennaInfoDic = new Dictionary<string, AntennaWorkParam_XJ>();
            try
            {
                reportInfo("开始读取天资实测数据...");

                List<string> fileNameList = new List<string>();
                if (System.IO.Directory.Exists(strAntennaInfoFolderPath))
                {
                    System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(strAntennaInfoFolderPath);
                    foreach (System.IO.FileInfo file in dinfo.GetFiles("*.txt", System.IO.SearchOption.TopDirectoryOnly))
                    {
                        fileNameList.Add(file.FullName);
                    }
                }
                if (fileNameList.Count > 0)
                {
                    string bcpFolderPath = strAntennaInfoFolderPath + "\\天资数据备份";
                    if (!Directory.Exists(bcpFolderPath))
                    {
                        Directory.CreateDirectory(bcpFolderPath);
                    }

                    fileNameList.Sort();
                    int fileIndex = 0;
                    foreach (string fileName in fileNameList)
                    {
                        fileIndex++;
                        string filePathNew = fileName.Replace(strAntennaInfoFolderPath, bcpFolderPath);
                        File.Copy(fileName, filePathNew, true);//将文件备份
                        if (fileIndex == fileNameList.Count)
                        {
                            //读取最新的一个文件信息
                            getAntennaInfoFromTxt(fileNameList[fileIndex - 1], ref antennaInfoDic);
                        }
                        else
                        {
                            File.Delete(fileName);//其他日期的文件，备份后删除
                        }
                    }
                }
                else
                {
                    reportInfo("未找到指定目录下的天资实测文件");
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
            return antennaInfoDic;
        }
        protected static void getAntennaInfoFromTxt(string fileName, ref Dictionary<string, AntennaWorkParam_XJ> antennaInfoDic)
        {
            StreamReader reader = null;
            try
            {
                reportInfo(string.Format("正在读取文件 {0} 信息...", fileName));
                int reReadCount = 0;
                while (reReadCount < 6 && FileStatus.FileIsOpen(fileName) == 1)
                {
                    System.Threading.Thread.Sleep(10000);
                    reReadCount++;
                }
                reader = new StreamReader(fileName, Encoding.Default);

                int lineIndex = 1;
                string strLine;
                while ((strLine = reader.ReadLine()) != null)
                {
                    lineIndex++;
                    string[] cells = strLine.Split('|');
                    if (cells != null && cells.Length >= 11)
                    {
                        try
                        {
                            string cgi = cells[4];
                            if (string.IsNullOrEmpty(cgi))
                            {
                                continue;
                            }

                            AntennaWorkParam_XJ antennaInfo = new AntennaWorkParam_XJ();
                            antennaInfo.Longitude = cells[6];
                            antennaInfo.Latitude = cells[7];
                            antennaInfo.Direction = cells[8];
                            antennaInfo.Altitude = cells[9];
                            antennaInfo.Downward = cells[10];

                            antennaInfoDic[cgi.Trim()] = antennaInfo;
                        }
                        catch (Exception ex)
                        {
                            reportError(ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                reportError(ex);
            }
            finally
            {
                if (reader != null)
                {
                    reader.Close();
                }
            }
        }

        #endregion
        protected static void reportInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }
        protected static void reportError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }

    public class BtsWorkParam_XJ : BtsWorkParamBase
    {
        public BtsWorkParam_XJ(CellWorkParam_XJ cellParam)
            : base(cellParam)
        {
            this.ENodeBCode = cellParam.ENodeBCode;
            if (cellParam.AntennaInfo != null)
            {
                this.TestLongitude = cellParam.AntennaInfo.Longitude;
                this.TestLatitude = cellParam.AntennaInfo.Latitude;
            }
        }
        public BtsWorkParam_XJ()
        { 
        }
        public string ENodeBCode { get; set; }
        public string Address   //暂取基站名的中文部分
        {
            get
            {
                StringBuilder strbAddress = new StringBuilder();

                char[] chars = BtsName.ToCharArray();
                for (int i = 0; i < chars.Length; i++)
                {
                    char charValue = chars[i];
                    if ((int)charValue > 127)//在 ASCII码表中，英文的范围是0-127，而汉字则是大于127
                    {
                        strbAddress.Append(charValue.ToString());
                    }
                    else
                    {
                        break;
                    }
                }
                return strbAddress.ToString().Replace("联通共享", "").Replace("电信共享", "").Replace("共享站", "")
                    .Replace("小基站", "").Replace("室分", "").Replace("拉远点", "").Replace("共享", "");
            }
        }
        public string TestLongitude { get; set; }//实测经度
        public string TestLatitude { get; set; }//实测纬度
        public void AddCellParamsInfo(CellWorkParam_XJ info)
        {
            this.IsOutDoorBts = info.IsOutDoor;
            CellWorkParamDic[info.CellID] = info;
        }
    }
    public class CellWorkParam_XJ : CellWorkParamBase
    {
        public AntennaWorkParamBase AntennaInfo { get; set; }
        public string ENodeBCode { get; set; }
    }

    public class UpLoadWorkParams_XJ : DiySqlMultiNonQuery
    {
        readonly Dictionary<string, BtsWorkParam_XJ> btsParamInfoDic;
        readonly Dictionary<string, CellWorkParam_XJ> cellParamInfoDic;
        public UpLoadWorkParams_XJ(Dictionary<string, BtsWorkParam_XJ> btsParamInfoDic
            , Dictionary<string, CellWorkParam_XJ> cellParamInfoDic)
            : base()
        {
            MainDB = true;
            this.btsParamInfoDic = btsParamInfoDic;
            this.cellParamInfoDic = cellParamInfoDic;
        }
        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (BtsWorkParam_XJ btsInfo in btsParamInfoDic.Values)
            {
                strb.Append(string.Format(@"delete from tb_xinjiang_btsInfo_accept where enodebid = {0} 
and (workOrderNum = '{1}' or workOrderNum is NULL);", btsInfo.ENodeBID, btsInfo.WorkOrderNum));

                strb.Append(string.Format(@"insert into tb_xinjiang_btsInfo_accept([enodebid],[btsName],[btsCode]
,[districtName],[iLongitude],[iLatitude],workOrderNum) values ({0}, '{1}','{2}' ,'{3}',{4}, {5},'{6}');"
                    , btsInfo.ENodeBID, btsInfo.BtsName, btsInfo.ENodeBCode, btsInfo.DistrictName
                    , btsInfo.Longitude * 10000000, btsInfo.Latitude * 10000000, btsInfo.WorkOrderNum));
            }
            foreach (CellWorkParam_XJ cellInfo in cellParamInfoDic.Values)
            {
                strb.Append(string.Format("delete from tb_xinjiang_cellInfo_accept where cellName = '{0}';"
                   , cellInfo.CellName));
                strb.Append(string.Format(@"insert into [tb_xinjiang_cellInfo_accept]([cellName],[sectorID]
,[btsCode],[btsName],[districtName],[tac],[coverType],[coverScene],[cgi]) values 
      ('{0}',{1}, '{2}', '{3}', '{4}', {5}, '{6}', '{7}', '{8}')"
                    , cellInfo.CellName, cellInfo.SectorID, cellInfo.ENodeBCode, cellInfo.BtsName
                    , cellInfo.DistrictName, cellInfo.Tac, cellInfo.CoverTypeDes, cellInfo.CoverScene
                    , cellInfo.CGI));
            }
            return strb.ToString();
        }
    }

    public class WorkParamsQuery_XJ : DIYSQLBase
    {
        readonly DateTime beginTime;
        readonly DateTime endTime;

        //Dictionary<CGI,AntennaWorkParam_XJ>
        readonly Dictionary<string, AntennaWorkParam_XJ> antennaInfoDic;//天线实测信息

        // Dictionary<地市, Dictionary<基站编号, BtsWorkParam_XJ>> 
        public Dictionary<string, Dictionary<string, BtsWorkParam_XJ>> WorkParamSumDic { get; set; } = new Dictionary<string, Dictionary<string, BtsWorkParam_XJ>>();
        public WorkParamsQuery_XJ(DateTime beginTime, DateTime endTime
            , Dictionary<string, AntennaWorkParam_XJ> antennaInfoDic)
        {
            MainDB = true;
            this.beginTime = beginTime;
            this.endTime = endTime;
            this.antennaInfoDic = antennaInfoDic;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select workOrderNum,enodebid,a.btsName,a.btsCode, a.districtName
,iLongitude,iLatitude,b.cellName, sectorID, tac,coverType, coverScene,cgi,a.updateTime 
from tb_xinjiang_btsInfo_accept a left join tb_xinjiang_cellInfo_accept b on a.btsCode = b.btsCode 
where a.updateTime between '{0}' and '{1}'", beginTime.ToString(), endTime.ToString());
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[14];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            WorkParamSumDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            string workOrderNum = package.Content.GetParamString();
            int enodebid = package.Content.GetParamInt();
            string btsName = package.Content.GetParamString();
            string btsCode = package.Content.GetParamString();
            string districtName = package.Content.GetParamString();
            int iLongitude = package.Content.GetParamInt();
            int iLatitude = package.Content.GetParamInt();

            CellWorkParam_XJ cellInfo = new CellWorkParam_XJ();
            cellInfo.BtsName = btsName;
            cellInfo.ENodeBCode = btsCode;
            cellInfo.ENodeBID = enodebid;
            cellInfo.DistrictName = districtName;
            cellInfo.CellName = package.Content.GetParamString();
            cellInfo.CellID = cellInfo.SectorID = package.Content.GetParamInt();
            cellInfo.Tac = package.Content.GetParamInt();
            cellInfo.CoverTypeDes = package.Content.GetParamString();
            cellInfo.CoverScene = package.Content.GetParamString();
            cellInfo.CGI = package.Content.GetParamString();
            string upDateTime = package.Content.GetParamString();

            LTECell lteCell = CellManager.GetInstance().GetLTECellLatest(cellInfo.CellName);
            if (lteCell != null)//待验收工参中没有的信息，关联客户端本地工参补全
            {
                cellInfo.SectorID = lteCell.SectorID;
                cellInfo.CellID = lteCell.CellID;
            }

            AntennaWorkParam_XJ antennaInfo;
            if (antennaInfoDic.TryGetValue(cellInfo.CGI.Trim(), out antennaInfo))
            {
                cellInfo.AntennaInfo = antennaInfo;
            }
            else
            {
                cellInfo.AntennaInfo = new AntennaWorkParam_XJ();
            }

            Dictionary<string, BtsWorkParam_XJ> btsInfoIDic;
            if (!WorkParamSumDic.TryGetValue(districtName, out btsInfoIDic))
            {
                btsInfoIDic = new Dictionary<string, BtsWorkParam_XJ>();
                WorkParamSumDic.Add(districtName, btsInfoIDic);
            }
            BtsWorkParam_XJ btsInfo;
            if (!btsInfoIDic.TryGetValue(btsCode, out btsInfo))
            {
                btsInfo = new BtsWorkParam_XJ(cellInfo);
                btsInfo.WorkOrderNum = workOrderNum;
                btsInfo.Longitude = (double)iLongitude / 10000000;
                btsInfo.Latitude = (double)iLatitude / 10000000;

                btsInfoIDic.Add(btsCode, btsInfo);
            }
            btsInfo.DateTimeDes = upDateTime;
            btsInfo.AddCellParamsInfo(cellInfo);
        }
    }

    public class AntennaWorkParam_XJ : AntennaWorkParamBase
    {
    }

    #region 新疆、青海单验工参基类
    public class BtsWorkParamBase
    {
        public BtsWorkParamBase(CellWorkParamBase cellParam)
        {
            this.ENodeBID = cellParam.ENodeBID;
            this.BtsName = cellParam.BtsName;
            this.DistrictName = cellParam.DistrictName;
            this.CoverScene = cellParam.CoverScene;
            this.IsOutDoorBts = cellParam.IsOutDoor;
        }
        public BtsWorkParamBase()
        { 
        }

        private string workOrderNum = "";
        public string WorkOrderNum
        {
            get { return string.IsNullOrEmpty(workOrderNum) ? "" : workOrderNum.Trim(); }
            set { workOrderNum = value; }
        }

        private string btsName = "";
        public string BtsName
        {
            get { return string.IsNullOrEmpty(btsName) ? "" : btsName.Trim(); }
            set { btsName = value; }
        }
        public int ENodeBID { get; set; }

        private string districtName;
        public string DistrictName
        {
            get { return string.IsNullOrEmpty(districtName) ? "" : districtName.Trim().Replace("市", ""); }
            set { districtName = value; }
        }
        public double Longitude { get; set; }//规划经度
        public double Latitude { get; set; }//规划纬度
        public string CoverScene { get; set; }//覆盖场景
        public bool IsOutDoorBts { get; set; }
        public string DateTimeDes { get; set; }
        public string DateDes
        {
            get
            {
                DateTime time;
                if (!string.IsNullOrEmpty(DateTimeDes) && DateTime.TryParse(DateTimeDes.Trim(), out time))
                {
                    return time.ToString("yyyyMMdd");
                }
                return string.Empty;
            }
        }
        
        public Dictionary<int, CellWorkParamBase> CellWorkParamDic { get; set; } = new Dictionary<int, CellWorkParamBase>();
        public List<CellWorkParamBase> CellWorkParams
        {
            get { return new List<CellWorkParamBase>(CellWorkParamDic.Values); }
        }
    }
    public class CellWorkParamBase
    {
        private string cgi;
        public string CGI
        {
            get { return string.IsNullOrEmpty(cgi) ? "" : cgi.Trim(); }
            set { cgi = value; }
        }

        private string cellName;
        public string CellName
        {
            get { return string.IsNullOrEmpty(cellName) ? "" : cellName.Trim(); }
            set { cellName = value; }
        }
        public int SectorID { get; set; }
        public int CellID { get; set; }

        private string btsName;
        public string BtsName
        {
            get { return string.IsNullOrEmpty(btsName) ? "" : btsName.Trim(); }
            set { btsName = value; }
        }
        public int ENodeBID { get; set; }
        public int Tac { get; set; }

        private string districtName;
        public string DistrictName
        {
            get { return string.IsNullOrEmpty(districtName) ? "" : districtName.Trim().Replace("市", ""); }
            set { districtName = value; }
        }
        public bool IsOutDoor
        {
            get
            {
                if (string.IsNullOrEmpty(CoverTypeDes) || !CoverTypeDes.Trim().Equals("室外"))
                {
                    return false;
                }
                return true;
            }
        }
        public string CoverTypeDes { get; set; }
        public string CoverScene { get; set; }//覆盖场景
    }
    public class AntennaWorkParamBase     //天资数据，用于输出报告时填写实测数据用
    {
        public string Longitude { get; set; }
        public string Latitude { get; set; }
        public string Altitude { get; set; }//挂高
        public string Direction { get; set; }//方向角
        public string Downward { get; set; }//下倾角
    }
    #endregion
}
