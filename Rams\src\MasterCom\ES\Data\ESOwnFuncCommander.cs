﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using Microsoft.CSharp;
using System.CodeDom.Compiler;
using System.Xml;
using System.Reflection;

namespace MasterCom.ES.Data
{
    internal class ESOwnFuncCommander
    {
        public string funcName;
        public string desc;
        public string codeString;
        public string descriptionNote;

        public override string ToString()
        {
            return desc;
        }
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(ESOwnFuncCommander).Name))
            {
                Dictionary<string, object> param = configFile.GetItemValue(item, "FuncParams") as Dictionary<string, object>;
                ESOwnFuncCommander cmd = new ESOwnFuncCommander();
                cmd.Param = param;
                return cmd;
            }
            return null;
        }
        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is ESOwnFuncCommander)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                ESOwnFuncCommander cmd = value as ESOwnFuncCommander;
                configFile.AddItem(item, "FuncParams", cmd.Param);
                return item;
            }
            return null;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["funcName"] = funcName;
                param["desc"] = desc;
                param["codeString"] = codeString;
                param["descriptionNote"] = descriptionNote;
                return param;
            }
            set
            {
                funcName = (string)value["funcName"];
                desc = (string)value["desc"];
                codeString = (string)value["codeString"];
                descriptionNote = (string)value["descriptionNote"];
            }
        }
        [NonSerialized]
        public bool _classReady = false;
        [NonSerialized]
        public bool _hasError = false;
        [NonSerialized]
        public object clzzInst;
        internal string initFuncClass()
        {
            StringBuilder strErrorMsg = new StringBuilder();
            CryptionData cdata = new CryptionData();
            string strSourceCode = cdata.DecryptionStringdata(codeString);
            CSharpCodeProvider objCSharpCodePrivoder = new CSharpCodeProvider();
            CompilerParameters objCompilerParameters = new CompilerParameters();
            objCompilerParameters.ReferencedAssemblies.Add("RAMS.exe");
            objCompilerParameters.ReferencedAssemblies.Add("DecodeDll.dll");
            objCompilerParameters.CompilerOptions = "/define:前台";
            objCompilerParameters.GenerateInMemory = true;
            CompilerResults cr = objCSharpCodePrivoder.CompileAssemblyFromSource(objCompilerParameters, strSourceCode);
            if (cr.Errors.HasErrors)
            {
                strErrorMsg.Append(cr.Errors.Count.ToString() + " Errors:");
                for (int x = 0; x < cr.Errors.Count; x++)
                {
                    strErrorMsg.Append("\r\nLine: " +
                                     cr.Errors[x].Line.ToString() + " - " +
                                     cr.Errors[x].ErrorText);
                }
                _hasError = true;
                _classReady = false;
            }
            else
            {
                System.Reflection.Assembly objAssembly = cr.CompiledAssembly;
                clzzInst = objAssembly.CreateInstance(funcName);
                if (clzzInst == null)
                {
                    strErrorMsg.Append("未能实例化类：" + funcName);
                    return strErrorMsg.ToString();
                }
                try
                {
                    object[] paramObj = new object[1];
                    paramObj[0] = "";
                    MethodInfo method = clzzInst.GetType().GetMethod("GetFuncResult");
                    if (method == null)
                    {
                        strErrorMsg.Append("未找到函数GetFuncResult！");
                        _hasError = true;
                        _classReady = false;
                        return strErrorMsg.ToString();
                    }
                    judgeReturnType(strErrorMsg, paramObj, method);
                }
                catch (MissingMethodException exx)
                {
                    strErrorMsg.Append("未找到函数或所制定的函数不满足要求！ " + exx.ToString());
                    _hasError = true;
                    _classReady = false;
                }
                catch (Exception ex)
                {
                    strErrorMsg.Append(ex.ToString());
                    _hasError = true;
                    _classReady = false;
                }
            }
            return strErrorMsg.ToString();
        }

        private void judgeReturnType(StringBuilder strErrorMsg, object[] paramObj, MethodInfo method)
        {
            Type restype = method.ReturnType;
            if (restype != typeof(double) && restype != typeof(string))
            {
                _hasError = true;
                _classReady = false;
                strErrorMsg.Append("自定义函数：" + funcName + "  GetFuncResult 返回类型非预期，必须为double 或 string");
            }
            else
            {
                clzzInst.GetType().InvokeMember(
                               "GetFuncResult",
                               System.Reflection.BindingFlags.InvokeMethod, null, clzzInst,
                               paramObj);
                _hasError = false;
                _classReady = true;
            }
        }
    }
}
