﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    partial class TestWorkForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.areaResultPanel = new MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestManager.AreaResultPanel();
            this.SuspendLayout();
            // 
            // areaResultPanel
            // 
            this.areaResultPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.areaResultPanel.Location = new System.Drawing.Point(0, 0);
            this.areaResultPanel.Name = "areaResultPanel";
            this.areaResultPanel.Size = new System.Drawing.Size(1184, 662);
            this.areaResultPanel.TabIndex = 2;
            // 
            // TestWorkForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1184, 662);
            this.Controls.Add(this.areaResultPanel);
            this.Name = "TestWorkForm";
            this.Text = "测试工作统计";
            this.ResumeLayout(false);

        }

        #endregion

        private MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestManager.AreaResultPanel areaResultPanel;

    }
}