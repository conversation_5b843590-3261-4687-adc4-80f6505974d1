﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class XtraCellCoverForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(XtraCellCoverForm));
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.ckb1800 = new System.Windows.Forms.CheckBox();
            this.ckb900 = new System.Windows.Forms.CheckBox();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlShow = new DevExpress.XtraGrid.GridControl();
            this.gridViewShow = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.panel1 = new System.Windows.Forms.Panel();
            this.button9 = new System.Windows.Forms.Button();
            this.button8 = new System.Windows.Forms.Button();
            this.button7 = new System.Windows.Forms.Button();
            this.button6 = new System.Windows.Forms.Button();
            this.button5 = new System.Windows.Forms.Button();
            this.button4 = new System.Windows.Forms.Button();
            this.button3 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlInfo = new DevExpress.XtraGrid.GridControl();
            this.gridViewInfo = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.gridControlTest = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip2 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.查看性能ToolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridView2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridViewTest = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControlPer = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.导出ExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.查看性能ToolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridViewPer = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn85 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.tableLayoutPanel3 = new System.Windows.Forms.TableLayoutPanel();
            this.gridControlTestAll = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip4 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.查看性能ToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridView4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn36 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn37 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn38 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn39 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn40 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn41 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn42 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn43 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn44 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn45 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn46 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridViewTestAll = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn69 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControlPerAll = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip3 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.查看性能ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridView3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridViewPerAll = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn75 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn76 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn77 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn78 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn79 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn81 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn82 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn83 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn84 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn86 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tableLayoutPanel1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlShow)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewShow)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            this.panel1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.xtraTabPage4.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTest)).BeginInit();
            this.contextMenuStrip2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTest)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlPer)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            this.tableLayoutPanel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTestAll)).BeginInit();
            this.contextMenuStrip4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTestAll)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlPerAll)).BeginInit();
            this.contextMenuStrip3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPerAll)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            this.SuspendLayout();
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.groupBox1, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.xtraTabControl1, 0, 0);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 0F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(902, 562);
            this.tableLayoutPanel1.TabIndex = 2;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.ckb1800);
            this.groupBox1.Controls.Add(this.ckb900);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(3, 565);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(896, 1);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "显示选项";
            // 
            // ckb1800
            // 
            this.ckb1800.AutoSize = true;
            this.ckb1800.Checked = true;
            this.ckb1800.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb1800.Location = new System.Drawing.Point(158, 26);
            this.ckb1800.Name = "ckb1800";
            this.ckb1800.Size = new System.Drawing.Size(78, 18);
            this.ckb1800.TabIndex = 1;
            this.ckb1800.Text = "1800频段";
            this.ckb1800.UseVisualStyleBackColor = true;
            this.ckb1800.CheckedChanged += new System.EventHandler(this.CheckBoxChanged);
            // 
            // ckb900
            // 
            this.ckb900.AutoSize = true;
            this.ckb900.Checked = true;
            this.ckb900.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb900.Location = new System.Drawing.Point(65, 26);
            this.ckb900.Name = "ckb900";
            this.ckb900.Size = new System.Drawing.Size(71, 18);
            this.ckb900.TabIndex = 0;
            this.ckb900.Text = "900频段";
            this.ckb900.UseVisualStyleBackColor = true;
            this.ckb900.CheckedChanged += new System.EventHandler(this.CheckBoxChanged);
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(3, 3);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(896, 556);
            this.xtraTabControl1.TabIndex = 3;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage3,
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage4,
            this.xtraTabPage5});
            this.xtraTabControl1.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xtraTabControl1_SelectedPageChanged);
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlShow);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.PageVisible = false;
            this.xtraTabPage1.Size = new System.Drawing.Size(889, 526);
            this.xtraTabPage1.Text = "全部小区";
            // 
            // gridControlShow
            // 
            this.gridControlShow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlShow.Location = new System.Drawing.Point(0, 0);
            this.gridControlShow.MainView = this.gridViewShow;
            this.gridControlShow.Name = "gridControlShow";
            this.gridControlShow.Size = new System.Drawing.Size(889, 526);
            this.gridControlShow.TabIndex = 2;
            this.gridControlShow.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewShow,
            this.gridView2});
            this.gridControlShow.DoubleClick += new System.EventHandler(this.gridControlShow_DoubleClick);
            this.gridControlShow.Click += new System.EventHandler(this.gridControlShow_Click);
            // 
            // gridViewShow
            // 
            this.gridViewShow.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11});
            this.gridViewShow.GridControl = this.gridControlShow;
            this.gridViewShow.Name = "gridViewShow";
            this.gridViewShow.OptionsBehavior.Editable = false;
            this.gridViewShow.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewShow.OptionsView.ColumnAutoWidth = false;
            this.gridViewShow.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "编号";
            this.gridColumn1.FieldName = "Id";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 76;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "小区名";
            this.gridColumn2.FieldName = "Cellname";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 231;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "LAC";
            this.gridColumn3.FieldName = "Lac";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 58;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "CI";
            this.gridColumn4.FieldName = "Ci";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 58;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "性能类型";
            this.gridColumn5.FieldName = "PerformanceType";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            this.gridColumn5.Width = 58;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "问题类型";
            this.gridColumn6.FieldName = "ProblemType";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 58;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "正常小时数";
            this.gridColumn7.FieldName = "NormalHour";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "异常小时数";
            this.gridColumn8.FieldName = "AbnormalHour";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "首次问题时间";
            this.gridColumn9.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.gridColumn9.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn9.FieldName = "Sdate";
            this.gridColumn9.GroupFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.gridColumn9.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 130;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "末次问题时间";
            this.gridColumn10.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn10.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn10.FieldName = "Edate";
            this.gridColumn10.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn10.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            this.gridColumn10.Width = 130;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "更新时间";
            this.gridColumn11.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn11.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn11.FieldName = "Udate";
            this.gridColumn11.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn11.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 10;
            this.gridColumn11.Width = 130;
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControlShow;
            this.gridView2.Name = "gridView2";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.panel1);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(889, 526);
            this.xtraTabPage3.Text = "预警点";
            // 
            // panel1
            // 
            this.panel1.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.panel1.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("panel1.BackgroundImage")));
            this.panel1.Controls.Add(this.button9);
            this.panel1.Controls.Add(this.button8);
            this.panel1.Controls.Add(this.button7);
            this.panel1.Controls.Add(this.button6);
            this.panel1.Controls.Add(this.button5);
            this.panel1.Controls.Add(this.button4);
            this.panel1.Controls.Add(this.button3);
            this.panel1.Controls.Add(this.button2);
            this.panel1.Controls.Add(this.button1);
            this.panel1.Location = new System.Drawing.Point(86, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(684, 503);
            this.panel1.TabIndex = 9;
            // 
            // button9
            // 
            this.button9.BackColor = System.Drawing.Color.DarkGray;
            this.button9.Location = new System.Drawing.Point(122, 340);
            this.button9.Name = "button9";
            this.button9.Size = new System.Drawing.Size(137, 107);
            this.button9.TabIndex = 17;
            this.button9.Text = "低级预警点";
            this.button9.UseVisualStyleBackColor = false;
            this.button9.Click += new System.EventHandler(this.button9_Click);
            // 
            // button8
            // 
            this.button8.BackColor = System.Drawing.Color.LimeGreen;
            this.button8.Location = new System.Drawing.Point(302, 340);
            this.button8.Name = "button8";
            this.button8.Size = new System.Drawing.Size(137, 107);
            this.button8.TabIndex = 16;
            this.button8.Text = "一般预警点";
            this.button8.UseVisualStyleBackColor = false;
            this.button8.Click += new System.EventHandler(this.button8_Click);
            // 
            // button7
            // 
            this.button7.BackColor = System.Drawing.Color.DeepSkyBlue;
            this.button7.Location = new System.Drawing.Point(481, 340);
            this.button7.Name = "button7";
            this.button7.Size = new System.Drawing.Size(137, 107);
            this.button7.TabIndex = 15;
            this.button7.Text = "重要预警点";
            this.button7.UseVisualStyleBackColor = false;
            this.button7.Click += new System.EventHandler(this.button7_Click);
            // 
            // button6
            // 
            this.button6.BackColor = System.Drawing.Color.LimeGreen;
            this.button6.Location = new System.Drawing.Point(122, 197);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(137, 107);
            this.button6.TabIndex = 14;
            this.button6.Text = "一般预警点";
            this.button6.UseVisualStyleBackColor = false;
            this.button6.Click += new System.EventHandler(this.button6_Click);
            // 
            // button5
            // 
            this.button5.BackColor = System.Drawing.Color.DeepSkyBlue;
            this.button5.Location = new System.Drawing.Point(302, 197);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(137, 107);
            this.button5.TabIndex = 13;
            this.button5.Text = "重要预警点";
            this.button5.UseVisualStyleBackColor = false;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // button4
            // 
            this.button4.BackColor = System.Drawing.Color.Gold;
            this.button4.Location = new System.Drawing.Point(481, 197);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(137, 107);
            this.button4.TabIndex = 12;
            this.button4.Text = "紧急预警点";
            this.button4.UseVisualStyleBackColor = false;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // button3
            // 
            this.button3.BackColor = System.Drawing.Color.DeepSkyBlue;
            this.button3.Location = new System.Drawing.Point(122, 52);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(137, 107);
            this.button3.TabIndex = 11;
            this.button3.Text = "重要预警点";
            this.button3.UseVisualStyleBackColor = false;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // button2
            // 
            this.button2.BackColor = System.Drawing.Color.Gold;
            this.button2.Location = new System.Drawing.Point(302, 52);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(137, 107);
            this.button2.TabIndex = 10;
            this.button2.Text = "紧急预警点";
            this.button2.UseVisualStyleBackColor = false;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button1
            // 
            this.button1.BackColor = System.Drawing.Color.Crimson;
            this.button1.Location = new System.Drawing.Point(481, 52);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(137, 107);
            this.button1.TabIndex = 9;
            this.button1.Text = "高危预警点";
            this.button1.UseVisualStyleBackColor = false;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControlInfo);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.PageVisible = false;
            this.xtraTabPage2.Size = new System.Drawing.Size(889, 526);
            this.xtraTabPage2.Text = "小区详情";
            // 
            // gridControlInfo
            // 
            this.gridControlInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlInfo.Location = new System.Drawing.Point(0, 0);
            this.gridControlInfo.MainView = this.gridViewInfo;
            this.gridControlInfo.Name = "gridControlInfo";
            this.gridControlInfo.Size = new System.Drawing.Size(889, 526);
            this.gridControlInfo.TabIndex = 3;
            this.gridControlInfo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewInfo,
            this.gridView3});
            // 
            // gridViewInfo
            // 
            this.gridViewInfo.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40});
            this.gridViewInfo.GridControl = this.gridControlInfo;
            this.gridViewInfo.Name = "gridViewInfo";
            this.gridViewInfo.OptionsBehavior.Editable = false;
            this.gridViewInfo.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewInfo.OptionsView.ColumnAutoWidth = false;
            this.gridViewInfo.OptionsView.ShowGroupPanel = false;
            this.gridViewInfo.RowCellStyle += new DevExpress.XtraGrid.Views.Grid.RowCellStyleEventHandler(this.gridViewInfo_RowCellStyle);
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "时间";
            this.gridColumn12.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn12.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn12.FieldName = "Tmdat";
            this.gridColumn12.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn12.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 0;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "LAC";
            this.gridColumn13.FieldName = "ILac";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 1;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "CI";
            this.gridColumn14.FieldName = "ICi";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 2;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "忙时SD可用数目";
            this.gridColumn15.FieldName = "忙时SD可用数目";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 3;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "信令信道拥塞率(不含切换)";
            this.gridColumn16.FieldName = "信令信道拥塞率_不含切换_";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 4;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "SD总掉话率";
            this.gridColumn17.FieldName = "SD总掉话率";
            this.gridColumn17.Name = "gridColumn17";
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "立即指配命令丢弃次数";
            this.gridColumn18.FieldName = "立即指配命令丢弃次数";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 5;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "SDCCH分配成功率";
            this.gridColumn19.FieldName = "SDCCH分配成功率";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 6;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "每SD信道话务量";
            this.gridColumn20.FieldName = "每SD信道话务量";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 7;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "无线接入性";
            this.gridColumn21.FieldName = "无线接入性";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 8;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "话音信道拥塞率(不含切换)";
            this.gridColumn22.FieldName = "话音信道拥塞率_不含切换_";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 9;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "TCH分配成功率";
            this.gridColumn23.FieldName = "TCH分配成功率";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 10;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "话音信道掉话率(不含切换)";
            this.gridColumn24.FieldName = "话音信道掉话率_不含切换_";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 11;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "忙时TCH分配失败总次数(不含切换)";
            this.gridColumn25.FieldName = "忙时TCH分配失败总次数_不含切换_";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 12;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "忙时话音信道可用总数";
            this.gridColumn26.FieldName = "忙时话音信道可用总数";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 13;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "BAND4_5";
            this.gridColumn27.FieldName = "BAND4_5";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 14;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "忙时话音信道掉话总次数";
            this.gridColumn28.FieldName = "忙时话音信道掉话总次数";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 15;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "每TCH信道话务量";
            this.gridColumn29.FieldName = "每TCH信道话务量";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 16;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "半速率话务量";
            this.gridColumn30.FieldName = "半速率话务量";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 17;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "全速率话务量";
            this.gridColumn31.FieldName = "全速率话务量";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 18;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "无线利用率";
            this.gridColumn32.FieldName = "无线利用率";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 19;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "切换成功率";
            this.gridColumn33.FieldName = "切换成功率";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 20;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "话音信道掉话率(含切换)";
            this.gridColumn34.FieldName = "话音信道掉话率_含切换_";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 21;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "话音信道拥塞率(含切换)";
            this.gridColumn35.FieldName = "话音信道拥塞率_含切换_";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 22;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "忙时TCH分配失败总次数(含切换)";
            this.gridColumn36.FieldName = "忙时TCH分配失败总次数_含切换_";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 23;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "BSC间入切换请求成功率";
            this.gridColumn37.FieldName = "BSC间入切换请求成功率";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 24;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "BSC间出切换请求成功率";
            this.gridColumn38.FieldName = "BSC间出切换请求成功率";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 25;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "BSC内入切换请求成功率";
            this.gridColumn39.FieldName = "BSC内入切换请求成功率";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 26;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "BSC内出切换请求成功率";
            this.gridColumn40.FieldName = "BSC内出切换请求成功率";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 27;
            // 
            // gridView3
            // 
            this.gridView3.GridControl = this.gridControlInfo;
            this.gridView3.Name = "gridView3";
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.tableLayoutPanel2);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(889, 526);
            this.xtraTabPage4.Text = "小区详情";
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.Controls.Add(this.gridControlTest, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.gridControlPer, 0, 0);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 2;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(889, 526);
            this.tableLayoutPanel2.TabIndex = 5;
            // 
            // gridControlTest
            // 
            this.gridControlTest.ContextMenuStrip = this.contextMenuStrip2;
            this.gridControlTest.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlTest.Location = new System.Drawing.Point(3, 266);
            this.gridControlTest.MainView = this.bandedGridView2;
            this.gridControlTest.Name = "gridControlTest";
            this.gridControlTest.Size = new System.Drawing.Size(883, 257);
            this.gridControlTest.TabIndex = 4;
            this.gridControlTest.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView2,
            this.gridViewTest,
            this.gridView6});
            this.gridControlTest.Click += new System.EventHandler(this.gridControl_Click);
            // 
            // contextMenuStrip2
            // 
            this.contextMenuStrip2.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem1,
            this.查看性能ToolStripMenuItem2});
            this.contextMenuStrip2.Name = "contextMenuStrip2";
            this.contextMenuStrip2.Size = new System.Drawing.Size(128, 48);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(127, 22);
            this.toolStripMenuItem1.Text = "导出Excel";
            this.toolStripMenuItem1.Click += new System.EventHandler(this.toolStripMenuItem1_Click);
            // 
            // 查看性能ToolStripMenuItem2
            // 
            this.查看性能ToolStripMenuItem2.Name = "查看性能ToolStripMenuItem2";
            this.查看性能ToolStripMenuItem2.Size = new System.Drawing.Size(127, 22);
            this.查看性能ToolStripMenuItem2.Text = "查看事件";
            this.查看性能ToolStripMenuItem2.Click += new System.EventHandler(this.查看性能ToolStripMenuItem2_Click);
            // 
            // bandedGridView2
            // 
            this.bandedGridView2.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand2});
            this.bandedGridView2.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn13,
            this.bandedGridColumn14,
            this.bandedGridColumn15,
            this.bandedGridColumn16,
            this.bandedGridColumn17,
            this.bandedGridColumn18,
            this.bandedGridColumn19,
            this.bandedGridColumn20,
            this.bandedGridColumn21,
            this.bandedGridColumn22,
            this.bandedGridColumn23});
            this.bandedGridView2.GridControl = this.gridControlTest;
            this.bandedGridView2.Name = "bandedGridView2";
            this.bandedGridView2.OptionsBehavior.Editable = false;
            this.bandedGridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView2.OptionsView.ShowDetailButtons = false;
            this.bandedGridView2.OptionsView.ShowGroupPanel = false;
            this.bandedGridView2.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.bandedGridView_CustomDrawCell);
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "测试预警小区";
            this.gridBand2.Columns.Add(this.bandedGridColumn13);
            this.gridBand2.Columns.Add(this.bandedGridColumn14);
            this.gridBand2.Columns.Add(this.bandedGridColumn15);
            this.gridBand2.Columns.Add(this.bandedGridColumn16);
            this.gridBand2.Columns.Add(this.bandedGridColumn17);
            this.gridBand2.Columns.Add(this.bandedGridColumn18);
            this.gridBand2.Columns.Add(this.bandedGridColumn19);
            this.gridBand2.Columns.Add(this.bandedGridColumn20);
            this.gridBand2.Columns.Add(this.bandedGridColumn21);
            this.gridBand2.Columns.Add(this.bandedGridColumn22);
            this.gridBand2.Columns.Add(this.bandedGridColumn23);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 797;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "时间";
            this.bandedGridColumn13.FieldName = "IMon";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.Visible = true;
            this.bandedGridColumn13.Width = 98;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "小区名";
            this.bandedGridColumn14.FieldName = "StrCellName";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.Visible = true;
            this.bandedGridColumn14.Width = 76;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "LAC";
            this.bandedGridColumn15.FieldName = "ILAC";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.Visible = true;
            this.bandedGridColumn15.Width = 59;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "CI";
            this.bandedGridColumn16.FieldName = "ICI";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.Visible = true;
            this.bandedGridColumn16.Width = 62;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "次数";
            this.bandedGridColumn17.FieldName = "INum";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.Visible = true;
            this.bandedGridColumn17.Width = 57;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "总占用时长（秒）";
            this.bandedGridColumn18.FieldName = "TotalTime";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.Visible = true;
            this.bandedGridColumn18.Width = 113;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "平均占用时长（秒）";
            this.bandedGridColumn19.FieldName = "AvgTime";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.Visible = true;
            this.bandedGridColumn19.Width = 135;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "区域";
            this.bandedGridColumn20.FieldName = "Strvalue2";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.Visible = true;
            this.bandedGridColumn20.Width = 65;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "问题级别";
            this.bandedGridColumn21.FieldName = "ILevel";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.Visible = true;
            this.bandedGridColumn21.Width = 65;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "事件次数";
            this.bandedGridColumn22.FieldName = "Strvalue4";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.Width = 65;
            // 
            // bandedGridColumn23
            // 
            this.bandedGridColumn23.Caption = "状态";
            this.bandedGridColumn23.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.bandedGridColumn23.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn23.FieldName = "Strtype";
            this.bandedGridColumn23.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.bandedGridColumn23.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn23.Name = "bandedGridColumn23";
            this.bandedGridColumn23.Visible = true;
            this.bandedGridColumn23.Width = 67;
            // 
            // gridViewTest
            // 
            this.gridViewTest.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn55,
            this.gridColumn56,
            this.gridColumn57,
            this.gridColumn58,
            this.gridColumn59,
            this.gridColumn60,
            this.gridColumn61,
            this.gridColumn62});
            this.gridViewTest.GridControl = this.gridControlTest;
            this.gridViewTest.Name = "gridViewTest";
            this.gridViewTest.OptionsBehavior.Editable = false;
            this.gridViewTest.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewTest.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "时间";
            this.gridColumn52.FieldName = "IMon";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 0;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "小区名";
            this.gridColumn53.FieldName = "StrCellName";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 1;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "LAC";
            this.gridColumn54.FieldName = "ILAC";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 2;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "CI";
            this.gridColumn55.FieldName = "ICI";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 3;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "次数";
            this.gridColumn56.FieldName = "INum";
            this.gridColumn56.Name = "gridColumn56";
            this.gridColumn56.Visible = true;
            this.gridColumn56.VisibleIndex = 4;
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "总占用时长";
            this.gridColumn57.FieldName = "TotalTime";
            this.gridColumn57.Name = "gridColumn57";
            this.gridColumn57.Visible = true;
            this.gridColumn57.VisibleIndex = 5;
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "平均占用时长";
            this.gridColumn58.FieldName = "AvgTime";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 6;
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "区域";
            this.gridColumn59.FieldName = "Strvalue2";
            this.gridColumn59.Name = "gridColumn59";
            this.gridColumn59.Visible = true;
            this.gridColumn59.VisibleIndex = 7;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "级别";
            this.gridColumn60.FieldName = "ILevel";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 8;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "value";
            this.gridColumn61.FieldName = "Strvalue4";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 9;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "状态";
            this.gridColumn62.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn62.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn62.FieldName = "Strtype";
            this.gridColumn62.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn62.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 10;
            // 
            // gridView6
            // 
            this.gridView6.GridControl = this.gridControlTest;
            this.gridView6.Name = "gridView6";
            // 
            // gridControlPer
            // 
            this.gridControlPer.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControlPer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlPer.Location = new System.Drawing.Point(3, 3);
            this.gridControlPer.MainView = this.bandedGridView1;
            this.gridControlPer.Name = "gridControlPer";
            this.gridControlPer.Size = new System.Drawing.Size(883, 257);
            this.gridControlPer.TabIndex = 3;
            this.gridControlPer.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1,
            this.gridViewPer,
            this.gridView4});
            this.gridControlPer.Click += new System.EventHandler(this.gridControl_Click);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.导出ExcelToolStripMenuItem,
            this.查看性能ToolStripMenuItem3});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(128, 48);
            // 
            // 导出ExcelToolStripMenuItem
            // 
            this.导出ExcelToolStripMenuItem.Name = "导出ExcelToolStripMenuItem";
            this.导出ExcelToolStripMenuItem.Size = new System.Drawing.Size(127, 22);
            this.导出ExcelToolStripMenuItem.Text = "导出Excel";
            this.导出ExcelToolStripMenuItem.Click += new System.EventHandler(this.导出ExcelToolStripMenuItem_Click);
            // 
            // 查看性能ToolStripMenuItem3
            // 
            this.查看性能ToolStripMenuItem3.Name = "查看性能ToolStripMenuItem3";
            this.查看性能ToolStripMenuItem3.Size = new System.Drawing.Size(127, 22);
            this.查看性能ToolStripMenuItem3.Text = "查看性能";
            this.查看性能ToolStripMenuItem3.Click += new System.EventHandler(this.查看性能ToolStripMenuItem3_Click);
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1});
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn9,
            this.bandedGridColumn10,
            this.bandedGridColumn11,
            this.bandedGridColumn12});
            this.bandedGridView1.GridControl = this.gridControlPer;
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView1.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "性能预警小区";
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Columns.Add(this.bandedGridColumn2);
            this.gridBand1.Columns.Add(this.bandedGridColumn3);
            this.gridBand1.Columns.Add(this.bandedGridColumn4);
            this.gridBand1.Columns.Add(this.bandedGridColumn5);
            this.gridBand1.Columns.Add(this.bandedGridColumn6);
            this.gridBand1.Columns.Add(this.bandedGridColumn7);
            this.gridBand1.Columns.Add(this.bandedGridColumn8);
            this.gridBand1.Columns.Add(this.bandedGridColumn9);
            this.gridBand1.Columns.Add(this.bandedGridColumn10);
            this.gridBand1.Columns.Add(this.bandedGridColumn11);
            this.gridBand1.Columns.Add(this.bandedGridColumn12);
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 1200;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "编号";
            this.bandedGridColumn1.FieldName = "Id";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "小区名";
            this.bandedGridColumn2.FieldName = "Cellname";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            this.bandedGridColumn2.Width = 200;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "LAC";
            this.bandedGridColumn3.FieldName = "Lac";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "CI";
            this.bandedGridColumn4.FieldName = "Ci";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "性能类型";
            this.bandedGridColumn5.FieldName = "PerformanceType";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "问题类型";
            this.bandedGridColumn6.FieldName = "ProblemType";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "正常小时数";
            this.bandedGridColumn7.FieldName = "NormalHour";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.Visible = true;
            this.bandedGridColumn7.Width = 80;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "异常小时数";
            this.bandedGridColumn8.FieldName = "AbnormalHour";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.Visible = true;
            this.bandedGridColumn8.Width = 80;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "首次问题时间";
            this.bandedGridColumn9.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn9.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn9.FieldName = "Sdate";
            this.bandedGridColumn9.GroupFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn9.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.Visible = true;
            this.bandedGridColumn9.Width = 130;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "末次问题时间";
            this.bandedGridColumn10.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn10.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn10.FieldName = "Edate";
            this.bandedGridColumn10.GroupFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn10.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.Visible = true;
            this.bandedGridColumn10.Width = 130;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "更新时间";
            this.bandedGridColumn11.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn11.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn11.FieldName = "Udate";
            this.bandedGridColumn11.GroupFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn11.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.Visible = true;
            this.bandedGridColumn11.Width = 130;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "状态";
            this.bandedGridColumn12.FieldName = "Status";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.Visible = true;
            // 
            // gridViewPer
            // 
            this.gridViewPer.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn85});
            this.gridViewPer.GridControl = this.gridControlPer;
            this.gridViewPer.Name = "gridViewPer";
            this.gridViewPer.OptionsBehavior.Editable = false;
            this.gridViewPer.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewPer.OptionsView.ColumnAutoWidth = false;
            this.gridViewPer.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "编号";
            this.gridColumn41.FieldName = "Id";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 0;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "小区名";
            this.gridColumn42.FieldName = "Cellname";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 1;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "LAC";
            this.gridColumn43.FieldName = "Lac";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 2;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "CI";
            this.gridColumn44.FieldName = "Ci";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 3;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "性能类型";
            this.gridColumn45.FieldName = "PerformanceType";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 4;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "问题类型";
            this.gridColumn46.FieldName = "ProblemType";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 5;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "正常小时数";
            this.gridColumn47.FieldName = "NormalHour";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 6;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "异常小时数";
            this.gridColumn48.FieldName = "AbnormalHour";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 7;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "首次问题时间";
            this.gridColumn49.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn49.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn49.FieldName = "Sdate";
            this.gridColumn49.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn49.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 8;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "末次问题时间";
            this.gridColumn50.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn50.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn50.FieldName = "Edate";
            this.gridColumn50.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn50.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 9;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "更新时间";
            this.gridColumn51.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn51.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn51.FieldName = "Udate";
            this.gridColumn51.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn51.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 10;
            // 
            // gridColumn85
            // 
            this.gridColumn85.Caption = "状态";
            this.gridColumn85.FieldName = "Status";
            this.gridColumn85.Name = "gridColumn85";
            this.gridColumn85.Visible = true;
            this.gridColumn85.VisibleIndex = 11;
            // 
            // gridView4
            // 
            this.gridView4.GridControl = this.gridControlPer;
            this.gridView4.Name = "gridView4";
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.tableLayoutPanel3);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(889, 526);
            this.xtraTabPage5.Text = "全部小区";
            // 
            // tableLayoutPanel3
            // 
            this.tableLayoutPanel3.ColumnCount = 1;
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel3.Controls.Add(this.gridControlTestAll, 0, 1);
            this.tableLayoutPanel3.Controls.Add(this.gridControlPerAll, 0, 0);
            this.tableLayoutPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel3.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel3.Name = "tableLayoutPanel3";
            this.tableLayoutPanel3.RowCount = 2;
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel3.Size = new System.Drawing.Size(889, 526);
            this.tableLayoutPanel3.TabIndex = 6;
            // 
            // gridControlTestAll
            // 
            this.gridControlTestAll.ContextMenuStrip = this.contextMenuStrip4;
            this.gridControlTestAll.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlTestAll.Location = new System.Drawing.Point(3, 266);
            this.gridControlTestAll.MainView = this.bandedGridView4;
            this.gridControlTestAll.Name = "gridControlTestAll";
            this.gridControlTestAll.Size = new System.Drawing.Size(883, 257);
            this.gridControlTestAll.TabIndex = 4;
            this.gridControlTestAll.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView4,
            this.gridViewTestAll,
            this.gridView5});
            this.gridControlTestAll.Click += new System.EventHandler(this.gridControl_Click);
            // 
            // contextMenuStrip4
            // 
            this.contextMenuStrip4.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem3,
            this.查看性能ToolStripMenuItem1});
            this.contextMenuStrip4.Name = "contextMenuStrip4";
            this.contextMenuStrip4.Size = new System.Drawing.Size(153, 70);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(152, 22);
            this.toolStripMenuItem3.Text = "导出Excel";
            this.toolStripMenuItem3.Click += new System.EventHandler(this.toolStripMenuItem3_Click);
            // 
            // 查看性能ToolStripMenuItem1
            // 
            this.查看性能ToolStripMenuItem1.Name = "查看性能ToolStripMenuItem1";
            this.查看性能ToolStripMenuItem1.Size = new System.Drawing.Size(152, 22);
            this.查看性能ToolStripMenuItem1.Text = "查看事件";
            this.查看性能ToolStripMenuItem1.Click += new System.EventHandler(this.查看性能ToolStripMenuItem1_Click);
            // 
            // bandedGridView4
            // 
            this.bandedGridView4.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand4});
            this.bandedGridView4.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn36,
            this.bandedGridColumn37,
            this.bandedGridColumn38,
            this.bandedGridColumn39,
            this.bandedGridColumn40,
            this.bandedGridColumn41,
            this.bandedGridColumn42,
            this.bandedGridColumn43,
            this.bandedGridColumn44,
            this.bandedGridColumn45,
            this.bandedGridColumn46});
            this.bandedGridView4.GridControl = this.gridControlTestAll;
            this.bandedGridView4.Name = "bandedGridView4";
            this.bandedGridView4.OptionsBehavior.Editable = false;
            this.bandedGridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView4.OptionsView.ShowDetailButtons = false;
            this.bandedGridView4.OptionsView.ShowGroupPanel = false;
            this.bandedGridView4.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.bandedGridView_CustomDrawCell);
            // 
            // gridBand4
            // 
            this.gridBand4.Caption = "测试预警小区";
            this.gridBand4.Columns.Add(this.bandedGridColumn36);
            this.gridBand4.Columns.Add(this.bandedGridColumn37);
            this.gridBand4.Columns.Add(this.bandedGridColumn38);
            this.gridBand4.Columns.Add(this.bandedGridColumn39);
            this.gridBand4.Columns.Add(this.bandedGridColumn40);
            this.gridBand4.Columns.Add(this.bandedGridColumn41);
            this.gridBand4.Columns.Add(this.bandedGridColumn42);
            this.gridBand4.Columns.Add(this.bandedGridColumn43);
            this.gridBand4.Columns.Add(this.bandedGridColumn44);
            this.gridBand4.Columns.Add(this.bandedGridColumn45);
            this.gridBand4.Columns.Add(this.bandedGridColumn46);
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 750;
            // 
            // bandedGridColumn36
            // 
            this.bandedGridColumn36.Caption = "时间";
            this.bandedGridColumn36.FieldName = "IMon";
            this.bandedGridColumn36.Name = "bandedGridColumn36";
            this.bandedGridColumn36.Visible = true;
            // 
            // bandedGridColumn37
            // 
            this.bandedGridColumn37.Caption = "小区名";
            this.bandedGridColumn37.FieldName = "StrCellName";
            this.bandedGridColumn37.Name = "bandedGridColumn37";
            this.bandedGridColumn37.Visible = true;
            // 
            // bandedGridColumn38
            // 
            this.bandedGridColumn38.Caption = "LAC";
            this.bandedGridColumn38.FieldName = "ILAC";
            this.bandedGridColumn38.Name = "bandedGridColumn38";
            this.bandedGridColumn38.Visible = true;
            // 
            // bandedGridColumn39
            // 
            this.bandedGridColumn39.Caption = "CI";
            this.bandedGridColumn39.FieldName = "ICI";
            this.bandedGridColumn39.Name = "bandedGridColumn39";
            this.bandedGridColumn39.Visible = true;
            // 
            // bandedGridColumn40
            // 
            this.bandedGridColumn40.Caption = "次数";
            this.bandedGridColumn40.FieldName = "INum";
            this.bandedGridColumn40.Name = "bandedGridColumn40";
            this.bandedGridColumn40.Visible = true;
            // 
            // bandedGridColumn41
            // 
            this.bandedGridColumn41.Caption = "总占用时长";
            this.bandedGridColumn41.FieldName = "TotalTime";
            this.bandedGridColumn41.Name = "bandedGridColumn41";
            this.bandedGridColumn41.Visible = true;
            // 
            // bandedGridColumn42
            // 
            this.bandedGridColumn42.Caption = "平均占用时长";
            this.bandedGridColumn42.FieldName = "AvgTime";
            this.bandedGridColumn42.Name = "bandedGridColumn42";
            this.bandedGridColumn42.Visible = true;
            // 
            // bandedGridColumn43
            // 
            this.bandedGridColumn43.Caption = "区域";
            this.bandedGridColumn43.FieldName = "Strvalue2";
            this.bandedGridColumn43.Name = "bandedGridColumn43";
            this.bandedGridColumn43.Visible = true;
            // 
            // bandedGridColumn44
            // 
            this.bandedGridColumn44.Caption = "问题级别";
            this.bandedGridColumn44.FieldName = "ILevel";
            this.bandedGridColumn44.Name = "bandedGridColumn44";
            this.bandedGridColumn44.Visible = true;
            // 
            // bandedGridColumn45
            // 
            this.bandedGridColumn45.Caption = "事件次数";
            this.bandedGridColumn45.FieldName = "Strvalue4";
            this.bandedGridColumn45.Name = "bandedGridColumn45";
            // 
            // bandedGridColumn46
            // 
            this.bandedGridColumn46.Caption = "状态";
            this.bandedGridColumn46.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.bandedGridColumn46.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn46.FieldName = "Strtype";
            this.bandedGridColumn46.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.bandedGridColumn46.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn46.Name = "bandedGridColumn46";
            this.bandedGridColumn46.Visible = true;
            // 
            // gridViewTestAll
            // 
            this.gridViewTestAll.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn63,
            this.gridColumn64,
            this.gridColumn65,
            this.gridColumn66,
            this.gridColumn67,
            this.gridColumn68,
            this.gridColumn69,
            this.gridColumn70,
            this.gridColumn71,
            this.gridColumn72,
            this.gridColumn73});
            this.gridViewTestAll.GridControl = this.gridControlTestAll;
            this.gridViewTestAll.Name = "gridViewTestAll";
            this.gridViewTestAll.OptionsBehavior.Editable = false;
            this.gridViewTestAll.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewTestAll.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "时间";
            this.gridColumn63.FieldName = "IMon";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 0;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "小区名";
            this.gridColumn64.FieldName = "StrCellName";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 1;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "LAC";
            this.gridColumn65.FieldName = "ILAC";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 2;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "CI";
            this.gridColumn66.FieldName = "ICI";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 3;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "次数";
            this.gridColumn67.FieldName = "INum";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 4;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "总占用时长";
            this.gridColumn68.FieldName = "TotalTime";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 5;
            // 
            // gridColumn69
            // 
            this.gridColumn69.Caption = "平均占用时长";
            this.gridColumn69.FieldName = "AvgTime";
            this.gridColumn69.Name = "gridColumn69";
            this.gridColumn69.Visible = true;
            this.gridColumn69.VisibleIndex = 6;
            // 
            // gridColumn70
            // 
            this.gridColumn70.Caption = "区域";
            this.gridColumn70.FieldName = "Strvalue2";
            this.gridColumn70.Name = "gridColumn70";
            this.gridColumn70.Visible = true;
            this.gridColumn70.VisibleIndex = 7;
            // 
            // gridColumn71
            // 
            this.gridColumn71.Caption = "级别";
            this.gridColumn71.FieldName = "ILevel";
            this.gridColumn71.Name = "gridColumn71";
            this.gridColumn71.Visible = true;
            this.gridColumn71.VisibleIndex = 8;
            // 
            // gridColumn72
            // 
            this.gridColumn72.Caption = "value";
            this.gridColumn72.FieldName = "Strvalue4";
            this.gridColumn72.Name = "gridColumn72";
            this.gridColumn72.Visible = true;
            this.gridColumn72.VisibleIndex = 9;
            // 
            // gridColumn73
            // 
            this.gridColumn73.Caption = "状态";
            this.gridColumn73.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn73.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn73.FieldName = "Strtype";
            this.gridColumn73.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn73.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn73.Name = "gridColumn73";
            this.gridColumn73.Visible = true;
            this.gridColumn73.VisibleIndex = 10;
            // 
            // gridView5
            // 
            this.gridView5.GridControl = this.gridControlTestAll;
            this.gridView5.Name = "gridView5";
            // 
            // gridControlPerAll
            // 
            this.gridControlPerAll.ContextMenuStrip = this.contextMenuStrip3;
            this.gridControlPerAll.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlPerAll.Location = new System.Drawing.Point(3, 3);
            this.gridControlPerAll.MainView = this.bandedGridView3;
            this.gridControlPerAll.Name = "gridControlPerAll";
            this.gridControlPerAll.Size = new System.Drawing.Size(883, 257);
            this.gridControlPerAll.TabIndex = 3;
            this.gridControlPerAll.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView3,
            this.gridViewPerAll,
            this.gridView8});
            this.gridControlPerAll.Click += new System.EventHandler(this.gridControl_Click);
            // 
            // contextMenuStrip3
            // 
            this.contextMenuStrip3.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem2,
            this.查看性能ToolStripMenuItem});
            this.contextMenuStrip3.Name = "contextMenuStrip3";
            this.contextMenuStrip3.Size = new System.Drawing.Size(128, 48);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(127, 22);
            this.toolStripMenuItem2.Text = "导出Excel";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.toolStripMenuItem2_Click);
            // 
            // 查看性能ToolStripMenuItem
            // 
            this.查看性能ToolStripMenuItem.Name = "查看性能ToolStripMenuItem";
            this.查看性能ToolStripMenuItem.Size = new System.Drawing.Size(127, 22);
            this.查看性能ToolStripMenuItem.Text = "查看性能";
            this.查看性能ToolStripMenuItem.Click += new System.EventHandler(this.查看性能ToolStripMenuItem_Click);
            // 
            // bandedGridView3
            // 
            this.bandedGridView3.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand3});
            this.bandedGridView3.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn24,
            this.bandedGridColumn25,
            this.bandedGridColumn26,
            this.bandedGridColumn27,
            this.bandedGridColumn28,
            this.bandedGridColumn29,
            this.bandedGridColumn30,
            this.bandedGridColumn31,
            this.bandedGridColumn32,
            this.bandedGridColumn33,
            this.bandedGridColumn34,
            this.bandedGridColumn35});
            this.bandedGridView3.GridControl = this.gridControlPerAll;
            this.bandedGridView3.Name = "bandedGridView3";
            this.bandedGridView3.OptionsBehavior.Editable = false;
            this.bandedGridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView3.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand3
            // 
            this.gridBand3.Caption = "性能预警小区";
            this.gridBand3.Columns.Add(this.bandedGridColumn24);
            this.gridBand3.Columns.Add(this.bandedGridColumn25);
            this.gridBand3.Columns.Add(this.bandedGridColumn26);
            this.gridBand3.Columns.Add(this.bandedGridColumn27);
            this.gridBand3.Columns.Add(this.bandedGridColumn28);
            this.gridBand3.Columns.Add(this.bandedGridColumn29);
            this.gridBand3.Columns.Add(this.bandedGridColumn30);
            this.gridBand3.Columns.Add(this.bandedGridColumn31);
            this.gridBand3.Columns.Add(this.bandedGridColumn32);
            this.gridBand3.Columns.Add(this.bandedGridColumn33);
            this.gridBand3.Columns.Add(this.bandedGridColumn34);
            this.gridBand3.Columns.Add(this.bandedGridColumn35);
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 1200;
            // 
            // bandedGridColumn24
            // 
            this.bandedGridColumn24.Caption = "编号";
            this.bandedGridColumn24.FieldName = "Id";
            this.bandedGridColumn24.Name = "bandedGridColumn24";
            this.bandedGridColumn24.Visible = true;
            // 
            // bandedGridColumn25
            // 
            this.bandedGridColumn25.Caption = "小区名";
            this.bandedGridColumn25.FieldName = "Cellname";
            this.bandedGridColumn25.Name = "bandedGridColumn25";
            this.bandedGridColumn25.Visible = true;
            this.bandedGridColumn25.Width = 200;
            // 
            // bandedGridColumn26
            // 
            this.bandedGridColumn26.Caption = "LAC";
            this.bandedGridColumn26.FieldName = "Lac";
            this.bandedGridColumn26.Name = "bandedGridColumn26";
            this.bandedGridColumn26.Visible = true;
            // 
            // bandedGridColumn27
            // 
            this.bandedGridColumn27.Caption = "CI";
            this.bandedGridColumn27.FieldName = "Ci";
            this.bandedGridColumn27.Name = "bandedGridColumn27";
            this.bandedGridColumn27.Visible = true;
            // 
            // bandedGridColumn28
            // 
            this.bandedGridColumn28.Caption = "性能类型";
            this.bandedGridColumn28.FieldName = "PerformanceType";
            this.bandedGridColumn28.Name = "bandedGridColumn28";
            this.bandedGridColumn28.Visible = true;
            // 
            // bandedGridColumn29
            // 
            this.bandedGridColumn29.Caption = "问题类型";
            this.bandedGridColumn29.FieldName = "ProblemType";
            this.bandedGridColumn29.Name = "bandedGridColumn29";
            this.bandedGridColumn29.Visible = true;
            // 
            // bandedGridColumn30
            // 
            this.bandedGridColumn30.Caption = "正常小时数";
            this.bandedGridColumn30.FieldName = "NormalHour";
            this.bandedGridColumn30.Name = "bandedGridColumn30";
            this.bandedGridColumn30.Visible = true;
            this.bandedGridColumn30.Width = 80;
            // 
            // bandedGridColumn31
            // 
            this.bandedGridColumn31.Caption = "异常小时数";
            this.bandedGridColumn31.FieldName = "AbnormalHour";
            this.bandedGridColumn31.Name = "bandedGridColumn31";
            this.bandedGridColumn31.Visible = true;
            this.bandedGridColumn31.Width = 80;
            // 
            // bandedGridColumn32
            // 
            this.bandedGridColumn32.Caption = "首次问题时间";
            this.bandedGridColumn32.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn32.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn32.FieldName = "Sdate";
            this.bandedGridColumn32.GroupFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn32.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn32.Name = "bandedGridColumn32";
            this.bandedGridColumn32.Visible = true;
            this.bandedGridColumn32.Width = 130;
            // 
            // bandedGridColumn33
            // 
            this.bandedGridColumn33.Caption = "末次问题时间";
            this.bandedGridColumn33.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn33.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn33.FieldName = "Edate";
            this.bandedGridColumn33.GroupFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn33.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn33.Name = "bandedGridColumn33";
            this.bandedGridColumn33.Visible = true;
            this.bandedGridColumn33.Width = 130;
            // 
            // bandedGridColumn34
            // 
            this.bandedGridColumn34.Caption = "更新时间";
            this.bandedGridColumn34.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn34.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn34.FieldName = "Udate";
            this.bandedGridColumn34.GroupFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.bandedGridColumn34.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn34.Name = "bandedGridColumn34";
            this.bandedGridColumn34.Visible = true;
            this.bandedGridColumn34.Width = 130;
            // 
            // bandedGridColumn35
            // 
            this.bandedGridColumn35.Caption = "状态";
            this.bandedGridColumn35.FieldName = "Status";
            this.bandedGridColumn35.Name = "bandedGridColumn35";
            this.bandedGridColumn35.Visible = true;
            // 
            // gridViewPerAll
            // 
            this.gridViewPerAll.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn74,
            this.gridColumn75,
            this.gridColumn76,
            this.gridColumn77,
            this.gridColumn78,
            this.gridColumn79,
            this.gridColumn80,
            this.gridColumn81,
            this.gridColumn82,
            this.gridColumn83,
            this.gridColumn84,
            this.gridColumn86});
            this.gridViewPerAll.GridControl = this.gridControlPerAll;
            this.gridViewPerAll.Name = "gridViewPerAll";
            this.gridViewPerAll.OptionsBehavior.Editable = false;
            this.gridViewPerAll.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewPerAll.OptionsView.ColumnAutoWidth = false;
            this.gridViewPerAll.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn74
            // 
            this.gridColumn74.Caption = "编号";
            this.gridColumn74.FieldName = "Id";
            this.gridColumn74.Name = "gridColumn74";
            this.gridColumn74.Visible = true;
            this.gridColumn74.VisibleIndex = 0;
            // 
            // gridColumn75
            // 
            this.gridColumn75.Caption = "小区名";
            this.gridColumn75.FieldName = "Cellname";
            this.gridColumn75.Name = "gridColumn75";
            this.gridColumn75.Visible = true;
            this.gridColumn75.VisibleIndex = 1;
            // 
            // gridColumn76
            // 
            this.gridColumn76.Caption = "LAC";
            this.gridColumn76.FieldName = "Lac";
            this.gridColumn76.Name = "gridColumn76";
            this.gridColumn76.Visible = true;
            this.gridColumn76.VisibleIndex = 2;
            // 
            // gridColumn77
            // 
            this.gridColumn77.Caption = "CI";
            this.gridColumn77.FieldName = "Ci";
            this.gridColumn77.Name = "gridColumn77";
            this.gridColumn77.Visible = true;
            this.gridColumn77.VisibleIndex = 3;
            // 
            // gridColumn78
            // 
            this.gridColumn78.Caption = "性能类型";
            this.gridColumn78.FieldName = "PerformanceType";
            this.gridColumn78.Name = "gridColumn78";
            this.gridColumn78.Visible = true;
            this.gridColumn78.VisibleIndex = 4;
            // 
            // gridColumn79
            // 
            this.gridColumn79.Caption = "问题类型";
            this.gridColumn79.FieldName = "ProblemType";
            this.gridColumn79.Name = "gridColumn79";
            this.gridColumn79.Visible = true;
            this.gridColumn79.VisibleIndex = 5;
            // 
            // gridColumn80
            // 
            this.gridColumn80.Caption = "正常小时数";
            this.gridColumn80.FieldName = "NormalHour";
            this.gridColumn80.Name = "gridColumn80";
            this.gridColumn80.Visible = true;
            this.gridColumn80.VisibleIndex = 6;
            // 
            // gridColumn81
            // 
            this.gridColumn81.Caption = "异常小时数";
            this.gridColumn81.FieldName = "AbnormalHour";
            this.gridColumn81.Name = "gridColumn81";
            this.gridColumn81.Visible = true;
            this.gridColumn81.VisibleIndex = 7;
            // 
            // gridColumn82
            // 
            this.gridColumn82.Caption = "首次问题时间";
            this.gridColumn82.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn82.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn82.FieldName = "Sdate";
            this.gridColumn82.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn82.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn82.Name = "gridColumn82";
            this.gridColumn82.Visible = true;
            this.gridColumn82.VisibleIndex = 8;
            // 
            // gridColumn83
            // 
            this.gridColumn83.Caption = "末次问题时间";
            this.gridColumn83.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn83.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn83.FieldName = "Edate";
            this.gridColumn83.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn83.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn83.Name = "gridColumn83";
            this.gridColumn83.Visible = true;
            this.gridColumn83.VisibleIndex = 9;
            // 
            // gridColumn84
            // 
            this.gridColumn84.Caption = "更新时间";
            this.gridColumn84.DisplayFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn84.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn84.FieldName = "Udate";
            this.gridColumn84.GroupFormat.FormatString = "yyyy-MM-dd HH-mm-ss";
            this.gridColumn84.GroupFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn84.Name = "gridColumn84";
            this.gridColumn84.Visible = true;
            this.gridColumn84.VisibleIndex = 10;
            // 
            // gridColumn86
            // 
            this.gridColumn86.Caption = "状态";
            this.gridColumn86.FieldName = "Status";
            this.gridColumn86.Name = "gridColumn86";
            this.gridColumn86.Visible = true;
            this.gridColumn86.VisibleIndex = 11;
            // 
            // gridView8
            // 
            this.gridView8.GridControl = this.gridControlPerAll;
            this.gridView8.Name = "gridView8";
            // 
            // XtraCellCoverForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(902, 562);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Name = "XtraCellCoverForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "测试联合性能预警";
            this.Deactivate += new System.EventHandler(this.XtraClusterForm_Deactivate);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlShow)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewShow)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlInfo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewInfo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.xtraTabPage4.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTest)).EndInit();
            this.contextMenuStrip2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTest)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlPer)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            this.tableLayoutPanel3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTestAll)).EndInit();
            this.contextMenuStrip4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTestAll)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlPerAll)).EndInit();
            this.contextMenuStrip3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPerAll)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox ckb1800;
        private System.Windows.Forms.CheckBox ckb900;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraGrid.GridControl gridControlShow;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewShow;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.GridControl gridControlInfo;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewInfo;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private DevExpress.XtraGrid.GridControl gridControlTest;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTest;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.GridControl gridControlPer;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewPer;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel3;
        private DevExpress.XtraGrid.GridControl gridControlTestAll;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTestAll;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn69;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.GridControl gridControlPerAll;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewPerAll;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn75;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn76;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn77;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn78;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn79;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn80;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn81;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn82;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn83;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn84;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Button button9;
        private System.Windows.Forms.Button button8;
        private System.Windows.Forms.Button button7;
        private System.Windows.Forms.Button button6;
        private System.Windows.Forms.Button button5;
        private System.Windows.Forms.Button button4;
        private System.Windows.Forms.Button button3;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Button button1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn85;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn86;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn38;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn39;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn40;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn41;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn42;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn43;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn44;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn45;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn46;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn35;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 导出ExcelToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip2;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip4;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip3;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem 查看性能ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 查看性能ToolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem 查看性能ToolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem 查看性能ToolStripMenuItem3;
    }
}