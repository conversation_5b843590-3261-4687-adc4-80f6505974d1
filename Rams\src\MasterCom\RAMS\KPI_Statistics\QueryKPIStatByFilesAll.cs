﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByFilesAll : QueryKpiStatByFiles
    {
        protected bool isQueryByTimePeriod;
        List<TimePeriod> periodList = null;

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11025, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            ReportPickerDlg dlg = new ReportPickerDlg();
            dlg.SetIsQueryByTimePeriod();//设置时间选项可选
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curReportStyle = dlg.Report;
            isQueryAllParams = dlg.IsQueryAllParams;
            isQueryByTimePeriod = dlg.IsQueryByTimePeriod;
            periodList = new List<TimePeriod>();
            if (isQueryByTimePeriod)//用时间段查询的话，把所有选择的时间段全部挑选出来
            {
                QueryCondition qCondition = MainModel.GetInstance().MainForm.GetQueryConditionTimePeriods();
                foreach (TimePeriod tp in qCondition.Periods)
                {
                    periodList.Add(tp);
                }
            }
            KpiDataManager = new KPIDataManager();
            return true;
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            if (isQueryByTimePeriod)
            {
                DateTime dt = JavaDate.GetDateTimeFromMilliseconds((long)singleStatData[KPIStatDataBase.FileTimeKey, KPIStatDataBase.NewGridFileTimeKey, - 1] * 1000L);
                TimePeriod timePeriod = getTimePeriod(dt);
                if (timePeriod != null)//没找到时间段且按照时间段查询直接返回
                {
                    KpiDataManager.AddStatData(string.Empty, timePeriod, curFile, singleStatData, false);
                }
            }
            else
            {
                KpiDataManager.AddStatData(string.Empty, string.Empty, curFile, singleStatData, false);
            }
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            if (isQueryByTimePeriod)
            {
                DateTime dt = evt.DateTime;
                TimePeriod timePeriod = getTimePeriod(dt);
                if (timePeriod != null)
                {
                    KpiDataManager.AddStatData(string.Empty, timePeriod, curFile, eventData, false);
                }
            }
            else
            {
                KpiDataManager.AddStatData(string.Empty, string.Empty, curFile, eventData, false);
            }
        }

        private TimePeriod getTimePeriod(DateTime dt)
        {
            TimePeriod timePeriod = null;
            foreach (TimePeriod tp in periodList)
            {
                if (tp.Contains(dt))
                {
                    timePeriod = tp;
                    break;
                }
            }
            return timePeriod;
        }
    }
}
