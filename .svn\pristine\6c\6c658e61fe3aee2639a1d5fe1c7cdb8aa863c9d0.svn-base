﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.RAMS.Net;
using MasterCom.MControls;

namespace MasterCom.RAMS.ZTFunc
{
    public class GridProblem
    {
        private int id;
        public int ID
        {
            get { return id; }
        }

        private int netGridID;
        public int NetGridID
        {
            get { return netGridID; }
        }

        private double ltLong;
        public double LTLong
        {
            get { return ltLong; }
        }

        private double ltLat;
        public double LTLat
        {
            get { return ltLat; }
        }

        private double brLong;
        public double BRLong
        {
            get { return brLong; }
        }

        private double brLat;
        public double BRLat
        {
            get { return brLat; }
        }

        private double midLong;
        public double MidLong
        {
            get { return midLong; }
        }

        private double midLat;
        public double MidLat
        {
            get { return midLat; }
        }

        private string areaAgent;
        public string AreaAgent
        {
            get { return areaAgent; }
        }

        private string areaOpt;
        public string AreaOpt
        {
            get { return areaOpt; }
        }

        private string areaATUGridID;
        public string AreaATUGridID
        {
            get { return areaATUGridID; }
        }

        private int status;
        public int Status
        {
            get { return status; }
        }
        public string StatusString
        {
            get { return status == 0 ? "已创建" : "已关闭"; }
        }

        private int repeatSixBatch;
        public int RepeatSixBatch
        {
            get { return repeatSixBatch; }
        }

        private int weight;
        public int Weight
        {
            get { return weight; }
        }

        private int createdYear;
        public int CreatedYear
        {
            get { return createdYear; }
        }

        private int createdBatch;
        public int CreatedBatch
        {
            get { return createdBatch; }
        }

        public string CreatedMonth
        {
            get
            {
                return getMonthStringFromBatch(createdYear, createdBatch);
            }
        }

        private int beginYear;
        public int BeginYear
        {
            get { return beginYear; }
        }

        private int beginBatch;
        public int BeginBatch
        {
            get { return beginBatch; }
        }

        private int lastAbnormalYear;
        public int LastAbnormalYear
        {
            get { return lastAbnormalYear; }
        }

        private int lastAbnormalBatch;
        public int LastAbnormalBatch
        {
            get { return lastAbnormalBatch; }
        }

        public string LastAbnormalMonth
        {
            get
            {
                return getMonthStringFromBatch(lastAbnormalYear, lastAbnormalBatch);
            }
        }

        private int closedYear;
        public int ClosedYear
        {
            get { return closedYear; }
        }

        private int closedBatch;
        public int ClosedBatch
        {
            get { return closedBatch; }
        }

        public string ClosedMonth
        {
            get
            {
                if (status == 0)
                {
                    return "";
                }
                return getMonthStringFromBatch(closedYear, closedBatch);
            }
        }

        private int goodDaysCount;
        public int GoodDaysCount
        {
            get { return goodDaysCount; }
        }

        private int lastTestYear;
        public int LastTestYear
        {
            get { return lastTestYear; }
        }

        private int lastTestBatch;
        public int LastTestBatch
        {
            get { return lastTestBatch; }
        }

        public string LastTestMonth
        {
            get
            {
                return getMonthStringFromBatch(lastTestYear, lastTestBatch);
            }
        }

        private int validateStatus;
        public int ValidateStatus
        {
            get { return validateStatus; }
        }
        public string ValidateStatusString
        {
            get 
            {
                if (validateStatus == 0)
                {
                    return "未验证";
                }
                else if (validateStatus == 1)
                {
                    return "验证正常";
                }
                else
                {
                    return "验证异常";
                }
            }
        }

        private int gridRepeatCount;
        public int GridRepeatCount
        {
            get { return gridRepeatCount; }
        }

        public List<GridKPIMonth> GridKPIMonthList { get; set; } = new List<GridKPIMonth>();

        public bool bQueryedKPI { get; set; } = false;

        public void Fill(Content content)
        {
            id = content.GetParamInt();
            netGridID = content.GetParamInt();
            int midLongitude = content.GetParamInt();
            int midLatitude = content.GetParamInt();
            this.midLong = Math.Round(0.0000001 * midLongitude, 5);
            this.midLat = Math.Round(0.0000001 * midLatitude, 5);
            this.ltLong = Math.Round(midLong - 0.002, 5);   //400米栅格
            this.ltLat = Math.Round(midLat + 0.0018, 5);
            this.brLong = Math.Round(midLong + 0.002, 5);
            this.brLat = Math.Round(midLat - 0.0018, 5);
            areaAgent = content.GetParamString();
            areaOpt = content.GetParamString();
            areaATUGridID = content.GetParamString();
            status = content.GetParamInt();
            weight = content.GetParamInt();
            repeatSixBatch = content.GetParamInt();
            createdYear = content.GetParamInt();
            createdBatch = content.GetParamInt();
            beginYear = content.GetParamInt();
            beginBatch = content.GetParamInt();
            lastAbnormalYear = content.GetParamInt();
            lastAbnormalBatch = content.GetParamInt();
            closedYear = content.GetParamInt();
            closedBatch = content.GetParamInt();
            goodDaysCount = content.GetParamInt();
            lastTestYear = content.GetParamInt();
            lastTestBatch = content.GetParamInt();
            validateStatus = content.GetParamInt();
            gridRepeatCount = content.GetParamInt();
        }

        internal bool Within(double x1, double y1, double x2, double y2)
        {
            if (midLong >= x1 && midLong <= x2 && midLat >= y1 && midLat <= y2)
            {
                return true;
            }
            return false;
        }

        private string getMonthStringFromBatch(int year, int batch)
        {
            int month = batch / 2;
            if (batch % 2 == 1)
            {
                month++;
            }
            string sMonth = month.ToString();
            if (sMonth.Length == 1)
            {
                sMonth = "0" + sMonth;
            }
            return year + "年" + sMonth + "月";
        }
    }

    public class GridKPIMonth
    {
        protected int year;
        public int Year
        {
            get { return year; }
        }

        protected int batch;
        public int Batch
        {
            get { return batch; }
        }

        public string Month
        {
            get
            {
                int batchTmp = batch;
                int month = batchTmp / 2;
                int monthBatch = 1;
                if (batchTmp % 2 == 1)
                {
                    month++;
                }
                else
                {
                    monthBatch = 2;
                }
                string sMonth = month.ToString();
                if (sMonth.Length == 1)
                {
                    sMonth = "0" + sMonth;
                }
                return year + "年" + sMonth + "月" + "第" + monthBatch + "轮";
            }
        }

        protected int netGridID;
        public int NetGridID
        {
            get { return netGridID; }
        }

        protected int isPoor;
        public int IsPoor
        {
            get { return isPoor; }
        }
        public string IsPoorString
        {
            get { return isPoor == 1 ? "是" : "否"; }
        }
    }

    public class GridKPIMonthGSM : GridKPIMonth
    {
        
        private int rxQualTotal = 0;
        public int RxQualTotal
        {
            get { return rxQualTotal; }
        }
        private float rxQual0_4Pct;
        private float rxQual5Pct;
        private float rxQual6_7Pct;
        private float mos2d8Pct;
        private int mosTotal = 0;
        public int MosTotal
        {
            get { return mosTotal; }
        }

        public string RxQual0_4Pct
        {
            get
            {
                if (rxQualTotal == 0)
                {
                    return "-";
                }
                return Math.Round(rxQual0_4Pct, 2).ToString() + "%";
            }
        }

        public string RxQual5Pct
        {
            get
            {
                if (rxQualTotal == 0)
                {
                    return "-";
                }
                return Math.Round(rxQual5Pct, 2).ToString() + "%";
            }
        }

        public string RxQual6_7Pct
        {
            get
            {
                if (rxQualTotal == 0)
                {
                    return "-";
                }
                return Math.Round(rxQual6_7Pct, 2).ToString() + "%";
            }
        }

        public string Mos2d8Pct
        {
            get
            {
                if (mosTotal == 0)
                {
                    return "-";
                }
                return Math.Round(mos2d8Pct, 2).ToString() + "%";
            }
        }

        public void Fill(Content content)
        {
            year = content.GetParamInt();
            batch = content.GetParamInt();
            netGridID = content.GetParamInt();
            isPoor = content.GetParamInt();
            rxQualTotal = content.GetParamInt();
            rxQual0_4Pct = content.GetParamFloat();
            rxQual5Pct = content.GetParamFloat();
            rxQual6_7Pct = content.GetParamFloat();
            mosTotal = content.GetParamInt();
            mos2d8Pct = content.GetParamFloat();
        }
    }

    public class GridKPIMonthTD : GridKPIMonth
    {
        private int blerTotal;
        public int BLERTotal
        {
            get { return blerTotal; }
        }

        private float bler5Pct;

        private float blerAvg;

        public string BLER5Pct
        {
            get
            {
                if (blerTotal == 0)
                {
                    return "-";
                }
                return Math.Round(bler5Pct, 2).ToString() + "%";
            }
        }

        public string BLERAvg
        {
            get
            {
                if (blerTotal == 0)
                {
                    return "-";
                }
                return Math.Round(blerAvg, 2).ToString();
            }
        }

        public void Fill(Content content)
        {
            year = content.GetParamInt();
            batch = content.GetParamInt();
            netGridID = content.GetParamInt();
            isPoor = content.GetParamInt();
            blerTotal = content.GetParamInt();
            bler5Pct = content.GetParamFloat();
            blerAvg = content.GetParamFloat();
        }
    }

    public class MapFormGridProblemRanges
    {

        public MapFormGridProblemRanges()
        {
            initialize();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> colorParams = new List<object>();
                param["GridProblemColorRanges"] = colorParams;
                foreach (ColorRange cr in GridProblemColorRanges)
                {
                    colorParams.Add(cr.Param);
                }
                return param;
            }
            set
            {
                GridProblemColorRanges.Clear();
                List<object> colorParams = (List<object>)value["GridProblemColorRanges"];
                foreach (object o in colorParams)
                {
                    Dictionary<string, object> colorParam = (Dictionary<string, object>)o;
                    ColorRange cr = new ColorRange();
                    cr.Param = colorParam;
                    GridProblemColorRanges.Add(cr);
                }
            }
        }

        private void initialize()
        {
            GridProblemColorRanges.Clear();
            GridProblemColorRanges.Add(new ColorRange(0, 2, Color.Cyan));
            GridProblemColorRanges.Add(new ColorRange(2, 4, Color.Lime));
            GridProblemColorRanges.Add(new ColorRange(4, 6, Color.Yellow));
            GridProblemColorRanges.Add(new ColorRange(6, 10, Color.Orange));
            GridProblemColorRanges.Add(new ColorRange(10, 18, Color.Red));
        }
        public List<ColorRange> GridProblemColorRanges { get; set; } = new List<ColorRange>();
    }
}
