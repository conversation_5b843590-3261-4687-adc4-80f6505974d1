﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class StationAcceptReportQuery : QueryBase
    {
        public StationAcceptReportQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "单站验收报告查询"; }
        }

        Dictionary<NetType, List<BtsAcceptRecordInfo_SX<string>>> btsRecordDic;

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22001, "单站验收报告查询");
        }

        StationAcceptReportCondtion curCondtion { get; set; }
        protected override bool isValidCondition()
        {
            StationAcceptReportDlg dlg = new StationAcceptReportDlg(curCondtion);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                curCondtion = dlg.Condtion;
                btsRecordDic = new Dictionary<NetType, List<BtsAcceptRecordInfo_SX<string>>>();
                return true;
            }
            return false;
        }

        protected override void query()
        {
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询单站验收报告...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                foreach (var type in curCondtion.TypeList)
                {
                    var query = new DiyQueryStationAcceptReport_SX(curCondtion,type);
                    query.Query();
                    btsRecordDic.Add(type, query.RecordList);
                }

                fireShowForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void fireShowForm()
        {
            StationAcceptReportForm frm = MainModel.GetInstance().CreateResultForm(typeof(StationAcceptReportForm)) as StationAcceptReportForm;
            frm.FillData(btsRecordDic);
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
