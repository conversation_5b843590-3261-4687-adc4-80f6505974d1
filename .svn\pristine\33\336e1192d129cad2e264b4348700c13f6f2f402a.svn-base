﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverBehindTimeQuery_W : ZTHandoverBehideTime_LTE
    {
        public HandoverBehindTimeQuery_W(MainModel mainModel) : base(mainModel)
        {
            Columns = new List<string>();
            Columns.Add("W_APP_type");
            Columns.Add("W_TotalRSCP");
            Columns.Add("W_SNeiRSCP");
            Columns.Add("W_SNeiFreq");
            Columns.Add("W_SNeiPSC");
            Columns.Add("W_SysLAI");
            Columns.Add("W_SysCellID");
            Columns.Add("W_frequency");
            Columns.Add("W_Reference_PSC");

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.WCDMA));
        }

        public override string Name
        {
            get
            {
                return "WCDMA切换不及时分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14031, this.Name);
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("WCDMA", "TotalRSCP");
        }

        protected override float? GetSCellRelev(TestPoint tp)
        {
            return (float?)tp["W_TotalRSCP"];
        }

        protected override float? GetNCellRelev(TestPoint tp, int index)
        {
            return (float?)tp["W_SNeiRSCP", index];
        }
        protected override object GetAppType(TestPoint tp)
        {
            return tp["W_APP_type"];
        }
    }
}
