﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class BcchTchScanRelatedInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BcchTchScanRelatedInfoForm));
            this.listView = new System.Windows.Forms.ListView();
            this.columnHeaderSN = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderMainCellName = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLAC = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCI = new System.Windows.Forms.ColumnHeader();
            this.columnHeadermainRxlevMean = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBCCH = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTCH = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderScanCellName = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderScanCellBCCH = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderScanCellTCH = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderScanCellRxlevMean = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderGridNum = new System.Windows.Forms.ColumnHeader();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.columnHeaderDistance = new System.Windows.Forms.ColumnHeader();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView
            // 
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderMainCellName,
            this.columnHeaderLAC,
            this.columnHeaderCI,
            this.columnHeadermainRxlevMean,
            this.columnHeaderBCCH,
            this.columnHeaderTCH,
            this.columnHeaderScanCellName,
            this.columnHeaderScanCellBCCH,
            this.columnHeaderScanCellTCH,
            this.columnHeaderScanCellRxlevMean,
            this.columnHeaderGridNum,
            this.columnHeaderDistance});
            resources.ApplyResources(this.listView, "listView");
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.Name = "listView";
            this.listView.ShowGroups = false;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            this.listView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listView_MouseDoubleClick);
            this.listView.DrawColumnHeader += new System.Windows.Forms.DrawListViewColumnHeaderEventHandler(this.listView_DrawColumnHeader);
            this.listView.DrawItem += new System.Windows.Forms.DrawListViewItemEventHandler(this.listView_DrawItem);
            // 
            // columnHeaderSN
            // 
            resources.ApplyResources(this.columnHeaderSN, "columnHeaderSN");
            // 
            // columnHeaderMainCellName
            // 
            resources.ApplyResources(this.columnHeaderMainCellName, "columnHeaderMainCellName");
            // 
            // columnHeaderLAC
            // 
            resources.ApplyResources(this.columnHeaderLAC, "columnHeaderLAC");
            // 
            // columnHeaderCI
            // 
            resources.ApplyResources(this.columnHeaderCI, "columnHeaderCI");
            // 
            // columnHeadermainRxlevMean
            // 
            resources.ApplyResources(this.columnHeadermainRxlevMean, "columnHeadermainRxlevMean");
            // 
            // columnHeaderBCCH
            // 
            resources.ApplyResources(this.columnHeaderBCCH, "columnHeaderBCCH");
            // 
            // columnHeaderTCH
            // 
            resources.ApplyResources(this.columnHeaderTCH, "columnHeaderTCH");
            // 
            // columnHeaderScanCellName
            // 
            resources.ApplyResources(this.columnHeaderScanCellName, "columnHeaderScanCellName");
            // 
            // columnHeaderScanCellBCCH
            // 
            resources.ApplyResources(this.columnHeaderScanCellBCCH, "columnHeaderScanCellBCCH");
            // 
            // columnHeaderScanCellTCH
            // 
            resources.ApplyResources(this.columnHeaderScanCellTCH, "columnHeaderScanCellTCH");
            // 
            // columnHeaderScanCellRxlevMean
            // 
            resources.ApplyResources(this.columnHeaderScanCellRxlevMean, "columnHeaderScanCellRxlevMean");
            // 
            // columnHeaderGridNum
            // 
            resources.ApplyResources(this.columnHeaderGridNum, "columnHeaderGridNum");
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            resources.ApplyResources(this.ToolStripMenuItemExport, "ToolStripMenuItemExport");
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // columnHeaderDistance
            // 
            resources.ApplyResources(this.columnHeaderDistance, "columnHeaderDistance");
            // 
            // BcchTchScanRelatedInfoForm
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.listView);
            this.Name = "BcchTchScanRelatedInfoForm";
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ListView listView;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderMainCellName;
        private System.Windows.Forms.ColumnHeader columnHeaderScanCellTCH;
        private System.Windows.Forms.ColumnHeader columnHeaderLAC;
        private System.Windows.Forms.ColumnHeader columnHeaderCI;
        private System.Windows.Forms.ColumnHeader columnHeadermainRxlevMean;
        private System.Windows.Forms.ColumnHeader columnHeaderBCCH;
        private System.Windows.Forms.ColumnHeader columnHeaderTCH;
        private System.Windows.Forms.ColumnHeader columnHeaderScanCellName;
        private System.Windows.Forms.ColumnHeader columnHeaderScanCellBCCH;
        private System.Windows.Forms.ColumnHeader columnHeaderScanCellRxlevMean;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private System.Windows.Forms.ColumnHeader columnHeaderGridNum;
        private System.Windows.Forms.ColumnHeader columnHeaderDistance;
    }
}