﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.CoverageCheck;
using MasterCom.RAMS.Chris.Util;
using DevExpress.XtraEditors;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS
{
    public partial class CPModeGridCellEditForm : BaseDialog
    {
        public enum ECarrier { 移动 = 1, 联通 = 2, 电信 = 3 }
        public enum ECompetitor { 主队 = 1, 客队 = 2 }
        public enum ECmpType { HOST_SUB_GUEST = 0, BOTH_STANDARD = 1 }
        public const string HOSTNULL = "只有客队";
        public const string GUESTNULL = "只有主队";
        public const string OTHERS = "其它";

        public const string HOST_SUB_GUEST = "主队-客队";
        public const string BOTH_STANDARD = "均达标(免比)";

        protected MapFormItemSelection ItemSelection;
        protected ItemSelectionPanel projPanelHost;
        protected ItemSelectionPanel projPanelGuest;
        protected ItemSelectionPanel servPanelHost;
        protected ItemSelectionPanel servPanelGuest;

        private Dictionary<string, string> paraDiscription = new Dictionary<string, string>();
        public CompareMode compareConfig { get; set; }
        private CompareParam paramTemp = null;
        private CompareParam paramSelect = null;
        private CompareDisplayColumn curDisplayColHost = null;
        private CompareDisplayColumn curDisplayColGuest = null;

        public CPModeGridCellEditForm(MapFormItemSelection itemSelection)
        {
            InitializeComponent();
            this.ItemSelection = itemSelection;
            
            initProjAndServ();
            initCarrier();
            initParaDiscription();
            initRangControl();
            enableControl();
        }

        private void enableControl()
        {
            cbxRangeHost.Checked = true;
            cbxRangeGuest.Checked = true;

            if (listBoxControlIndex.Items.Count <= 0)
            {
                btnUp.Enabled = false;
                btnDown.Enabled = false;
                btnDelete.Enabled = false;
                btnColorAdd.Enabled = false;
                btnColorModify.Enabled = false;
                btnColorDel.Enabled = false;
                labelColorHost.Enabled = false;
                labelColorGuest.Enabled = false;
                labelColorOthers.Enabled = false;
            }
        }

        private void initRangControl()
        {
            rangeSettingHost.NumericUpDownMin.Minimum = int.MinValue;
            rangeSettingHost.NumericUpDownMin.Maximum = int.MaxValue;
            rangeSettingHost.NumericUpDownMin.Value = -100;
            rangeSettingHost.NumericUpDownMin.Increment = 0.1M;
            rangeSettingHost.NumericUpDownMin.DecimalPlaces = 1;
            rangeSettingHost.NumericUpDownMax.Minimum = int.MinValue;
            rangeSettingHost.NumericUpDownMax.Maximum = int.MaxValue;
            rangeSettingHost.NumericUpDownMax.Value = -55;
            rangeSettingHost.NumericUpDownMax.Increment = 0.1M;
            rangeSettingHost.NumericUpDownMax.DecimalPlaces = 1;

            rangeSettingGuest.NumericUpDownMin.Minimum = int.MinValue;
            rangeSettingGuest.NumericUpDownMin.Maximum = int.MaxValue;
            rangeSettingGuest.NumericUpDownMin.Value = -100;
            rangeSettingGuest.NumericUpDownMin.Increment = 0.1M;
            rangeSettingGuest.NumericUpDownMin.DecimalPlaces = 1;
            rangeSettingGuest.NumericUpDownMax.Minimum = int.MinValue;
            rangeSettingGuest.NumericUpDownMax.Maximum = int.MaxValue;
            rangeSettingGuest.NumericUpDownMax.Value = -55;
            rangeSettingGuest.NumericUpDownMax.Increment = 0.1M;
            rangeSettingGuest.NumericUpDownMax.DecimalPlaces = 1;
        }
        
        private void initProjAndServ()
        {
            listViewServiceHost.Items.Clear();
            listViewServiceGuest.Items.Clear();
            if (mainModel.CategoryManager["ServiceType"] != null)
            {
                servPanelHost = new ItemSelectionPanel(toolStripDropDownServiceHost, listViewServiceHost, lbSvCountHost, ItemSelection, "ServiceType", true);
                servPanelGuest = new ItemSelectionPanel(toolStripDropDownServiceGuest, listViewServiceGuest, lbSvCountGuest, ItemSelection, "ServiceType", true);
                toolStripDropDownServiceHost.Items.Clear();
                toolStripDropDownServiceGuest.Items.Clear();
                servPanelHost.FreshItems();
                servPanelGuest.FreshItems();
                toolStripDropDownServiceHost.Items.Add(new ToolStripControlHost(servPanelHost));
                toolStripDropDownServiceGuest.Items.Add(new ToolStripControlHost(servPanelGuest));
            }
        }
        private void initCarrier()
        {
            radioGroupCarrierHost.Properties.Items.Clear();
            radioGroupCarrierGuest.Properties.Items.Clear();

            radioGroupCarrierHost.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.移动, "移动"));
            radioGroupCarrierHost.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.联通, "联通"));
            radioGroupCarrierHost.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.电信, "电信"));
            radioGroupCarrierHost.SelectedIndex = 0;

            radioGroupCarrierGuest.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.移动, "移动"));
            radioGroupCarrierGuest.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.联通, "联通"));
            radioGroupCarrierGuest.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.电信, "电信"));
            radioGroupCarrierGuest.SelectedIndex = 1;
        }

        private void initParaDiscription()
        {
            ConfigSetSwitch css = new ConfigSetSwitch();
            System.IO.DirectoryInfo directory = new System.IO.DirectoryInfo(string.Format(Application.StartupPath + "/config"));
            foreach (System.IO.FileInfo file in directory.GetFiles("statinitargs_kpi_*.xml"))
            {
                css.addData(ParamCfgItem.loadParamCfgFromFile(file.FullName));
            }
            if (css.paramNodesItems == null)
            {
                return;
            }
            paraDiscription.Clear();
            foreach (ParamCfgItem cfgItem in css.paramNodesItems)
            {
                ParamCfgItem.prepareDescriptionDic(paraDiscription, cfgItem);
            }
        }

        public void FillData(CompareMode compareConfig)
        {
            this.compareConfig = compareConfig;

            List<CompareParam> compareParamConfigList = compareConfig.CompareConfigList;
            foreach (CompareParam compareParamConfig in compareParamConfigList)
            {
                listBoxControlIndex.Items.Add(compareParamConfig);
            }
            if (listBoxControlIndex.Items.Count > 0)
            {
                listBoxControlIndex.SelectedIndex = 0;
            }
        }

        private void buttonServHost_Click(object sender, EventArgs e)
        {
            Point pt = new Point(buttonServHost.Width, buttonServHost.Height);
            toolStripDropDownServiceHost.Show(buttonServHost, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServGueest_Click(object sender, EventArgs e)
        {
            Point pt = new Point(buttonServGuest.Width, buttonServGuest.Height);
            toolStripDropDownServiceGuest.Show(buttonServGuest, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void rtxtFormularCmpHost_MouseUp(object sender, MouseEventArgs e)
        {
            int pos = rtxtFormularCmpHost.SelectionStart;
            string tokenStr = getTokenStrFrom(pos, rtxtFormularCmpHost.Text);
            string desc;
            if (paraDiscription.TryGetValue(tokenStr, out desc))
            {
                lbDescriptionHost.Text = desc;
            }
            else
            {
                lbDescriptionHost.Text = "";
            }
        }

        private void rtxtFormularCmpGuest_MouseUp(object sender, MouseEventArgs e)
        {
            int pos = rtxtFormularCmpGuest.SelectionStart;
            string tokenStr = getTokenStrFrom(pos, rtxtFormularCmpGuest.Text);
            string desc;
            if (paraDiscription.TryGetValue(tokenStr, out desc))
            {
                lbDescriptionGuest.Text = desc;
            }
            else
            {
                lbDescriptionGuest.Text = "";
            }
        }

        private string getTokenStrFrom(int pos, string str)
        {
            if (pos < 0 || pos > str.Length - 1)
            {
                return "";
            }
            int start = pos;
            int end = pos;
            while (start >= 0)//向前找
            {
                char ch = str[start];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                start--;
            }
            while (end <= str.Length - 1)//向后找
            {
                char ch = str[end];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                end++;
            }
            if (end <= str.Length && end > start)
            {
                return str.Substring(start + 1, end - start - 1);
            }
            return "";
        }

        private void listBoxControlIndex_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxControlIndex.SelectedItems.Count <= 0)
            {
                btnUp.Enabled = false;
                btnDown.Enabled = false;
                btnDelete.Enabled = false;
                dgvColorRange.Rows.Clear();
                btnColorAdd.Enabled = false;
                btnColorModify.Enabled = false;
                btnColorDel.Enabled = false;
                labelColorHost.Enabled = false;
                labelColorGuest.Enabled = false;
                labelColorOthers.Enabled = false;
                return;
            } 
            btnUp.Enabled = true;
            btnDown.Enabled = true;
            btnDelete.Enabled = true;
            btnColorAdd.Enabled = true;
            labelColorHost.Enabled = true;
            labelColorGuest.Enabled = true;
            labelColorOthers.Enabled = true;
            paramSelect = listBoxControlIndex.SelectedItem as CompareParam;
            if (paramTemp != null && paramSelect == paramTemp)
            {
                setCfgControlNull(paramSelect);
                return;
            }
            fillSettingControl(paramSelect);
            fillHostDisplayColumnView(paramSelect,null);
            fillGuestDisplayColumnView(paramSelect, null);
        }

        private void setCfgControlNull(CompareParam paramSelect)
        {
            listViewServiceHost.Items.Clear();
            listViewServiceGuest.Items.Clear();
            radioGroupCarrierHost.SelectedIndex = 0;
            radioGroupCarrierGuest.SelectedIndex = 1;
            rtxtFormularCmpHost.Text = "";
            rtxtFormularCmpGuest.Text = "";
            cbxRangeHost.Checked = true;
            cbxRangeGuest.Checked = true;
            showColorData(paramSelect.AlgorithmCfg);
        }

        private void fillSettingControl(CompareParam paramSelect)
        {
            listViewServiceHost.Items.Clear();
            CategoryEnum serviceCate = (CategoryEnum)CategoryManager.GetInstance()["ServiceType"];
            foreach (int servID in paramSelect.serviceList_A)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Text = serviceCate[servID].Name;
                lvi.Tag = serviceCate[servID].ID;
                listViewServiceHost.Items.Add(lvi);
            }
            listViewServiceGuest.Items.Clear();
            foreach (int servID in paramSelect.serviceList_B)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Text = serviceCate[servID].Name;
                lvi.Tag = serviceCate[servID].ID;
                listViewServiceGuest.Items.Add(lvi);
            }
            radioGroupCarrierHost.SelectedIndex = paramSelect.carrier_A - 1;
            radioGroupCarrierGuest.SelectedIndex = paramSelect.carrier_B - 1;
            rtxtFormularCmpHost.Text = paramSelect.formula_A;
            rtxtFormularCmpGuest.Text = paramSelect.formula_B;
            cbxRangeHost.Checked = paramSelect.isLimit_A;
            rangeSettingHost.Enabled = true;
            rangeSettingHost.Range = paramSelect.Range_A;
            cbxRangeGuest.Checked = paramSelect.isLimit_B;
            rangeSettingGuest.Enabled = true;
            rangeSettingGuest.Range = paramSelect.Range_B;
            showColorData(paramSelect.AlgorithmCfg);
            setSpecialLable(paramSelect.AlgorithmCfg);
            rangeSettingHost.Enabled = cbxRangeHost.Checked;
            rangeSettingGuest.Enabled = cbxRangeGuest.Checked;
            rabAnd.Checked = paramSelect.judgeByBndOr;

            if (rabAnd.Checked)
            {
                rabOr.Checked = false;
            } 
            else
            {
                rabOr.Checked = true;
            }
        }
        private void fillHostDisplayColumnView(CompareParam cmp, CompareDisplayColumn selCol)
        {
            #region 主队
            listDisplayHost.SelectedIndexChanged -= listDisplayHost_SelectedIndexChanged;
            listDisplayHost.Items.Clear();
            btnRemoveDisColHost.Enabled = false;
            foreach (CompareDisplayColumn cnd in cmp.displayColumnList_A)
            {
                listDisplayHost.Items.Add(cnd);
            }
            listDisplayHost.SelectedIndexChanged += listDisplayHost_SelectedIndexChanged;
            if (listDisplayHost.Items.Count > 0)
            {
                if (selCol != null)
                {
                    listDisplayHost.SelectedItem = selCol;
                }
                else
                {
                    listDisplayHost.SelectedIndex = 0;
                }
            }
            else
            {
                lbDisDescriptionHost.Text = "描述";
                rtxtDisColHost.Text = "";
                txtCaptionHost.Text = "";
            }
            #endregion
        }

        private void fillGuestDisplayColumnView(CompareParam cmp, CompareDisplayColumn selCol)
        {
            #region 客队
            listDisplayGuest.SelectedIndexChanged -= listDisplayGuest_SelectedIndexChanged;
            listDisplayGuest.Items.Clear();
            btnRemoveDisColGuest.Enabled = false;
            foreach (CompareDisplayColumn cnd in cmp.displayColumnList_B)
            {
                listDisplayGuest.Items.Add(cnd);
            }
            listDisplayGuest.SelectedIndexChanged += listDisplayGuest_SelectedIndexChanged;
            if (listDisplayGuest.Items.Count > 0)
            {
                if (selCol != null)
                {
                    listDisplayGuest.SelectedItem = selCol;
                }
                else
                {
                    listDisplayGuest.SelectedIndex = 0;
                }
            }
            else
            {
                lbDisDescriptionGuest.Text = "描述";
                rtxtDisColGuest.Text = "";
                txtCaptionGuest.Text = "";
            }
            #endregion
        }

        void listDisplayHost_SelectedIndexChanged(object sender, EventArgs e)
        {
            curDisplayColHost = listDisplayHost.SelectedItem as CompareDisplayColumn;
            if (curDisplayColHost == null)
            {
                btnRemoveDisColHost.Enabled = false;
                return;
            }
            btnRemoveDisColHost.Enabled = true;
            rtxtDisColHost.Text = curDisplayColHost.DisplayIndexName;
            txtCaptionHost.Text = curDisplayColHost.Caption;
        }
        void listDisplayGuest_SelectedIndexChanged(object sender, EventArgs e)
        {
            curDisplayColGuest = listDisplayGuest.SelectedItem as CompareDisplayColumn;
            if (curDisplayColGuest == null)
            {
                btnRemoveDisColGuest.Enabled = false;
                return;
            }
            btnRemoveDisColGuest.Enabled = true;
            rtxtDisColGuest.Text = curDisplayColGuest.DisplayIndexName;
            txtCaptionGuest.Text = curDisplayColGuest.Caption;
        }
        private void btnUp_Click(object sender, EventArgs e)
        {
            if (listBoxControlIndex.Items.Count <= 1)
            {
                return;
            }
            int idx = listBoxControlIndex.SelectedIndex;
            if (idx == 0)
            {
                return;
            }
            object item = listBoxControlIndex.SelectedItem;
            listBoxControlIndex.Items.RemoveAt(idx);
            listBoxControlIndex.Items.Insert(idx - 1, item);
            listBoxControlIndex.SelectedIndex = idx - 1;
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            if (listBoxControlIndex.Items.Count <= 1)
            {
                return;
            }
            int idx = listBoxControlIndex.SelectedIndex;
            if (idx == listBoxControlIndex.Items.Count - 1)
            {
                return;
            }
            object item = listBoxControlIndex.SelectedItem;
            listBoxControlIndex.Items.RemoveAt(idx);
            listBoxControlIndex.Items.Insert(idx + 1, item);
            listBoxControlIndex.SelectedIndex = idx + 1;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (paramTemp != null && !compareConfig.compareConfigDic.ContainsKey(paramTemp.name))
            {
                MessageBox.Show("模式[" + paramTemp.name + "]还没保存,请保存!", "提示");
                return;
            }
            CPModeAddForm mAddForm = new CPModeAddForm(compareConfig);
            if (mAddForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            paramTemp = new CompareParam();
            paramTemp.name = mAddForm.ModeName;
            paramTemp.sn = findIndex();
            listBoxControlIndex.Items.Add(paramTemp);
            listBoxControlIndex.SelectedIndex += 1;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (listBoxControlIndex.SelectedItem == null)
            {
                return;
            }
            if (MessageBox.Show("确定删除该模式?", "警告") == DialogResult.OK)
            {
                if (paramTemp != null && (paramSelect).name.Equals(paramTemp.name))
                {
                    paramTemp = null;
                }
                else if (compareConfig.compareConfigDic.ContainsKey(paramSelect.name))
                {
                    compareConfig.compareConfigDic.Remove(paramSelect.name);
                }
                listBoxControlIndex.Items.Remove(listBoxControlIndex.SelectedItem);
            }
        }

        private void simpleBtnSave_Click(object sender, EventArgs e)
        {
            rangeSettingHost.Enabled = true;
            rangeSettingGuest.Enabled = true;
            if (check())
            {
                if (paramTemp != null)
                {
                    fillSettingCfg(paramTemp);
                    compareConfig.compareConfigDic.Add(paramTemp.name, paramTemp);
                    paramTemp = null;
                }
                else if (listBoxControlIndex.SelectedItem != null)
                {
                    fillSettingCfg((CompareParam)listBoxControlIndex.SelectedItem);
                }
                if (compareConfig.saveConfig())
                {
                    MessageBox.Show("保存配置成功", "提示");
                }
                else { MessageBox.Show("保存配置失败", "提示"); }
            }
            rangeSettingHost.Enabled = cbxRangeHost.Checked;
            rangeSettingGuest.Enabled = cbxRangeGuest.Checked;
        }

        private bool check()
        {
            if (listBoxControlIndex.Items.Count <= 0)
            {
                MessageBox.Show("请添加竞对模式", "提示");
                return false;
            }
            else if (listViewServiceHost.Items.Count == 0 || listViewServiceGuest.Items.Count == 0)
            {
                MessageBox.Show("网络类型不能为空", "提示");
                return false;
            }
            else if (radioGroupCarrierHost.Properties.Items.Count == 0 ||
                radioGroupCarrierGuest.Properties.Items.Count == 0)
            {
                MessageBox.Show("竞对运营商不能为空", "提示");
                return false;
            }
            else if (rtxtFormularCmpHost.Text.Trim() == "" || rtxtFormularCmpGuest.Text.Trim() == "")
            {
                MessageBox.Show("竞对指标不能为空", "提示");
                return false;
            }
            return true;
        }

        Dictionary<string, object> settingCfgDic = new Dictionary<string, object>();
        private void fillSettingCfg(CompareParam param)
        {
            settingCfgDic.Clear();
            settingCfgDic["sn"] = param.sn;
            settingCfgDic["name"] = param.name;

            List<object> serviceAList = new List<object>();
            foreach (ListViewItem lviServ in listViewServiceHost.Items)
            {
                serviceAList.Add(lviServ.Tag);
            }

            List<object> dispalyList_A = new List<object>();
            foreach (CompareDisplayColumn col in param.displayColumnList_A)
            {
                dispalyList_A.Add(col.CfgParam);
            }

            settingCfgDic["serviceList_A"] = serviceAList;
            settingCfgDic["carrier_A"] = (int)(ECarrier)radioGroupCarrierHost.SelectedIndex + 1;
            settingCfgDic["formula_A"] = rtxtFormularCmpHost.Text.Trim();
            settingCfgDic["isLimit_A"] = cbxRangeHost.Checked;
            settingCfgDic["Range_A"] = rangeSettingHost.Range.Param;
            settingCfgDic["judgeByBndOr"] = rabAnd.Checked;
            settingCfgDic["displayColumnList_A"] = dispalyList_A;

            List<object> serviceBList = new List<object>();
            foreach (ListViewItem lviServ in listViewServiceGuest.Items)
            {
                serviceBList.Add(lviServ.Tag);
            }
            List<object> dispalyList_B = new List<object>();
            foreach (CompareDisplayColumn col in param.displayColumnList_B)
            {
                dispalyList_B.Add(col.CfgParam);
            }

            settingCfgDic["serviceList_B"] = serviceBList;
            settingCfgDic["carrier_B"] = (int)(ECarrier)radioGroupCarrierGuest.SelectedIndex + 1;
            settingCfgDic["formula_B"] = rtxtFormularCmpGuest.Text.Trim();
            settingCfgDic["isLimit_B"] = cbxRangeGuest.Checked;
            settingCfgDic["Range_B"] = rangeSettingGuest.Range.Param;
            settingCfgDic["displayColumnList_B"] = dispalyList_B;

            settingCfgDic["algorithmName"] = param.AlgorithmCfg.name;
            List<object> algorithmList = new List<object>();
            foreach (CPModeColorItem colorItem in param.AlgorithmCfg.colorItemList)
            {
                Dictionary<string, object> agthmDic = colorItem.Param;
                algorithmList.Add(agthmDic);
            }
            settingCfgDic["colorItems"] = algorithmList;

            List<object> standardList = new List<object>();
            foreach (CPModeColorItem standardItem in param.AlgorithmCfg.bothStandardList)
            {
                Dictionary<string, object> standardDic = standardItem.Param;
                standardList.Add(standardDic);
            }
            settingCfgDic["bothStandards"] = standardList;

            List<object> specialList = new List<object>();
            foreach (MasterCom.RAMS.Func.CoverageCheck.TextColorRange tcRange in param.AlgorithmCfg.specialColorList)
            {
                Dictionary<string, object> specialDic = tcRange.Param;
                specialDic["Visible"] = tcRange.Visible;
                specialList.Add(specialDic);
            }
            settingCfgDic["specials"] = specialList;
            param.Param = settingCfgDic;
        }

        private int findIndex()
        {
            int sn = 1;
            foreach (CompareParam cpParam in compareConfig.CompareConfigList)
            {
                if (cpParam.sn == sn)
                {
                    sn++;
                }
                else
                {
                    return sn;
                }
            }
            return sn + 1;
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void cbxRangeHost_CheckedChanged(object sender, EventArgs e)
        {
            if (!cbxRangeHost.Checked)
            {
                rangeSettingHost.Enabled = false;
            }
            else
            {
                rangeSettingHost.Enabled = true;
            }
        }

        private void cbxRangeGuest_CheckedChanged(object sender, EventArgs e)
        {
            if (!cbxRangeGuest.Checked)
            {
                rangeSettingGuest.Enabled = false;
            }
            else
            {
                rangeSettingGuest.Enabled = true;
            }
        }

        private void showColorData(CPModeAlgorithmItem algorithmItem)
        {
            dgvColorRange.Rows.Clear();
            if (algorithmItem == null) return;
            dgvColorRange.RowCount = algorithmItem.colorItemList.Count + algorithmItem.bothStandardList.Count;
            for (int i = 0; i < algorithmItem.colorItemList.Count; i++)
            {
                DataGridViewRow row = dgvColorRange.Rows[i];
                row.Cells[0].Value = algorithmItem.colorItemList[i].colorRange.description;
                row.Cells[1].Value = algorithmItem.colorItemList[i].range.Min.ToString() +
                    (algorithmItem.colorItemList[i].range.MinIncluded ? "<=" : "<") + "X" +
                    (algorithmItem.colorItemList[i].range.MaxIncluded ? "<=" : "<") +
                    algorithmItem.colorItemList[i].range.Max.ToString();
                row.Cells[2].Style.BackColor = algorithmItem.colorItemList[i].colorRange.color;
                row.Cells[2].Style.SelectionBackColor = algorithmItem.colorItemList[i].colorRange.color;
                row.Tag = algorithmItem.colorItemList[i];
            }
            for (int i = 0; i < algorithmItem.bothStandardList.Count; i++)
            {
                DataGridViewRow row = dgvColorRange.Rows[i + algorithmItem.colorItemList.Count];
                row.Cells[0].Value = algorithmItem.bothStandardList[i].colorRange.description;
                row.Cells[1].Value = "主队" +
                    (algorithmItem.bothStandardList[i].bHostMinInclude ? "≥" : "＞") + 
                    algorithmItem.bothStandardList[i].fHostMin +
                    " && 客队" +
                    (algorithmItem.bothStandardList[i].bGuestMinInclude ? "≥" : "＞") +
                    algorithmItem.bothStandardList[i].fGuestMin;
                row.Cells[2].Style.BackColor = algorithmItem.bothStandardList[i].colorRange.color;
                row.Cells[2].Style.SelectionBackColor = algorithmItem.bothStandardList[i].colorRange.color;
                row.Tag = algorithmItem.bothStandardList[i];
            }
        }

        private void setSpecialLable(CPModeAlgorithmItem algorithmItem)
        {
            foreach (MasterCom.RAMS.Func.CoverageCheck.TextColorRange tcRange in algorithmItem.specialColorList)
            {
                if (tcRange.description.Equals(CPModeGridCellEditForm.GUESTNULL))
                {
                    labelColorHost.BackColor = tcRange.color;
                }
                else if (tcRange.description.Equals(CPModeGridCellEditForm.HOSTNULL))
                {
                    labelColorGuest.BackColor = tcRange.color;
                }
                else if (tcRange.description.Equals(CPModeGridCellEditForm.OTHERS))
                {
                    labelColorOthers.BackColor = tcRange.color;
                }
            }
        }

        private void btnColorAdd_Click(object sender, EventArgs e)
        {
            CPTextColorEditDlg dlg = new CPTextColorEditDlg();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                switch (dlg.cpColorItem.CmpType)
                {
                    case (int)CPModeGridCellEditForm.ECmpType.HOST_SUB_GUEST:
                        paramSelect.AlgorithmCfg.colorItemList.Add(dlg.cpColorItem);
                        break;
                    case (int)CPModeGridCellEditForm.ECmpType.BOTH_STANDARD:
                        paramSelect.AlgorithmCfg.bothStandardList.Add(dlg.cpColorItem);
                        break;
                    default:
                        break;
                }
                showColorData(paramSelect.AlgorithmCfg);
                dgvColorRange.Invalidate();
            }
        }

        private void btnColorModify_Click(object sender, EventArgs e)
        {
            modifyColor();
        }

        private void btnColorDel_Click(object sender, EventArgs e)
        {
            CPModeColorItem colorSelect = paramSelect.AlgorithmCfg.colorItemList[dgvColorRange.SelectedRows[0].Index];
            switch (colorSelect.CmpType)
            {
                case (int)CPModeGridCellEditForm.ECmpType.HOST_SUB_GUEST:
                    paramSelect.AlgorithmCfg.colorItemList.Remove(colorSelect);
                    break;
                case (int)CPModeGridCellEditForm.ECmpType.BOTH_STANDARD:
                    paramSelect.AlgorithmCfg.bothStandardList.Remove(colorSelect);
                    break;
                default:
                    break;
            }
            showColorData(paramSelect.AlgorithmCfg);
            dgvColorRange.Invalidate();
        }

        private void dgvColorRange_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvColorRange.SelectedRows.Count > 0)
            {
                btnColorModify.Enabled = true;
                btnColorDel.Enabled = true;
            }
            else
            {
                btnColorModify.Enabled = false;
                btnColorDel.Enabled = false;
            }
        }

        private void dgvColorRange_DoubleClick(object sender, EventArgs e)
        {
            if (dgvColorRange.SelectedRows.Count > 0)
            {
                modifyColor();
            }
        }

        private void modifyColor()
        {
            CPTextColorEditDlg dlg = new CPTextColorEditDlg();
            dlg.Text = "修改显示颜色";
            CPModeColorItem colorSelect = dgvColorRange.SelectedRows[0].Tag as CPModeColorItem;
            int prevCmpType = colorSelect.CmpType;
            dlg.SetColorItem(colorSelect);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                colorSelect = dlg.cpColorItem;
                if (colorSelect.CmpType != prevCmpType)
                {
                    switch (prevCmpType)
                    {
                        case (int)CPModeGridCellEditForm.ECmpType.HOST_SUB_GUEST:
                            paramSelect.AlgorithmCfg.colorItemList.Remove(colorSelect);
                            paramSelect.AlgorithmCfg.bothStandardList.Add(colorSelect);
                            break;
                        case (int)CPModeGridCellEditForm.ECmpType.BOTH_STANDARD:
                            paramSelect.AlgorithmCfg.bothStandardList.Remove(colorSelect);
                            paramSelect.AlgorithmCfg.colorItemList.Add(colorSelect);
                            break;
                        default:
                            break;
                    }
                }
                showColorData(paramSelect.AlgorithmCfg);
                dgvColorRange.Invalidate();
            }
        }

        ExpEditDlg expDlg = null;
        private string getExpEditDlgFormula(string formulaText)
        {
            string formula = "";
            if (expDlg == null)
            {
                expDlg = new ExpEditDlg();
                expDlg.SelectSwitchParaTree(3);
            }
            else
            {
                expDlg.ReSetForm(formulaText);
            }
            if (expDlg.ShowDialog() != DialogResult.OK)
            {
                return formula;
            }
            formula = expDlg.GetExpInput();
            if (formula.StartsWith("{"))
            {
                formula = formula.Remove(0, 1);
            }
            if (formula.EndsWith("}"))
            {
                formula = formula.Remove(formula.Length - 1, 1);
            }
            return formula;
        }
        private void btnFormulaEditHost_Click(object sender, EventArgs e)
        {
            string strFormula = getExpEditDlgFormula(rtxtFormularCmpHost.Text);
            if (strFormula != "")
            {
                rtxtFormularCmpHost.Text = strFormula;
            }
        }

        private void btnFormulaEditGuest_Click(object sender, EventArgs e)
        {
            string strFormula = getExpEditDlgFormula(rtxtFormularCmpGuest.Text);
              if (strFormula != "")
              {
                  rtxtFormularCmpGuest.Text = strFormula;
              }
        }

        private ColorDialog colorDialog = new ColorDialog();
        private void labelColorHost_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorHost.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                TextColorRange tcRange = paramSelect.AlgorithmCfg.specialColorList[0];
                labelColorHost.BackColor = Color.FromArgb(colorDialog.Color.R, colorDialog.Color.G, colorDialog.Color.B);
                tcRange.color = labelColorHost.BackColor;
            }
        }

        private void labelColorGuest_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorGuest.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                TextColorRange tcRange = paramSelect.AlgorithmCfg.specialColorList[1];
                labelColorGuest.BackColor = Color.FromArgb(colorDialog.Color.R, colorDialog.Color.G, colorDialog.Color.B);
                tcRange.color = labelColorGuest.BackColor;
            }
        }

        private void labelColorOthers_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorOthers.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                TextColorRange tcRange = paramSelect.AlgorithmCfg.specialColorList[2];
                labelColorOthers.BackColor = Color.FromArgb(colorDialog.Color.R, colorDialog.Color.G, colorDialog.Color.B);
                tcRange.color = labelColorOthers.BackColor;
            }
        }

        private void btnEditDisColHost_Click(object sender, EventArgs e)
        {
            string strFormula = getExpEditDlgFormula(listDisplayHost.Text);
            if (strFormula != "")
            {
                rtxtDisColHost.Text = strFormula;
            }
        }

        private void btnEditDisColGuest_Click(object sender, EventArgs e)
        {
            string strFormula = getExpEditDlgFormula(listDisplayGuest.Text);
             if (strFormula != "")
             {
                 rtxtDisColGuest.Text = strFormula;
             }
        }

        private void rtxtDisColHost_MouseUp(object sender, MouseEventArgs e)
        {
            int pos = rtxtDisColHost.SelectionStart;
            string tokenStr = getTokenStrFrom(pos, rtxtDisColHost.Text);
            string desc;
            if (paraDiscription.TryGetValue(tokenStr, out desc))
            {
                lbDisDescriptionHost.Text = desc;
            }
            else
            {
                lbDisDescriptionHost.Text = "";
            }
        }

        private void rtxtDisColGuest_MouseUp(object sender, MouseEventArgs e)
        {
            int pos = rtxtDisColGuest.SelectionStart;
            string tokenStr = getTokenStrFrom(pos, rtxtDisColGuest.Text);
            string desc;
            if (paraDiscription.TryGetValue(tokenStr, out desc))
            {
                lbDisDescriptionGuest.Text = desc;
            }
            else
            {
                lbDisDescriptionGuest.Text = "";
            }
        }

        private void btnAddDisColHost_Click(object sender, EventArgs e)
        {
            if (txtCaptionHost.Text == "")
            {
                MessageBox.Show("列标题不能为空！");
                txtCaptionHost.Focus();
                return;
            }
            CompareDisplayColumn colHost = new CompareDisplayColumn(txtCaptionHost.Text, rtxtDisColHost.Text, "A");

            if (paramSelect.AddHostDisplayColumn(colHost))
            {
                fillHostDisplayColumnView(paramSelect, colHost);
            }
            else
            {
                MessageBox.Show("已存在相同名的参数列！");
                txtCaptionHost.Focus();
            }
        }

        private void btnRemoveDisColHost_Click(object sender, EventArgs e)
        {
            if (curDisplayColHost == null)
            {
                return;
            }
            paramSelect.displayColumnList_A.Remove(curDisplayColHost);
            curDisplayColHost = null;
            fillHostDisplayColumnView(paramSelect, null);
        }

        private void btnRemoveDisColGuest_Click(object sender, EventArgs e)
        {
            if (curDisplayColGuest == null)
            {
                return;
            }
            paramSelect.displayColumnList_B.Remove(curDisplayColGuest);
            curDisplayColGuest = null;
            fillGuestDisplayColumnView(paramSelect, null);
        }

        private void btnAddDisColGuest_Click(object sender, EventArgs e)
        {
            if (txtCaptionGuest.Text == "")
            {
                MessageBox.Show("列标题不能为空！");
                txtCaptionGuest.Focus();
                return;
            }
            CompareDisplayColumn colGuest = new CompareDisplayColumn(txtCaptionGuest.Text, rtxtDisColGuest.Text, "B");

            if (paramSelect.AddGuestDisplayColumn(colGuest))
            {
                fillGuestDisplayColumnView(paramSelect, colGuest);
            }
            else
            {
                MessageBox.Show("已存在相同名的参数列！");
                txtCaptionGuest.Focus();
            }
        }

        private void listDisplayHost_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            if (listDisplayHost.Items.Count > 0)
            {
                e.Graphics.DrawString(listDisplayHost.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
            }
        }

        private void listDisplayGuest_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            if (listDisplayGuest.Items.Count > 0)
            {
                e.Graphics.DrawString(listDisplayGuest.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
            }
        }

        private void rtxtDisColHost_TextChanged(object sender, EventArgs e)
        {
            if (rtxtDisColHost.Text == "")
            {
                btnAddDisColHost.Enabled = false;
                return;
            }
            rtxtDisColHost_MouseUp(null, null);
            btnAddDisColHost.Enabled = true;
        }

        private void rtxtDisColGuest_TextChanged(object sender, EventArgs e)
        {
            if (rtxtDisColGuest.Text == "")
            {
                btnAddDisColGuest.Enabled = false;
                return;
            }
            rtxtDisColGuest_MouseUp(null, null);
            btnAddDisColGuest.Enabled = true;
        }

        private void CPModeEditForm_Shown(object sender, EventArgs e)
        {
            expDlg = new ExpEditDlg();//预加载窗体，提高点击编辑时的反应速度
            expDlg.SelectSwitchParaTree(3);
        }

        private void CPModeEditForm_Activated(object sender, EventArgs e)
        {
            this.Refresh();
        }
    }
}
