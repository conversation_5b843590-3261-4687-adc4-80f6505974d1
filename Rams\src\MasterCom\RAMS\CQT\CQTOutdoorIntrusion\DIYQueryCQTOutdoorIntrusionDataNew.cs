﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTOutdoorIntrusionNew : DIYStatQuery
    {
        public DIYQueryCQTOutdoorIntrusionNew(MainModel mainModel, string netWorker)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            netType = netWorker;
        }
        readonly string netType;
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return ""; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            string str  = "查询" + netType + "业务覆盖入侵";
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21018, string.Format("Name[{0}], {1}", this.Name,str));
        }
       

        private readonly List<FileInfo> fileList = new List<FileInfo>();
        private readonly List<string> fileValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();
        readonly List<CQTInvadeCoverItem> cqtInvadeCoverItemList = new List<CQTInvadeCoverItem>();
        /// <summary>
        /// 准备查询数据
        /// </summary>
        protected override void query()
        {
            fileList.Clear();
            fileValueNameList.Clear();
            fileValueList.Clear();
            cqtInvadeCoverItemList.Clear();
            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);
            //找出所有文件中包含的地点名称
            foreach (FileInfo fileIn in fileList)
            { 
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
            //每个地点所涉及的文件
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in fileList)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
            WaitBox.Show("开始分析各个测试地点的覆盖入侵情况", cqtCoverIntrusion);
            CQTOutdoorIntrusionFrmNew cqtFrm = new CQTOutdoorIntrusionFrmNew(MainModel, netType);
            cqtFrm.Show();
            cqtFrm.InvadeCoverList = cqtInvadeCoverItemList;
            cqtFrm.Refresh();
        }
        private void cqtCoverIntrusion()
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            int no = 1;
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                    break;
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的覆盖入侵情况...";
                WaitBox.ProgressPercent = 30;
                if (netType.Equals("GSM"))
                {
                    List<CQTCellSetSubInfo> cqtCellSetSubInfo = getMainAndNbcellDetailGSM(replayCQTFileTest(fileValueList[cpn]));
                    CQTInvadeCoverItem cqtInvadeCoverItem = anaInvadeCover(cqtCellSetSubInfo, cpn, no);
                    if (cqtInvadeCoverItem.cellSetList.Count == 0 || cqtInvadeCoverItem.StrProType == "")
                        continue;
                    cqtInvadeCoverItemList.Add(cqtInvadeCoverItem);
                    no++;
                }
                else if (netType.Equals("TD"))
                {
                    List<CQTCellSetSubInfo> cqtCellSetSubInfo = getMainAndNbcellDetailTD(replayCQTFileTest(fileValueList[cpn]));
                    CQTInvadeCoverItem cqtInvadeCoverItem = anaInvadeCover(cqtCellSetSubInfo, cpn, no);
                    if (cqtInvadeCoverItem.cellSetList.Count == 0 || cqtInvadeCoverItem.StrProType == "")
                        continue;
                    cqtInvadeCoverItemList.Add(cqtInvadeCoverItem);
                    no++;
                }
            }
            WaitBox.Close();
        }
        /// <summary>
        /// 回放文件获取每个CQT地点涉及文件所有有效的采样点
        /// </summary>
        private List<TestPoint> replayCQTFileTest(List<FileInfo> filelist)
        { 
            List<TestPoint> fileTestPointList = new List<TestPoint>();
            QueryCondition condition = new QueryCondition();
            condition.FileInfos.AddRange(filelist);
            ReplayFileCQT query = new ReplayFileCQT(mainModel);
            query.SetQueryCondition(condition);
            query.Query();
            foreach (TestPoint tp in query.testPointList)
            {
                if (WaitBox.CancelRequest)
                    break;
                addFileTestPointList(fileTestPointList, tp);
            }
            return fileTestPointList;
        }

        private void addFileTestPointList(List<TestPoint> fileTestPointList, TestPoint tp)
        {
            if (netType.Equals("GSM"))
            {
                int? iCi = (int?)tp["CI"];
                int? iRxlevSub = (int?)(short?)tp["RxLevSub"];
                if (iCi != null && iRxlevSub != null && iCi > 0 && iRxlevSub <= -10 && iRxlevSub >= -150)
                {
                    fileTestPointList.Add(tp);
                }
            }
            else if (netType.Equals("TD"))
            {
                float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
                if (rscp <= -10 && rscp >= -150)
                {
                    fileTestPointList.Add(tp);
                }
            }
            else
            {
                fileTestPointList.Add(tp);
            }
        }

        /// <summary>
        /// 获取主邻小区及主服采样点、电平等信息（GSM）
        /// </summary>
        private List<CQTCellSetSubInfo> getMainAndNbcellDetailGSM(List<TestPoint> filePointList)
        {
            Dictionary<Cell, List<int>> cellMainNbRxlev = new Dictionary<Cell, List<int>>();//小区主邻服电平（GSM）
            Dictionary<Cell, List<int>> cellMainRxlev = new Dictionary<Cell, List<int>>();//小区主服电平（GSM）
            Dictionary<Cell, int> cellAvgRxlev = new Dictionary<Cell, int>(); //小区平均主服电平（GSM）

            List<CQTCellSetSubInfo> cqtCellInfoList = new List<CQTCellSetSubInfo>();
            try
            {
                dealTestpoint(filePointList, cellMainNbRxlev, cellMainRxlev);
            }
            catch
            {
                //continue
            }
            int mainTestPoint = 0;  //主服所有采样点数                               
            mainTestPoint = addcellAvgRxlev(cellMainRxlev, cellAvgRxlev, mainTestPoint);
            //将归并的信息转化为窗体输出的下层信息
            foreach (Cell cell in cellMainNbRxlev.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;
                CQTCellSetSubInfo cqtCellSetSubInfo = new CQTCellSetSubInfo();
                cqtCellSetSubInfo.Strcellname = cell.Name;
                cqtCellSetSubInfo.Ilac = cell.LAC;
                cqtCellSetSubInfo.Ici = cell.CI;
                if (cell.Type == BTSType.Indoor)
                    cqtCellSetSubInfo.Strtype = "室内";
                else if (cell.Type == BTSType.Outdoor)
                    cqtCellSetSubInfo.Strtype = "室外";
                List<int> mainNbRxlev = cellMainNbRxlev[cell];
                cqtCellSetSubInfo.Isamplenum = mainNbRxlev.Count;
                if (cellMainRxlev.ContainsKey(cell) && mainTestPoint != 0)
                {
                    cqtCellSetSubInfo.IServerSampleNum = cellMainRxlev[cell].Count;
                    cqtCellSetSubInfo.StrMainPointAvgRxlev = cellAvgRxlev[cell].ToString();
                }
                else
                {
                    cqtCellSetSubInfo.IServerSampleNum = 0;
                    cqtCellSetSubInfo.StrMainPointAvgRxlev = "-";
                }
                addCQTCellSetSubInfo(cqtCellInfoList, mainTestPoint, cqtCellSetSubInfo, mainNbRxlev);
            }
            return cqtCellInfoList;
        }

        private void dealTestpoint(List<TestPoint> filePointList, Dictionary<Cell, List<int>> cellMainNbRxlev, Dictionary<Cell, List<int>> cellMainRxlev)
        {
            foreach (TestPoint tp in filePointList)
            {
                if (WaitBox.CancelRequest)
                    break;
                int? lac = (int?)tp["LAC"];
                int? ci = (int?)tp["CI"];
                short? bcch = (short?)tp["BCCH"];
                byte? bsic = (byte?)tp["BSIC"];
                if (lac == null || ci == null || bcch == null || bsic == null)
                    continue;
                Cell cellCur = CellManager.GetInstance().GetCell(tp.DateTime, (ushort)lac, (ushort)ci);
                if (cellCur != null)
                {
                    addCellRxlev(cellMainNbRxlev, tp, cellCur);
                    addCellRxlev(cellMainRxlev, tp, cellCur);
                }
                dealNbTestPoint(cellMainNbRxlev, tp);
            }
        }

        private void addCellRxlev(Dictionary<Cell, List<int>> cellRxlev, TestPoint tp, Cell cellCur)
        {
            if (!cellRxlev.ContainsKey(cellCur))
            {
                List<int> rxlev = new List<int>();
                rxlev.Add((int)(short?)tp["RxLevSub"]);
                cellRxlev.Add(cellCur, rxlev);
            }
            else
                cellRxlev[cellCur].Add((int)(short?)tp["RxLevSub"]);
        }


        private void dealNbTestPoint(Dictionary<Cell, List<int>> cellMainNbRxlev, TestPoint tp)
        {
            for (int i = 0; i < 6; i++)
            {
                if (WaitBox.CancelRequest)
                    break;
                short? bcchCur = (short?)tp["N_BCCH", i];
                byte? bsicCur = (byte?)tp["N_BSIC", i];
                if (bcchCur == null || bsicCur == null || (int)bsicCur == 255 || tp.Longitude < 70 || tp.Latitude < 3)
                    continue;
                Cell cellNbCur = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short)bcchCur, (byte)bsicCur, tp.Longitude, tp.Latitude, null, null, null, null);
                if (cellNbCur != null)
                {
                    addCellRxlev(cellMainNbRxlev, tp, cellNbCur);
                }
            }
        }

        private static int addcellAvgRxlev(Dictionary<Cell, List<int>> cellMainRxlev, Dictionary<Cell, int> cellAvgRxlev, int mainTestPoint)
        {
            if (cellMainRxlev.Keys.Count > 0)
            {
                foreach (Cell cell in cellMainRxlev.Keys)
                {
                    if (WaitBox.CancelRequest)
                        break;
                    mainTestPoint += cellMainRxlev[cell].Count;
                    int sumMainTestPoint = 0;
                    foreach (int rxlev in cellMainRxlev[cell])
                    {
                        sumMainTestPoint += rxlev;
                    }
                    cellAvgRxlev.Add(cell, sumMainTestPoint / cellMainRxlev[cell].Count);
                }
            }

            return mainTestPoint;
        }

        /// <summary>
        /// 获取主邻小区及主服采样点、电平等信息（TD）
        /// </summary>
        private List<CQTCellSetSubInfo> getMainAndNbcellDetailTD(List<TestPoint> filePointList)
        {
            Dictionary<TDCell, List<int>> tdcellMainNbRxlev = new Dictionary<TDCell, List<int>>();//小区主邻服电平（TD）
            Dictionary<TDCell, List<int>> tdcellMainRxlev = new Dictionary<TDCell, List<int>>();//小区主服电平（TD）
            Dictionary<TDCell, int> tdcellAvgRxlev = new Dictionary<TDCell, int>(); //小区平均主服电平（TD）

            List<CQTCellSetSubInfo> cqtCellInfoList = new List<CQTCellSetSubInfo>();
            try
            {
                dealTDTestpoint(filePointList, tdcellMainNbRxlev, tdcellMainRxlev);
            }
            catch
            {
                //continue
            }
            int mainTestPoint = 0;  //主服所有采样点数                               
            mainTestPoint = addTdcellAvgRxlev(tdcellMainRxlev, tdcellAvgRxlev, mainTestPoint);
            //将归并的信息转化为窗体输出的下层信息
            foreach (TDCell cell in tdcellMainNbRxlev.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;
                CQTCellSetSubInfo cqtCellSetSubInfo = new CQTCellSetSubInfo();
                cqtCellSetSubInfo.Strcellname = cell.Name;
                cqtCellSetSubInfo.Ilac = cell.LAC;
                cqtCellSetSubInfo.Ici = cell.CI;
                if (cell.Type == TDNodeBType.Indoor)
                    cqtCellSetSubInfo.Strtype = "室内";
                else if (cell.Type == TDNodeBType.Outdoor)
                    cqtCellSetSubInfo.Strtype = "室外";
                List<int> mainNbRxlev = tdcellMainNbRxlev[cell];
                cqtCellSetSubInfo.Isamplenum = mainNbRxlev.Count;
                if (tdcellMainRxlev.ContainsKey(cell) && mainTestPoint != 0)
                {
                    cqtCellSetSubInfo.IServerSampleNum = tdcellMainRxlev[cell].Count;
                    cqtCellSetSubInfo.StrMainPointAvgRxlev = tdcellAvgRxlev[cell].ToString();
                }
                else
                {
                    cqtCellSetSubInfo.IServerSampleNum = 0;
                    cqtCellSetSubInfo.StrMainPointAvgRxlev = "-";
                }
                addCQTCellSetSubInfo(cqtCellInfoList, mainTestPoint, cqtCellSetSubInfo, mainNbRxlev);
            }
            return cqtCellInfoList;
        }

        private void dealTDTestpoint(List<TestPoint> filePointList, Dictionary<TDCell, List<int>> tdcellMainNbRxlev, Dictionary<TDCell, List<int>> tdcellMainRxlev)
        {
            foreach (TestPoint tp in filePointList)
            {
                if (WaitBox.CancelRequest)
                    break;
                int? lac = (int?)tp["TD_SCell_LAC"];
                int? ci = (int?)tp["TD_SCell_CI"];
                int? uarfcn = (int?)tp["TD_SCell_UARFCN"];
                int? cpi = (int?)tp["TD_SCell_CPI"];
                if (lac == null || ci == null || uarfcn == null || cpi == null || (int)cpi > 130)
                    continue;
                TDCell cellCur = CellManager.GetInstance().GetTDCell(tp.DateTime, (ushort)lac, (ushort)ci);
                if (cellCur != null)
                {
                    addTDCellRxlev(tdcellMainNbRxlev, tp, cellCur);
                    addTDCellRxlev(tdcellMainRxlev, tp, cellCur);
                }
                dealTDNbTestPoint(tdcellMainNbRxlev, tp);
            }
        }

        private void addTDCellRxlev(Dictionary<TDCell, List<int>> cellRxlev, TestPoint tp, TDCell cellCur)
        {
            if (!cellRxlev.ContainsKey(cellCur))
            {
                List<int> rxlev = new List<int>();
                rxlev.Add((int)(float?)tp["TD_PCCPCH_RSCP"]);
                cellRxlev.Add(cellCur, rxlev);
            }
            else
                cellRxlev[cellCur].Add((int)(float?)tp["TD_PCCPCH_RSCP"]);
        }

        private void dealTDNbTestPoint(Dictionary<TDCell, List<int>> tdcellMainNbRxlev, TestPoint tp)
        {
            for (int i = 0; i < 6; i++)
            {
                if (WaitBox.CancelRequest)
                    break;
                int? nuarfcn = (int?)tp["TD_NCell_UARFCN", i];
                int? ncpi = (int?)tp["TD_NCell_CPI", i];
                if (nuarfcn == null || ncpi == null || (int)ncpi > 130 || tp.Longitude < 70 || tp.Latitude < 3)
                    continue;
                TDCell cellNbCur = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, nuarfcn, ncpi, tp.Longitude, tp.Latitude, null, null, null, null);
                if (cellNbCur != null)
                {
                    addTDCellRxlev(tdcellMainNbRxlev, tp, cellNbCur);
                }
            }
        }

        private int addTdcellAvgRxlev(Dictionary<TDCell, List<int>> tdcellMainRxlev, Dictionary<TDCell, int> tdcellAvgRxlev, int mainTestPoint)
        {
            if (tdcellMainRxlev.Keys.Count > 0)
            {
                foreach (TDCell cell in tdcellMainRxlev.Keys)
                {
                    if (WaitBox.CancelRequest)
                        break;
                    mainTestPoint += tdcellMainRxlev[cell].Count;
                    int sumMainTestPoint = 0;
                    foreach (int rxlev in tdcellMainRxlev[cell])
                    {
                        sumMainTestPoint += rxlev;
                    }
                    tdcellAvgRxlev.Add(cell, sumMainTestPoint / tdcellMainRxlev[cell].Count);
                }
            }

            return mainTestPoint;
        }

        private void addCQTCellSetSubInfo(List<CQTCellSetSubInfo> cqtCellInfoList, int mainTestPoint,
          CQTCellSetSubInfo cqtCellSetSubInfo, List<int> mainNbRxlev)
        {
            cqtCellSetSubInfo.StrMainPointProportion = ((100.0 * cqtCellSetSubInfo.IServerSampleNum) / mainTestPoint).ToString("0.00") + "%";
            int maxRxlev = -999;
            int minRxlev = 0;
            int sumRxlev = 0;
            foreach (int rxlveMainNb in mainNbRxlev)
            {
                if (WaitBox.CancelRequest)
                    break;
                if (rxlveMainNb > maxRxlev)
                    maxRxlev = rxlveMainNb;
                if (rxlveMainNb < minRxlev)
                    minRxlev = rxlveMainNb;
                sumRxlev += rxlveMainNb;
            }
            cqtCellSetSubInfo.IMaxRxlev = maxRxlev;
            cqtCellSetSubInfo.IMinRxlev = minRxlev;
            cqtCellSetSubInfo.IAvgRxlev = sumRxlev / mainNbRxlev.Count;
            cqtCellInfoList.Add(cqtCellSetSubInfo);
        }

        /// <summary>
        /// 分析上层主信息
        /// </summary>
        private CQTInvadeCoverItem anaInvadeCover(List<CQTCellSetSubInfo> cellSetInfo, string cqtName, int id)
        {
            CQTInvadeCoverItem icItem = new CQTInvadeCoverItem();
            int iOutDoorCell = 0;
            int iInDoorCell = 0;
            int iOutDoorRxlev = -999;//室外最大电平
            int iInDoorRxlev = -999; //室内最大电平
            int iOutDoorMainRxlev = -999; //室外主服最大电平
            int iInDoorMainRxlev = -999;  //室内主服最大电平
            int iIndoor = 0;//室分的主服采样点数
            int iMainPoint = 0;//主服采样点总数
            foreach (CQTCellSetSubInfo cssi in cellSetInfo)
            {
                if (WaitBox.CancelRequest)
                    break;
                iMainPoint += cssi.IServerSampleNum;
                if (cssi.Strtype == "室内")
                {
                    setIndoorRxlev(ref iInDoorCell, ref iInDoorRxlev, ref iInDoorMainRxlev, ref iIndoor, cssi);
                }
                else
                {
                    setOutdoorRxlev(ref iOutDoorCell, ref iOutDoorRxlev, ref iOutDoorMainRxlev, cssi);
                }
            }
            if (cellSetInfo.Count > 0)
            {
                if (iInDoorCell == 0 && iOutDoorMainRxlev < -85)
                {
                    icItem.StrProType = "无室分且室外弱覆盖";
                }
                else if (iInDoorCell == 0)
                {
                    icItem.StrProType = "无室分";
                }
                else
                {
                    setStrProType(icItem, iOutDoorRxlev, iInDoorRxlev, iInDoorMainRxlev, iIndoor, iMainPoint);
                }
            }
            icItem.IIndoorCellNum = iInDoorCell;
            icItem.IOutdoorCellNum = iOutDoorCell;
            icItem.cellSetList = cellSetInfo;
            icItem.StrNo = id.ToString();
            icItem.StrCqtName = cqtName;

            return icItem;
        }

        private static void setIndoorRxlev(ref int iInDoorCell, ref int iInDoorRxlev, ref int iInDoorMainRxlev, ref int iIndoor, CQTCellSetSubInfo cssi)
        {
            iIndoor += cssi.IServerSampleNum;
            iInDoorCell++;
            if (cssi.IAvgRxlev > iInDoorRxlev)
            {
                iInDoorRxlev = cssi.IAvgRxlev;
                if (cssi.IServerSampleNum > 0 && cssi.IAvgRxlev > iInDoorMainRxlev)
                    iInDoorMainRxlev = cssi.IAvgRxlev;
            }
        }

        private static void setOutdoorRxlev(ref int iOutDoorCell, ref int iOutDoorRxlev, ref int iOutDoorMainRxlev, CQTCellSetSubInfo cssi)
        {
            iOutDoorCell++;
            if (cssi.IAvgRxlev > iOutDoorRxlev)
            {
                iOutDoorRxlev = cssi.IAvgRxlev;
                if (cssi.IServerSampleNum > 0 && cssi.IAvgRxlev > iOutDoorMainRxlev)
                    iOutDoorMainRxlev = cssi.IAvgRxlev;
            }
        }

        private void setStrProType(CQTInvadeCoverItem icItem, int iOutDoorRxlev, int iInDoorRxlev, int iInDoorMainRxlev, int iIndoor, int iMainPoint)
        {
            if (iInDoorMainRxlev == -999)
            {
                if (iOutDoorRxlev > iInDoorRxlev && iInDoorRxlev < -90)
                    icItem.StrProType = "室分弱覆盖";
                else if (iOutDoorRxlev > iInDoorRxlev && iInDoorRxlev < -85)
                    icItem.StrProType = "室分深度覆盖不足";
                else if (iOutDoorRxlev > iInDoorRxlev)
                    icItem.StrProType = "室外抢夺";
                else
                    icItem.StrProType = "";
            }
            else
            {
                double fmain = 1.0 * iIndoor / iMainPoint;
                if (iOutDoorRxlev >= -85 && fmain < 0.8)
                    icItem.StrProType = "室外入侵";
                else
                    icItem.StrProType = "";
            }
        }
    }
    public class CQTInvadeCoverItem
    {
        /// <summary>
        /// 序号
        /// </summary>
        public string StrNo { get; set; }
        /// <summary>
        /// CQT地点名称
        /// </summary>
        public string StrCqtName { get; set; }
        /// <summary>
        /// 入侵类型
        /// </summary>
        public string StrProType { get; set; }
        /// <summary>
        /// 室内小区数
        /// </summary>
        public int IIndoorCellNum { get; set; }
        /// <summary>
        /// 室外小区数
        /// </summary>
        public int IOutdoorCellNum { get; set; }

        public CQTInvadeCoverItem()
        {
            StrProType = "";
        }

        public List<CQTCellSetSubInfo> cellSetList { get; set; } = new List<CQTCellSetSubInfo>();
    }
    public class CQTCellSetSubInfo
    {
        /// <summary>
        /// 小区名
        /// </summary>
        public string Strcellname { get; set; }
        /// <summary>
        /// LAC
        /// </summary>
        public int Ilac { get; set; }
        /// <summary>
        /// CI
        /// </summary>
        public int Ici { get; set; }
        /// <summary>
        /// 站点类型
        /// </summary>
        public string Strtype { get; set; }
        /// <summary>
        /// 覆盖采样点数
        /// </summary>
        public int Isamplenum { get; set; }
        /// <summary>
        /// 主服采样点数
        /// </summary>
        public int IServerSampleNum { get; set; }
        /// <summary>
        /// 主服采样点占比
        /// </summary>
        public string StrMainPointProportion { get; set; }
        /// <summary>
        /// 主服平均电平
        /// </summary>
        public string StrMainPointAvgRxlev { get; set; }
        /// <summary>
        /// 电平最大值(dBm)
        /// </summary>
        public int IMaxRxlev { get; set; }
        /// <summary>
        /// 电平最小值(dBm)
        /// </summary>
        public int IMinRxlev { get; set; }
        /// <summary>
        /// 电平平均值(dBm)
        /// </summary>
        public int IAvgRxlev { get; set; }
        public CQTCellSetSubInfo()
        {
            IServerSampleNum = 0;
            Isamplenum = 0;
        }
    }
}
