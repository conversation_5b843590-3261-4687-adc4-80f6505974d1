﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class HighRSRPLowSINRNRSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.numMinRsrp = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.numMinDistance = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numMaxTPDistance = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.numMaxSINR = new System.Windows.Forms.NumericUpDown();
            this.chkTime = new System.Windows.Forms.CheckBox();
            this.numTime = new System.Windows.Forms.NumericUpDown();
            this.chkDistance = new System.Windows.Forms.CheckBox();
            this.chk2TPDistance = new System.Windows.Forms.CheckBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numPer = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRsrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPer)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(212, 217);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(131, 217);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确定";
            // 
            // numMinRsrp
            // 
            this.numMinRsrp.Location = new System.Drawing.Point(161, 17);
            this.numMinRsrp.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numMinRsrp.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numMinRsrp.Name = "numMinRsrp";
            this.numMinRsrp.Size = new System.Drawing.Size(80, 21);
            this.numMinRsrp.TabIndex = 0;
            this.numMinRsrp.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinRsrp.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(92, 22);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 12);
            this.label1.TabIndex = 61;
            this.label1.Text = "SS-RSRP≥";
            // 
            // numMinDistance
            // 
            this.numMinDistance.Enabled = false;
            this.numMinDistance.Location = new System.Drawing.Point(161, 110);
            this.numMinDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDistance.Name = "numMinDistance";
            this.numMinDistance.Size = new System.Drawing.Size(80, 21);
            this.numMinDistance.TabIndex = 3;
            this.numMinDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(248, 148);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 66;
            this.label7.Text = "米";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(247, 22);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 63;
            this.label3.Text = "dBm";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(247, 117);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 64;
            this.label4.Text = "米";
            // 
            // numMaxTPDistance
            // 
            this.numMaxTPDistance.Enabled = false;
            this.numMaxTPDistance.Location = new System.Drawing.Point(161, 141);
            this.numMaxTPDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxTPDistance.Name = "numMaxTPDistance";
            this.numMaxTPDistance.Size = new System.Drawing.Size(80, 21);
            this.numMaxTPDistance.TabIndex = 4;
            this.numMaxTPDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxTPDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(92, 54);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(59, 12);
            this.label5.TabIndex = 61;
            this.label5.Text = "SS-SINR≤";
            // 
            // numMaxSINR
            // 
            this.numMaxSINR.Location = new System.Drawing.Point(161, 48);
            this.numMaxSINR.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMaxSINR.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numMaxSINR.Name = "numMaxSINR";
            this.numMaxSINR.Size = new System.Drawing.Size(80, 21);
            this.numMaxSINR.TabIndex = 1;
            this.numMaxSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxSINR.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // chkTime
            // 
            this.chkTime.AutoSize = true;
            this.chkTime.Checked = true;
            this.chkTime.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkTime.Location = new System.Drawing.Point(71, 80);
            this.chkTime.Name = "chkTime";
            this.chkTime.Size = new System.Drawing.Size(84, 16);
            this.chkTime.TabIndex = 68;
            this.chkTime.Text = "持续时间≥";
            this.chkTime.UseVisualStyleBackColor = true;
            this.chkTime.CheckedChanged += new System.EventHandler(this.chkTime_CheckedChanged);
            // 
            // numTime
            // 
            this.numTime.Location = new System.Drawing.Point(161, 79);
            this.numTime.Maximum = new decimal(new int[] {
            3600,
            0,
            0,
            0});
            this.numTime.Name = "numTime";
            this.numTime.Size = new System.Drawing.Size(80, 21);
            this.numTime.TabIndex = 2;
            this.numTime.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTime.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // chkDistance
            // 
            this.chkDistance.AutoSize = true;
            this.chkDistance.Location = new System.Drawing.Point(71, 111);
            this.chkDistance.Name = "chkDistance";
            this.chkDistance.Size = new System.Drawing.Size(84, 16);
            this.chkDistance.TabIndex = 68;
            this.chkDistance.Text = "持续距离≥";
            this.chkDistance.UseVisualStyleBackColor = true;
            this.chkDistance.CheckedChanged += new System.EventHandler(this.chkDistance_CheckedChanged);
            // 
            // chk2TPDistance
            // 
            this.chk2TPDistance.AutoSize = true;
            this.chk2TPDistance.Location = new System.Drawing.Point(35, 142);
            this.chk2TPDistance.Name = "chk2TPDistance";
            this.chk2TPDistance.Size = new System.Drawing.Size(120, 16);
            this.chk2TPDistance.TabIndex = 68;
            this.chk2TPDistance.Text = "相邻采样点距离≤";
            this.chk2TPDistance.UseVisualStyleBackColor = true;
            this.chk2TPDistance.CheckedChanged += new System.EventHandler(this.chk2TPDistance_CheckedChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(248, 85);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 64;
            this.label2.Text = "秒";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(248, 54);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 69;
            this.label6.Text = "dB";
            // 
            // numPer
            // 
            this.numPer.Location = new System.Drawing.Point(161, 172);
            this.numPer.Name = "numPer";
            this.numPer.Size = new System.Drawing.Size(80, 21);
            this.numPer.TabIndex = 5;
            this.numPer.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPer.Value = new decimal(new int[] {
            70,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(78, 174);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(77, 12);
            this.label8.TabIndex = 61;
            this.label8.Text = "采样点占比≥";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(247, 174);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(11, 12);
            this.label9.TabIndex = 66;
            this.label9.Text = "%";
            // 
            // HighRSRPLowSINRNRSettingDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(299, 254);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.chk2TPDistance);
            this.Controls.Add(this.chkDistance);
            this.Controls.Add(this.chkTime);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numTime);
            this.Controls.Add(this.numMaxSINR);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.numMinRsrp);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.numMinDistance);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.numPer);
            this.Controls.Add(this.numMaxTPDistance);
            this.Name = "HighRSRPLowSINRNRSettingDlg";
            this.Text = "强信号弱质量设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMinRsrp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPer)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private System.Windows.Forms.NumericUpDown numMinRsrp;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numMinDistance;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numMaxTPDistance;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numMaxSINR;
        private System.Windows.Forms.CheckBox chkTime;
        private System.Windows.Forms.NumericUpDown numTime;
        private System.Windows.Forms.CheckBox chkDistance;
        private System.Windows.Forms.CheckBox chk2TPDistance;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numPer;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
    }
}