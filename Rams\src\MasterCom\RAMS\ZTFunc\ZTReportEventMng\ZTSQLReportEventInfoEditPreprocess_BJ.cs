﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSQLReportEventInfoEditPreprocess_BJ : DIYSQLBase
    {
        public ZTSQLReportEventInfoEditPreprocess_BJ(MainModel mainModel, ZTReportEventInfo_BJ reportEventInfo)
            : base(mainModel)
        {
            this.reportEventInfo = reportEventInfo;
        }

        private readonly ZTReportEventInfo_BJ reportEventInfo;

        public ZTReportEventInfo_BJ GetReportEventInfo()
        {
            return reportEventInfo;
        }

        protected override string getSqlTextString()
        {
            return string.Format("exec mc_sp_beijing_report_event_edit_preprocess {0},{1},'{2}','{3}','{4}';"
                  , reportEventInfo.FileID, reportEventInfo.SeqID, reportEventInfo.CauseProcess, reportEventInfo.CauseDetailProcess
                  , reportEventInfo.MethodProcess);
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[1];
            rType[index] = E_VType.E_String;

            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string result = null;
                    result = package.Content.GetParamString();
                    this.reportEventInfo.PreprocessTime = result;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "ZTSQLReportEventInfoEditPreprocess_BJ"; }
        }
    }
}
