﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class PerCellDetailShowTD
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.eleNameSelect1 = new MasterCom.RAMS.ZTFunc.EleNameSelectTD();
            this.gridControlDetail = new DevExpress.XtraGrid.GridControl();
            this.gridViewDetail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemDesc = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.FixedPanel = System.Windows.Forms.FixedPanel.Panel1;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.simpleButton2);
            this.splitContainer1.Panel1.Controls.Add(this.simpleButton1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.eleNameSelect1);
            this.splitContainer1.Panel2.Controls.Add(this.gridControlDetail);
            this.splitContainer1.Size = new System.Drawing.Size(502, 385);
            this.splitContainer1.SplitterDistance = 37;
            this.splitContainer1.TabIndex = 10;
            // 
            // simpleButton2
            // 
            this.simpleButton2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButton2.Location = new System.Drawing.Point(390, 7);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new System.Drawing.Size(98, 23);
            this.simpleButton2.TabIndex = 1;
            this.simpleButton2.Text = "返回";
            this.simpleButton2.Click += new System.EventHandler(this.simpleButton2_Click);
            // 
            // simpleButton1
            // 
            this.simpleButton1.Location = new System.Drawing.Point(12, 7);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(98, 23);
            this.simpleButton1.TabIndex = 0;
            this.simpleButton1.Text = "添加其它指标";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // eleNameSelect1
            // 
            this.eleNameSelect1.Location = new System.Drawing.Point(12, 0);
            this.eleNameSelect1.MainSelectedEleName = null;
            this.eleNameSelect1.Name = "eleNameSelect1";
            this.eleNameSelect1.Size = new System.Drawing.Size(200, 277);
            this.eleNameSelect1.TabIndex = 9;
            // 
            // gridControlDetail
            // 
            this.gridControlDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlDetail.Font = new System.Drawing.Font("Tahoma", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gridLevelNode1.RelationName = "Level1";
            this.gridControlDetail.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlDetail.Location = new System.Drawing.Point(0, 0);
            this.gridControlDetail.MainView = this.gridViewDetail;
            this.gridControlDetail.Name = "gridControlDetail";
            this.gridControlDetail.Size = new System.Drawing.Size(502, 344);
            this.gridControlDetail.TabIndex = 8;
            this.gridControlDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewDetail,
            this.gridView3});
            // 
            // gridViewDetail
            // 
            this.gridViewDetail.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewDetail.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewDetail.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridViewDetail.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewDetail.BestFitMaxRowCount = 50;
            this.gridViewDetail.ColumnPanelRowHeight = 50;
            this.gridViewDetail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gc1,
            this.gc2,
            this.gc3,
            this.gc4,
            this.gc5,
            this.gc6,
            this.gc7,
            this.gc8,
            this.gc9,
            this.gc10});
            this.gridViewDetail.GridControl = this.gridControlDetail;
            this.gridViewDetail.Name = "gridViewDetail";
            this.gridViewDetail.OptionsBehavior.Editable = false;
            this.gridViewDetail.OptionsSelection.InvertSelection = true;
            this.gridViewDetail.OptionsView.ColumnAutoWidth = false;
            this.gridViewDetail.OptionsView.ShowGroupPanel = false;
            this.gridViewDetail.RowCellStyle += new DevExpress.XtraGrid.Views.Grid.RowCellStyleEventHandler(this.gridViewDetail_RowCellStyle);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "时间";
            this.gridColumn1.FieldName = "tmdat";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 100;
            // 
            // gc1
            // 
            this.gc1.Caption = "CS域无线接通率";
            this.gc1.FieldName = "col1";
            this.gc1.Name = "gc1";
            this.gc1.Visible = true;
            this.gc1.VisibleIndex = 1;
            this.gc1.Width = 56;
            // 
            // gc2
            // 
            this.gc2.Caption = "CS域误块率";
            this.gc2.FieldName = "col2";
            this.gc2.Name = "gc2";
            this.gc2.Visible = true;
            this.gc2.VisibleIndex = 2;
            this.gc2.Width = 70;
            // 
            // gc3
            // 
            this.gc3.Caption = "语音业务无线掉话率";
            this.gc3.FieldName = "col3";
            this.gc3.Name = "gc3";
            this.gc3.Visible = true;
            this.gc3.VisibleIndex = 3;
            this.gc3.Width = 70;
            // 
            // gc4
            // 
            this.gc4.Caption = "接力切换成功率";
            this.gc4.FieldName = "col4";
            this.gc4.Name = "gc4";
            this.gc4.Visible = true;
            this.gc4.VisibleIndex = 4;
            this.gc4.Width = 85;
            // 
            // gc5
            // 
            this.gc5.Caption = "码资源利用率";
            this.gc5.FieldName = "col5";
            this.gc5.Name = "gc5";
            this.gc5.Visible = true;
            this.gc5.VisibleIndex = 5;
            this.gc5.Width = 85;
            // 
            // gc6
            // 
            this.gc6.Caption = "CS域RAB拥塞率";
            this.gc6.FieldName = "col6";
            this.gc6.Name = "gc6";
            this.gc6.Width = 70;
            // 
            // gc7
            // 
            this.gc7.Caption = "PS域RAB拥塞率";
            this.gc7.FieldName = "col7";
            this.gc7.Name = "gc7";
            // 
            // gc8
            // 
            this.gc8.Caption = "PS域RAB建立成功率";
            this.gc8.FieldName = "col8";
            this.gc8.Name = "gc8";
            // 
            // gc9
            // 
            this.gc9.Caption = "PS域误块率";
            this.gc9.FieldName = "col9";
            this.gc9.Name = "gc9";
            // 
            // gc10
            // 
            this.gc10.Caption = "PS域无线掉线率";
            this.gc10.FieldName = "col10";
            this.gc10.Name = "gc10";
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21});
            this.gridView3.GridControl = this.gridControlDetail;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsCustomization.AllowGroup = false;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "目标LAC";
            this.gridColumn19.FieldName = "targetLAC";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 0;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "目标CI";
            this.gridColumn20.FieldName = "targetCI";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 1;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "LAC_CI";
            this.gridColumn21.FieldName = "LAC_CI";
            this.gridColumn21.Name = "gridColumn21";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemDesc});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(95, 26);
            // 
            // ToolStripMenuItemDesc
            // 
            this.ToolStripMenuItemDesc.Name = "ToolStripMenuItemDesc";
            this.ToolStripMenuItemDesc.Size = new System.Drawing.Size(94, 22);
            this.ToolStripMenuItemDesc.Text = "导出";
            this.ToolStripMenuItemDesc.Click += new System.EventHandler(this.ToolStripMenuItemDesc_Click);
            // 
            // PerCellDetailShowTD
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(502, 385);
            this.Controls.Add(this.splitContainer1);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "PerCellDetailShowTD";
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "性能详情(TD)";
            this.Deactivate += new System.EventHandler(this.PerCellDetailShowTD_Deactivate);
            this.Load += new System.EventHandler(this.PerCellDetailShowTD_Load);
            this.VisibleChanged += new System.EventHandler(this.PerCellDetailShowTD_VisibleChanged);
            this.Validating += new System.ComponentModel.CancelEventHandler(this.PerCellDetailShowTD_Validating);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainer1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private EleNameSelectTD eleNameSelect1;
        private DevExpress.XtraGrid.GridControl gridControlDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gc1;
        private DevExpress.XtraGrid.Columns.GridColumn gc2;
        private DevExpress.XtraGrid.Columns.GridColumn gc3;
        private DevExpress.XtraGrid.Columns.GridColumn gc4;
        private DevExpress.XtraGrid.Columns.GridColumn gc5;
        private DevExpress.XtraGrid.Columns.GridColumn gc6;
        private DevExpress.XtraGrid.Columns.GridColumn gc7;
        private DevExpress.XtraGrid.Columns.GridColumn gc8;
        private DevExpress.XtraGrid.Columns.GridColumn gc9;
        private DevExpress.XtraGrid.Columns.GridColumn gc10;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemDesc;
    }
}