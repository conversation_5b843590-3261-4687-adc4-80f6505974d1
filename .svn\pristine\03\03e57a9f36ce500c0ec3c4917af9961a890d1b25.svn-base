﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class MultiCoverageSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numRxLevDValue = new DevExpress.XtraEditors.SpinEdit();
            this.numRxLevThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.chkCoFreq = new DevExpress.XtraEditors.CheckEdit();
            this.cbxCoFreqType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.cbxMultiCoverType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.radioGroupChannel = new DevExpress.XtraEditors.RadioGroup();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.numQualityDividingLine = new DevExpress.XtraEditors.SpinEdit();
            this.numMultiCoverageDividingLine = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCoFreq.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCoFreqType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxMultiCoverType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupChannel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numQualityDividingLine.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCoverageDividingLine.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F);
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(17, 23);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(96, 12);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "与最强信号差异 <";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F);
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(53, 52);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 12);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "信号强度 >";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(141, 19);
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevDValue.Size = new System.Drawing.Size(70, 21);
            this.numRxLevDValue.TabIndex = 2;
            // 
            // numRxLevThreshold
            // 
            this.numRxLevThreshold.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numRxLevThreshold.Location = new System.Drawing.Point(141, 48);
            this.numRxLevThreshold.Name = "numRxLevThreshold";
            this.numRxLevThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevThreshold.Size = new System.Drawing.Size(70, 21);
            this.numRxLevThreshold.TabIndex = 3;
            // 
            // chkCoFreq
            // 
            this.chkCoFreq.Location = new System.Drawing.Point(69, 78);
            this.chkCoFreq.Name = "chkCoFreq";
            this.chkCoFreq.Properties.Caption = "同频";
            this.chkCoFreq.Size = new System.Drawing.Size(58, 19);
            this.chkCoFreq.TabIndex = 4;
            this.chkCoFreq.CheckedChanged += new System.EventHandler(this.chkCoFreq_CheckedChanged);
            // 
            // cbxCoFreqType
            // 
            this.cbxCoFreqType.Enabled = false;
            this.cbxCoFreqType.Location = new System.Drawing.Point(141, 77);
            this.cbxCoFreqType.Name = "cbxCoFreqType";
            this.cbxCoFreqType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxCoFreqType.Size = new System.Drawing.Size(120, 21);
            this.cbxCoFreqType.TabIndex = 5;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(30, 111);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(84, 14);
            this.labelControl3.TabIndex = 6;
            this.labelControl3.Text = "显示重叠度类型";
            // 
            // cbxMultiCoverType
            // 
            this.cbxMultiCoverType.Location = new System.Drawing.Point(141, 108);
            this.cbxMultiCoverType.Name = "cbxMultiCoverType";
            this.cbxMultiCoverType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxMultiCoverType.Size = new System.Drawing.Size(120, 21);
            this.cbxMultiCoverType.TabIndex = 7;
            // 
            // simpleButton1
            // 
            this.simpleButton1.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.simpleButton1.Location = new System.Drawing.Point(84, 308);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(75, 23);
            this.simpleButton1.TabIndex = 8;
            this.simpleButton1.Text = "确定";
            // 
            // simpleButton2
            // 
            this.simpleButton2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.simpleButton2.Location = new System.Drawing.Point(221, 308);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new System.Drawing.Size(75, 23);
            this.simpleButton2.TabIndex = 9;
            this.simpleButton2.Text = "取消";
            // 
            // radioGroupChannel
            // 
            this.radioGroupChannel.EditValue = "900";
            this.radioGroupChannel.Location = new System.Drawing.Point(141, 142);
            this.radioGroupChannel.Name = "radioGroupChannel";
            this.radioGroupChannel.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem("900", "900"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem("1800", "1800"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem("All", "All")});
            this.radioGroupChannel.Size = new System.Drawing.Size(160, 21);
            this.radioGroupChannel.TabIndex = 10;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(90, 144);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(24, 14);
            this.labelControl4.TabIndex = 11;
            this.labelControl4.Text = "频段";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(21, 27);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(128, 14);
            this.labelControl5.TabIndex = 12;
            this.labelControl5.Text = "栅格RxQuality0-5划分界";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(51, 65);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(96, 14);
            this.labelControl6.TabIndex = 13;
            this.labelControl6.Text = "栅格重叠度划分界";
            // 
            // numQualityDividingLine
            // 
            this.numQualityDividingLine.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            0});
            this.numQualityDividingLine.Location = new System.Drawing.Point(165, 25);
            this.numQualityDividingLine.Name = "numQualityDividingLine";
            this.numQualityDividingLine.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numQualityDividingLine.Properties.IsFloatValue = false;
            this.numQualityDividingLine.Properties.Mask.EditMask = "N00";
            this.numQualityDividingLine.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numQualityDividingLine.Size = new System.Drawing.Size(70, 21);
            this.numQualityDividingLine.TabIndex = 14;
            // 
            // numMultiCoverageDividingLine
            // 
            this.numMultiCoverageDividingLine.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numMultiCoverageDividingLine.Location = new System.Drawing.Point(165, 62);
            this.numMultiCoverageDividingLine.Name = "numMultiCoverageDividingLine";
            this.numMultiCoverageDividingLine.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMultiCoverageDividingLine.Properties.IsFloatValue = false;
            this.numMultiCoverageDividingLine.Properties.Mask.EditMask = "N00";
            this.numMultiCoverageDividingLine.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numMultiCoverageDividingLine.Size = new System.Drawing.Size(70, 21);
            this.numMultiCoverageDividingLine.TabIndex = 15;
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(238, 29);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 14);
            this.labelControl7.TabIndex = 16;
            this.labelControl7.Text = "%";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.labelControl5);
            this.groupBox1.Controls.Add(this.labelControl7);
            this.groupBox1.Controls.Add(this.labelControl6);
            this.groupBox1.Controls.Add(this.numMultiCoverageDividingLine);
            this.groupBox1.Controls.Add(this.numQualityDividingLine);
            this.groupBox1.Location = new System.Drawing.Point(12, 198);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(357, 100);
            this.groupBox1.TabIndex = 17;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "栅格分界值";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.labelControl9);
            this.groupBox2.Controls.Add(this.labelControl8);
            this.groupBox2.Controls.Add(this.labelControl1);
            this.groupBox2.Controls.Add(this.numRxLevDValue);
            this.groupBox2.Controls.Add(this.radioGroupChannel);
            this.groupBox2.Controls.Add(this.labelControl4);
            this.groupBox2.Controls.Add(this.labelControl2);
            this.groupBox2.Controls.Add(this.numRxLevThreshold);
            this.groupBox2.Controls.Add(this.cbxMultiCoverType);
            this.groupBox2.Controls.Add(this.chkCoFreq);
            this.groupBox2.Controls.Add(this.cbxCoFreqType);
            this.groupBox2.Controls.Add(this.labelControl3);
            this.groupBox2.Location = new System.Drawing.Point(12, 12);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(357, 180);
            this.groupBox2.TabIndex = 18;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "重叠度";
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(214, 52);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(24, 14);
            this.labelControl9.TabIndex = 13;
            this.labelControl9.Text = "dBm";
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(215, 23);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(14, 14);
            this.labelControl8.TabIndex = 12;
            this.labelControl8.Text = "dB";
            // 
            // MultiCoverageSetForm
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(381, 347);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.simpleButton2);
            this.Controls.Add(this.simpleButton1);
            this.Name = "MultiCoverageSetForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCoFreq.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCoFreqType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxMultiCoverType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupChannel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numQualityDividingLine.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCoverageDividingLine.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numRxLevDValue;
        private DevExpress.XtraEditors.SpinEdit numRxLevThreshold;
        private DevExpress.XtraEditors.CheckEdit chkCoFreq;
        private DevExpress.XtraEditors.ComboBoxEdit cbxCoFreqType;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.ComboBoxEdit cbxMultiCoverType;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private DevExpress.XtraEditors.RadioGroup radioGroupChannel;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit numQualityDividingLine;
        private DevExpress.XtraEditors.SpinEdit numMultiCoverageDividingLine;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl9;
    }
}