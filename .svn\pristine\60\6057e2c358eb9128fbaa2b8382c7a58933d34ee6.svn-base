﻿using System;
using System.Collections.Generic;
using System.Text;
using AxMapWinGIS;
using MasterCom.MTGis;
using MapWinGIS;
using GMap.NET;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.MapControlTool
{
    class MapControlToolRectFlyLine
    {
        private MapForm mapForm { get; set; }
        private AxMap map { get; set; }
        private readonly MapControlToolAddRect rectTool;

        public event EventHandler RectangleCreated;

        public Shape Rectangle
        {
            get { return rectTool.Rectangle; }
        }

        public DbRect RectangleRegion
        {
            get { return rectTool.RectangleRegion; }
        }

        public MapControlToolRectFlyLine(MapForm mapForm, AxMap map)
        {
            this.map = map;
            this.mapForm = mapForm;
            rectTool = new MapControlToolAddRect(map);
            rectTool.RectangleCreated += FireRectangleCreated;
        }

        public void Clear()
        {
            rectTool.Clear();
        }

        public void Activate()
        {
            rectTool.Activate();
            SetRectLayerStyle();
        }

        public void Deactivate()
        {
            rectTool.Deactivate();
        }

        private void FireRectangleCreated(object sender, EventArgs e)
        {
            RectangleCreated(this, EventArgs.Empty);
            //mapForm.SelectDTDataByRegionTool();
        }

        private void SetRectLayerStyle()
        {
            if (rectTool.ShapeLayer == null)
            {
                return;
            }
            Shapefile sf = rectTool.ShapeLayer;
            sf.DefaultDrawingOptions.LineColor = (uint)System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.Blue);
            sf.DefaultDrawingOptions.LineWidth = 2;
            sf.DefaultDrawingOptions.FillVisible = false;
            //sf.DefaultDrawingOptions.FillColor = (uint)System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.Green);
            //sf.DefaultDrawingOptions.FillTransparency = 35;
        }
    }
}
