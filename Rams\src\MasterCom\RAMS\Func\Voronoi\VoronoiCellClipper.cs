﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MapWinGIS;
using MasterCom.RAMS.Func.Voronoi;

namespace MasterCom.RAMS.Func
{
    public static class VoronoiCellClipper
    {
        public static Dictionary<short, List<Vertex[]>> DoClip(Vertex origin, List<Vertex[]> origPoly, List<short> angles)   
        {
            Dictionary<short, List<Vertex[]>> retDict = new Dictionary<short, List<Vertex[]>>();
            if (angles.Count == 0 || origPoly.Count == 0)
            {
                return retDict;
            }
            if (angles.Count == 1)
            {
                retDict.Add(angles[0], origPoly);
                return retDict;
            }

            // 获取切割多边形列表
            List<double> rayRads = AngleToRadians(angles);
            List<double> halfRads = BisectRadians(rayRads);
            List<Vertex> rayPts = GetInfinitePoints(rayRads, origin);
            List<Vertex> halfPts = GetInfinitePoints(halfRads, origin);
            List<Vertex[]> polys = GetPolygons(rayPts, halfPts, origin);

            // 切割
            for (int i = 0; i < angles.Count; ++i)
            {
                List<Vertex[]> retPoly = Clip(polys[i], origPoly);
                retDict.Add(angles[i], retPoly);
            }

            return retDict;
        }

        /// <summary>
        /// 对原多边形进行一次切割
        /// </summary>
        /// <param name="border"></param>
        /// <param name="poly"></param>
        /// <returns></returns>
        private static List<Vertex[]> Clip(Vertex[] border, List<Vertex[]> poly)
        {
            List<Vertex[]> newBorder = new List<Vertex[]>();
            newBorder.Add(border);

            CPolygonClipper clipper = new CPolygonClipper();
            return clipper.ClipPolygon(CPolygonClipper.ClipOperation.Intersection, poly, newBorder);
        }
        
        /// <summary>
        /// 由原点，射线的无穷远点，相邻三射线的两条角平分线的的无穷远点，这四个点组成一个多边形
        /// </summary>
        /// <param name="rayPts"></param>
        /// <param name="halfRads"></param>
        /// <param name="origin"></param>
        /// <returns></returns>
        private static List<Vertex[]> GetPolygons(List<Vertex> rayPts, List<Vertex> halfPts, Vertex origin)
        {
            List<Vertex[]> retList = new List<Vertex[]>();
            for (int i = 0; i < rayPts.Count; ++i)
            {
                Vertex[] poly = new Vertex[4];
                poly[0] = origin;
                poly[1] = halfPts[(i + halfPts.Count - 1) % halfPts.Count];
                poly[2] = rayPts[i];
                poly[3] = halfPts[i];
                retList.Add(poly);
            }
            return retList;
        }

        /// <summary>
        /// 获取射线列表的无穷远点
        /// </summary>
        /// <param name="rads"></param>
        /// <param name="v"></param>
        /// <returns></returns>
        private static List<Vertex> GetInfinitePoints(List<double> rads, Vertex v)
        {
            List<Vertex> retList = new List<Vertex>();
            foreach (double rad in rads)
            {
                retList.Add(GetInfinitePoint(rad, v));
            }
            return retList;
        }

        /// <summary>
        /// 根据弧度和原点，求射线的无穷远点
        /// </summary>
        /// <param name="rad"></param>
        /// <param name="v"></param>
        /// <returns></returns>
        private static Vertex GetInfinitePoint(double rad, Vertex v)
        {
            double inf = 20000;
            double precision = 0.0000000001;

            if (Math.Abs(Math.PI / 2 - rad) < precision)
            {
                return new Vertex(v.X, v.Y + inf);
            }
            if (Math.Abs(Math.PI * 3 / 2 - rad) < precision)
            {
                return new Vertex(v.X, v.Y - inf);
            }

            double slope = Math.Tan(rad);
            double sqrt = Math.Sqrt(inf * inf / (slope * slope + 1));
            double x = 0, y = 0;
            if (rad < Math.PI / 2 || rad > Math.PI * 3 / 2)
            {
                x = v.X + sqrt;
            }
            else
            {
                x = v.X - sqrt;
            }
            y = v.Y + (x - v.X) * slope;
            return new Vertex(x, y);
        }

        /// <summary>
        /// 方位角角度列表转数学角弧度列表
        /// </summary>
        /// <param name="angles"></param>
        /// <returns></returns>
        private static List<double> AngleToRadians(List<short> angles)
        {
            List<double> retList = new List<double>();
            foreach (short angle in angles)
            {
                retList.Add(AngleToRadian(angle));
            }
            return retList;
        }

        /// <summary>
        /// 方位角角度转数学角弧度
        /// </summary>
        /// <param name="angle"></param>
        /// <returns></returns>
        private static double AngleToRadian(short angle)
        {
            int M = (450 - angle) % 360; // 数学角度
            return M * Math.PI / 180;   // 数学弧度
        }

        /// <summary>
        /// 计算输入列表的平分弧度
        /// </summary>
        /// <param name="rads"></param>
        /// <returns></returns>
        private static List<double> BisectRadians(List<double> rads)
        {
            List<double> retList = new List<double>();
            for (int i = 0; i < rads.Count; ++i)
            {
                retList.Add(BisectRadian(rads[i], rads[(i + 1) % rads.Count]));
            }
            return retList;
        }

        /// <summary>
        /// 计算弧度r1与r2顺时针平分弧度
        /// </summary>
        /// <param name="r1"></param>
        /// <param name="r2"></param>
        /// <returns></returns>
        private static double BisectRadian(double r1, double r2)
        {
            return ((r1 - r2) / 2 + r2 + (r1 >= r2 ? 0 : Math.PI)) % (Math.PI * 2);
        }
    }
}
