﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEHighRailwayWeakSINRRoadDlg : BaseDialog
    {
        public LTEHighRailwayWeakSINRRoadDlg(WeakSINRRoadCondition_LTEHighRailWay condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(WeakSINRRoadCondition_LTEHighRailWay condition)
        {
            if (condition == null)
            {
                return;
            }
            numRSRP.Value = (decimal)condition.SINR;
            numWeakTestPoints.Value = condition.WeakSINRRoadTestPoints;
            numTestPoints.Value = condition.NormalTestPoints;
        }

        public WeakSINRRoadCondition_LTEHighRailWay GetConditon()
        {
            WeakSINRRoadCondition_LTEHighRailWay condition = new WeakSINRRoadCondition_LTEHighRailWay();
            condition.SINR = (float)numRSRP.Value;
            condition.WeakSINRRoadTestPoints = (int)numWeakTestPoints.Value;
            condition.NormalTestPoints = (int)numTestPoints.Value;
            
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }

    public class WeakSINRRoadCondition_LTEHighRailWay
    {
        public float SINR { get; set; } = -3;
        public int WeakSINRRoadTestPoints { get; set; } = 3;
        public int NormalTestPoints { get; set; } = 3;
    }
}
