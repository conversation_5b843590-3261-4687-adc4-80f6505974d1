﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public class ZTNBCellMissByRegion_T2T : ZTNBCellMissByRegion
    {
        public ZTNBCellMissByRegion_T2T(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "T2T邻区配置核查"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16013, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            cellGridDic.Clear();
            CellManager.GetInstance().GetTDNBCellInfo();
            return true;
        }

        protected override void fireShowResult()
        {
            ZTNBCellMissForm frm = MainModel.CreateResultForm(typeof(ZTNBCellMissForm)) as ZTNBCellMissForm;
            frm.FillDatas(cellGridDic, InspectType.T2T);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add(DataScan_TD.PCCPCHRSCPMeanValueID);
            formulaSet.Add(DataScan_TD.PCCPCHRSCPMaxID);
            formulaSet.Add(DataScan_TD.PCCPCHRSCPSampleNum);
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected override void getGridCells()
        {
            foreach (GridDataUnit grid in CurScanGridUnitMatrix)
            {
                StatDataSCAN_TD dataScam_TD = grid.GetStatData(typeof(StatDataSCAN_TD)) as StatDataSCAN_TD;
                if (dataScam_TD != null)
                {
                    GridItem gi = new GridItem(grid.LTLng, grid.LTLat);
                    foreach (int cellID in dataScam_TD.CellInicatorDic.Keys)
                    {
                        if (cellID == 0)
                        {
                            continue;
                        }
                        setCellGridDic(dataScam_TD, gi, cellID);
                    }
                }
            }
        }

        private void setCellGridDic(StatDataSCAN_TD dataScam_TD, GridItem gi, int cellID)
        {
            if (!cellGridDic.ContainsKey(gi))
            {
                Dictionary<string, Dictionary<int, GSMCellRxLev>> serviceCellsInfo = new Dictionary<string, Dictionary<int, GSMCellRxLev>>();
                serviceCellsInfo["TD"] = new Dictionary<int, GSMCellRxLev>();
                cellGridDic[gi] = serviceCellsInfo;
            }
            if (!cellGridDic[gi].ContainsKey("TD"))
            {
                cellGridDic[gi]["TD"] = new Dictionary<int, GSMCellRxLev>();
            }
            if (!cellGridDic[gi]["TD"].ContainsKey(cellID))
            {
                cellGridDic[gi]["TD"][cellID] = new GSMCellRxLev();
            }
            cellGridDic[gi]["TD"][cellID].cellID = cellID;
            cellGridDic[gi]["TD"][cellID].rxlevAvg = dataScam_TD[cellID, DataScan_TD.PCCPCHRSCPMeanValueID];
            cellGridDic[gi]["TD"][cellID].rxlevMax = dataScam_TD[cellID, DataScan_TD.PCCPCHRSCPMaxID];

            double num = dataScam_TD[cellID, DataScan_TD.PCCPCHRSCPSampleNum];
            if (!double.IsNaN(num))
            {
                cellGridDic[gi]["TD"][cellID].sampleNum = (int)num;
            }
        }
    }
}
