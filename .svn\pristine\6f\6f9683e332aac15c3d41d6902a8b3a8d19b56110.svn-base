﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NOP
{
    public class QueryProcRelationCfg : DIYSQLBase
    {
        private readonly bool isVer2017 = false;
        public QueryProcRelationCfg(bool isVer2017 = false)
            : base(MainModel.GetInstance())
        {
            this.isVer2017 = isVer2017;
        }

        protected override string getSqlTextString()
        {
            string svrName = NopCfgMngr.Instance.TaskDBServerName;

            return @"select [rid],[pid],[cid] from "
               + svrName + ".dbo.tb_cfg_proc_relation order by rid;";
            /*
            return @"select [rid],[pid],[cid] from " 
                + svrName + (isVer2017 ? "_2017" : "") 
                + ".dbo.tb_cfg_proc_relation order by rid;";
                */
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[3];
            int i = 0;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i] = E_VType.E_Int;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int rId = package.Content.GetParamInt();
                    int pId = package.Content.GetParamInt();
                    int cId = package.Content.GetParamInt();
                    if (isVer2017)
                    {
                        ProcRoutineManager2017.Instance.AddProcRelation(rId, pId, cId);
                    }
                    else
                    {
                        ProcRoutineManager.Instance.AddProcRelation(rId, pId, cId);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }

    }
}
