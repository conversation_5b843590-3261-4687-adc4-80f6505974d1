﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class AreaInfo
    {
        public AreaInfo(Content cnt)
        {
            CityID = cnt.GetParamInt();
            AreaName = cnt.GetParamString();
            AreaType = cnt.GetParamString();
            PareantArea = cnt.GetParamString();
            ParentDistrict = cnt.GetParamString();
            Desc = cnt.GetParamString();
        }

        public string Key
        {
            get { return string.Format("{0}&{1}&{2}", CityID, AreaType, AreaName); }
        }

        public int CityID
        {
            get;
            private set;
        }

        public string AreaType
        {
            get;
            private set;
        }

        public string AreaName { get; private set; }

        public string PareantArea
        {
            get;
            private set;
        }

        public string ParentDistrict
        {
            get;
            private set;
        }


        public string Desc { get;private set; }
    }
}
