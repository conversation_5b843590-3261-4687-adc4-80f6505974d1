﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTReportEventStatQueryMulti_BJ : QueryBase
    {
        public ZTReportEventStatQueryMulti_BJ(MainModel mainModel)
            : base(mainModel)
        {
        }

        private Dictionary<string, Dictionary<string,ZTReportEventStatInfo>> areaGroupDic = null;  //Dictionary<areaName|groupName,Dictionary<netType|eventType,XXXXX>>
        private Dictionary<string, Dictionary<string, ZTReportEventStatInfo>> areaReasonDic = null;  //Dictionary<areaName,Dictionary<netType|eventType,XXXXX>>
     
        private ReportEventCondition reportEventCond = null;

        ZTReportEventStatSetForm_BJ reportEventStatSetForm = null;
        private bool getCondition()
        {
            if (reportEventStatSetForm == null)
            {
                reportEventStatSetForm = new ZTReportEventStatSetForm_BJ(this.MainModel, 2);
            }

            if (reportEventStatSetForm.ShowDialog() == DialogResult.OK)
            {
                reportEventStatSetForm.GetCondition(out reportEventCond);
                reportEventCond.UserName = MainModel.User.LoginName;     //用户权限决定能看到的信息
            }
            else
            {
                return false;
            }

            return true;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                if (!getCondition())
                {
                    return;
                }

                areaGroupDic = new Dictionary<string,Dictionary<string,ZTReportEventStatInfo>>(); //重新初始化
                areaReasonDic = new Dictionary<string, Dictionary<string, ZTReportEventStatInfo>>();

                MainModel.ClearDTData();
                WaitBox.Show("开始查询异常事件...", queryInThread, clientProxy);
                MainModel.FireDTDataChanged(this);
                MainModel.RefreshLegend();
                fireShowFormAfterQuery();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected void queryInThread(object o)
        {
            try
            {
                getReportEventStatDic(reportEventCond);
            }
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void getReportEventStatDic(ReportEventCondition reportEventCond)
        {
            ZTSQLReportEventInfoQuery_BJ query = new ZTSQLReportEventInfoQuery_BJ(MainModel, reportEventCond);
            query.Query();
            List<ZTReportEventInfo_BJ> eventInfoList = query.GetReportEventInfoList();

            foreach (ZTReportEventInfo_BJ eventInfo in eventInfoList)
            {
                addEventInfoToDic(eventInfo);  
            }
        }

        private void addEventInfoToDic(ZTReportEventInfo_BJ eventInfo)
        {
            string netEvent = eventInfo.NetType + "|" + eventInfo.EventType;

            #region areaGroupDic
            string areaGroup = eventInfo.AreaName + "|" + eventInfo.GridGroup;
            Dictionary<string, ZTReportEventStatInfo> statAreaGroupDic = null;            

            if (this.areaGroupDic.ContainsKey(areaGroup))
            {
                statAreaGroupDic = areaGroupDic[areaGroup];
            }
            else
            {
                statAreaGroupDic = initStatDic();
                areaGroupDic.Add(areaGroup, statAreaGroupDic);
            }

            if (statAreaGroupDic.ContainsKey(netEvent))
            {
                statAreaGroupDic[netEvent].Merge(eventInfo);
            }
            #endregion

            #region areaReasonDic
            Dictionary<string, ZTReportEventStatInfo> statReasonDic = null;

            if (this.areaReasonDic.ContainsKey(eventInfo.AreaName))
            {
                statReasonDic = areaReasonDic[eventInfo.AreaName];
            }
            else
            {
                statReasonDic = initStatDic();
                areaReasonDic.Add(eventInfo.AreaName, statReasonDic);
            }
            if (statReasonDic.ContainsKey(netEvent))
            {
                statReasonDic[netEvent].Merge(eventInfo);
            }
            #endregion
        }

        private Dictionary<string, ZTReportEventStatInfo> initStatDic()
        {
            Dictionary<string, ZTReportEventStatInfo> statDic = new Dictionary<string, ZTReportEventStatInfo>();

            ZTReportEventStatInfo statInfo1 = new ZTReportEventStatInfo("GSM", "掉话");
            statDic.Add("GSM|掉话",statInfo1);

            ZTReportEventStatInfo statInfo2 = new ZTReportEventStatInfo("GSM", "未接通");
            statDic.Add("GSM|未接通", statInfo2);

            ZTReportEventStatInfo statInfo3 = new ZTReportEventStatInfo("TD", "掉话");
            statDic.Add("TD|掉话", statInfo3);

            ZTReportEventStatInfo statInfo4 = new ZTReportEventStatInfo("TD", "未接通");
            statDic.Add("TD|未接通", statInfo4);

            return statDic;
        }

        private void fireShowFormAfterQuery()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTReportEventStatListFormMulti_BJ).FullName);
            ZTReportEventStatListFormMulti_BJ reportEventStatListForm = obj == null ? null : obj as ZTReportEventStatListFormMulti_BJ;
            if (reportEventStatListForm == null || reportEventStatListForm.IsDisposed)
            {
                reportEventStatListForm = new ZTReportEventStatListFormMulti_BJ(MainModel);
            }
            reportEventStatListForm.FillData(areaGroupDic, areaReasonDic, reportEventCond);
            if (!reportEventStatListForm.Visible)
            {
                reportEventStatListForm.Show(MainModel.MainForm);
            }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18020, this.Name);
        }

        //private bool isSelectRegion() //是否框选区域
        //{
        //    return true;
        //}

        public override string Name
        {
            get { return "BJ异常事件跟踪"; }
        }

        public override string IconName
        {
            get { return "Images/cellquery.gif"; }
        }
    }
}