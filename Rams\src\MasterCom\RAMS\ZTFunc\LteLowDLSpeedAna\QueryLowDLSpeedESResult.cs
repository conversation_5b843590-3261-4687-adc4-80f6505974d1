﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.NOP;
using MasterCom.RAMS.ZTFunc.LteLowDLSpeedAna;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryLowDLSpeedESResult : DIYEventByRegion
    {
        public override string Name
        {
            get { return "低速率路段关联分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22018, this.Name);
        }

        public QueryLowDLSpeedESResult()
            : base(MainModel.GetInstance())
        {
            this.showEventChooser = false;
            this.IsQueryAllEvents = false;
        }

        protected override bool isValidCondition()
        {
            condition.ServiceTypes.Clear();
            condition.ServiceTypes.Add(34);
            condition.EventIDs = new List<int>();
            condition.EventIDs.Add(1275);
            return true;
        }

        protected override void query()
        {
            base.query();
            List<Event> evts = new List<Event>();
            foreach (DTFileDataManager fMng in MainModel.DTDataManager.FileDataManagers)
            {
                evts.AddRange(fMng.Events);
            }
            if (evts.Count == 0)
            {
                MessageBox.Show("所选区域无低速率路段！");
                return;
            }
            else
            {
                WaitTextBox.Show("正在获取分析结果...", queryEsResultInThread, evts);
            }

            if (results == null || results.Count == 0)
            {
                MessageBox.Show("后台未执行智能关联分析！");
                return;
            }
            
            QueryLowSpeedDataFromFileName lowSpeedAnalyse = new QueryLowSpeedDataFromFileName(MainModel);
            lowSpeedAnalyse.SetCondition(this.condition,results);
            lowSpeedAnalyse.Query();
            results = lowSpeedAnalyse.GetResult();
            LowDLSpeedResultForm frm = MainModel.CreateResultForm(typeof(LowDLSpeedResultForm)) as LowDLSpeedResultForm;
            frm.FillData(results);
            frm.Visible = true;
            frm.BringToFront();
        }

        private List<CDLowSpeedESResult> results = null;
        private void queryEsResultInThread(object param)
        {
            List<Event> evts = param as List<Event>;
            QueryTaskESResultInfo qry = new QueryTaskESResultInfo(evts);
            try
            {
                qry.Query();
                results = qry.Results;
            }
            finally
            {
                WaitTextBox.Close();
            }

         
        }

    }

    public class QueryTaskESResultInfo : DIYSQLBase
    {
        readonly Dictionary<int, Dictionary<int, Event>> fileEvtSNDic = null;
        readonly Dictionary<string, bool> resultTbDic = null;
        public QueryTaskESResultInfo(IEnumerable<Event> events)
            : base(MainModel.GetInstance())
        {
            fileEvtSNDic = new Dictionary<int, Dictionary<int, Event>>();
            resultTbDic = new Dictionary<string, bool>();
            foreach (Event evt in events)
            {
                string tbName = string.Format("tb_focus_event_result_{0}"
                    , JavaDate.GetDateTimeFromMilliseconds(evt.Time * 1000L).ToString("yyMM"));
                resultTbDic[tbName] = true;
                Dictionary<int, Event> snEvtDic = null;
                if (fileEvtSNDic.TryGetValue(evt.FileID,out snEvtDic))
                {
                    snEvtDic[evt.SN] = evt;
                }
                else
                {
                    snEvtDic = new Dictionary<int, Event>();
                    snEvtDic[evt.SN] = evt;
                    fileEvtSNDic[evt.FileID] = snEvtDic;
                }
            }
        }

        private string curTbName = null;
        protected override string getSqlTextString()
        {
            return @"select [fileid],[evtid],[seqid],[ctime],[evttime],[stime],[etime],[midlongitude],[midlatitude],[primarytype],[specifictype],[detail],[suggest],[rid],[msg] from "
                + curTbName;
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                E_VType[] retArrDef = getSqlRetTypeArr();
                foreach (string tbName in resultTbDic.Keys)
                {
                    curTbName = tbName;
                    string strsql = getSqlTextString();
                    package.Command = Command.DIYSearch;
                    package.SubCommand = SubCommand.Request;

                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;

                    package.Content.PrepareAddParam();
                    package.Content.AddParam(strsql);
                    StringBuilder sb = new StringBuilder();
                    if (retArrDef != null)
                    {
                        for (int i = 0; i < retArrDef.Length; i++)
                        {
                            sb.Append((int)retArrDef[i]);
                            sb.Append(",");
                        }
                    }

                    package.Content.AddParam(sb.ToString().TrimEnd(','));
                    clientProxy.Send();
                    receiveRetData(clientProxy);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[15];
            int i = 0;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i] = E_VType.E_VARYBIN;
            return arr;
        }

        public List<CDLowSpeedESResult> Results { get; set; } = new List<CDLowSpeedESResult>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CDLowSpeedESResult resultInfo = new CDLowSpeedESResult();
                    resultInfo.FileID = package.Content.GetParamInt();
                    resultInfo.EventID = package.Content.GetParamInt();
                    resultInfo.SeqID = package.Content.GetParamInt();

                    Dictionary<int, Event> snEvtDic = null;
                    Event evt;
                    if (!fileEvtSNDic.TryGetValue(resultInfo.FileID, out snEvtDic)
                        || (!snEvtDic.TryGetValue(resultInfo.SeqID, out evt)))
                    {
                        continue;
                    }
                    Results.Add(resultInfo);
                    resultInfo.Event = evt;
                    resultInfo.CreateTime = DateTime.Parse(package.Content.GetParamString());
                    resultInfo.EventTime = DateTime.Parse(package.Content.GetParamString());
                    resultInfo.BeginTime = DateTime.Parse(package.Content.GetParamString());
                    resultInfo.EndTime = DateTime.Parse(package.Content.GetParamString());
                    resultInfo.MidLng = package.Content.GetParamFloat();
                    resultInfo.MidLat = package.Content.GetParamFloat();
                    resultInfo.PrimaryType = package.Content.GetParamString();
                    resultInfo.SpecificType = package.Content.GetParamString();
                    resultInfo.Detail = package.Content.GetParamString();
                    resultInfo.Suggest = package.Content.GetParamString();
                    resultInfo.RelationID = package.Content.GetParamInt();
                    resultInfo.FillMsg(package.Content.GetParamBytes());
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }

    }

}
