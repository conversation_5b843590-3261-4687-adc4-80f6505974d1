﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using AxMapWinGIS;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class ZTAreaArchiveLayer : LayerBase
    {
        public ZTAreaArchiveLayer()
            : base("覆盖档案区域图层")
        {
            Areas = new List<AreaBase>();
            SelectedAreas = new List<AreaBase>();
        }

        public List<AreaBase> Areas
        {
            get;
            set;
        }

        public List<AreaBase> SelectedAreas
        {
            get
            {
                List<AreaBase> ret = new List<AreaBase>();
                if (selAreaDic != null)
                {
                    ret.AddRange(selAreaDic.Keys);
                }
                return ret;
            }
            set
            {
                selAreaDic = new Dictionary<AreaBase, bool>();
                if (value==null)
                {
                    return;
                }
                foreach (AreaBase area in value)
                {
                    selAreaDic[area] = true;
                }
            }
        }

        private Dictionary<AreaBase, bool> selAreaDic;

        public Dictionary<AreaBase, Color> AreaColorDic { get; set; } = new Dictionary<AreaBase, Color>();

        public void AddSelectArea(AreaBase area)
        {
            if (selAreaDic == null)
                selAreaDic = new Dictionary<AreaBase, bool>();
            selAreaDic[area] = true;
        }

        public void ClearSelectArea()
        {
            if (selAreaDic != null)
                selAreaDic.Clear();
        }

        public void ClearAreaColor()
        {
            if (AreaColorDic != null)
            {
                AreaColorDic.Clear();
            }
        }

        public override void Draw(Rectangle clientRect
            , Rectangle updateRect, Graphics graphics)
        {
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);

            foreach (AreaBase area in Areas)
            {
                if (dRect.Within(area.Bounds))
                {
                    paintPolygon(graphics, area);
                }
            }
        }

        private readonly Pen penBorder = new Pen(Color.SkyBlue, 2);
        private readonly Pen penSelected = new Pen(Color.Red, 2);
        private void paintPolygon(Graphics graphics, AreaBase area)
        {
            PointF[] pts = new PointF[area.PolygonPoints.Count];
            int i = 0;
            foreach (DbPoint pt in area.PolygonPoints)
            {
                PointF ptf;
                gisAdapter.ToDisplay(pt, out ptf);
                pts[i++] = ptf;
            }

            Color color;
            SolidBrush brush;
            if (AreaColorDic.TryGetValue(area, out color) && color != Color.Empty)
            {
                brush = new SolidBrush(Color.FromArgb(200, color));
                graphics.FillPolygon(brush, pts);
            }
            else if (selAreaDic != null && selAreaDic.ContainsKey(area))
            {
                graphics.DrawPolygon(penSelected, pts);
            }
            else
            {
                graphics.DrawPolygon(penBorder, pts);
            }

            if (gisAdapter.DistancePer50Pixel <= 500 && pts.Length > 0)
            {
                graphics.DrawString(string.Format("{0}.{1}", area.PareantAreaName, area.Name), new Font("宋体", 9F, FontStyle.Regular), new SolidBrush(Color.Black), pts[0]);
            }
        }

    }

}
