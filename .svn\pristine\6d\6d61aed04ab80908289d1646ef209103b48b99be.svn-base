﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteVoiceAnaByFreqBandResult : VoiceAnaByFreqBandResult
    {
        public LteVoiceAnaByFreqBandResult(string freqBand)
            : base(freqBand)
        {

        }

        protected override int? getPCI(TestPoint tp)
        {
            return (int?)(short?)tp["lte_PCI"];
        }

        protected override float? getMOSParamName(TestPoint tp, ref string mosParamName)
        {
            float? mos = null;
            if (mosParamName == "")
            {
                mos = getMos(tp, "lte_PESQMos");
                if (mos != null)
                {
                    mosParamName = "lte_PESQMos";
                    return mos;
                }
                mos = getMos(tp, "lte_POLQA_Score_SWB");
                if (mos != null)
                {
                    mosParamName = "lte_POLQA_Score_SWB";
                    return mos;
                }
            }
            else
            {
                mos = (float?)tp[mosParamName];
            }
            return mos;
        }

        private float? getMos(TestPoint tp, string mosParamName)
        {
            float? mos = (float?)tp[mosParamName];
            if (mos != null && mos > 0 && mos <= 5)
            {
                return mos;
            }
            return null;
        }
    }

    public class LteVoiceAnaByFreqBandFileResult
    { 
        public string FileName { get; set; }
        public List<LteVoiceAnaByFreqBandResult> ResList { get; set; } = new List<LteVoiceAnaByFreqBandResult>();
    }
}
