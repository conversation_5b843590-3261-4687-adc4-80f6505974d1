﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class EveryCallHandoverInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(EveryCallHandoverInfoForm));
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel4 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel2 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel3 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripButton1 = new System.Windows.Forms.ToolStripButton();
            this.tsBtnExp2Xls = new System.Windows.Forms.ToolStripButton();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMileage = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnOccupation = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpeed = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTpCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMinDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMaxDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAvgDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMinRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMaxRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAvgRxLev = new BrightIdeasSoftware.OLVColumn();
            this.toolStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.SuspendLayout();
            // 
            // toolStrip1
            // 
            this.toolStrip1.BackColor = System.Drawing.SystemColors.Control;
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel1,
            this.toolStripLabel4,
            this.toolStripLabel2,
            this.toolStripLabel3,
            this.toolStripButton1,
            this.tsBtnExp2Xls});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(1017, 25);
            this.toolStrip1.TabIndex = 7;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(97, 22);
            this.toolStripLabel1.Text = "小区占用时间<=";
            // 
            // toolStripLabel4
            // 
            this.toolStripLabel4.Name = "toolStripLabel4";
            this.toolStripLabel4.Size = new System.Drawing.Size(19, 22);
            this.toolStripLabel4.Text = "秒";
            // 
            // toolStripLabel2
            // 
            this.toolStripLabel2.Name = "toolStripLabel2";
            this.toolStripLabel2.Size = new System.Drawing.Size(113, 22);
            this.toolStripLabel2.Text = "每呼叫切换次数 >=";
            // 
            // toolStripLabel3
            // 
            this.toolStripLabel3.Name = "toolStripLabel3";
            this.toolStripLabel3.Size = new System.Drawing.Size(19, 22);
            this.toolStripLabel3.Text = "次";
            // 
            // toolStripButton1
            // 
            this.toolStripButton1.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.None;
            this.toolStripButton1.Image = ((System.Drawing.Image)(resources.GetObject("toolStripButton1.Image")));
            this.toolStripButton1.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton1.Name = "toolStripButton1";
            this.toolStripButton1.Size = new System.Drawing.Size(23, 22);
            this.toolStripButton1.Text = "toolStripButton1";
            // 
            // tsBtnExp2Xls
            // 
            this.tsBtnExp2Xls.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.tsBtnExp2Xls.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnExp2Xls.Image")));
            this.tsBtnExp2Xls.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnExp2Xls.Name = "tsBtnExp2Xls";
            this.tsBtnExp2Xls.Size = new System.Drawing.Size(73, 22);
            this.tsBtnExp2Xls.Text = "导出Excel...";
            this.tsBtnExp2Xls.Click += new System.EventHandler(this.tsBtnExp2Xls_Click);
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnSN);
            this.treeListView.AllColumns.Add(this.olvColumnName);
            this.treeListView.AllColumns.Add(this.olvColumnLAC);
            this.treeListView.AllColumns.Add(this.olvColumnCI);
            this.treeListView.AllColumns.Add(this.olvColumnMileage);
            this.treeListView.AllColumns.Add(this.olvColumnOccupation);
            this.treeListView.AllColumns.Add(this.olvColumnSpeed);
            this.treeListView.AllColumns.Add(this.olvColumnTpCount);
            this.treeListView.AllColumns.Add(this.olvColumnMinDistance);
            this.treeListView.AllColumns.Add(this.olvColumnMaxDistance);
            this.treeListView.AllColumns.Add(this.olvColumnAvgDistance);
            this.treeListView.AllColumns.Add(this.olvColumnMinRxLev);
            this.treeListView.AllColumns.Add(this.olvColumnMaxRxLev);
            this.treeListView.AllColumns.Add(this.olvColumnAvgRxLev);
            this.treeListView.AllowColumnReorder = true;
            this.treeListView.AlternateRowBackColor = System.Drawing.Color.Ivory;
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnMileage,
            this.olvColumnOccupation,
            this.olvColumnSpeed,
            this.olvColumnTpCount,
            this.olvColumnMinDistance,
            this.olvColumnMaxDistance,
            this.olvColumnAvgDistance,
            this.olvColumnMinRxLev,
            this.olvColumnMaxRxLev,
            this.olvColumnAvgRxLev});
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.IsNeedShowOverlay = false;
            this.treeListView.Location = new System.Drawing.Point(0, 25);
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1017, 396);
            this.treeListView.TabIndex = 8;
            this.treeListView.UseAlternatingBackColors = true;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.UseHotItem = true;
            this.treeListView.UseTranslucentHotItem = true;
            this.treeListView.UseTranslucentSelection = true;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 76;
            // 
            // olvColumnName
            // 
            this.olvColumnName.AspectName = "";
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.Text = "名称";
            this.olvColumnName.Width = 234;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.AspectName = "";
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            this.olvColumnLAC.Width = 65;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.AspectName = "";
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnMileage
            // 
            this.olvColumnMileage.AspectName = "";
            this.olvColumnMileage.HeaderFont = null;
            this.olvColumnMileage.Text = "里程(米)";
            this.olvColumnMileage.Width = 64;
            // 
            // olvColumnOccupation
            // 
            this.olvColumnOccupation.AspectName = "";
            this.olvColumnOccupation.HeaderFont = null;
            this.olvColumnOccupation.Text = "占用时长(s)";
            this.olvColumnOccupation.Width = 86;
            // 
            // olvColumnSpeed
            // 
            this.olvColumnSpeed.AspectName = "";
            this.olvColumnSpeed.HeaderFont = null;
            this.olvColumnSpeed.Text = "平均速度(m/s)";
            this.olvColumnSpeed.Width = 93;
            // 
            // olvColumnTpCount
            // 
            this.olvColumnTpCount.AspectName = "";
            this.olvColumnTpCount.HeaderFont = null;
            this.olvColumnTpCount.Text = "采样点个数";
            this.olvColumnTpCount.Width = 82;
            // 
            // olvColumnMinDistance
            // 
            this.olvColumnMinDistance.AspectName = "";
            this.olvColumnMinDistance.HeaderFont = null;
            this.olvColumnMinDistance.Text = "最小距离(m)";
            // 
            // olvColumnMaxDistance
            // 
            this.olvColumnMaxDistance.AspectName = "";
            this.olvColumnMaxDistance.HeaderFont = null;
            this.olvColumnMaxDistance.Text = "最大距离(m)";
            this.olvColumnMaxDistance.Width = 83;
            // 
            // olvColumnAvgDistance
            // 
            this.olvColumnAvgDistance.AspectName = "";
            this.olvColumnAvgDistance.HeaderFont = null;
            this.olvColumnAvgDistance.Text = "平均距离(m)";
            this.olvColumnAvgDistance.Width = 81;
            // 
            // olvColumnMinRxLev
            // 
            this.olvColumnMinRxLev.AspectName = "";
            this.olvColumnMinRxLev.HeaderFont = null;
            this.olvColumnMinRxLev.Text = "最低电平";
            this.olvColumnMinRxLev.Width = 68;
            // 
            // olvColumnMaxRxLev
            // 
            this.olvColumnMaxRxLev.AspectName = "";
            this.olvColumnMaxRxLev.HeaderFont = null;
            this.olvColumnMaxRxLev.Text = "最大电平";
            // 
            // olvColumnAvgRxLev
            // 
            this.olvColumnAvgRxLev.AspectName = "";
            this.olvColumnAvgRxLev.HeaderFont = null;
            this.olvColumnAvgRxLev.Text = "平均电平";
            // 
            // EveryCallHandoverInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1017, 421);
            this.Controls.Add(this.treeListView);
            this.Controls.Add(this.toolStrip1);
            this.Name = "EveryCallHandoverInfoForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "每通话切换情况";
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel4;
        private System.Windows.Forms.ToolStripLabel toolStripLabel2;
        private System.Windows.Forms.ToolStripLabel toolStripLabel3;
        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnMileage;
        private BrightIdeasSoftware.OLVColumn olvColumnOccupation;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnTpCount;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxLev;
        private BrightIdeasSoftware.OLVColumn olvColumnMinDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxLev;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxLev;
        private System.Windows.Forms.ToolStripButton toolStripButton1;
        private System.Windows.Forms.ToolStripButton tsBtnExp2Xls;
    }
}