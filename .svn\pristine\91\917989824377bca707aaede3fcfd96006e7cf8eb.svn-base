﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverCountStatByRegion_TD : DIYEventByRegion
    {
        public HandoverCountStatByRegion_TD(MainModel mainModel)
            : base(mainModel)
        {
            showEventChooser = false;
        }

        public override string Name
        {
            get { return "切换次数统计"; }
        }

        public override string IconName
        {
            get { return "Images/event/handover.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13046, this.Name);
        }

        protected override void getResultAfterQuery()
        {
            infoList.Clear();
            Dictionary<string, HandoverCountInfo> keyInfoDict = new Dictionary<string, HandoverCountInfo>();
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (Event e in file.Events)
                {
                    if (!HandoverType.TDHandoverEventList.Contains(e.ID))
                    {
                        continue;
                    }

                    int? tarCpi = int.Parse(e["Value3"].ToString());
                    int? tarFreq = int.Parse(e["Value4"].ToString());
                    if (tarCpi == null || tarFreq == null || (tarCpi == 0 && tarFreq == 0) || (tarCpi == 255 && tarFreq == -255))
                    {
                        continue;
                    }

                    addValidHandoverCountInfo(keyInfoDict, e, tarCpi, tarFreq);
                }
            }

            foreach (HandoverCountInfo info in keyInfoDict.Values)
            {
                infoList.Add(info);
            }
        }

        private void addValidHandoverCountInfo(Dictionary<string, HandoverCountInfo> keyInfoDict, Event e, int? tarCpi, int? tarFreq)
        {
            TDCell srcCell = CellManager.GetInstance().GetNearestTDCell(e.DateTime, (int?)e["LAC"], (int?)e["CI"], int.Parse(e["Value2"].ToString()), int.Parse(e["Value1"].ToString()), e.Longitude, e.Latitude);
            object tarCell = getTarCell(e, tarCpi, tarFreq);

            if (srcCell != null && tarCell != null)
            {
                HandoverCountInfo info = new HandoverCountInfo(srcCell, tarCell, e);
                addKeyInfoDict(keyInfoDict, info);
            }
        }

        private static void addKeyInfoDict(Dictionary<string, HandoverCountInfo> keyInfoDict, HandoverCountInfo info)
        {
            if (!keyInfoDict.ContainsKey(info.Key))
            {
                keyInfoDict.Add(info.Key, info);
            }
            else
            {
                keyInfoDict[info.Key].Merge(info);
            }
        }

        private object getTarCell(Event e, int? tarCpi, int? tarFreq)
        {
            object tarCell = null;
            if (e.ID == 142 || e.ID == 143 || e.ID == 141)
            {
                tarCell = CellManager.GetInstance().GetNearestCell(e.DateTime, (short)tarFreq, (byte)tarCpi, e.Longitude, e.Latitude);
            }
            else
            {
                tarCell = CellManager.GetInstance().GetNearestTDCell(e.DateTime, (short)tarFreq, (short)tarCpi, e.Longitude, e.Latitude);
            }

            return tarCell;
        }

        protected override void fireShowFormAfterQuery()
        {
            object obj = MainModel.GetObjectFromBlackboard(typeof(HandoverCountStatResultForm).FullName);
            HandoverCountStatResultForm form = obj as HandoverCountStatResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new HandoverCountStatResultForm(MainModel);
            }
            form.FillData(infoList);
            form.Show(MainModel.MainForm);
        }

        protected List<HandoverCountInfo> infoList = new List<HandoverCountInfo>();
    }

    public class HandoverCountInfo
    {
        public HandoverCountInfo(object srcCell, object tarCell, Event evt)
        {
            this.srcCell = srcCell;
            this.tarCell = tarCell;
            this.TarLac = this.SrcLac = -1;
            this.TarCi = this.SrcCi = -1;
            EventID = evt.ID;
            HType = new HandoverType(evt.ID);
            Evt = evt;
            InitLacCi();
            InitHandoverCount();
        }

        public void Merge(HandoverCountInfo info)
        {
            HandoverRequestCount += info.HandoverRequestCount;
            HandoverSucceedCount += info.HandoverSucceedCount;
            HandoverFailedCount += info.HandoverFailedCount;
        }

        public Event Evt
        {
            get;
            private set;
        }

        public string Key
        {
            get { return string.Format("{0}:{1}->{2}:{3}", SrcLacStr, SrcCiStr, TarLacStr, TarCiStr); }
        }

        public int EventID
        {
            get;
            private set;
        }

        public string EventDesc
        {
            get { return HType.EventDesc; }
        }

        public HandoverType HType
        {
            get;
            private set;
        }

        public int SrcLac
        {
            get;
            protected set;
        }

        public int SrcCi
        {
            get;
            protected set;
        }

        public int TarLac
        {
            get;
            protected set;
        }

        public int TarCi
        {
            get;
            protected set;
        }

        public string SrcLacStr
        {
            get;
            protected set;
        }

        public string SrcCiStr
        {
            get;
            protected set;
        }

        public string TarLacStr
        {
            get;
            protected set;
        }

        public string TarCiStr
        {
            get;
            protected set;
        }

        public string SrcCellName
        {
            get { return srcCell == null ? string.Format("{0}-{1}", SrcLacStr, SrcCiStr) : (srcCell as ICell).Name; }
        }

        public string TarCellName
        {
            get { return tarCell == null ? string.Format("{0}-{1}", TarLacStr, TarCiStr) : (tarCell as ICell).Name; }
        }

        public int HandoverRequestCount
        {
            get;
            set;
        }

        public int HandoverSucceedCount
        {
            get;
            set;
        }

        public int HandoverFailedCount
        {
            get;
            set;
        }

        protected void InitLacCi()
        {
            try
            {
                if (srcCell is TDCell)
                {
                    TDCell cell = srcCell as TDCell;
                    SrcLac = cell.LAC;
                    SrcCi = cell.CI;
                }
                if (tarCell is Cell)
                {
                    Cell cell = tarCell as Cell;
                    TarLac = cell.LAC;
                    TarCi = cell.CI;
                }
                else if (tarCell is TDCell)
                {
                    TDCell cell = tarCell as TDCell;
                    TarLac = cell.LAC;
                    TarCi = cell.CI;
                }
            }
            catch (Exception ex)
            {
                throw (new Exception("Arguments Invalid: " + ex.Message));
            }

            SrcLacStr = SrcLac == -1000000 || SrcLac == -1 ? "" : SrcLac.ToString();
            SrcCiStr = SrcCi == -1000000 || SrcCi == -1 ? "" : SrcCi.ToString();
            TarLacStr = TarLac == -1000000 || TarLac == -1 ? "" : TarLac.ToString();
            TarCiStr = TarCi == -1000000 || TarCi == -1 ? "" : TarCi.ToString();
        }

        protected void InitHandoverCount()
        {
            if (EventID == 141 || EventID == 144 || EventID == 147)
            {
                HandoverRequestCount = 1;
            }
            else if (EventID == 142 || EventID == 145 || EventID == 148)
            {
                HandoverSucceedCount = 1;
            }
            else if (EventID == 143 || EventID == 146 || EventID == 149)
            {
                HandoverFailedCount = 1;
            }
        }

        protected object srcCell;
        protected object tarCell;
    }

    public class HandoverType
    {
        public int EventID
        {
            get;
            set;
        }

        public string EventDesc
        {
            get;
            set;
        }

        public HandoverType(int handoverEventID)
        {
            EventID = handoverEventID;
            if (!eventDescDic.ContainsKey(EventID))
            {
                EventDesc = "";
            }
            else
            {
                EventDesc = eventDescDic[EventID];
            }
        }

        public static List<int> TDHandoverEventList
        {
            get { return tdHandoverEventList; }
        }

        private static Dictionary<int, string> eventDescDic = InitEvtDic();
        private static List<int> tdHandoverEventList = InitEvtLst();

        static Dictionary<int, string> InitEvtDic()
        {
            Dictionary<int, string> dic = new Dictionary<int, string>();
            // TD切换事件
            dic.Add(141, "T2G");
            dic.Add(142, "T2G");
            dic.Add(143, "T2G");
            dic.Add(144, "IntraT");
            dic.Add(145, "IntraT");
            dic.Add(146, "IntraT");
            dic.Add(147, "Baton");
            dic.Add(148, "Baton");
            dic.Add(149, "Baton");
            //dic.Add(231, "RBReconfigT");
            //dic.Add(232, "RBReconfigT");
            //dic.Add(234, "RBReconfigT");

            // WCDMA切换事件
            dic.Add(541, "W2G Request");
            dic.Add(542, "W2G Success");
            dic.Add(543, "W2G Fail");
            dic.Add(544, "IntraW Request");
            dic.Add(545, "IntraW Success");
            dic.Add(546, "IntraW Fail");
            dic.Add(547, "Soft Request");
            dic.Add(548, "Soft Success");
            dic.Add(549, "Soft Fail");
            return dic;
        }

        static List<int> InitEvtLst()
        {
            List<int> lst = new List<int>(eventDescDic.Keys);
            return lst;
        }
    }
}
