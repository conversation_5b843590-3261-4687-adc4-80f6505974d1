﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NoCoverRoadAnaBase_NR : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();

        protected PercentRoadBuilder roadBuilder;
        NoCoverRoadCondition_NR funcCond = new NoCoverRoadCondition_NR();
        protected List<NoCoverRoadInfo_NR> resultList = new List<NoCoverRoadInfo_NR>();

        protected NoCoverRoadAnaBase_NR()
            : base(MainModel.GetInstance())
        {
            this.IncludeEvent = false;
            IncludeMessage = true;
            Columns = new List<string>();
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35006, this.Name);
        }
        protected override void getReadyBeforeQuery()
        {
            resultList.Clear();

            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);
            Columns.Add("NR_lte_RSRQ");
            Columns.Add("NR_lte_RSSI");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                return true;
            }

            NoCoverRoadSettingDlg_NR dlg = new NoCoverRoadSettingDlg_NR();
            dlg.SetCondition(funcCond);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.GetCondition();
            setRoadCond();
            return true;
        }

        protected void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(funcCond.MinNoCoverPercent / 100, onOneRoadComplete);
            roadCond.IsCheckDuration = funcCond.CheckMinDuration;
            roadCond.MinDuration = funcCond.MinRoadDuration;
            roadCond.IsCheckMinLength = funcCond.CheckMinDistance;
            roadCond.MinLength = funcCond.MinRoadDistance;
            roadCond.IsCheckDistanceGap = true;
            roadCond.MaxDistanceGap = funcCond.MaxTpDistance;

            this.roadBuilder = new PercentRoadBuilder(roadCond);
        }

        protected override void doStatWithQuery()
        {
            try
            {
                List<int> startMsgList = new List<int>() { 1107689472, 1107361792, 769 };
                List<int> endMsgList = new List<int>() { 1107443912, 1107492864, 805 };
                List<TimePeriod> callTimePeriodList = new List<TimePeriod>();
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    if (WaitBox.CancelRequest)
                    {
                        return;
                    }

                    //根据信令查询语音呼叫时间段
                    if (funcCond.CheckFilterMultiVoice
                        && (file.ServiceType == (int)ServiceType.SER_NR_SA_TDD_MULTI
                        || file.ServiceType == (int)ServiceType.SER_NR_NSA_TDD_MULTI
                        || file.ServiceType == (int)ServiceType.SER_NR_DM_TDD_MULTI))
                    {
                        callTimePeriodList = NRMsgHelper.GetCallTimePeriod(file.Messages, startMsgList, endMsgList);
                    }

                    dealTPs(callTimePeriodList, file);
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void dealTPs(List<TimePeriod> callTimePeriodList, DTFileDataManager file)
        {
            foreach (TestPoint tp in file.TestPoints)
            {
                bool isIgnore = false;
                if (callTimePeriodList.Count > 0)
                {
                    isIgnore = NRMsgHelper.JudgeInTimePeriod(callTimePeriodList, tp.DateTime);
                }

                if (!isIgnore)
                {
                    roadBuilder.AddPoint(tp, isNoCoverTp(tp));
                }
            }
            roadBuilder.StopRoading();
        }

        protected virtual bool isNoCoverTp(TestPoint tp)
        {
            if (tp is TestPoint_NR && isValidTestPoint(tp))
            {
                float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                if (rsrp == null || rsrp < funcCond.MaxRsrp)
                {
                    return true;
                }
            }
            return false;
        }

        protected void onOneRoadComplete(object sender, PercentRoadItem roadItem)
        {
            PercentRoadBuilder curRoadBuilder = sender as PercentRoadBuilder;
            double curLowPercent = Math.Round(roadItem.ValidPercent * 100, 2);
            if (curLowPercent < funcCond.MinNoCoverPercent
                || (funcCond.CheckMinDistance && roadItem.Length < curRoadBuilder.RoadCond.MinLength)
                || (funcCond.CheckMinDuration && roadItem.Duration < curRoadBuilder.RoadCond.MinDuration))
            {
                return;
            }

            NoCoverRoadInfo_NR info = new NoCoverRoadInfo_NR();
            info.Distance = roadItem.Length;
            info.Duration = roadItem.Duration;
            info.TpCount_NoCover = roadItem.ValidCount;
            info.TpRate_NoCover = curLowPercent;
            fillNoCoverInfoByTps(info, roadItem.TestPoints);

            info.SN = resultList.Count + 1;
            resultList.Add(info);
        }
        private void fillNoCoverInfoByTps(NoCoverRoadInfo_NR info, List<TestPoint> testPoints)
        {
            info.TestPointList = testPoints;
            if (testPoints == null || testPoints.Count <= 0)
            {
                return;
            }
            TestPoint firstTp = testPoints[0];
            TestPoint midTp = testPoints[testPoints.Count / 2];
            TestPoint endTp = testPoints[testPoints.Count - 1];

            info.BeginTime = firstTp.DateTime;
            info.EndTime = endTp.DateTime;
            info.FileName = firstTp.FileName;
            info.Longitude = midTp.Longitude;
            info.Latitude = midTp.Latitude;

            DoubleKpiGroup rsrpKpi = new DoubleKpiGroup();
            DoubleKpiGroup sinrKpi = new DoubleKpiGroup();
            DoubleKpiGroup lteRsrpKpi = new DoubleKpiGroup();
            DoubleKpiGroup lteSinrKpi = new DoubleKpiGroup();
            List<string> roadList = new List<string>();
            List<string> gridList = new List<string>();
            List<string> areaList = new List<string>();

            GISManager gis = GISManager.GetInstance();
            foreach (TestPoint tp in testPoints)
            {
                float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                if (rsrp != null && rsrp > -141)
                {
                    rsrpKpi.AddSinglePointKpi((float)rsrp);
                }

                float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
                if (sinr != null && sinr > -20)
                {
                    sinrKpi.AddSinglePointKpi((float)sinr);
                }

                float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
                if (lteRsrp != null && lteRsrp > -141)
                {
                    lteRsrpKpi.AddSinglePointKpi((float)lteRsrp);
                }

                float? lteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
                if (lteSinr != null && lteSinr > -50)
                {
                    lteSinrKpi.AddSinglePointKpi((float)lteSinr);
                }

                addStr(roadList, gis.GetRoadPlaceDesc(tp.Longitude, tp.Latitude));
                addStr(gridList, gis.GetGridDesc(tp.Longitude, tp.Latitude));
                addStr(areaList, gis.GetAreaPlaceDesc(tp.Longitude, tp.Latitude));
            }

            info.RsrpAvg = rsrpKpi.KpiAvgValue;
            info.RsrpMax = rsrpKpi.KpiMax;
            info.RsrpMin = rsrpKpi.KpiMin;
            info.SinrAvg = sinrKpi.KpiAvgValue;
            info.SinrMax = sinrKpi.KpiMax;
            info.SinrMin = sinrKpi.KpiMin;

            info.LteRsrpAvg = lteRsrpKpi.KpiAvgValue;
            info.LteRsrpMax = lteRsrpKpi.KpiMax;
            info.LteRsrpMin = lteRsrpKpi.KpiMin;
            info.LteSinrAvg = lteSinrKpi.KpiAvgValue;
            info.LteSinrMax = lteSinrKpi.KpiMax;
            info.LteSinrMin = lteSinrKpi.KpiMin;

            info.RoadDesc = getStrDesc(roadList);
            info.GridName = getStrDesc(gridList);
            info.AreaName = getStrDesc(areaList);
        }
        private void addStr(List<string> strList, string str)
        {
            if (!strList.Contains(str))
            {
                strList.Add(str);
            }
        }

        private string getStrDesc(List<string> strList)
        {
            char charSplit = '；';
            StringBuilder strb = new StringBuilder();
            foreach (string str in strList)
            {
                strb.Append(str + charSplit);
            }

            return strb.ToString().TrimEnd(charSplit);
        }

        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            
            NoCoverRoadInfoForm_NR frm = MainModel.CreateResultForm(typeof(NoCoverRoadInfoForm_NR)) as NoCoverRoadInfoForm_NR;
            frm.FillData(resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class NoCoverRoadAnaByRegion_NR : NoCoverRoadAnaBase_NR
    {
        private static NoCoverRoadAnaByRegion_NR instance = null;
        public static NoCoverRoadAnaByRegion_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NoCoverRoadAnaByRegion_NR();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "无覆盖路段分析(按区域)"; }
        }

    }

    public class NoCoverRoadAnaByFile_NR : NoCoverRoadAnaBase_NR
    {
        private static NoCoverRoadAnaByFile_NR intance = null;
        public static NoCoverRoadAnaByFile_NR GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NoCoverRoadAnaByFile_NR();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "无覆盖路段分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
