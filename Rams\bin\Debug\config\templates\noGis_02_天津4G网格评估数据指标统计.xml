<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">02_天津4G网格评估数据指标统计</Item>
          <Item typeName="Int32" key="KeyCount">2</Item>
          <Item typeName="Int32" key="TimeShowType">0</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试日期</Item>
              <Item typeName="String" key="Exp">{kTimeValue}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试网格</Item>
              <Item typeName="String" key="Exp">{kAreaId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">场景</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">维护区域</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试时长（小时)</Item>
              <Item typeName="String" key="Exp">{Lf_0805/(60*60*1000) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试里程（公里）</Item>
              <Item typeName="String" key="Exp">{Lf_0806/1000 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">厂家</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">LOG名称</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RSRP</Item>
              <Item typeName="String" key="Exp">{Lf_612D0309}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RSRP&gt;=-100dBm比例%</Item>
              <Item typeName="String" key="Exp">{100*Lf_612D0302/Lf_612D0301 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RSRP&gt;=-90dBm比例%</Item>
              <Item typeName="String" key="Exp">{100*(Lf_612D0308+Lf_612D0307+Lf_612D034B+Lf_612D034A+Lf_612D0349+Lf_612D0382+Lf_612D0381 )/Lf_612D0301 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">SINR</Item>
              <Item typeName="String" key="Exp">{Lf_612D0403}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">大于0dB的占比%</Item>
              <Item typeName="String" key="Exp">{100*(Lf_612D040E+Lf_612D040F+Lf_612D0410+Lf_612D0411+Lf_612D0412)/Lf_612D0401 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">大于3dB的占比%</Item>
              <Item typeName="String" key="Exp">{100*(Lf_612D040F+Lf_612D0410+Lf_612D0411+Lf_612D0412)/Lf_612D0401 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">重叠覆盖率%</Item>
              <Item typeName="String" key="Exp">{Lf_612D0F02*100/Lf_612D0F01}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均下行速率Mbps</Item>
              <Item typeName="String" key="Exp">{(value10[3557]+value10[3558])*(1000*8)/((value4[3557]+value4[3558])*1024*1024)) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">下行吞吐率&lt;5Mbps的比例%</Item>
              <Item typeName="String" key="Exp">{100*(Lf_052D64020105+Lf_052D64020106+Lf_052D64020107+Lf_052D64020108+Lf_052D64020109+Lf_052D640201D0+Lf_052D6402010205+Lf_052D6402010206+Lf_052D6402010207+Lf_052D6402010208+Lf_052D6402010209+Lf_052D64020102D0)/(Lf_052D64020103+Lf_052D6402010203) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">下行吞吐率&lt;10Mbps的比例%</Item>
              <Item typeName="String" key="Exp">{100*(Lf_052D64020105+Lf_052D64020106+Lf_052D640201F5+Lf_052D6402010205+Lf_052D6402010206+Lf_052D64020102F5)/(Lf_052D64020103+Lf_052D6402010203) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均上行速率Mbps</Item>
              <Item typeName="String" key="Exp">{(value10[3560]+value10[3561])*(1000*8)/((value2[3560]+value2[3561])*1024*1024) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">上行吞吐率&lt;1Mbps的比例%</Item>
              <Item typeName="String" key="Exp">{100*(Lf_052D64030205+Lf_052D64030206+Lf_052D6403020205+Lf_052D6403020206)/(Lf_052D64030203+Lf_052D6403020203) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">上行吞吐率&lt;4Mbps的比例%</Item>
              <Item typeName="String" key="Exp">{100*(Lf_052D64030205+Lf_052D64030206+Lf_052D64030207+Lf_052D64030208+Lf_052D64030209+Lf_052D6403020205+Lf_052D6403020206+Lf_052D6403020207+Lf_052D6403020208+Lf_052D6403020209)/(Lf_052D64030203+Lf_052D6403020203) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">建立成功率%</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[3075]+value9[3075])/(evtIdCount[3069]+value9[3069])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">建立时延s</Item>
              <Item typeName="String" key="Exp">{value1[3075]/(1000.0*evtIdCount[3075])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">返回时延s</Item>
              <Item typeName="String" key="Exp">{value1[3083]/(1000.0*evtIdCount[3083])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">2</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet">
                <Item typeName="Int32">46</Item>
                <Item typeName="Int32">47</Item>
                <Item typeName="Int32">48</Item>
                <Item typeName="Int32">45</Item>
                <Item typeName="Int32">49</Item>
                <Item typeName="Int32">52</Item>
              </Item>
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">设备</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">备注</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">是否废弃</Item>
              <Item key="Exp" />
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>