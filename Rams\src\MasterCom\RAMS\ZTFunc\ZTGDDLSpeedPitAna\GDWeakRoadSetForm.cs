﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GDWeakRoadSetForm : BaseDialog
    {
        public GDWeakRoadSetForm()
        {
            InitializeComponent();
            if (cmbRoadType.Items.Count > 0)
                cmbRoadType.SelectedIndex = 0;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public WeakRoadCond GetCond()
        {
            WeakRoadCond weakRoad = new WeakRoadCond();
            if (cmbRoadType.SelectedItem.ToString().Contains("弱覆盖"))
                weakRoad.IsWeakCover = true;
            else
                weakRoad.IsWeakCover = false;
            weakRoad.ISampleNum = (int)numSample.Value;
            weakRoad.DWeakPercent = (double)numPercent.Value;
            weakRoad.DThreshold = (double)numThreshold.Value;
            return weakRoad;
        }

        private void cmbRoadType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbRoadType.SelectedItem.ToString().Contains("弱覆盖"))
                numThreshold.Value = (decimal)-110.00;
            else
                numThreshold.Value = (decimal)-3.00;
        }
    }
}
