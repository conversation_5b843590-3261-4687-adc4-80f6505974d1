﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WCDMAScanNonconformityForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.label1 = new System.Windows.Forms.Label();
            this.numUPPct = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.btnRefresh = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.cbxIsIndoor = new System.Windows.Forms.ComboBox();
            this.chSN = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chCellname = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chLacCi = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chFreq = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chCPI = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chLongitude = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chLatitude = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chDirection = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chCountTestpoint = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chCountNormalTp = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chTpPct = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chCellAvgDistance = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chCellClosestDistance = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chCellFarestDistance = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.chIndoor = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.listView = new System.Windows.Forms.ListView();
            this.colWrongDir = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.colDirDiff = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numUPPct)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(142, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(141, 22);
            this.miExportExcel.Text = "导出到Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(673, 4);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(139, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "显示异常采样点比例大于";
            // 
            // numUPPct
            // 
            this.numUPPct.Location = new System.Drawing.Point(818, 2);
            this.numUPPct.Name = "numUPPct";
            this.numUPPct.Size = new System.Drawing.Size(47, 22);
            this.numUPPct.TabIndex = 2;
            this.numUPPct.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(871, 4);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(203, 14);
            this.label2.TabIndex = 3;
            this.label2.Text = "% 的小区，并在地图上橙色高亮标记";
            // 
            // btnRefresh
            // 
            this.btnRefresh.Location = new System.Drawing.Point(1096, 4);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(75, 23);
            this.btnRefresh.TabIndex = 4;
            this.btnRefresh.Text = "确定";
            this.btnRefresh.UseVisualStyleBackColor = true;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(474, 4);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(91, 14);
            this.label3.TabIndex = 5;
            this.label3.Text = "按室分类型显示";
            // 
            // cbxIsIndoor
            // 
            this.cbxIsIndoor.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxIsIndoor.FormattingEnabled = true;
            this.cbxIsIndoor.Items.AddRange(new object[] {
            "非室分",
            "是室分",
            "全部"});
            this.cbxIsIndoor.Location = new System.Drawing.Point(571, 1);
            this.cbxIsIndoor.Name = "cbxIsIndoor";
            this.cbxIsIndoor.Size = new System.Drawing.Size(80, 22);
            this.cbxIsIndoor.TabIndex = 22;
            this.cbxIsIndoor.SelectedIndexChanged += new System.EventHandler(this.cbxIsIndoor_SelectedIndexChanged);
            // 
            // chSN
            // 
            this.chSN.Text = "序号";
            this.chSN.Width = 40;
            // 
            // chCellname
            // 
            this.chCellname.Text = "小区名称";
            this.chCellname.Width = 180;
            // 
            // chLacCi
            // 
            this.chLacCi.Text = "LAC_CI";
            this.chLacCi.Width = 70;
            // 
            // chFreq
            // 
            this.chFreq.Text = "频点";
            this.chFreq.Width = 70;
            // 
            // chCPI
            // 
            this.chCPI.Text = "扰码";
            this.chCPI.Width = 70;
            // 
            // chLongitude
            // 
            this.chLongitude.Text = "经度";
            this.chLongitude.Width = 70;
            // 
            // chLatitude
            // 
            this.chLatitude.Text = "纬度";
            this.chLatitude.Width = 70;
            // 
            // chDirection
            // 
            this.chDirection.Text = "工参方向角";
            this.chDirection.Width = 75;
            // 
            // chCountTestpoint
            // 
            this.chCountTestpoint.Text = "覆盖不符采样点数量";
            this.chCountTestpoint.Width = 120;
            // 
            // chCountNormalTp
            // 
            this.chCountNormalTp.Text = "正常采样点数量";
            this.chCountNormalTp.Width = 100;
            // 
            // chTpPct
            // 
            this.chTpPct.Text = "异常采样点比例";
            this.chTpPct.Width = 100;
            // 
            // chCellAvgDistance
            // 
            this.chCellAvgDistance.Text = "到小区平均距离";
            this.chCellAvgDistance.Width = 100;
            // 
            // chCellClosestDistance
            // 
            this.chCellClosestDistance.Text = "到小区最近距离";
            this.chCellClosestDistance.Width = 100;
            // 
            // chCellFarestDistance
            // 
            this.chCellFarestDistance.Text = "到小区最远距离";
            this.chCellFarestDistance.Width = 100;
            // 
            // chIndoor
            // 
            this.chIndoor.Text = "是否室分小区";
            this.chIndoor.Width = 100;
            // 
            // listView
            // 
            this.listView.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.chSN,
            this.chCellname,
            this.chLacCi,
            this.chFreq,
            this.chCPI,
            this.chLongitude,
            this.chLatitude,
            this.chDirection,
            this.colWrongDir,
            this.colDirDiff,
            this.chCountTestpoint,
            this.chCountNormalTp,
            this.chTpPct,
            this.chCellAvgDistance,
            this.chCellClosestDistance,
            this.chCellFarestDistance,
            this.chIndoor});
            this.listView.ContextMenuStrip = this.contextMenuStrip;
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.Location = new System.Drawing.Point(0, 29);
            this.listView.Name = "listView";
            this.listView.Size = new System.Drawing.Size(1260, 420);
            this.listView.TabIndex = 0;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            this.listView.DoubleClick += new System.EventHandler(this.listView_DoubleClick);
            // 
            // colWrongDir
            // 
            this.colWrongDir.Text = "判断方位角";
            // 
            // colDirDiff
            // 
            this.colDirDiff.Text = "两角差值";
            // 
            // TDScanNonconformityForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1260, 449);
            this.Controls.Add(this.cbxIsIndoor);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.btnRefresh);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.numUPPct);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.listView);
            this.Name = "TDScanNonconformityForm";
            this.ShowIcon = false;
            this.Text = "TD扫频覆盖不符";
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.numUPPct)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numUPPct;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cbxIsIndoor;
        private System.Windows.Forms.ColumnHeader chSN;
        private System.Windows.Forms.ColumnHeader chCellname;
        private System.Windows.Forms.ColumnHeader chLacCi;
        private System.Windows.Forms.ColumnHeader chFreq;
        private System.Windows.Forms.ColumnHeader chCPI;
        private System.Windows.Forms.ColumnHeader chLongitude;
        private System.Windows.Forms.ColumnHeader chLatitude;
        private System.Windows.Forms.ColumnHeader chDirection;
        private System.Windows.Forms.ColumnHeader chCountTestpoint;
        private System.Windows.Forms.ColumnHeader chCountNormalTp;
        private System.Windows.Forms.ColumnHeader chTpPct;
        private System.Windows.Forms.ColumnHeader chCellAvgDistance;
        private System.Windows.Forms.ColumnHeader chCellClosestDistance;
        private System.Windows.Forms.ColumnHeader chCellFarestDistance;
        private System.Windows.Forms.ColumnHeader chIndoor;
        private System.Windows.Forms.ListView listView;
        private System.Windows.Forms.ColumnHeader colWrongDir;
        private System.Windows.Forms.ColumnHeader colDirDiff;
    }
}