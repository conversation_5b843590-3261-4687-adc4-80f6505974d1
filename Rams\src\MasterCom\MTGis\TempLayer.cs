﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;

using MapWinGIS;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.MTGis
{
    public class TempLayer
    {
        public static TempLayer Instance
        {
            get
            {
                if (instance == null)
                {
                    lock (lockObj)
                    {
                        instance = new TempLayer();
                    }
                }
                return instance;
            }
        }

        public void Clear()
        {
            if (tmpShapefileLayer != null)
            {
                tmpShapefileLayer.Clear();
            }
            if (tmpCustomLayer != null)
            {
                tmpCustomLayer.Clear();
            }
        }

        public void Draw(Shape shape)
        {
            InitShapefileLayer();
            tmpShapefileLayer.Draw(shape);
        }

        public void Draw(Shape shape, ShapeDrawingOptions option)
        {
            InitShapefileLayer();
            tmpShapefileLayer.Draw(shape, option);
        }

        public void Draw(Shapefile shp)
        {
            InitShapefileLayer();
            tmpShapefileLayer.Draw(shp);
        }

        public void Draw(TempLayerDrawHandler drawMethod)
        {
            Draw(drawMethod, false);
        }

        public void Draw(TempLayerDrawHandler drawMethod, bool bringTop)
        {
            InitCustomLayer(bringTop);
            tmpCustomLayer.Draw(drawMethod);
        }

        #region private member
        private TempLayer()
        {
            mainModel = MainModel.GetInstance();
            mf = mainModel.MainForm.GetMapForm();
            mop = mf.GetMapOperation();
        }

        private void InitCustomLayer(bool bringTop)
        {
            if (tmpCustomLayer == null)
            {
                tmpCustomLayer = new TempCustomLayer(mop);
            }
            mf.MakeSureCustomLayerVisible(tmpCustomLayer, bringTop);
        }

        private void InitShapefileLayer()
        {
            if (tmpShapefileLayer == null)
            {
                tmpShapefileLayer = new TempShapefileLayer(mainModel);
            }
        }

        private readonly MainModel mainModel;
        private readonly MapForm mf;
        readonly MapOperation mop;
        private TempCustomLayer tmpCustomLayer;
        private TempShapefileLayer tmpShapefileLayer;
        private static TempLayer instance;
        private static object lockObj = new object();
        #endregion
    }

    public delegate void TempLayerDrawHandler(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop);

    public class TempCustomLayer : CustomDrawLayer
    {
        private TempLayerDrawHandler drawHandler;

        public TempCustomLayer(MapOperation mop)
            : base(mop, "临时绘制图层")
        {
            VisibleScaleEnabled = false;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (drawHandler != null)
            {
                drawHandler(clientRect, updateRect, graphics, Map);
            }
        }

        public void Draw(TempLayerDrawHandler drawMethod)
        {
            drawHandler = drawMethod;
        }

        public void Clear()
        {
            drawHandler = null;
        }
    }

    public class TempShapefileLayer
    {
        private readonly AxMapWinGIS.AxMap axMap;
        private int layerHandler;

        public TempShapefileLayer(MainModel mainModel)
        {
            axMap = mainModel.MainForm.GetMapForm().GetMapFormControl();
            layerHandler = -1;
        }

        public void Draw(Shape shape)
        {
            if (axMap == null || shape == null)
            {
                return;
            }

            Shapefile shp = GetShapefile(shape.ShapeType);
            AppendShape(shp, shape);
            AddLayer(shp);
        }

        public void Draw(Shape shape, ShapeDrawingOptions option)
        {
            if (axMap == null || shape == null || option == null)
            {
                return;
            }

            Shapefile shp = GetShapefile(shape.ShapeType);
            shp.DefaultDrawingOptions = option;
            AppendShape(shp, shape);
            AddLayer(shp);
        }

        public void Draw(Shapefile shp)
        {
            if (axMap == null || shp == null)
            {
                return;
            }
            AddLayer(shp);
        }

        public void Clear()
        {
            RemoveLayer();
        }

        private Shapefile GetShapefile(ShpfileType shpType)
        {
            Shapefile shp = new Shapefile();
            shp.CreateNew("", shpType);
            return shp;
        }

        private void AppendShape(Shapefile shp, Shape shape)
        {
            int shapeIndex = shp.NumShapes;
            shp.EditInsertShape(shape, ref shapeIndex);
        }

        private void AddLayer(Shapefile shp)
        {
            RemoveLayer();
            if (axMap != null)
            {
                layerHandler = axMap.AddLayer(shp, true);
            }
        }

        private void RemoveLayer()
        {
            if (layerHandler != -1 && axMap != null)
            {
                axMap.RemoveLayer(layerHandler);
                layerHandler = -1;
            }
        }
    }

    public class OutlineOfRoad
    {
        public OutlineOfRoad()
            : this(-1)
        {
            this.isFixedRoadWidth = false;
        }

        public OutlineOfRoad(float roadWidth)
        {
            this.roadWidth = roadWidth;
            this.LineWidth = 5;
            this.LineColor = Color.Red;
            this.isFixedRoadWidth = true;
        }

        public TempLayerDrawHandler Drawer
        {
            get { return Draw; }
        }

        public float LineWidth { get; set; }

        public Color LineColor { get; set; }

        public virtual void SetPoints(List<PointF> pts)
        {
            this.pixelPoints = pts.ToArray();
        }

        public virtual void SetPoints(List<DbPoint> lnglats)
        {
            this.lnglatPoints = lnglats.ToArray();
        }

        public virtual void SetPoints(List<TestPoint> tps)
        {
            List<DbPoint> lnglats = new List<DbPoint>();
            foreach (TestPoint tp in tps)
            {
                DbPoint p = new DbPoint(tp.Longitude, tp.Latitude);
                lnglats.Add(p);
            }
            SetPoints(lnglats);
        }

        protected virtual void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            // Convert Points
            if (this.lnglatPoints != null)
            {
                mop.ToDisplay(this.lnglatPoints, out this.pixelPoints);
            }
            if (this.pixelPoints == null || this.pixelPoints.Length < 2)
            {
                return;
            }
            PointF[] ptsToDraw = this.pixelPoints;

            // GetRoadWidth
            if (!isFixedRoadWidth)
            {
                roadWidth = GetRoadWidth();
                roadWidth = roadWidth *  CustomDrawLayer.GetTestPointDisplayRatio(mop.Scale);
            }

            // DrawRoad
            DrawRoad(ptsToDraw, clientRect, graphics);
        }

        protected virtual void DrawRoad(PointF[] pts, Rectangle clientRect, Graphics graphics)
        {
            GraphicsPath outerGp = new GraphicsPath();
            Pen outerPen = new Pen(Color.Empty, roadWidth + LineWidth);
            outerGp.AddLines(pts);
            outerGp.Widen(outerPen);

            GraphicsPath innerGp = new GraphicsPath();
            Pen innerPen = new Pen(Color.Empty, roadWidth);
            innerGp.AddLines(pts);
            innerGp.Widen(innerPen);

            SolidBrush brush = new SolidBrush(LineColor);
            graphics.SetClip(outerGp, CombineMode.Replace);
            graphics.SetClip(innerGp, CombineMode.Exclude);
            graphics.FillRectangle(brush, clientRect);

            graphics.ResetClip();
            outerGp.Dispose();
            innerGp.Dispose();
        }

        protected virtual float GetRoadWidth()
        {
            float defaultRet = 20;
            try
            {
                MapDTLayer dtLayer = MainModel.GetInstance().MainForm.GetMapForm().GetDTLayer();
                foreach (MapSerialInfo mInfo in dtLayer.SerialInfos)
                {
                    if (!mInfo.Visible)
                    {
                        continue;
                    }
                    if (mInfo.SizeParamEnabled)
                    {
                        continue;
                    }
                    defaultRet = mInfo.Size;
                    break;
                }
            }
            catch
            {
                //continue
            }

            return defaultRet * MasterCom.RAMS.Model.SymbolManager.SymbolWidth;
        }

        protected float roadWidth;
        protected bool isFixedRoadWidth;
        protected DbPoint[] lnglatPoints;
        protected PointF[] pixelPoints;
    }
}
