﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTPoorRSRQRoad;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryPoorRsrqRoad_NR : QueryPoorRsrqRoadBase
    {
        protected override string themeName { get { return "NR:SS_RSRQ"; } }
        protected override string rsrpName { get { return "NR_SS_RSRP"; } }
        protected override string sinrName { get { return "NR_SS_SINR"; } }
        protected override string rsrqName { get { return "NR_SS_RSRQ"; } }

        protected static readonly object lockObj = new object();
        private static QueryPoorRsrqRoad_NR intance = null;
        public static QueryPoorRsrqRoad_NR Instance
        {
            get
            {
                if (intance == null)
                {
                    lock (lockObj)
                    {
                        if (intance == null)
                        {
                            intance = new QueryPoorRsrqRoad_NR();
                        }
                    }
                }
                return intance;
            }
        }

        public QueryPoorRsrqRoad_NR()
            : base()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_SS_RSRQ");
            Columns.Add("NR_lte_RSRP");
            Columns.Add("NR_lte_SINR");
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "低RSRQ路段_NR"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35010, this.Name);
        }

        protected override PoorRsrqRoad initPoorRsrqRoad()
        {
            return new PoorRsrqRoad_NR();
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            SettingFormNR dlg = new SettingFormNR(poorCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                poorCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            if (poorRoadList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            ResultFormNR frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ResultFormNR)) as ResultFormNR;
            if (frm == null || frm.IsDisposed)
            {
                frm = new ResultFormNR();
                frm.Owner = MainModel.MainForm;
            }

            resetTheme();
            List<PoorRsrqRoad_NR> resList = new List<PoorRsrqRoad_NR>();
            foreach (var item in poorRoadList)
            {
                resList.Add(item as PoorRsrqRoad_NR);
            }
            frm.FillData(resList);
            frm.Visible = true;
            frm.BringToFront();
            poorRoadList = null;
        }

        protected override float? getSinr(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetSCellSinr(tp);
        }

        protected override float? getRsrp(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetSCellRsrp(tp);
        }

        protected override float? getRsrq(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetSCellRsrq(tp);
        }
    }

    public class NRQueryPoorRsrqRoadByFile : QueryPoorRsrqRoad_NR
    {
        private static NRQueryPoorRsrqRoadByFile instance = null;
        public static NRQueryPoorRsrqRoadByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new NRQueryPoorRsrqRoadByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "低RSRQ路段_NR(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
