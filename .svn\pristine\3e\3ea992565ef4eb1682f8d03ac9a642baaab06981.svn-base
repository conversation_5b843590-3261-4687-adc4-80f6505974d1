﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Columns;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage.KPIReport;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class AreaKPIReportForm : MinCloseForm
    {
        private ZTAreaArchiveLayer layer
        {
            get
            {
                ZTAreaArchiveLayer layerTmp = MainModel.GetInstance().MainForm.GetMapForm().GetLayerBase(typeof(ZTAreaArchiveLayer)) as ZTAreaArchiveLayer;
                if (layerTmp == null)
                {
                    layerTmp = new ZTAreaArchiveLayer();
                    MainModel.GetInstance().MainForm.GetMapForm().AddLayerBase(layerTmp);
                }
                return layerTmp;
            }
        }

        Dictionary<AreaBase, List<AreaBase>> rootAreaDic;

        Dictionary<AreaBase, bool> areaSelDic;

        private AreaReportTemplate template = null;

        private List<AreaBase> areas = null;

        private Dictionary<AreaBase, TreeListNode> areaNodeDic;

        private bool isStatLatestOnly;

        private List<string> formulas;

        private ArchiveCondition archiveCondition;

        List<TemplateColumn> cols;

        public AreaKPIReportForm()
            : base()
        {
            InitializeComponent();

            rootAreaDic = new Dictionary<AreaBase, List<AreaBase>>();
            areaNodeDic = new Dictionary<AreaBase, TreeListNode>();
            areaSelDic = new Dictionary<AreaBase, bool>();

            cbxCol.SelectedIndexChanged += cbxCol_SelectedIndexChanged;
            cbxRank.SelectedIndexChanged += cbxRank_SelectedIndexChanged;
        }

        Dictionary<AreaBase, CKpiValue> kpiValueDic = new Dictionary<AreaBase, CKpiValue>();
        public void FillData(AreaReportTemplate template, Dictionary<AreaBase, CKpiValue> KpiValueDic, bool isStatLatestOnly, List<string> formulas, ArchiveCondition archiveCondition, List<TemplateColumn> cols)
        {
            this.cols = cols;
            this.formulas = formulas;
            this.archiveCondition = archiveCondition;
            this.isStatLatestOnly = isStatLatestOnly;
            this.kpiValueDic = KpiValueDic;
            this.rootAreaDic = ArchiveSettingManager.GetInstance().Condition.VillageCondition.RootLeafDic;
            this.areaSelDic = ArchiveSettingManager.GetInstance().Condition.VillageCondition.AreaSelDic;
            this.template = template;

            areas = new List<AreaBase>();
            foreach (AreaBase root in rootAreaDic.Keys)
            {
                areas.AddRange(rootAreaDic[root]);
            }
            layer.Areas = areas;
            WaitBox.Show("正在填充数据...", fileKpiReport);

            cbxCol.Items.Clear();
            foreach (TemplateColumn col in template.Columns)
            {
                if (col.IsDynamicBKColor && col.DynamicBKColorRanges.Count > 0)
                {
                    cbxCol.Items.Add(col);
                }
            }
            if (cbxCol.Items.Count > 0)
            {
                cbxCol.SelectedIndex = 0;
            }

            cbxRank.Items.Clear();
            foreach (AreaRank rank in ZTAreaManager.Instance.Ranks)
            {
                cbxRank.Items.Add(rank);
            }
            cbxRank.SelectedItem = ZTAreaManager.Instance.LowestRank;

            foreach (TreeListNode root in treeList.Nodes)
            {
                refreshGis(root);
            }
        }

        private void makeReportColumn(DevExpress.XtraTreeList.TreeList treeList)
        {
            treeList.Columns.Clear();
            DevExpress.XtraTreeList.Columns.TreeListColumn colFixed = treeList.Columns.Add();
            colFixed.Caption = "区域名称";
            colFixed.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixed.OptionsColumn.AllowEdit = false;
            colFixed.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixed.OptionsColumn.ReadOnly = true;
            colFixed.Visible = true;
            colFixed.Fixed = FixedStyle.Left;
            colFixed.Width = 200;

            colFixed = treeList.Columns.Add();
            colFixed.Caption = "行政级别";
            colFixed.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixed.OptionsColumn.AllowEdit = false;
            colFixed.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixed.OptionsColumn.ReadOnly = true;
            colFixed.Visible = true;
            colFixed.Fixed = FixedStyle.Left;

            colFixed = treeList.Columns.Add();
            colFixed.Caption = "中心经度";
            colFixed.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixed.OptionsColumn.AllowEdit = false;
            colFixed.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixed.OptionsColumn.ReadOnly = true;
            colFixed.Visible = true;
            colFixed.Fixed = FixedStyle.Left;

            colFixed = treeList.Columns.Add();
            colFixed.Caption = "中心纬度";
            colFixed.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixed.OptionsColumn.AllowEdit = false;
            colFixed.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixed.OptionsColumn.ReadOnly = true;
            colFixed.Visible = true;
            colFixed.Fixed = FixedStyle.Left;

            foreach (TemplateColumn rptCol in template.Columns)
            {
                DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
                col.Caption = rptCol.Caption;
                col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col.OptionsColumn.AllowEdit = false;
                col.OptionsColumn.AllowMoveToCustomizationForm = false;
                col.OptionsColumn.ReadOnly = true;
                col.Visible = true;
                col.Tag = rptCol;
            }
        }

        private void fileKpiReport()
        {
            try
            {
                treeList.BeginUpdate();
                treeList.Nodes.Clear();
                makeReportColumn(treeList);
                foreach (AreaBase area in rootAreaDic.Keys)
                {
                    if (area.SubAreas == null)
                    {
                        continue;
                    }
                    appendTreeNode(area, null);
                }                
                treeList.EndUpdate();
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        private void appendTreeNode(AreaBase area, TreeListNode parentNode)
        {
            TreeListNode node;
            CKpiValue kpi;
            if (!kpiValueDic.TryGetValue(area, out kpi))
            {
                if (!areaSelDic.ContainsKey(area))
                    return;
                node = treeList.AppendNode(new object[] { area, area.Rank, area.Centroid == null ? "-" : area.Centroid.x.ToString(), area.Centroid == null ? "-" : area.Centroid.y.ToString() }, parentNode);
                node.Tag = area;
            }
            else
            {
                node = treeList.AppendNode(kpi.getDetail(), parentNode);
                node.Tag = kpi;
            }

            if(area.SubAreas == null) return;

            foreach (AreaBase sub in area.SubAreas)
            {
                appendTreeNode(sub, node);
            }
        }

        private void treeList_BeforeExpand(object sender, BeforeExpandEventArgs e)
        {
            initOneNode(e.Node);
        }

        private void initOneNode(TreeListNode node)
        {
            if (!node.HasChildren)
            {
                return;
            }

            List<TreeListNode> nodes = getSearchNodes(node);
            if (nodes.Count > 0)
            {
                Dictionary<AreaBase, CKpiValue> kpiDic = searchKpi(nodes);

                updateNodes(nodes, kpiDic);
            }
        }

        private Dictionary<AreaBase, CKpiValue> searchKpi(List<TreeListNode> nodes)
        {
            AreaKpiSearchByCountry query = new AreaKpiSearchByCountry();
            query.IsStatLatestOnly = this.isStatLatestOnly;
            QueryCondition searchCond = archiveCondition.GetBaseConditionBackUp();
            query.SetQueryCondition(searchCond);
            query.SetFormula(formulas);
            query.SetTypes(getTypeIds(nodes));
            query.SetColumns(cols);
            query.Query();

            return query.KpiValueDic;
        }

        private void updateNodes(List<TreeListNode> nodes, Dictionary<AreaBase, CKpiValue> kpiDic)
        {
            treeList.BeginUpdate();
            WaitBox.Show("正在更新数据...", updateNodesWithWaitBox, new object[] { nodes, kpiDic });
            treeList.EndUpdate();
        }

        private void updateNodesWithWaitBox(object o)
        {
            try
            {
                object[] oArr = o as object[];
                if (oArr.Length < 2) return;

                List<TreeListNode> nodes = oArr[0] as List<TreeListNode>;
                Dictionary<AreaBase, CKpiValue> kpiDic = oArr[1] as Dictionary<AreaBase, CKpiValue>;
                if (nodes == null || kpiDic == null) return;

                int idx = 0;
                foreach (TreeListNode rtNode in nodes)
                {
                    if (rtNode.Tag is AreaBase)
                    {
                        setKpiValue(kpiDic, rtNode);
                        WaitBox.ProgressPercent = 100 * (++idx) / nodes.Count;
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        private void setKpiValue(Dictionary<AreaBase, CKpiValue> kpiDic, TreeListNode rtNode)
        {
            CKpiValue kpiValue;
            if (kpiDic.TryGetValue(rtNode.Tag as AreaBase, out kpiValue))
            {
                foreach (TreeListColumn col in treeList.Columns)
                {
                    TemplateColumn tCol = col.Tag as TemplateColumn;
                    if (tCol != null)
                    {
                        double dValue = kpiValue.KpiValueDic[tCol];
                        rtNode.SetValue(col, double.IsNaN(dValue) ? "-" : Convert.ToString(dValue));
                    }
                }
                rtNode.Tag = kpiValue;
            }
        }

        private List<TreeListNode> getSearchNodes(TreeListNode node)
        {
            List<TreeListNode> rtLst = new List<TreeListNode>();
            foreach (TreeListNode sub in node.Nodes)
            {
                if (sub.Tag is AreaBase)
                {
                    rtLst.Add(sub);
                }
            }
            return rtLst;
        }

        private Dictionary<int, Dictionary<int, AreaBase>> getTypeIds(List<TreeListNode> nodes)
        {
            Dictionary<int, Dictionary<int, AreaBase>> typeIds = new Dictionary<int, Dictionary<int, AreaBase>>();

            foreach (TreeListNode sub in nodes)
            {
                AreaBase area = sub.Tag as AreaBase;

                Dictionary<int, AreaBase> ids;
                if (!typeIds.TryGetValue(area.AreaTypeID, out ids))
                {
                    ids = new Dictionary<int, AreaBase>();
                    typeIds[area.AreaTypeID] = ids;
                }
                ids[area.AreaID] = area;
            }
            return typeIds;
        }

        private void treeList_DoubleClick(object sender, EventArgs e)
        {
            System.Windows.Forms.MouseEventArgs me = e as System.Windows.Forms.MouseEventArgs;
            TreeListHitInfo info = treeList.CalcHitInfo(me.Location);

            if (info.Node == null)
            {
                return;
            }
            List<AreaBase> selAreas = new List<AreaBase>();
            getLeafNode(info.Node, selAreas);
            layer.SelectedAreas = selAreas;
            DbRect bounds;

            double maxX = double.MinValue;
            double maxY = double.MinValue;
            double minX = double.MaxValue;
            double minY = double.MaxValue;
            foreach (AreaBase area in selAreas)
            {
                maxX = Math.Max(maxX, area.Bounds.x2);
                maxY = Math.Max(maxY, area.Bounds.y2);
                minY = Math.Min(minY, area.Bounds.y1);
                minX = Math.Min(minX, area.Bounds.x1);
            }
            bounds = new DbRect(minX, minY, maxX, maxY);
            refreshGis(info.Node);
            MainModel.MainForm.GetMapForm().GoToView(bounds);
        }

        void refreshGis(TreeListNode node)
        {
            TemplateColumn col = cbxCol.SelectedItem as TemplateColumn;
            AreaRank rank = cbxRank.SelectedItem as AreaRank;
            if (col == null || rank == null)
            {
                return;
            }
            foreach (TreeListColumn treeCol in treeList.Columns)
            {
                if (treeCol.Tag == col)
                {
                    gisCol = treeCol;
                    break;
                }
            }
            Color color = Color.Empty;
            getLeafNodeColor(node, color, layer.AreaColorDic);
            layer.Invalidate();
        }

        private void getLeafNode(TreeListNode node, List<AreaBase> areas)
        {
            if (node.HasChildren)
            {
                foreach (TreeListNode subNode in node.Nodes)
                {
                    getLeafNode(subNode, areas);
                }
            }
            else
            {
                if (node.Tag is CKpiValue)
                {
                    areas.Add((node.Tag as CKpiValue).Area);
                }
                else if (node.Tag is AreaBase)
                {
                    areas.Add(node.Tag as AreaBase);
                }
            }
        }

        private void getLeafNodeColor(TreeListNode node, Color color, Dictionary<AreaBase, Color> areaColorDic)
        {
            initOneNode(node);
            
            refreshColor(node, color, areaColorDic);
        }

        private void refreshColor(TreeListNode node, Color color, Dictionary<AreaBase, Color> areaColorDic)
        {
            if (node.Nodes.Count == 0)
            {
                Color freshColor = getColor(node, color);

                if (node.Tag is AreaBase)
                {
                    AreaBase area = node.Tag as AreaBase;
                    areaColorDic[area] = freshColor;
                }
                else if (node.Tag is CKpiValue)
                {
                    CKpiValue kpi = node.Tag as CKpiValue;
                    areaColorDic[kpi.Area] = freshColor;
                }
            }
            foreach (TreeListNode sub in node.Nodes)
            {
                refreshColor(sub, getColor(sub, color), areaColorDic);
            }
        }

        private Color getColor(TreeListNode node, Color color)
        {
            Color rtColor = color;
            if (node.Tag is CKpiValue)
            {
                TemplateColumn col = gisCol.Tag as TemplateColumn;
                object obj = node.GetValue(gisCol);
                if (obj != null)
                {
                    float value;
                    if (float.TryParse(obj.ToString(), out value))
                    {
                        foreach (DTParameterRangeColor rng in col.DynamicBKColorRanges)
                        {
                            if (rng.Within(value))
                            {
                                rtColor = rng.Value;
                                break;
                            }
                        }
                    }
                }
            }
            return rtColor;
        }

        TreeListColumn gisCol = null;

        private void treeList_NodeCellStyle(object sender, GetCustomNodeCellStyleEventArgs e)
        {
            if (e.Node == null || e.Node.Tag == null)
            {
                return;
            }
            TemplateColumn col = e.Column.Tag as TemplateColumn;
            if (col == null || !col.IsDynamicBKColor)
            {
                return;
            }
            CKpiValue kpi = e.Node.Tag as CKpiValue;
            if (kpi == null)
            {
                return;
            }
            object obj = e.Node.GetValue(e.Column);
            if (obj != null)
            {
                float value;
                if (float.TryParse(obj.ToString(), out value))
                {
                    foreach (DTParameterRangeColor rng in col.DynamicBKColorRanges)
                    {
                        if (rng.Within(value))
                        {
                            e.Appearance.BackColor = rng.Value;
                            break;
                        }
                    }
                }
            }
            e.Appearance.BorderColor = Color.Red;
        }

        private void treeList_FocusedNodeChanged(object sender, FocusedNodeChangedEventArgs e)
        {
            refreshFileView(e.Node);
        }

        private void refreshFileView(TreeListNode focusNode)
        {
            Dictionary<int, FileInfo> files = new Dictionary<int, FileInfo>();

            if (focusNode != null && focusNode.Tag is CKpiValue)
            {
                CKpiValue kpi = focusNode.Tag as CKpiValue;

                if(kpi.Area.Rank == ZTAreaManager.Instance.LowestRank)
                    files = kpi.IdFileDic;
            }
            gridControlFileInfo.DataSource = new List<FileInfo>(files.Values);
            gridControlFileInfo.RefreshDataSource();
        }

        private void miRelayFiles_Click(object sender, EventArgs e)
        {
            List<FileInfo> retList = new List<FileInfo>();
            foreach (int handle in gridViewFile.GetSelectedRows())
            {
                FileInfo file = gridViewFile.GetRow(handle) as FileInfo;
                if (file != null)
                {
                    retList.Add(file);
                }
            }

            if (retList.Count > 0)
            {
                ReplayFileManager.ReplayFiles(retList);
            }
        }

        private bool isMouseDown = false;
        private bool isSetStartRow = false;
        private int hStartRow = -1;
        private int hCurrentRow = -1;
        private void gridViewFile_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                isMouseDown = true;
            }
        }

        private void gridViewFile_MouseUp(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            isMouseDown = false;
            isSetStartRow = false;
            hStartRow = -1;
            hCurrentRow = -1;
        }

        private void gridViewFile_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (isMouseDown)
            {
                DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridViewFile.CalcHitInfo(e.X, e.Y);
                if (info.InRow)
                {
                    if (!isSetStartRow)
                    {
                        hStartRow = info.RowHandle;
                        isSetStartRow = true;
                    }
                    else if (info.RowHandle != hCurrentRow)
                    {
                        hCurrentRow = info.RowHandle;
                        SelectRows(hStartRow, hCurrentRow);
                    }
                }
            }
        }

        private void SelectRows(int startRow, int endRow)
        {
            if (startRow > -1 && endRow > -1)
            {
                gridViewFile.BeginSelection();
                gridViewFile.ClearSelection();
                gridViewFile.SelectRange(startRow, endRow);
                gridViewFile.EndSelection();
            }
        }

        private void searchLastNodes()
        {
            List<TreeListNode> searchNodes = new List<TreeListNode>();
            foreach (TreeListNode root in treeList.Nodes)
            {
                searchNodes.AddRange(getAllNodes(root));
            }

            if (searchNodes.Count > 0)
            {
                Dictionary<AreaBase, CKpiValue> kpiDic = searchKpi(searchNodes);

                updateNodes(searchNodes, kpiDic);
            }
        }

        private List<TreeListNode> getAllNodes(TreeListNode node)
        {
            List<TreeListNode> rtNodes = new List<TreeListNode>();
            foreach (TreeListNode sub in node.Nodes)
            {
                rtNodes.AddRange(getSearchNodes(sub));
                rtNodes.AddRange(getAllNodes(sub));
            }
            return rtNodes;
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            searchLastNodes();

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow captionRow = new NPOIRow();
            foreach (AreaRank rank in ZTAreaManager.Instance.Ranks)
            {
                captionRow.AddCellValue(rank.ToString());
            }
            captionRow.AddCellValue("村庄经度");
            captionRow.AddCellValue("村庄纬度");

            foreach (TreeListColumn col in treeList.Columns)
            {
                if (col.Fixed != FixedStyle.None)
                {
                    continue;
                }
                captionRow.AddCellValue(col.Caption);
            }
            rows.Add(captionRow);
            foreach (TreeListNode node in treeList.Nodes)
            {
                getDataRow(node, rows);
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void getDataRow(TreeListNode node, List<NPOIRow> rows)
        {
            NPOIRow row = new NPOIRow();
            AreaBase area;
            if (node.Tag is AreaBase)
            {
                area = node.Tag as AreaBase;
            }
            else if (node.Tag is CKpiValue)
            {
                area = (node.Tag as CKpiValue).Area;
            }
            else
                return;
            foreach (AreaRank rank in ZTAreaManager.Instance.Ranks)
            {
                AreaBase a = area.GetUpperSectionArea(rank);
                if (a == null)
                {
                    row.AddCellValue("-");
                }
                else
                {
                    row.AddCellValue(a.Name);
                }
            }
            row.AddCellValue(area.Centroid == null ? "-" : area.Centroid.x.ToString());
            row.AddCellValue(area.Centroid == null ? "-" : area.Centroid.y.ToString());
            foreach (TreeListColumn col in treeList.Columns)
            {
                if (col.Fixed != FixedStyle.None)
                {
                    continue;
                }
                row.AddCellValue(node.GetValue(col));
            }
            rows.Add(row);
            foreach (TreeListNode subNode in node.Nodes)
            {
                getDataRow(subNode, rows);
            }
        }

        //以下为旧方式
        //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        void cbxRank_SelectedIndexChanged(object sender, EventArgs e)
        {
            foreach (TreeListNode root in treeList.Nodes)
            {
                refreshGis(root);
            }
        }

        private void cbxCol_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshLegend();

            cbxRank_SelectedIndexChanged(null, null);
        }

        private void refreshLegend()
        {
            lbxLegend.Items.Clear();
            TemplateColumn tCol = cbxCol.SelectedItem as TemplateColumn;
            if (tCol != null)
            {
                lbxLegend.Items.Add("当前GIS渲染图例");
                foreach (DTParameterRangeColor rng in tCol.DynamicBKColorRanges)
                {
                    lbxLegend.Items.Add(rng);
                }
                lbxLegend.Items.Add("");
            }

            if (treeList.FocusedColumn != null)
            {
                TemplateColumn col = treeList.FocusedColumn.Tag as TemplateColumn;
                if (col != null && col.IsDynamicBKColor)
                {
                    lbxLegend.Items.Add("当前列图例");
                    foreach (DTParameterRangeColor rng in col.DynamicBKColorRanges)
                    {
                        lbxLegend.Items.Add(rng);
                    }
                }
            }
            lbxLegend.Invalidate();
        }

        private void treeList_FocusedColumnChanged(object sender, FocusedColumnChangedEventArgs e)
        {
            refreshLegend();
        }

        private void lbxGis_DrawItem(object sender, System.Windows.Forms.DrawItemEventArgs e)
        {
            System.Windows.Forms.ListBox listBoxLegend = sender as System.Windows.Forms.ListBox;
            if (e.Index < 0)
            {
                return;
            }
            object item = listBoxLegend.Items[e.Index];
            string text = "";
            if (item is DTParameterRangeColor)
            {
                e.Graphics.FillRectangle(new SolidBrush((item as DTParameterRangeColor).Value), e.Bounds.X, e.Bounds.Y, 16, 16);
                text = ((DTParameterRange)item).RangeDescription + "  " + ((DTParameterRange)item).DesInfo;
            }
            else if(item is string)
            {
                text = item.ToString();
            }
            e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Black, e.Bounds.X + 20, e.Bounds.Y);
        }

        private void gridViewFile_DoubleClick(object sender, EventArgs e)
        {
            //
        }

        private void ToolStripMenuItemGrid_Click(object sender, EventArgs e)
        {
            TemplateColumn column = cbxCol.SelectedItem as TemplateColumn;
            if (column == null)
            {
                System.Windows.Forms.MessageBox.Show("尚未选择图例，请选择...", "提示");
                return;
            }

            TreeListNode node = treeList.FocusedNode;
            if (node == null) return;

            CKpiValue kpi = node.Tag as CKpiValue;
            if (kpi == null) return;

            setGridColorMode(column);

            CoverGridQuery query = new CoverGridQuery(getAreas(node));
            query.SetQueryCondition(createQueryCondition());
            query.Query();

            layer.ClearAreaColor();
            MainModel.MainForm.GetMapForm().GoToView(kpi.Area.Bounds);
        }

        private void setGridColorMode(TemplateColumn column)
        {
            MapGridLayer gridLayer = this.MainModel.MainForm.GetMapForm().GetGridShowLayer();
            GridColorModeItem item = new GridColorModeItem();
            item.formula = column.Expression;
            List<MasterCom.MControls.ColorRange> crVec = new List<MasterCom.MControls.ColorRange>();
            foreach (DTParameterRangeColor paramRc in column.DynamicBKColorRanges)
            {
                crVec.Add(new MasterCom.MControls.ColorRange(paramRc.Min, paramRc.Max, paramRc.Value));
            }
            item.colorRanges = crVec;
            gridLayer.CurUsingColorMode = item;
            gridLayer.CurUsingColorModeList = new List<GridColorModeItem>();
            gridLayer.CurUsingColorModeList.Add(item);
        }

        private List<AreaBase> getAreas(TreeListNode node)
        {
            List<AreaBase> rtLst = new List<AreaBase>();

            if (node.Nodes.Count == 0)
            {
                addArea(rtLst, node);
            }

            foreach (TreeListNode sub in node.Nodes)
            {
                rtLst.AddRange(getAreas(sub));
            }
            return rtLst;
        }

        private void addArea(List<AreaBase> rtLst, TreeListNode node)
        {
            if (node.Tag is CKpiValue)
            {
                CKpiValue kpi = node.Tag as CKpiValue;
                rtLst.Add(kpi.Area);
            }
            else if (node.Tag is AreaBase)
            {
                rtLst.Add(node.Tag as AreaBase);
            }
        }

        private void ToolStripMenuItemSample_Click(object sender, EventArgs e)
        {
            MainModel.CurGridColorUnitMatrix.Grids.Clear();
            layer.ClearAreaColor();
            if (treeList.FocusedNode == null)
            {
                return;
            }

            CKpiValue kpi = treeList.FocusedNode.Tag as CKpiValue;
            if (kpi == null)
            {
                return;
            }

            CoverTestPointQuery query = new CoverTestPointQuery(getAreas(treeList.FocusedNode));
            query.SetQueryCondition(createQueryCondition());
            query.Query();

            MainModel.MainForm.GetMapForm().GoToView(kpi.Area.Bounds);
        }

        private QueryCondition createQueryCondition()
        {
            QueryCondition cond = new QueryCondition();
            cond.Periods = archiveCondition.BaseCondition.Periods;
            cond.CarrierTypes = archiveCondition.BaseCondition.CarrierTypes;
            cond.Projects = archiveCondition.BaseCondition.Projects;
            cond.ServiceTypes = archiveCondition.BaseCondition.ServiceTypes;
            cond.FileInfos = archiveCondition.BaseCondition.FileInfos;
            return cond;
        }

        private void ToolStripMenuItemCellCoverage_Click(object sender, EventArgs e)
        {
            if (treeList.FocusedNode==null)
            {
                return;
            }
            CKpiValue kpi = treeList.FocusedNode.Tag as CKpiValue;
            if (kpi == null)
            {
                return;
            }

            CellCoverageAnaQuery query = new CellCoverageAnaQuery(kpi.Area, getAreas(treeList.FocusedNode));
            query.SetQueryCondition(createQueryCondition());
            query.Query();

            showCellCoverageForm(query.AreaCellCoverage);
            layer.ClearAreaColor();
        }

        private void showCellCoverageForm(CAreaCellCoverageInfo areaCellInfo)
        {
            CellCoverageAnaForm form = MainModel.GetObjectFromBlackboard(typeof(CellCoverageAnaForm)) as CellCoverageAnaForm;
            if (form == null || form.IsDisposed)
            {
                form = new CellCoverageAnaForm();
            }
            form.FillData(areaCellInfo);
            form.Show(this);
        }

        private void ctxMenu_Opening(object sender, CancelEventArgs e)
        {
            TreeListNode node = treeList.FocusedNode;
            if (node == null) return;

            CAreaSummary summary = node.Tag as CAreaSummary;
            if (summary == null) return;

            ToolStripMenuItemCellCoverage.Enabled = summary.Area.ParentArea != null;
        }
    }
}
