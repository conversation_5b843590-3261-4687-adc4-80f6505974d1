﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteHandOverTooSlowAnaBase : LteHandOverInReasonAnaBase
    {
        public HandoverTooSlowConditiont HandoverTooSlowCond { get; set; } = new HandoverTooSlowConditiont();
        protected LteHandOverTooSlowAnaBase()
            : base()
        {
            FilterSampleByRegion = true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22095, this.Name);
        }

        protected override bool showFuncCondSetDlg()
        {
            HandoverTooSlowConditiontDlg dlg = new HandoverTooSlowConditiontDlg();
            dlg.SetCondition(HandoverTooSlowCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                HandoverTooSlowCond = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override string getReasonDes(HandoverReasonInfo info, out bool isUnReasonable)
        {
            isUnReasonable = false;
            if (info.RsrpBefore == null || info.SinrBefore == null || info.RsrpAfter == null)
            {
                return "";
            }
            float rsrpAdd = (float)info.RsrpAfter - (float)info.RsrpBefore;
            StringBuilder strbDes = getReasonInfo(info, ref isUnReasonable, rsrpAdd);
            if (strbDes.Length > 0)
            {
                strbDes = strbDes.Remove(strbDes.Length - 1, 1);
            }
            return strbDes.ToString();
        }

        private StringBuilder getReasonInfo(HandoverReasonInfo info, ref bool isUnReasonable, float rsrpAdd)
        {
            StringBuilder strbDes = new StringBuilder();
            if (HandoverTooSlowCond.IsChkGoodRsrp && info.RsrpBefore > HandoverTooSlowCond.RsrpWeakGate)
            {
                isUnReasonable = true;
                if (info.SinrBefore <= HandoverTooSlowCond.SinrBefore1Gate)
                {
                    dealUnderGate1(rsrpAdd, strbDes);
                }
                else
                {
                    dealUpGate1(rsrpAdd, strbDes);
                }
            }
            else if (HandoverTooSlowCond.IsChkWeakRsrp && info.RsrpBefore <= HandoverTooSlowCond.RsrpWeakGate)
            {
                isUnReasonable = true;
                if (info.SinrBefore <= HandoverTooSlowCond.SinrBefore2Gate)
                {
                    dealUnderGate2(info, rsrpAdd, strbDes);
                }
                else
                {
                    dealUpGate2(rsrpAdd, strbDes);
                }
            }

            return strbDes;
        }

        private void dealUnderGate1(float rsrpAdd, StringBuilder strbDes)
        {
            if (rsrpAdd > HandoverTooSlowCond.Rsrp1Add1Min1 && rsrpAdd <= HandoverTooSlowCond.Rsrp1Add1Max1)
            {
                strbDes.Append("切换正常；");
            }
            else if (rsrpAdd > HandoverTooSlowCond.Rsrp1Add1Max1 && rsrpAdd <= HandoverTooSlowCond.Rsrp1Add1Max2)
            {
                strbDes.Append("切换过慢，次优先级；");
            }
            else if (rsrpAdd > HandoverTooSlowCond.Rsrp1Add1Max2)
            {
                strbDes.Append("切换过慢，严重问题；");
            }
        }

        private void dealUpGate1(float rsrpAdd, StringBuilder strbDes)
        {
            if (rsrpAdd <= HandoverTooSlowCond.Rsrp1Add2Max1)
            {
                strbDes.Append("切换正常；");
            }
            else if (rsrpAdd > HandoverTooSlowCond.Rsrp1Add2Max1 && rsrpAdd <= HandoverTooSlowCond.Rsrp1Add2Max2)
            {
                strbDes.Append("切换可优化；");
            }
            else if (rsrpAdd > HandoverTooSlowCond.Rsrp1Add2Max2)
            {
                strbDes.Append("切换过慢，次优先级；");
            }
        }

        private void dealUnderGate2(HandoverReasonInfo info, float rsrpAdd, StringBuilder strbDes)
        {
            if (info.RsrpAfter <= HandoverTooSlowCond.RsrpWeakGate2)
            {
                strbDes.Append("无线优化问题；");
            }
            else
            {
                if (rsrpAdd > HandoverTooSlowCond.Rsrp2Add1Min1 && rsrpAdd <= HandoverTooSlowCond.Rsrp2Add1Max1)
                {
                    strbDes.Append("切换正常；");
                }
                else if (rsrpAdd > HandoverTooSlowCond.Rsrp2Add1Max1)
                {
                    strbDes.Append("切换过慢，严重问题；");
                }
            }
        }

        private void dealUpGate2(float rsrpAdd, StringBuilder strbDes)
        {
            if (rsrpAdd <= HandoverTooSlowCond.Rsrp2Add2Max1)
            {
                strbDes.Append("弱覆盖；");
            }
            else if (rsrpAdd > HandoverTooSlowCond.Rsrp2Add2Max1 && rsrpAdd <= HandoverTooSlowCond.Rsrp2Add2Max2)
            {
                strbDes.Append("切换正常；");
            }
            else if (rsrpAdd > HandoverTooSlowCond.Rsrp2Add2Max2 && rsrpAdd <= HandoverTooSlowCond.Rsrp2Add2Max3)
            {
                strbDes.Append("切换可优化；");
            }
            else if (rsrpAdd > HandoverTooSlowCond.Rsrp2Add2Max3)
            {
                strbDes.Append("切换过慢，次优先级；");
            }
        }

        #region Background
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["IsChkGoodRsrp"] = this.HandoverTooSlowCond.IsChkGoodRsrp;
                param["RsrpWeakGate"] = this.HandoverTooSlowCond.RsrpWeakGate;
                param["SinrBefore1Gate"] = this.HandoverTooSlowCond.SinrBefore1Gate;
                param["Rsrp1Add1Min1"] = this.HandoverTooSlowCond.Rsrp1Add1Min1;
                param["Rsrp1Add1Max1"] = this.HandoverTooSlowCond.Rsrp1Add1Max1;
                param["Rsrp1Add1Max2"] = this.HandoverTooSlowCond.Rsrp1Add1Max2;
                param["Rsrp1Add2Max1"] = this.HandoverTooSlowCond.Rsrp1Add2Max1;
                param["Rsrp1Add2Max2"] = this.HandoverTooSlowCond.Rsrp1Add2Max2;
                param["IsChkWeakRsrp"] = this.HandoverTooSlowCond.IsChkWeakRsrp;
                param["SinrBefore2Gate"] = this.HandoverTooSlowCond.SinrBefore2Gate;
                param["RsrpWeakGate2"] = this.HandoverTooSlowCond.RsrpWeakGate2;
                param["Rsrp2Add1Min1"] = this.HandoverTooSlowCond.Rsrp2Add1Min1;
                param["Rsrp2Add1Max1"] = this.HandoverTooSlowCond.Rsrp2Add1Max1;
                param["Rsrp2Add2Max1"] = this.HandoverTooSlowCond.Rsrp2Add2Max1;
                param["Rsrp2Add2Max2"] = this.HandoverTooSlowCond.Rsrp2Add2Max2;
                param["Rsrp2Add2Max3"] = this.HandoverTooSlowCond.Rsrp2Add2Max3;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;

                BackgroundStat = DataConverter.GetValidValue(param, "BackgroundStat", BackgroundStat);
                HandoverTooSlowCond.IsChkGoodRsrp = DataConverter.GetValidValue(param, "IsChkGoodRsrp", HandoverTooSlowCond.IsChkGoodRsrp);
                HandoverTooSlowCond.RsrpWeakGate = DataConverter.GetValidValue(param, "RsrpWeakGate", HandoverTooSlowCond.RsrpWeakGate);
                HandoverTooSlowCond.SinrBefore1Gate = DataConverter.GetValidValue(param, "SinrBefore1Gate", HandoverTooSlowCond.SinrBefore1Gate);
                HandoverTooSlowCond.Rsrp1Add1Min1 = DataConverter.GetValidValue(param, "Rsrp1Add1Min1", HandoverTooSlowCond.Rsrp1Add1Min1);
                HandoverTooSlowCond.Rsrp1Add1Max1 = DataConverter.GetValidValue(param, "Rsrp1Add1Max1", HandoverTooSlowCond.Rsrp1Add1Max1);
                HandoverTooSlowCond.Rsrp1Add1Max2 = DataConverter.GetValidValue(param, "Rsrp1Add1Max2", HandoverTooSlowCond.Rsrp1Add1Max2);
                HandoverTooSlowCond.Rsrp1Add2Max1 = DataConverter.GetValidValue(param, "Rsrp1Add2Max1", HandoverTooSlowCond.Rsrp1Add2Max1);
                HandoverTooSlowCond.Rsrp1Add2Max2 = DataConverter.GetValidValue(param, "Rsrp1Add2Max2", HandoverTooSlowCond.Rsrp1Add2Max2);
                HandoverTooSlowCond.IsChkWeakRsrp = DataConverter.GetValidValue(param, "IsChkWeakRsrp", HandoverTooSlowCond.IsChkWeakRsrp);
                HandoverTooSlowCond.SinrBefore2Gate = DataConverter.GetValidValue(param, "SinrBefore2Gate", HandoverTooSlowCond.SinrBefore2Gate);
                HandoverTooSlowCond.RsrpWeakGate2 = DataConverter.GetValidValue(param, "RsrpWeakGate2", HandoverTooSlowCond.RsrpWeakGate2);
                HandoverTooSlowCond.Rsrp2Add1Min1 = DataConverter.GetValidValue(param, "Rsrp2Add1Min1", HandoverTooSlowCond.Rsrp2Add1Min1);
                HandoverTooSlowCond.Rsrp2Add1Max1 = DataConverter.GetValidValue(param, "Rsrp2Add1Max1", HandoverTooSlowCond.Rsrp2Add1Max1);
                HandoverTooSlowCond.Rsrp2Add2Max1 = DataConverter.GetValidValue(param, "Rsrp2Add2Max1", HandoverTooSlowCond.Rsrp2Add2Max1);
                HandoverTooSlowCond.Rsrp2Add2Max2 = DataConverter.GetValidValue(param, "Rsrp2Add2Max2", HandoverTooSlowCond.Rsrp2Add2Max2);
                HandoverTooSlowCond.Rsrp2Add2Max3 = DataConverter.GetValidValue(param, "Rsrp2Add2Max3", HandoverTooSlowCond.Rsrp2Add2Max3);
                HandoverTooSlowCond.Rsrp2Add2Max3 = DataConverter.GetValidValue(param, "Rsrp2Add2Max3", HandoverTooSlowCond.Rsrp2Add2Max3);
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CommonSimpleProperties(this, showFuncCondSetDlg);
            }
        }
        #endregion
    }

    public class LteHandOverTooSlowAnaBase_FDD : LteHandOverTooSlowAnaBase
    {
        protected LteHandOverTooSlowAnaBase_FDD()
            : base()
        {
            this.hoSuccessEvtIdList = new List<int> { 3156, 3159 };
            carrierID = CarrierType.ChinaUnicom;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26042, this.Name);
        }
        protected override void getReadyBeforeQuery()
        {
            ServiceTypes.Clear();

            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);

            Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_RSRQ");
            Columns.Add("lte_fdd_RSSI");
            Columns.Add("lte_fdd_SCell_LAC");
            Columns.Add("lte_fdd_SCell_CI");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
        }
    }

    public class VoLteHandOverTooSlowAnaBase_FDD : LteHandOverTooSlowAnaBase_FDD
    {
        protected VoLteHandOverTooSlowAnaBase_FDD()
            : base()
        {

        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30030, this.Name);
        }
        protected override void getReadyBeforeQuery()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);

            Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_RSRQ");
            Columns.Add("lte_fdd_RSSI");
            Columns.Add("lte_fdd_SCell_LAC");
            Columns.Add("lte_fdd_SCell_CI");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
        }
    }
}
