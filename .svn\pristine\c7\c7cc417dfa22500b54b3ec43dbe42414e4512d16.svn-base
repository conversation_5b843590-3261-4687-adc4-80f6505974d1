﻿namespace MasterCom.RAMS.Func.EventBlock
{
    partial class EventBlockCompetitionResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.colorGuest = new DevExpress.XtraEditors.ColorEdit();
            this.chkGuest = new DevExpress.XtraEditors.CheckEdit();
            this.chkHost = new DevExpress.XtraEditors.CheckEdit();
            this.colorOverlap = new DevExpress.XtraEditors.ColorEdit();
            this.chkOverlap = new DevExpress.XtraEditors.CheckEdit();
            this.colorHost = new DevExpress.XtraEditors.ColorEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rbRepeat = new System.Windows.Forms.RadioButton();
            this.rbDiff = new System.Windows.Forms.RadioButton();
            this.rbAll = new System.Windows.Forms.RadioButton();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.splitContainer2 = new System.Windows.Forms.SplitContainer();
            this.gbxTime1 = new System.Windows.Forms.GroupBox();
            this.listView1 = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbCount1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEvtName1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadDesc1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLogfile1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll1 = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport1 = new System.Windows.Forms.ToolStripMenuItem();
            this.gbxTime2 = new System.Windows.Forms.GroupBox();
            this.listView2 = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbCount2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEvtName2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadDesc2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLogfile2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip2 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll2 = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2 = new System.Windows.Forms.ToolStripMenuItem();
            this.gbxRepeat = new System.Windows.Forms.GroupBox();
            this.listViewOverlap = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEvtName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLogfile = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStripOverlap = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAllOverlap = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAllOverlap = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportOverlap = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorGuest.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkGuest.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHost.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorOverlap.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOverlap.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorHost.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.splitContainer2.Panel1.SuspendLayout();
            this.splitContainer2.Panel2.SuspendLayout();
            this.splitContainer2.SuspendLayout();
            this.gbxTime1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listView1)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.gbxTime2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listView2)).BeginInit();
            this.contextMenuStrip2.SuspendLayout();
            this.gbxRepeat.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewOverlap)).BeginInit();
            this.contextMenuStripOverlap.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.groupBox2);
            this.panel1.Controls.Add(this.groupBox1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(937, 56);
            this.panel1.TabIndex = 1;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.colorGuest);
            this.groupBox2.Controls.Add(this.chkGuest);
            this.groupBox2.Controls.Add(this.chkHost);
            this.groupBox2.Controls.Add(this.colorOverlap);
            this.groupBox2.Controls.Add(this.chkOverlap);
            this.groupBox2.Controls.Add(this.colorHost);
            this.groupBox2.Location = new System.Drawing.Point(327, 3);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(598, 45);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "图层显示设置";
            // 
            // colorGuest
            // 
            this.colorGuest.EditValue = System.Drawing.Color.Lime;
            this.colorGuest.Location = new System.Drawing.Point(230, 16);
            this.colorGuest.Name = "colorGuest";
            this.colorGuest.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorGuest.Size = new System.Drawing.Size(51, 21);
            this.colorGuest.TabIndex = 3;
            this.colorGuest.EditValueChanged += new System.EventHandler(this.colorGuest_EditValueChanged);
            // 
            // chkGuest
            // 
            this.chkGuest.EditValue = true;
            this.chkGuest.Location = new System.Drawing.Point(173, 18);
            this.chkGuest.Name = "chkGuest";
            this.chkGuest.Properties.Caption = "客队：";
            this.chkGuest.Size = new System.Drawing.Size(51, 19);
            this.chkGuest.TabIndex = 2;
            this.chkGuest.CheckedChanged += new System.EventHandler(this.chkGuest_CheckedChanged);
            // 
            // chkHost
            // 
            this.chkHost.EditValue = true;
            this.chkHost.Location = new System.Drawing.Point(18, 18);
            this.chkHost.Name = "chkHost";
            this.chkHost.Properties.Caption = "主队：";
            this.chkHost.Size = new System.Drawing.Size(50, 19);
            this.chkHost.TabIndex = 2;
            this.chkHost.CheckedChanged += new System.EventHandler(this.chkHost_CheckedChanged);
            // 
            // colorOverlap
            // 
            this.colorOverlap.EditValue = System.Drawing.Color.Red;
            this.colorOverlap.Location = new System.Drawing.Point(389, 15);
            this.colorOverlap.Name = "colorOverlap";
            this.colorOverlap.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorOverlap.Size = new System.Drawing.Size(51, 21);
            this.colorOverlap.TabIndex = 3;
            this.colorOverlap.EditValueChanged += new System.EventHandler(this.colorOverlap_EditValueChanged);
            // 
            // chkOverlap
            // 
            this.chkOverlap.Location = new System.Drawing.Point(332, 16);
            this.chkOverlap.Name = "chkOverlap";
            this.chkOverlap.Properties.Caption = "重叠：";
            this.chkOverlap.Size = new System.Drawing.Size(51, 19);
            this.chkOverlap.TabIndex = 2;
            this.chkOverlap.CheckedChanged += new System.EventHandler(this.chkOverlap_CheckedChanged);
            // 
            // colorHost
            // 
            this.colorHost.EditValue = System.Drawing.Color.Blue;
            this.colorHost.Location = new System.Drawing.Point(74, 16);
            this.colorHost.Name = "colorHost";
            this.colorHost.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorHost.Size = new System.Drawing.Size(51, 21);
            this.colorHost.TabIndex = 3;
            this.colorHost.EditValueChanged += new System.EventHandler(this.colorHost_EditValueChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rbRepeat);
            this.groupBox1.Controls.Add(this.rbDiff);
            this.groupBox1.Controls.Add(this.rbAll);
            this.groupBox1.Location = new System.Drawing.Point(3, 3);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(318, 45);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "汇聚列表显示选择";
            // 
            // rbRepeat
            // 
            this.rbRepeat.AutoSize = true;
            this.rbRepeat.Location = new System.Drawing.Point(212, 20);
            this.rbRepeat.Name = "rbRepeat";
            this.rbRepeat.Size = new System.Drawing.Size(73, 18);
            this.rbRepeat.TabIndex = 2;
            this.rbRepeat.Text = "重叠部分";
            this.rbRepeat.UseVisualStyleBackColor = true;
            this.rbRepeat.CheckedChanged += new System.EventHandler(this.rbRepeat_CheckedChanged);
            // 
            // rbDiff
            // 
            this.rbDiff.AutoSize = true;
            this.rbDiff.Location = new System.Drawing.Point(125, 20);
            this.rbDiff.Name = "rbDiff";
            this.rbDiff.Size = new System.Drawing.Size(73, 18);
            this.rbDiff.TabIndex = 1;
            this.rbDiff.Text = "各队独有";
            this.rbDiff.UseVisualStyleBackColor = true;
            this.rbDiff.CheckedChanged += new System.EventHandler(this.rbDiff_CheckedChanged);
            // 
            // rbAll
            // 
            this.rbAll.AutoSize = true;
            this.rbAll.Checked = true;
            this.rbAll.Location = new System.Drawing.Point(33, 20);
            this.rbAll.Name = "rbAll";
            this.rbAll.Size = new System.Drawing.Size(49, 18);
            this.rbAll.TabIndex = 0;
            this.rbAll.TabStop = true;
            this.rbAll.Text = "全部";
            this.rbAll.UseVisualStyleBackColor = true;
            this.rbAll.CheckedChanged += new System.EventHandler(this.rbAll_CheckedChanged);
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 56);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.splitContainer2);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.gbxRepeat);
            this.splitContainer1.Size = new System.Drawing.Size(937, 480);
            this.splitContainer1.SplitterDistance = 266;
            this.splitContainer1.SplitterWidth = 5;
            this.splitContainer1.TabIndex = 2;
            // 
            // splitContainer2
            // 
            this.splitContainer2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer2.Location = new System.Drawing.Point(0, 0);
            this.splitContainer2.Name = "splitContainer2";
            // 
            // splitContainer2.Panel1
            // 
            this.splitContainer2.Panel1.Controls.Add(this.gbxTime1);
            // 
            // splitContainer2.Panel2
            // 
            this.splitContainer2.Panel2.Controls.Add(this.gbxTime2);
            this.splitContainer2.Size = new System.Drawing.Size(937, 266);
            this.splitContainer2.SplitterDistance = 464;
            this.splitContainer2.SplitterWidth = 5;
            this.splitContainer2.TabIndex = 3;
            // 
            // gbxTime1
            // 
            this.gbxTime1.Controls.Add(this.listView1);
            this.gbxTime1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbxTime1.Location = new System.Drawing.Point(0, 0);
            this.gbxTime1.Name = "gbxTime1";
            this.gbxTime1.Size = new System.Drawing.Size(464, 266);
            this.gbxTime1.TabIndex = 0;
            this.gbxTime1.TabStop = false;
            this.gbxTime1.Text = "主队";
            // 
            // listView1
            // 
            this.listView1.AllColumns.Add(this.olvColumnSN1);
            this.listView1.AllColumns.Add(this.olvColumnAbCount1);
            this.listView1.AllColumns.Add(this.olvColumnEvtName1);
            this.listView1.AllColumns.Add(this.olvColumnTime1);
            this.listView1.AllColumns.Add(this.olvColumnLongitude1);
            this.listView1.AllColumns.Add(this.olvColumnLatitude1);
            this.listView1.AllColumns.Add(this.olvColumnRoadDesc1);
            this.listView1.AllColumns.Add(this.olvColumnLogfile1);
            this.listView1.AllColumns.Add(this.olvColumnLAC1);
            this.listView1.AllColumns.Add(this.olvColumnCI1);
            this.listView1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN1,
            this.olvColumnAbCount1,
            this.olvColumnEvtName1,
            this.olvColumnTime1,
            this.olvColumnLongitude1,
            this.olvColumnLatitude1,
            this.olvColumnRoadDesc1,
            this.olvColumnLogfile1,
            this.olvColumnLAC1,
            this.olvColumnCI1});
            this.listView1.ContextMenuStrip = this.contextMenuStrip1;
            this.listView1.Cursor = System.Windows.Forms.Cursors.Default;
            this.listView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView1.FullRowSelect = true;
            this.listView1.GridLines = true;
            this.listView1.HeaderWordWrap = true;
            this.listView1.IsNeedShowOverlay = false;
            this.listView1.Location = new System.Drawing.Point(3, 18);
            this.listView1.Name = "listView1";
            this.listView1.OwnerDraw = true;
            this.listView1.ShowGroups = false;
            this.listView1.Size = new System.Drawing.Size(458, 245);
            this.listView1.TabIndex = 2;
            this.listView1.UseCompatibleStateImageBehavior = false;
            this.listView1.View = System.Windows.Forms.View.Details;
            this.listView1.VirtualMode = true;
            this.listView1.DoubleClick += new System.EventHandler(this.listView_DoubleClick);
            // 
            // olvColumnSN1
            // 
            this.olvColumnSN1.AspectName = "Index";
            this.olvColumnSN1.HeaderFont = null;
            this.olvColumnSN1.Text = "序号";
            this.olvColumnSN1.Width = 40;
            // 
            // olvColumnAbCount1
            // 
            this.olvColumnAbCount1.AspectName = "AbnormalEventCount";
            this.olvColumnAbCount1.HeaderFont = null;
            this.olvColumnAbCount1.Text = "事件数量";
            this.olvColumnAbCount1.Width = 78;
            // 
            // olvColumnEvtName1
            // 
            this.olvColumnEvtName1.HeaderFont = null;
            this.olvColumnEvtName1.Text = "事件";
            // 
            // olvColumnTime1
            // 
            this.olvColumnTime1.AspectName = "";
            this.olvColumnTime1.HeaderFont = null;
            this.olvColumnTime1.Text = "测试时间";
            this.olvColumnTime1.Width = 146;
            // 
            // olvColumnLongitude1
            // 
            this.olvColumnLongitude1.HeaderFont = null;
            this.olvColumnLongitude1.Text = "经度";
            this.olvColumnLongitude1.Width = 128;
            // 
            // olvColumnLatitude1
            // 
            this.olvColumnLatitude1.HeaderFont = null;
            this.olvColumnLatitude1.Text = "纬度";
            this.olvColumnLatitude1.Width = 123;
            // 
            // olvColumnRoadDesc1
            // 
            this.olvColumnRoadDesc1.HeaderFont = null;
            this.olvColumnRoadDesc1.Text = "道路";
            // 
            // olvColumnLogfile1
            // 
            this.olvColumnLogfile1.HeaderFont = null;
            this.olvColumnLogfile1.Text = "测试文件";
            this.olvColumnLogfile1.Width = 335;
            // 
            // olvColumnLAC1
            // 
            this.olvColumnLAC1.HeaderFont = null;
            this.olvColumnLAC1.Text = "LAC";
            // 
            // olvColumnCI1
            // 
            this.olvColumnCI1.HeaderFont = null;
            this.olvColumnCI1.Text = "CI";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll1,
            this.miCollapseAll1,
            this.toolStripSeparator1,
            this.miExport1});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(149, 76);
            // 
            // miExpandAll1
            // 
            this.miExpandAll1.Name = "miExpandAll1";
            this.miExpandAll1.Size = new System.Drawing.Size(148, 22);
            this.miExpandAll1.Text = "展开所有节点";
            this.miExpandAll1.Click += new System.EventHandler(this.miExpandAll1_Click);
            // 
            // miCollapseAll1
            // 
            this.miCollapseAll1.Name = "miCollapseAll1";
            this.miCollapseAll1.Size = new System.Drawing.Size(148, 22);
            this.miCollapseAll1.Text = "折叠所有节点";
            this.miCollapseAll1.Click += new System.EventHandler(this.miCollapseAll1_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(145, 6);
            // 
            // miExport1
            // 
            this.miExport1.Name = "miExport1";
            this.miExport1.Size = new System.Drawing.Size(148, 22);
            this.miExport1.Text = "导出Excel...";
            this.miExport1.Click += new System.EventHandler(this.miExport1_Click);
            // 
            // gbxTime2
            // 
            this.gbxTime2.Controls.Add(this.listView2);
            this.gbxTime2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbxTime2.Location = new System.Drawing.Point(0, 0);
            this.gbxTime2.Name = "gbxTime2";
            this.gbxTime2.Size = new System.Drawing.Size(468, 266);
            this.gbxTime2.TabIndex = 0;
            this.gbxTime2.TabStop = false;
            this.gbxTime2.Text = "客队";
            // 
            // listView2
            // 
            this.listView2.AllColumns.Add(this.olvColumnSN2);
            this.listView2.AllColumns.Add(this.olvColumnAbCount2);
            this.listView2.AllColumns.Add(this.olvColumnEvtName2);
            this.listView2.AllColumns.Add(this.olvColumnTime2);
            this.listView2.AllColumns.Add(this.olvColumnLongitude2);
            this.listView2.AllColumns.Add(this.olvColumnLatitude2);
            this.listView2.AllColumns.Add(this.olvColumnRoadDesc2);
            this.listView2.AllColumns.Add(this.olvColumnLogfile2);
            this.listView2.AllColumns.Add(this.olvColumnLAC2);
            this.listView2.AllColumns.Add(this.olvColumnCI2);
            this.listView2.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN2,
            this.olvColumnAbCount2,
            this.olvColumnEvtName2,
            this.olvColumnTime2,
            this.olvColumnLongitude2,
            this.olvColumnLatitude2,
            this.olvColumnRoadDesc2,
            this.olvColumnLogfile2,
            this.olvColumnLAC2,
            this.olvColumnCI2});
            this.listView2.ContextMenuStrip = this.contextMenuStrip2;
            this.listView2.Cursor = System.Windows.Forms.Cursors.Default;
            this.listView2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView2.FullRowSelect = true;
            this.listView2.GridLines = true;
            this.listView2.HeaderWordWrap = true;
            this.listView2.IsNeedShowOverlay = false;
            this.listView2.Location = new System.Drawing.Point(3, 18);
            this.listView2.Name = "listView2";
            this.listView2.OwnerDraw = true;
            this.listView2.ShowGroups = false;
            this.listView2.Size = new System.Drawing.Size(462, 245);
            this.listView2.TabIndex = 2;
            this.listView2.UseCompatibleStateImageBehavior = false;
            this.listView2.View = System.Windows.Forms.View.Details;
            this.listView2.VirtualMode = true;
            this.listView2.DoubleClick += new System.EventHandler(this.listView_DoubleClick);
            // 
            // olvColumnSN2
            // 
            this.olvColumnSN2.AspectName = "";
            this.olvColumnSN2.HeaderFont = null;
            this.olvColumnSN2.Text = "序号";
            this.olvColumnSN2.Width = 40;
            // 
            // olvColumnAbCount2
            // 
            this.olvColumnAbCount2.AspectName = "AbnormalEventCount";
            this.olvColumnAbCount2.HeaderFont = null;
            this.olvColumnAbCount2.Text = "事件数量";
            this.olvColumnAbCount2.Width = 78;
            // 
            // olvColumnEvtName2
            // 
            this.olvColumnEvtName2.HeaderFont = null;
            this.olvColumnEvtName2.Text = "事件";
            // 
            // olvColumnTime2
            // 
            this.olvColumnTime2.AspectName = "";
            this.olvColumnTime2.HeaderFont = null;
            this.olvColumnTime2.Text = "测试时间";
            this.olvColumnTime2.Width = 146;
            // 
            // olvColumnLongitude2
            // 
            this.olvColumnLongitude2.HeaderFont = null;
            this.olvColumnLongitude2.Text = "经度";
            this.olvColumnLongitude2.Width = 128;
            // 
            // olvColumnLatitude2
            // 
            this.olvColumnLatitude2.HeaderFont = null;
            this.olvColumnLatitude2.Text = "纬度";
            this.olvColumnLatitude2.Width = 123;
            // 
            // olvColumnRoadDesc2
            // 
            this.olvColumnRoadDesc2.HeaderFont = null;
            this.olvColumnRoadDesc2.Text = "道路";
            // 
            // olvColumnLogfile2
            // 
            this.olvColumnLogfile2.HeaderFont = null;
            this.olvColumnLogfile2.Text = "测试文件";
            this.olvColumnLogfile2.Width = 335;
            // 
            // olvColumnLAC2
            // 
            this.olvColumnLAC2.HeaderFont = null;
            this.olvColumnLAC2.Text = "LAC";
            // 
            // olvColumnCI2
            // 
            this.olvColumnCI2.HeaderFont = null;
            this.olvColumnCI2.Text = "CI2";
            // 
            // contextMenuStrip2
            // 
            this.contextMenuStrip2.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll2,
            this.miCollapseAll2,
            this.toolStripSeparator2,
            this.miExport2});
            this.contextMenuStrip2.Name = "contextMenuStrip1";
            this.contextMenuStrip2.Size = new System.Drawing.Size(149, 76);
            // 
            // miExpandAll2
            // 
            this.miExpandAll2.Name = "miExpandAll2";
            this.miExpandAll2.Size = new System.Drawing.Size(148, 22);
            this.miExpandAll2.Text = "展开所有节点";
            this.miExpandAll2.Click += new System.EventHandler(this.miExpandAll2_Click);
            // 
            // miCollapseAll2
            // 
            this.miCollapseAll2.Name = "miCollapseAll2";
            this.miCollapseAll2.Size = new System.Drawing.Size(148, 22);
            this.miCollapseAll2.Text = "折叠所有节点";
            this.miCollapseAll2.Click += new System.EventHandler(this.miCollapseAll2_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(145, 6);
            // 
            // miExport2
            // 
            this.miExport2.Name = "miExport2";
            this.miExport2.Size = new System.Drawing.Size(148, 22);
            this.miExport2.Text = "导出Excel...";
            this.miExport2.Click += new System.EventHandler(this.miExport2_Click);
            // 
            // gbxRepeat
            // 
            this.gbxRepeat.Controls.Add(this.listViewOverlap);
            this.gbxRepeat.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbxRepeat.Location = new System.Drawing.Point(0, 0);
            this.gbxRepeat.Name = "gbxRepeat";
            this.gbxRepeat.Size = new System.Drawing.Size(937, 209);
            this.gbxRepeat.TabIndex = 1;
            this.gbxRepeat.TabStop = false;
            this.gbxRepeat.Text = "重叠部分";
            // 
            // listViewOverlap
            // 
            this.listViewOverlap.AllColumns.Add(this.olvColumnSN);
            this.listViewOverlap.AllColumns.Add(this.olvColumnAbCount);
            this.listViewOverlap.AllColumns.Add(this.olvColumnEvtName);
            this.listViewOverlap.AllColumns.Add(this.olvColumnTime);
            this.listViewOverlap.AllColumns.Add(this.olvColumnLongitude);
            this.listViewOverlap.AllColumns.Add(this.olvColumnLatitude);
            this.listViewOverlap.AllColumns.Add(this.olvColumnRoadDesc);
            this.listViewOverlap.AllColumns.Add(this.olvColumnLogfile);
            this.listViewOverlap.AllColumns.Add(this.olvColumnLAC);
            this.listViewOverlap.AllColumns.Add(this.olvColumnCI);
            this.listViewOverlap.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnAbCount,
            this.olvColumnEvtName,
            this.olvColumnTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnRoadDesc,
            this.olvColumnLogfile,
            this.olvColumnLAC,
            this.olvColumnCI});
            this.listViewOverlap.ContextMenuStrip = this.contextMenuStripOverlap;
            this.listViewOverlap.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewOverlap.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewOverlap.FullRowSelect = true;
            this.listViewOverlap.GridLines = true;
            this.listViewOverlap.HeaderWordWrap = true;
            this.listViewOverlap.IsNeedShowOverlay = false;
            this.listViewOverlap.Location = new System.Drawing.Point(3, 18);
            this.listViewOverlap.Name = "listViewOverlap";
            this.listViewOverlap.OwnerDraw = true;
            this.listViewOverlap.ShowGroups = false;
            this.listViewOverlap.Size = new System.Drawing.Size(931, 188);
            this.listViewOverlap.TabIndex = 2;
            this.listViewOverlap.UseCompatibleStateImageBehavior = false;
            this.listViewOverlap.View = System.Windows.Forms.View.Details;
            this.listViewOverlap.VirtualMode = true;
            this.listViewOverlap.DoubleClick += new System.EventHandler(this.listView_DoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // olvColumnAbCount
            // 
            this.olvColumnAbCount.AspectName = "AbnormalEventCount";
            this.olvColumnAbCount.HeaderFont = null;
            this.olvColumnAbCount.Text = "事件数量";
            this.olvColumnAbCount.Width = 78;
            // 
            // olvColumnEvtName
            // 
            this.olvColumnEvtName.HeaderFont = null;
            this.olvColumnEvtName.Text = "事件";
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.AspectName = "";
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "测试时间";
            this.olvColumnTime.Width = 146;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 128;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 123;
            // 
            // olvColumnRoadDesc
            // 
            this.olvColumnRoadDesc.HeaderFont = null;
            this.olvColumnRoadDesc.Text = "道路";
            // 
            // olvColumnLogfile
            // 
            this.olvColumnLogfile.HeaderFont = null;
            this.olvColumnLogfile.Text = "测试文件";
            this.olvColumnLogfile.Width = 335;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // contextMenuStripOverlap
            // 
            this.contextMenuStripOverlap.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAllOverlap,
            this.miCollapseAllOverlap,
            this.toolStripSeparator3,
            this.miExportOverlap});
            this.contextMenuStripOverlap.Name = "contextMenuStrip1";
            this.contextMenuStripOverlap.Size = new System.Drawing.Size(149, 76);
            // 
            // miExpandAllOverlap
            // 
            this.miExpandAllOverlap.Name = "miExpandAllOverlap";
            this.miExpandAllOverlap.Size = new System.Drawing.Size(148, 22);
            this.miExpandAllOverlap.Text = "展开所有节点";
            this.miExpandAllOverlap.Click += new System.EventHandler(this.miExpandAllOverlap_Click);
            // 
            // miCollapseAllOverlap
            // 
            this.miCollapseAllOverlap.Name = "miCollapseAllOverlap";
            this.miCollapseAllOverlap.Size = new System.Drawing.Size(148, 22);
            this.miCollapseAllOverlap.Text = "折叠所有节点";
            this.miCollapseAllOverlap.Click += new System.EventHandler(this.miCollapseAllOverlap_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(145, 6);
            // 
            // miExportOverlap
            // 
            this.miExportOverlap.Name = "miExportOverlap";
            this.miExportOverlap.Size = new System.Drawing.Size(148, 22);
            this.miExportOverlap.Text = "导出Excel...";
            this.miExportOverlap.Click += new System.EventHandler(this.miExportOverlap_Click);
            // 
            // EventBlockCompetitionResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(937, 536);
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.panel1);
            this.Name = "EventBlockCompetitionResultForm";
            this.Text = "事件汇聚-竞比窗口";
            this.panel1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.colorGuest.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkGuest.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHost.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorOverlap.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOverlap.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorHost.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            this.splitContainer2.Panel1.ResumeLayout(false);
            this.splitContainer2.Panel2.ResumeLayout(false);
            this.splitContainer2.ResumeLayout(false);
            this.gbxTime1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.listView1)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.gbxTime2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.listView2)).EndInit();
            this.contextMenuStrip2.ResumeLayout(false);
            this.gbxRepeat.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.listViewOverlap)).EndInit();
            this.contextMenuStripOverlap.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton rbRepeat;
        private System.Windows.Forms.RadioButton rbDiff;
        private System.Windows.Forms.RadioButton rbAll;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.SplitContainer splitContainer2;
        private System.Windows.Forms.GroupBox gbxTime1;
        private BrightIdeasSoftware.TreeListView listView1;
        private BrightIdeasSoftware.OLVColumn olvColumnSN1;
        private BrightIdeasSoftware.OLVColumn olvColumnAbCount1;
        private BrightIdeasSoftware.OLVColumn olvColumnEvtName1;
        private BrightIdeasSoftware.OLVColumn olvColumnTime1;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude1;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude1;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadDesc1;
        private BrightIdeasSoftware.OLVColumn olvColumnLogfile1;
        private System.Windows.Forms.GroupBox gbxTime2;
        private BrightIdeasSoftware.TreeListView listView2;
        private BrightIdeasSoftware.OLVColumn olvColumnSN2;
        private BrightIdeasSoftware.OLVColumn olvColumnAbCount2;
        private BrightIdeasSoftware.OLVColumn olvColumnEvtName2;
        private BrightIdeasSoftware.OLVColumn olvColumnTime2;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude2;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude2;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadDesc2;
        private BrightIdeasSoftware.OLVColumn olvColumnLogfile2;
        private System.Windows.Forms.GroupBox gbxRepeat;
        private BrightIdeasSoftware.TreeListView listViewOverlap;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnAbCount;
        private BrightIdeasSoftware.OLVColumn olvColumnEvtName;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnLogfile;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.ColorEdit colorGuest;
        private DevExpress.XtraEditors.CheckEdit chkGuest;
        private DevExpress.XtraEditors.CheckEdit chkHost;
        private DevExpress.XtraEditors.ColorEdit colorOverlap;
        private DevExpress.XtraEditors.CheckEdit chkOverlap;
        private DevExpress.XtraEditors.ColorEdit colorHost;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll1;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll1;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip2;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll2;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll2;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem miExport2;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripOverlap;
        private System.Windows.Forms.ToolStripMenuItem miExpandAllOverlap;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAllOverlap;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem miExportOverlap;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC1;
        private BrightIdeasSoftware.OLVColumn olvColumnCI1;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC2;
        private BrightIdeasSoftware.OLVColumn olvColumnCI2;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
    }
}