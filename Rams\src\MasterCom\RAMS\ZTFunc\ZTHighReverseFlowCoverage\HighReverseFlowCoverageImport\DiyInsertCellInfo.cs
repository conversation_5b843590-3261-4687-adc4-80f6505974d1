﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage.HighReverseFlowCoverageImport
{
    public abstract class DiyInsertCellInfo
    {
        public string ErrMsg { get; protected set; }
        protected abstract string tableName { get; }
        protected List<CellParamInfo> cellInfos = null;
        protected DiyInsertCellInfo(List<CellParamInfo> cellInfos)
        {
            this.cellInfos = cellInfos;
        }

        #region 直连主库进行BCP
        public virtual bool Bcp(SqlConnectionStringBuilder sb)
        {
            string errMsg = BcpHelper<List<CellParamInfo>>.Bcp(sb, tableName, addDatas, cellInfos);

            if (!string.IsNullOrEmpty(errMsg))
            {
                MessageBox.Show(errMsg);
                return false;
            }

            return true;
        }

        protected virtual void addDatas(BCPStore bcp, List<CellParamInfo> cellInfos)
        {
            foreach (var info in cellInfos)
            {
                object[] values = new object[] { info.Province, info.ProvinceID, info.City
                        , info.CityID, info.Region, info.Grid, info.Enodebid, info.CellName
                        , info.Type, info.TAC, info.CI, info.Cell.ILongitude, info.Cell.ILatitude
                        , info.Bts.ILongitude, info.Bts.ILatitude, info.HasErr, info.ErrMsg };
                bcp.AddData(values);
            }
        }
        #endregion
    }

    public class DiyInsertNrCellInfo : DiyInsertCellInfo
    {
        protected override string tableName { get { return "tb_high_reverse_flow_coverage_nr_cell"; } }

        public DiyInsertNrCellInfo(List<CellParamInfo> cellInfos)
            : base(cellInfos)
        {
        }

        protected override void addDatas(BCPStore bcp, List<CellParamInfo> cellInfos)
        {
            foreach (var info in cellInfos)
            {
                NRCellInfo nrCell = info as NRCellInfo;
                object[] values = new object[] { info.Province, info.ProvinceID, info.City
                        , info.CityID, info.Region, info.Grid, info.Enodebid, info.CellName
                        , info.Type, info.TAC, info.CI, info.Cell.ILongitude, info.Cell.ILatitude
                        , info.Bts.ILongitude, info.Bts.ILatitude, nrCell.FreqBandType, info.HasErr
                        , info.ErrMsg };
                bcp.AddData(values);
            }
        }
    }

    public class DiyInsertLteCellInfo : DiyInsertCellInfo
    {
        protected override string tableName { get { return "tb_high_reverse_flow_coverage_lte_cell"; } }

        public DiyInsertLteCellInfo(List<CellParamInfo> cellInfos)
            : base(cellInfos)
        {
        }
    }
}

