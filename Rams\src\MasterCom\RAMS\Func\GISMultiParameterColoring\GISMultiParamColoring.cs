﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.MControls;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 采样点自定义多指标渲染的条件组
    /// 条件组在外部使用字符串表示，格式如下所示：
    /// GSM,RxLevSub,0,-70,10;GSM,RxQualSub,0,4,10|GSM,RxLevSub,0,-70,10|GSM,RxQualSub,0,4,10|default
    /// 在内部解析为List(List(MultiParamColorItem))进行操作
    /// </summary>
    public class MultiParamColorTranslator
    {
        public static string OwnFuncCommanderName
        {
            get
            {
                return "自定义采样点多指标";
            }
        }

        /// <summary>
        /// 如果条件组不合法返回null
        /// </summary>
        public string ParamString
        {
            get
            {
                foreach (List<MultiParamColorItem> lst in itemList)
                {
                    if (lst.Count == 0)
                    {
                        return null;
                    }
                }
                return DisparseParamString(itemList);
            }
        }

        public int DefaultCondIndex
        {
            get
            {
                return defaultItemIndex;
            }
        }

        public int InvalidCondIndex
        {
            get
            {
                return invalidItemIndex;
            }
        }

        public List<List<MultiParamColorItem>> ItemList
        {
            get
            {
                return itemList;
            }
        }

        public MultiParamColorTranslator(string paramString)
        {
            itemList = ParseParamString(paramString);

            defaultItemIndex = invalidItemIndex = -1;
            for (int i = 0; i < itemList.Count; ++i)
            {
                if (itemList[i].Count == 1 && itemList[i][0].ItemType == MultiParamColorType.Default)
                {
                    defaultItemIndex = i;
                }
                if (itemList[i].Count == 1 && itemList[i][0].ItemType == MultiParamColorType.Invalid)
                {
                    invalidItemIndex = i;
                }
            }
        }

        public List<List<MultiParamColorItem>> ParseParamString(string paramString)
        {
            List<List<MultiParamColorItem>> retList = new List<List<MultiParamColorItem>>();
            try
            {
                string[] grps = paramString.Split('|');
                foreach (string grp in grps)
                {
                    List<MultiParamColorItem> lst = new List<MultiParamColorItem>();
                    string[] items = grp.Split(';');
                    foreach (string item in items)
                    {
                        MultiParamColorItem crItem = MultiParamColorItem.CreateInstance(item);
                        if (crItem == null)
                        {
                            return new List<List<MultiParamColorItem>>();
                        }
                        lst.Add(crItem);
                    }
                    retList.Add(lst);
                }
            }
            catch
            {
                return new List<List<MultiParamColorItem>>();
            }
            return retList;
        }

        public string DisparseParamString(List<List<MultiParamColorItem>> crItemList)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < crItemList.Count; ++i)
            {
                List<MultiParamColorItem> lst = crItemList[i];
                for (int j = 0; j < lst.Count; ++j)
                {
                    MultiParamColorItem item = lst[j];
                    sb.Append(item.ParseString);
                    if (j < lst.Count - 1)
                    {
                        sb.Append(";");
                    }
                }
                if (i < crItemList.Count - 1)
                {
                    sb.Append("|");
                }
            }
            return sb.ToString();
        }

        public bool RemoveCondition(int condIndex)
        {
            if (condIndex < 0 || condIndex >= itemList.Count)
            {
                return false;
            }
            itemList.RemoveAt(condIndex);

            defaultItemIndex = invalidItemIndex = -1;
            for (int i = 0; i < itemList.Count; ++i)
            {
                if (itemList[i].Count == 1 && itemList[i][0].ItemType == MultiParamColorType.Default)
                {
                    defaultItemIndex = i;
                }
                if (itemList[i].Count == 1 && itemList[i][0].ItemType == MultiParamColorType.Invalid)
                {
                    invalidItemIndex = i;
                }
            }
            return true;
        }

        public bool RemoveArgumentItem(int condIndex, int itemIndex)
        {
            if (condIndex < 0 || condIndex >= itemList.Count)
            {
                return false;
            }
            if (condIndex == defaultItemIndex || condIndex == invalidItemIndex)
            {
                return false;
            }

            List<MultiParamColorItem> lst = itemList[condIndex];
            if (itemIndex >= 0 && itemIndex < lst.Count)
            {
                lst.RemoveAt(itemIndex);
            }
            return true;
        }

        public int AddArgumentCondition()
        {
            itemList.Add(new List<MultiParamColorItem>());
            return itemList.Count - 1;
        }

        public MultiParamColorItem AddArgumentItem(int condIndex, string parseString)
        {
            MultiParamColorItem item = MultiParamColorItem.CreateInstance(parseString);
            if (item == null)
            {
                return null;
            }
            if (condIndex < 0 || condIndex >= itemList.Count
                || condIndex == defaultItemIndex || condIndex == invalidItemIndex)
            {
                return null;
            }
            itemList[condIndex].Add(item);
            return item;
        }

        public MultiParamColorItem AddArgumentItem(int condIndex, string sysName, string paramName, int arrayIndex, float minValue, float maxValue)
        {
            if (condIndex < 0 || condIndex >= itemList.Count
                || condIndex == defaultItemIndex || condIndex == invalidItemIndex)
            {
                return null;
            }
            MultiParamColorItem item = new MultiParamColorItem(sysName, paramName, arrayIndex, minValue, maxValue);
            itemList[condIndex].Add(item);
            return item;
        }

        public MultiParamColorItem UpdateArgumentItem(int condIndex, int itemIndex, string sysName, string paramName, int arrayIndex, float minValue, float maxValue)
        {
            if (condIndex < 0 || condIndex >= itemList.Count
                || condIndex == defaultItemIndex || condIndex == invalidItemIndex)
            {
                return null;
            }

            List<MultiParamColorItem> lst = itemList[condIndex];
            if (itemIndex < 0 || itemIndex >= lst.Count)
            {
                return null;
            }
            MultiParamColorItem item = new MultiParamColorItem(sysName, paramName, arrayIndex, minValue, maxValue);
            itemList[condIndex][itemIndex] = item;
            return item;
        }

        public int AddDefaultCondition()
        {
            if (defaultItemIndex != -1)
            {
                return -1;
            }

            int idx = AddArgumentCondition();
            MultiParamColorItem item = new MultiParamColorItem(MultiParamColorType.Default);
            itemList[idx].Add(item);
            defaultItemIndex = idx;
            return idx;
        }

        public int AddInvalidCondition()
        {
            if (invalidItemIndex != -1)
            {
                return -1;
            }

            int idx = AddArgumentCondition();
            MultiParamColorItem item = new MultiParamColorItem(MultiParamColorType.Invalid);
            itemList[idx].Add(item);
            invalidItemIndex = idx;
            return idx;
        }

        public bool MoveConditionUp(int condIndex)
        {
            if (condIndex <= 0 || condIndex >= itemList.Count)
            {
                return false;
            }

            List<MultiParamColorItem> lst = itemList[condIndex];
            itemList[condIndex] = itemList[condIndex - 1];
            itemList[condIndex - 1] = lst;

            if (condIndex - 1 == defaultItemIndex)
            {
                defaultItemIndex = condIndex;
            }
            else if (condIndex - 1 == invalidItemIndex)
            {
                invalidItemIndex = condIndex;
            }
            else if (condIndex == defaultItemIndex)
            {
                defaultItemIndex = condIndex - 1;
            }
            else if (condIndex == invalidItemIndex)
            {
                invalidItemIndex = condIndex - 1;
            }
            return true;
        }

        public bool MoveConditionDown(int condIndex)
        {
            if (condIndex >= itemList.Count - 1 || condIndex < 0)
            {
                return false;
            }

            List<MultiParamColorItem> lst = itemList[condIndex];
            itemList[condIndex] = itemList[condIndex + 1];
            itemList[condIndex + 1] = lst;

            if (condIndex + 1 == defaultItemIndex)
            {
                defaultItemIndex = condIndex;
            }
            else if (condIndex + 1 == invalidItemIndex)
            {
                invalidItemIndex = condIndex;
            }
            else if (condIndex == defaultItemIndex)
            {
                defaultItemIndex = condIndex + 1;
            }
            else if (condIndex == invalidItemIndex)
            {
                invalidItemIndex = condIndex + 1;
            }
            return true;
        }

        /// <summary>
        /// 对采样点匹配条件组
        /// </summary>
        /// <param name="tp"></param>
        /// <returns>条件组所在索引位置(从0开始)；如果没有条件组匹配，则返回float.NaN</returns>
        public float GetColorValue(TestPoint tp)
        {
            MultiParamColorJudgeResult result = MultiParamColorJudgeResult.Invalid;

            // 先匹配参数条件组
            for (int i = 0; i < itemList.Count; ++i)
            {
                if (i == defaultItemIndex || i == invalidItemIndex)
                {
                    continue;
                }

                foreach (MultiParamColorItem item in itemList[i])
                {
                    if ((result = item.JudgeTestPoint(tp)) != MultiParamColorJudgeResult.Pass)
                    {
                        break;
                    }
                }
                if (result == MultiParamColorJudgeResult.Pass)  // 找到匹配的参数条件组
                {
                    return i;
                }
            }

            return getResult(result);
        }

        private float getResult(MultiParamColorJudgeResult result)
        {
            // 参数条件组没找到匹配
            // 如果不存在参数条件组或者在匹配参数条件项中发现无效参数
            if (result == MultiParamColorJudgeResult.Invalid)
            {
                if (invalidItemIndex != -1)
                {
                    return invalidItemIndex;
                }
                else
                {
                    return float.NaN;
                }
            }
            // == MultiParamColorJudgeResult.NoPass
            // 缺省条件组是在参数条件组中匹配失败后使用的
            else
            {
                if (defaultItemIndex != -1)
                {
                    return defaultItemIndex;
                }
                else
                {
                    return float.NaN;
                }
            }
        }

        private readonly List<List<MultiParamColorItem>> itemList;
        private int defaultItemIndex;
        private int invalidItemIndex;

        // 以下静态成员用于加快判断
        // 如果字符串不等符号重写方法不是先判断引用地址，那就有点坑了
        private static string sParamString;
        private static MultiParamColorTranslator sTranslator;
        public static float GetColorValue(TestPoint tp, string paramString)
        {
            if (sParamString == null || paramString != sParamString)
            {
                sParamString = paramString;
                sTranslator = new MultiParamColorTranslator(sParamString);
            }
            return sTranslator.GetColorValue(tp);
        }
    }

    /// <summary>
    /// 多指标渲染条件组类型
    /// </summary>
    public enum MultiParamColorType
    {
        Argument,   // 参数条件
        Invalid,    // 无效采样点条件
        Default,    // 缺省的条件
    }

    /// <summary>
    /// 采样点匹配参数条件组结果
    /// 注：无效采样点和缺省的条件不会对采样点进行匹配
    /// </summary>
    public enum MultiParamColorJudgeResult
    {
        Pass,       // 匹配参数条件成功
        NoPass,     // 匹配参数条件失败
        Invalid,    // 采样点参数无效
    }

    /// <summary>
    /// 条件组中的条件项
    /// </summary>
    public class MultiParamColorItem
    {
        public DTDisplayParameter DisplayParam
        {
            get;
            private set;
        }
        public MultiParamColorType ItemType
        {
            get;
            private set;
        }
        public string SystemName
        {
            get;
            private set;
        }
        public string ParamName
        {
            get;
            private set;
        }
        public int ArrayIndex
        {
            get;
            private set;
        }
        public float MinValue
        {
            get;
            private set;
        }
        public float MaxValue
        {
            get;
            private set;
        }
        public string ParseString
        {
            get
            {
                if (ItemType == MultiParamColorType.Default)
                {
                    return "default";
                }
                if (ItemType == MultiParamColorType.Invalid)
                {
                    return "invalid";
                }
                return string.Format("{0},{1},{2},{3},{4}", SystemName, ParamName, ArrayIndex, MinValue, MaxValue);
            }
        }
        public string ShowString
        {
            get
            {
                if (ItemType == MultiParamColorType.Default)
                {
                    return "缺省参数条件";
                }
                if (ItemType == MultiParamColorType.Invalid)
                {
                    return "参数无效采样点";
                }
                return string.Format("{0}≤[{1},{2},{3}]<{4}", MinValue, SystemName, ParamName, ArrayIndex, MaxValue);
            }
        }

        public MultiParamColorItem(MultiParamColorType itemType)
        {
            if (itemType == MultiParamColorType.Argument)
            {
                throw new ArgumentException("参数条件");
            }
            ItemType = itemType;
        }

        public MultiParamColorItem(string systemName, string paramName, int arrayIndex, float minValue, float maxValue)
        {
            SystemName = systemName;
            ParamName = paramName;
            ArrayIndex = arrayIndex;
            MinValue = minValue;
            MaxValue = maxValue;
            DisplayParam = DTDisplayParameterManager.GetInstance()[SystemName, ParamName, ArrayIndex];
            ItemType = MultiParamColorType.Argument;
        }

        /// <summary>
        /// 用当前条件项匹配采样点参数
        /// 注：当前条件项只能是参数条件项
        /// 逻辑：先判断是否无效参数，再判断参数是否匹配条件项
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        public MultiParamColorJudgeResult JudgeTestPoint(TestPoint tp)
        {
            if (DisplayParam == null || !DTParameterManager.GetInstance().CanConvertToFloat(tp[DisplayParam.Info.ParamInfo.Name, DisplayParam.ArrayIndex], DisplayParam.Info.ParamInfo.ValueType))
            {
                return MultiParamColorJudgeResult.Invalid;
            }

            float value = DTParameterManager.GetInstance().ConvertToFloat(tp[DisplayParam.Info.ParamInfo.Name, DisplayParam.ArrayIndex], DisplayParam.Info.ParamInfo.ValueType);
            if (value < DisplayParam.Info.ValueMin || value > DisplayParam.Info.ValueMax)
            {
                return MultiParamColorJudgeResult.Invalid;
            }
            return value >= MinValue && value < MaxValue ? MultiParamColorJudgeResult.Pass : MultiParamColorJudgeResult.NoPass;
        }

        public static MultiParamColorItem CreateInstance(string itemString)
        {
            MultiParamColorItem retItem = null;

            if (itemString == "default")
            {
                retItem = new MultiParamColorItem(MultiParamColorType.Default);
                return retItem;
            }
            else if (itemString == "invalid")
            {
                retItem = new MultiParamColorItem(MultiParamColorType.Invalid);
                return retItem;
            }

            try
            {
                string[] fields = itemString.Split(',');
                retItem = new MultiParamColorItem(fields[0], fields[1], Convert.ToInt32(fields[2]), Convert.ToInt32(fields[3]), Convert.ToInt32(fields[4]));
            }
            catch
            {
                return null;
            }
            return retItem;
        }
    }
}
