﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.SiteCellInfo
{
    public class SiteInfo
    {
        public SiteInfo(System.Data.DataRow row)
        {
            this.Name = row[0].ToString();
            this.SiteType = row[1].ToString();
            this.CenterLng = double.Parse(row[2].ToString());
            this.CenterLat = double.Parse(row[3].ToString());
        }

        public string Name
        {
            get;
            set;
        }

        public string SiteType
        { get; set; }

        public double CenterLng
        {
            get;
            set;
        }

        public double CenterLat { get; set; }
        
        public Dictionary<BTSSiteInfo, double> btsDisDic { get; set; } = new Dictionary<BTSSiteInfo, double>();
        internal void AddBTS(BTSSiteInfo bts, double dis)
        {
            btsDisDic[bts] = dis;
        }

        public int CellCount
        { get; private set; }
        public void MakeSummary()
        {
            foreach (BTSSiteInfo item in btsDisDic.Keys)
            {
                CellCount += item.BTS.Cells.Count;
            }
        }
    }
}
