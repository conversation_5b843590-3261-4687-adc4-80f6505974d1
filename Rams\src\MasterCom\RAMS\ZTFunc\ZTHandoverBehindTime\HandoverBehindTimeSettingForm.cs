﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HandoverBehindTimeSettingForm : BaseForm
    {
        public HandoverBehindTimeSettingForm(ZTHandoverBehindTimeCondition condition)
            :base()
        {
            InitializeComponent();
            initUIValue(condition);
        }

        private void initUIValue(ZTHandoverBehindTimeCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            numSvrPccpch.Value = (decimal)condition.SvrPccpchMax;
            numMaxNCellPccpch.Value = (decimal)condition.NCellPccpch;
            numPccpchDiff.Value = (decimal)condition.PccpchDiffMin;
            numStaySecond.Value = (decimal)condition.StaySecondsMin;
            chkType.Checked = condition.CheckType;
            chkSameBand.Checked = condition.CheckSameBand;
        }

        public ZTHandoverBehindTimeCondition GetCondition()
        {
            float svrPccpch = (float)numSvrPccpch.Value;
            float nCellPccpch = (float)numMaxNCellPccpch.Value;
            float pccpchDiff = (float)numPccpchDiff.Value;
            int staySeconds = (int)numStaySecond.Value;
            bool checkType = chkType.Checked;
            ZTHandoverBehindTimeCondition condition = new ZTHandoverBehindTimeCondition(svrPccpch, nCellPccpch, pccpchDiff, staySeconds, checkType);
            condition.CheckSameBand = chkSameBand.Checked;
            return condition;
        }

    }
}
