using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class UpdateFileAgentForm : Form
    {
        private CategoryEnumItem agentItem;
        public CategoryEnumItem AgentItem
        {
            get { return agentItem; }
        }
        private MainModel mainModel;

        public UpdateFileAgentForm(MainModel mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            InitAgent();
        }

        private void InitAgent()
        {
            cbxAgent.Items.Clear();
            if (mainModel.CategoryManager["Agent"] != null)
            {
                foreach (CategoryEnumItem item in ((CategoryEnum)mainModel.CategoryManager["Agent"]).Items)
                {
                    cbxAgent.Items.Add(item.Description);
                }
            }
            cbxAgent.SelectedIndex = 0;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            string agentName = cbxAgent.SelectedItem.ToString();
            foreach (CategoryEnumItem item in ((CategoryEnum)mainModel.CategoryManager["Agent"]).Items)
            {
                if (item.Description.Equals(agentName))
                {
                    agentItem = item;
                    break;
                }
            }
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}