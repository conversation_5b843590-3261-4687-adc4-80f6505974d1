﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTHandoverBehideTime_LTE : DIYAnalyseByFileBackgroundBase
   {
        public ZTHandoverBehideTime_LTE(MainModel mm)
            : base(mm)
        {
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
            FilterSampleByRegion = false;
            Columns = new List<string>();
            Columns.Add("lte_APP_type");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_ECI");
            Columns.Add("lte_TAC");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_NCell_RSRP");
            Columns.Add("lte_NCell_EARFCN");
        }
        public ZTHandoverBehideTime_LTE(ServiceName serviceName)
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
            FilterSampleByRegion = false;
        }

        protected static readonly object lockObj = new object();
        private static ZTHandoverBehideTime_LTE instance = null;
        public static ZTHandoverBehideTime_LTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTHandoverBehideTime_LTE(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public ZTHandoverBehindTimeCondition HandoverCondition { get; set; } = new ZTHandoverBehindTimeCondition(-101, -95, 6, 3, true);
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                HandoverCondition.MinValue = -141;
                HandoverCondition.MaxValue = 25;
                return true;
            }

            HandoverBehindTimeSettingForm conditionDlg = new HandoverBehindTimeSettingForm(HandoverCondition);
            if (conditionDlg.ShowDialog()==System.Windows.Forms.DialogResult.OK)
            {
                HandoverCondition = conditionDlg.GetCondition();
                HandoverCondition.MinValue = -141;
                HandoverCondition.MaxValue = 25;
                return true;
            }
            return false;
        }

        public override string Name
        {
            get
            {
                return "切换不及时分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22015, this.Name);
        }

        List<LTEHandoverBehindTime> results = null;
        protected override void getReadyBeforeQuery()
        {
            results = new List<LTEHandoverBehindTime>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                LTEHandoverBehindTime info = null;
                foreach (TestPoint tp in fileDataManager.TestPoints)
                {
                    if (HandoverCondition.CheckType && GetAppType(tp) == null)
                    {
                        continue;
                    }
                    if (!isValidTestPoint(tp))//非区域内点
                    {
                        saveAndResetOneResult(ref info);
                    }
                    else
                    {
                        info = saveValidLTEHandoverBehindTime(fileDataManager, info, tp);
                    }
                }
            }
        }

        private LTEHandoverBehindTime saveValidLTEHandoverBehindTime(DTFileDataManager fileDataManager, LTEHandoverBehindTime info, TestPoint tp)
        {
            float? pccpchValue = this.GetSCellRelev(tp);
            float maxNCellPccpch = float.MinValue;
            int? earfcn = GetSCellEarfcn(tp);
            for (int i = 0; i < 10; i++)
            {
                int? nCellEarfcn = GetNCellEarfcn(tp, i);
                bool isValidBand = judgeValidBand(earfcn, nCellEarfcn);
                if (!isValidBand)
                {
                    continue;
                }

                float? nCellPccpch = this.GetNCellRelev(tp, i);
                if (nCellPccpch != null)
                {
                    maxNCellPccpch = Math.Max((float)nCellPccpch, maxNCellPccpch);
                }
            }

            if (HandoverCondition.IsMatchMaxSvrPccpch(pccpchValue)
           && HandoverCondition.IsMatchMinNCellPccpch(maxNCellPccpch)
           && HandoverCondition.IsMatchMinPccpchDiff((float)(maxNCellPccpch - pccpchValue)))
            {
                if (info == null)
                {
                    info = new LTEHandoverBehindTime();
                }
                info.AddTestPoint(tp, (float)pccpchValue);
                if (tp.Equals(fileDataManager.TestPoints[fileDataManager.TestPoints.Count - 1]))
                {//文件最后一点，需要把前面的信息保存起来
                    saveAndResetOneResult(ref info);
                }
            }
            else
            {
                saveAndResetOneResult(ref info);
            }

            return info;
        }

        private bool judgeValidBand(int? earfcn, int? nCellEarfcn)
        {
            bool isValidBand;
            if (!HandoverCondition.CheckSameBand || 
                HandoverCondition.CheckSameBand && earfcn != null && earfcn == nCellEarfcn)
            {
                isValidBand = true;
            }
            else
            {
                isValidBand = false;
            }

            return isValidBand;
        }

        private void saveAndResetOneResult(ref LTEHandoverBehindTime info)
        {
            if (info == null)
            {
                return;
            }
            if (HandoverCondition.IsMatchMinStaySeconds(info.StaySeconds))//持续时间判断
            {
                results.Add(info);
                info.SN = results.Count;
                info.FindRoadName();
            }
            info = null;//重置
        }

        protected override void fireShowForm()
        {
            if (results.Count > 0)
            {
                HandoverBehindTimeListForm_LTE frm = null;
                frm = MainModel.CreateResultForm(typeof(HandoverBehindTimeListForm_LTE)) as HandoverBehindTimeListForm_LTE;
                frm.FillData(results);
                frm.Visible = true;
                frm.BringToFront();
                results = new List<LTEHandoverBehindTime>();
                MainModel.FireSetDefaultMapSerialThemes("LTE_TDD:RSRP", "LTE_FDD:RSRP");
            }
            else
            {
                System.Windows.Forms.MessageBox.Show("在设置的条件下，没有符合的数据。请尝试放宽条件。");
            }
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("LTE", "RSRP");
        }

        protected virtual int? GetSCellEarfcn(TestPoint tp)
        {
            return (int?)tp["lte_EARFCN"];
        }

        protected virtual int? GetNCellEarfcn(TestPoint tp, int index)
        {
            return (int?)tp["lte_NCell_EARFCN", index];
        }

        protected virtual float? GetSCellRelev(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        protected virtual float? GetNCellRelev(TestPoint tp, int index)
        {
            return (float?)tp["lte_NCell_RSRP", index];
        }
        protected virtual object GetAppType(TestPoint tp)
        {
            return tp["lte_APP_type"];
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.切换; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["SvrPccpchMax"] = HandoverCondition.SvrPccpchMax;
                param["NCellPccpch"] = HandoverCondition.NCellPccpch;
                param["PccpchDiffMin"] = HandoverCondition.PccpchDiffMin;
                param["StaySecondsMin"] = HandoverCondition.StaySecondsMin;
                param["CheckType"] = HandoverCondition.CheckType;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("SvrPccpchMax"))
                {
                    HandoverCondition.SvrPccpchMax = (float)param["SvrPccpchMax"];
                }
                if (param.ContainsKey("NCellPccpch"))
                {
                    HandoverCondition.NCellPccpch = (float)param["NCellPccpch"];
                }
                if (param.ContainsKey("PccpchDiffMin"))
                {
                    HandoverCondition.PccpchDiffMin = (float)param["PccpchDiffMin"];
                }
                if (param.ContainsKey("StaySecondsMin"))
                {
                    HandoverCondition.StaySecondsMin = (int)param["StaySecondsMin"];
                }
                if (param.ContainsKey("CheckType"))
                {
                    HandoverCondition.CheckType = (bool)param["CheckType"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new HandoverBehindTimeProperties_LTE(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (LTEHandoverBehindTime item in results)
            {
                BackgroundResult result = item.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            results.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                string svrCellName = bgResult.GetImageValueString();
                bgResult.ImageDesc = "主服小区：" + svrCellName;
            }
        }
        #endregion
    }

    public class ZTHandoverBehideTime_LTE_FDD : ZTHandoverBehideTime_LTE
    {
        private static ZTHandoverBehideTime_LTE_FDD instance = null;
        public static new ZTHandoverBehideTime_LTE_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTHandoverBehideTime_LTE_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public ZTHandoverBehideTime_LTE_FDD(MainModel mainModel)
            : base(mainModel)
        {
            ServiceTypes.Clear();
            FilterSampleByRegion = false;
            Columns = new List<string>();
            Columns.Add("lte_fdd_APP_type");
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_NCell_RSRP");
            carrierID = CarrierType.ChinaUnicom;
        }

        public override string Name
        {
            get
            {
                return "切换不及时分析LTE_FDD";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26035, this.Name);
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("LTE_FDD", "RSRP");
        }

        protected override float? GetSCellRelev(TestPoint tp)
        {
            return (float?)tp["lte_fdd_RSRP"];
        }

        protected override float? GetNCellRelev(TestPoint tp, int index)
        {
            return (float?)tp["lte_fdd_NCell_RSRP", index];
        }
        protected override object GetAppType(TestPoint tp)
        {
            return tp["lte_fdd_APP_type"];
        }
    }

    public class ZTHandoverBehideTime_VOLTE_FDD : ZTHandoverBehideTime_LTE_FDD
    {
        private static ZTHandoverBehideTime_VOLTE_FDD instance = null;
        public static new ZTHandoverBehideTime_VOLTE_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTHandoverBehideTime_VOLTE_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTHandoverBehideTime_VOLTE_FDD(MainModel mainModel)
            : base(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get
            {
                return "VOLTE_FDD切换不及时分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30027, this.Name);
        }
    }
}
