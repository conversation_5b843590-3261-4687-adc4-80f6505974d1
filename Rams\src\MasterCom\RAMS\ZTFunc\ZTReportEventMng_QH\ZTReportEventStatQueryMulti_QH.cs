﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTReportEventStatQueryMulti_QH : QueryBase
    {
        public ZTReportEventStatQueryMulti_QH(MainModel mainModel)
            : base(mainModel)
        {
        }

        private Dictionary<string, Dictionary<string, ZTReportEventStatInfo_QH>> areaGroupDic = null;  //Dictionary<areaName|groupName,Dictionary<netType|eventType,XXXXX>>
        private Dictionary<string, Dictionary<string, ZTReportEventStatInfo_QH>> areaReasonDic = null;  //Dictionary<areaName,Dictionary<netType|eventType,XXXXX>>

        private ReportEventCondition_QH reportEventCond = null;

        ZTReportEventStatSetForm_QH reportEventStatSetForm = null;
        private bool getCondition()
        {
            if (reportEventStatSetForm == null)
            {
                reportEventStatSetForm = new ZTReportEventStatSetForm_QH(this.MainModel, 2);
            }

            if (reportEventStatSetForm.ShowDialog() == DialogResult.OK)
            {
                reportEventStatSetForm.GetCondition(out reportEventCond);
                reportEventCond.UserName = MainModel.User.LoginName;     //用户权限决定能看到的信息
            }
            else
            {
                return false;
            }

            return true;
        }

        protected override void query()
        {
            //if (ZTReportEventMngQuery_QH.thirdNameList.Count == 0)
            if(ZTReportEventMngQuery_QH.thirdNameList.Count != 0
                && ZTReportEventMngQuery_QH.thirdNameList[0].Equals(ZTReportEventMngQuery_QH.nonThird))
            {
                MessageBox.Show("抱歉，您当前没有权限查看此模块", "warmming!", MessageBoxButtons.OK);
                return;
            }
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                if (!getCondition())
                {
                    return;
                }

                areaGroupDic = new Dictionary<string, Dictionary<string, ZTReportEventStatInfo_QH>>(); //重新初始化
                areaReasonDic = new Dictionary<string, Dictionary<string, ZTReportEventStatInfo_QH>>();

                WaitBox.Show("开始查询异常事件...", queryInThread, clientProxy);
                fireShowFormAfterQuery();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected void queryInThread(object o)
        {
            try
            {
                List<int> selDistrictIDList = new List<int>();
                selDistrictIDList.Add(MainModel.DistrictID);
                if (MainModel.User.DBID == -1 && MainModel.MainForm.SelDistrictIDs.Count != 0)
                {
                    selDistrictIDList.Clear();
                    selDistrictIDList.AddRange(MainModel.MainForm.SelDistrictIDs);
                }

                foreach (int did in selDistrictIDList)
                {
                    getReportEventStatDic(reportEventCond, did);
                }
            }
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void getReportEventStatDic(ReportEventCondition_QH reportEventCond, int did)
        {
            ZTSQLReportEventInfoQuery_QH query = new ZTSQLReportEventInfoQuery_QH(MainModel, reportEventCond, did);
            query.Query();
            List<ZTReportEventInfo_QH> eventInfoList = query.GetReportEventInfoList();

            ZTSQLReportEventInfoArchivedQuery_QH queryArchived = new ZTSQLReportEventInfoArchivedQuery_QH(MainModel, reportEventCond, did);
            queryArchived.Query();
            List<ZTReportEventInfo_QH> eventInfoArchivedList = queryArchived.GetReportEventInfoList();

            foreach (ZTReportEventInfo_QH eventInfo in eventInfoList)
            {
                addEventInfoToDic(eventInfo);
            }

            foreach (ZTReportEventInfo_QH eventInfo in eventInfoArchivedList)
            {
                addEventInfoToDic(eventInfo);
            }
        }

        private void addEventInfoToDic(ZTReportEventInfo_QH eventInfo)
        {
            string netEvent = eventInfo.NetType + "|" + eventInfo.EventType;

            #region areaGroupDic
            string areaGroup = eventInfo.AreaName;
            //string areaGroup = eventInfo.AreaName + "|" + eventInfo.GridGroup;
            Dictionary<string, ZTReportEventStatInfo_QH> statAreaGroupDic = null;

            if (this.areaGroupDic.ContainsKey(areaGroup))
            {
                statAreaGroupDic = areaGroupDic[areaGroup];
            }
            else
            {
                statAreaGroupDic = initStatDic();
                areaGroupDic.Add(areaGroup, statAreaGroupDic);
            }

            if (statAreaGroupDic.ContainsKey(netEvent))
            {
                statAreaGroupDic[netEvent].Merge(eventInfo);
            }
            #endregion

            #region areaReasonDic
            Dictionary<string, ZTReportEventStatInfo_QH> statReasonDic = null;

            if (this.areaReasonDic.ContainsKey(eventInfo.AreaName))
            {
                statReasonDic = areaReasonDic[eventInfo.AreaName];
            }
            else
            {
                statReasonDic = initStatDic();
                areaReasonDic.Add(eventInfo.AreaName, statReasonDic);
            }
            if (statReasonDic.ContainsKey(netEvent))
            {
                statReasonDic[netEvent].Merge(eventInfo);
            }
            #endregion
        }

        private Dictionary<string, ZTReportEventStatInfo_QH> initStatDic()
        {
            Dictionary<string, ZTReportEventStatInfo_QH> statDic = new Dictionary<string, ZTReportEventStatInfo_QH>();

            ZTReportEventStatInfo_QH statInfo1 = new ZTReportEventStatInfo_QH("GSM", "掉话");
            statDic.Add("GSM|掉话", statInfo1);

            ZTReportEventStatInfo_QH statInfo2 = new ZTReportEventStatInfo_QH("GSM", "未接通");
            statDic.Add("GSM|未接通", statInfo2);

            ZTReportEventStatInfo_QH statInfo3 = new ZTReportEventStatInfo_QH("TD", "掉话");
            statDic.Add("TD|掉话", statInfo3);

            ZTReportEventStatInfo_QH statInfo4 = new ZTReportEventStatInfo_QH("TD", "未接通");
            statDic.Add("TD|未接通", statInfo4);

            ZTReportEventStatInfo_QH statInfo5 = new ZTReportEventStatInfo_QH("LTE", "掉话");
            statDic.Add("LTE|掉话", statInfo5);

            ZTReportEventStatInfo_QH statInfo6 = new ZTReportEventStatInfo_QH("LTE", "未接通");
            statDic.Add("LTE|未接通", statInfo6);

            ZTReportEventStatInfo_QH statInfo7 = new ZTReportEventStatInfo_QH("LTEScan", "掉话");
            statDic.Add("LTEScan|掉话", statInfo7);

            ZTReportEventStatInfo_QH statInfo8 = new ZTReportEventStatInfo_QH("LTEScan", "未接通");
            statDic.Add("LTEScan|未接通", statInfo8);

            return statDic;
        }

        private void fireShowFormAfterQuery()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTReportEventStatListFormMulti_QH).FullName);
            ZTReportEventStatListFormMulti_QH reportEventStatListForm = obj == null ? null : obj as ZTReportEventStatListFormMulti_QH;
            if (reportEventStatListForm == null || reportEventStatListForm.IsDisposed)
            {
                reportEventStatListForm = new ZTReportEventStatListFormMulti_QH(MainModel);
            }
            reportEventStatListForm.FillData(areaGroupDic, areaReasonDic, reportEventCond);
            if (!reportEventStatListForm.Visible)
            {
                reportEventStatListForm.Show(MainModel.MainForm);
            }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18032, this.Name);
        }

        public override string Name
        {
            get { return "QH异常事件统计"; }
        }

        public override string IconName
        {
            get { return "Images/cellquery.gif"; }
        }
    }
}