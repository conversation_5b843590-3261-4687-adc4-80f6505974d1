﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.VoLTEBlockCallCause;
using MasterCom.Util;
using Message = MasterCom.RAMS.Model.Message;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoLTEBlockCallCauseQuery : DIYAnalyseByFileBackgroundBase
    {
        readonly List<int> moCallBlockEventIds = new List<int> { 1080, 3608 };
        readonly List<int> moCallAttemptEventIds = new List<int> { 1001, 1070, 3001, 3609 };
        readonly List<int> moCallOverEventIds = new List<int> { 1006, 1008, 1010, 1076, 1078, 1080,
            1084, 1086, 1090, 3006, 3008, 3010, 3608, 3610, 3613, 3622, 3623, 3625 };

        readonly List<int> mtCallBlockEventIds = new List<int> { 1081, 3615 };
        readonly List<int> mtCallAttemptEventIds = new List<int> { 1002, 1071, 3002, 3616 };
        readonly List<int> mtCallOverEventIds = new List<int> { 1007, 1009, 1011, 1077, 1079, 1081,
            1085, 1087, 1091, 3007, 3009, 3011, 3615, 3617, 3620, 3626, 3627, 3629 };

        protected static readonly object lockObj = new object();
        private static VoLTEBlockCallCauseQuery instance = null;
        public static VoLTEBlockCallCauseQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLTEBlockCallCauseQuery();
                    }
                }
            }
            return instance;
        }

        protected VoLTEBlockCallCauseQuery()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.queryAllEvent = false;
            Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRQ");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_SCell_LAC");
            Columns.Add("lte_SCell_CI");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_NCell_RSRP");
            Columns.Add("lte_NCell_SINR");
            Columns.Add("lte_NCell_EARFCN");
            Columns.Add("lte_NCell_PCI");
        }

        protected override void queryTimePeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byRound)
        {//只查询含VoLTE MO/MT Block Call事件的文件
            condition.EventIDs = new List<int>();
            condition.EventIDs.AddRange(moCallBlockEventIds);
            condition.EventIDs.AddRange(mtCallBlockEventIds);
            base.queryTimePeriodInfo(clientProxy, package, period, byRound);
        }

        public override string Name
        {
            get
            {
                return "VoLTE未接通原因分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27007, this.Name);
        }

        protected override bool getCondition()
        {
            CauseSettingForm dlg = new CauseSettingForm();
            dlg.Condition = this.blockCallCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            blockCallCond = dlg.Condition;
            blockCalls = new List<BlockCallInfo>();
            return true;
        }

        protected override void fireShowForm()
        {
            if (blockCalls == null || blockCalls.Count == 0)
            {
                MessageBox.Show("没有未接通事件。");
                return;
            }

            BlockCallCauseForm frm = MainModel.CreateResultForm(typeof(BlockCallCauseForm)) as BlockCallCauseForm;
            frm.FillData(this.blockCalls, this.blockCallCond);
            frm.Visible = true;
            frm.BringToFront();
            blockCalls = null;
        }

        /// <summary>
        /// 先进行主被叫关联
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = getMoMtPair();

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private Dictionary<FileInfo, FileInfo> getMoMtPair()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            Dictionary<int, bool> fileAdded = new Dictionary<int, bool>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileAdded.ContainsKey(fileInfo.ID))
                {
                    continue;
                }
                fileAdded[fileInfo.ID] = true;
                if (fileInfo.EventCount == 0)
                {
                    moMtPair[fileInfo] = null;
                }
                else
                {
                    FileInfo mtFile = MainModel.FileInfos.Find(
                        delegate (FileInfo x) { return x.ID == fileInfo.EventCount; });
                    if (mtFile == null)
                    {
                        mtFile = new FileInfo();
                        mtFile.ID = fileInfo.EventCount;
                        mtFile.LogTable = fileInfo.LogTable;
                    }
                    fileAdded[mtFile.ID] = true;
                    moMtPair[fileInfo] = mtFile;
                }
            }

            return moMtPair;
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == 1)
                {
                    moFile = file;
                }
                else if (file.MoMtFlag == 2)
                {
                    mtFile = file;
                }
            }

            List<CallInfo> moCalls = getCallInfos(moFile, moCallAttemptEventIds, moCallOverEventIds, moCallBlockEventIds);
            List<CallInfo> mtCalls = getCallInfos(mtFile, mtCallAttemptEventIds, mtCallOverEventIds, mtCallBlockEventIds);
            anaBothSideFiles(moCalls, mtCalls);
        }

        private List<CallInfo> getCallInfos(DTFileDataManager dtFile, List<int> callAttemptEventIds
            , List<int> callOverEventIds, List<int> callBlockEventIds)
        {
            List<CallInfo> callInfoList = new List<CallInfo>();
            if (dtFile == null)
            {
                return callInfoList;
            }

            string strMoMtDesc = dtFile.GetMoMtDesc();

            CallInfo curCall = null;
            CallInfo lastCall = null;
            foreach (DTData data in dtFile.DTDatas)
            {
                if (data is TestPoint)
                {
                    doWithTestPoint(curCall, data);
                }
                else if (data is Message)
                {
                    doWithMessage(curCall, data);
                }
                else if (data is Event)
                {
                    Event evt = data as Event;
                    if (callAttemptEventIds.Contains(evt.ID))
                    {
                        doWithCallAttemptEvent(callInfoList, strMoMtDesc, evt, ref curCall, ref lastCall);
                    }
                    else if (callOverEventIds.Contains(evt.ID))
                    {
                        doWithCallOverEvent(callBlockEventIds, evt, ref curCall, lastCall);
                    }
                    else if (curCall != null)
                    {
                        curCall.AddEvent(evt);
                    }

                }
            }
            return callInfoList;
        }

        private void doWithTestPoint(CallInfo curCall, DTData data)
        {
            TestPoint tp = data as TestPoint;
            if (tp != null && curCall != null)
            {
                curCall.AddTestPoint(tp);
            }
        }

        private void doWithMessage(CallInfo curCall, DTData data)
        {
            Message msg = data as Message;
            if (msg != null && curCall != null)
            {
                curCall.AddMsg(msg);
            }
        }

        private void doWithCallAttemptEvent(List<CallInfo> callInfoList, string strMoMtDesc, Event evt
            , ref CallInfo curCall, ref CallInfo lastCall)
        {
            lastCall = curCall;
            if (curCall != null)
            {
                curCall.EndTime = evt.DateTime;
            }
            curCall = new CallInfo();
            curCall.FileName = evt.FileName;
            curCall.MoMtDesc = strMoMtDesc;
            curCall.BeginTime = evt.DateTime;
            curCall.AddEvent(evt);
            callInfoList.Add(curCall);
        }

        private void doWithCallOverEvent(List<int> callBlockEventIds, Event evt
            , ref CallInfo curCall, CallInfo lastCall)
        {
            if (curCall != null)
            {
                bool needAdd = true;
                if (callBlockEventIds.Contains(evt.ID))
                {
                    if (lastCall != null && evt.DateTime == curCall.BeginTime)
                    {
                        //block time与attempt time一致，则该block call属于上一次通话结果
                        needAdd = false;
                        lastCall.IsBlockCall = true;
                        lastCall.BlockEvt = evt;
                        lastCall.BlockTime = evt.DateTime;
                    }
                    else
                    {
                        curCall.BlockEvt = evt;
                        curCall.IsBlockCall = true;
                        curCall.BlockTime = evt.DateTime;
                    }
                }
                if (needAdd)
                {
                    curCall.AddEvent(evt);
                    curCall.EndTime = evt.DateTime;
                    curCall = null;
                }
            }
        }

        private List<BlockCallInfo> blockCalls = new List<BlockCallInfo>();
        private void anaBothSideFiles(List<CallInfo> moCalls, List<CallInfo> mtCalls)
        {
            int lastCallIdx = 0;
            for (int i = 0; i < moCalls.Count; i++)
            {
                CallInfo moCall = moCalls[i];
                if (moCall.IsBlockCall)
                {
                    lastCallIdx = setOtherSideCall(lastCallIdx, moCall, mtCalls);
                    saveBlockCall(moCall);
                }
            }

            lastCallIdx = 0;
            for (int i = 0; i < mtCalls.Count; i++)
            {
                CallInfo mtCall = mtCalls[i];
                if (mtCall.IsBlockCall && mtCall.OtherSideCall == null)
                {
                    lastCallIdx = setOtherSideCall(lastCallIdx, mtCall, moCalls);
                    saveBlockCall(mtCall);
                }
            }

        }

        private int setOtherSideCall(int lastCallIdx, CallInfo call, List<CallInfo> otherSideCalls)
        {
            for (int j = lastCallIdx; j < otherSideCalls.Count; j++)
            {
                CallInfo otherSideCall = otherSideCalls[j];
                if (call.BeginTime > otherSideCall.BeginTime
                    && (call.BeginTime - otherSideCall.BeginTime).TotalSeconds < 30)
                {
                    call.OtherSideCall = otherSideCall;
                    lastCallIdx = j;
                    if (!otherSideCall.IsBlockCall)
                    {
                        otherSideCall.BlockTime = call.BlockTime;
                    }
                    break;
                }
            }

            return lastCallIdx;
        }

        BlockCallCondition blockCallCond;
        private void saveBlockCall(CallInfo call)
        {
            call.Evaluate(blockCallCond, true);
            int sn = this.blockCalls.Count + 1;
            BlockCallInfo dropCall = new BlockCallInfo(sn, call);
            this.blockCalls.Add(dropCall);
        }

    }

    public class VoLTEBlockCallCauseQuery_FDD : VoLTEBlockCallCauseQuery
    {
        private static VoLTEBlockCallCauseQuery_FDD instance = null;
        public static new VoLTEBlockCallCauseQuery_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLTEBlockCallCauseQuery_FDD();
                    }
                }
            }
            return instance;
        }
        protected VoLTEBlockCallCauseQuery_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            carrierID = CarrierType.ChinaUnicom;

            Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_RSRQ");
            Columns.Add("lte_fdd_RSSI");
            Columns.Add("lte_fdd_SCell_LAC");
            Columns.Add("lte_fdd_SCell_CI");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_NCell_RSRP");
            Columns.Add("lte_fdd_NCell_SINR");
            Columns.Add("lte_fdd_NCell_EARFCN");
            Columns.Add("lte_fdd_NCell_PCI");
        }
        public override string Name
        {
            get
            {
                return "VoLTE_FDD未接通原因分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30034, this.Name);
        }
    }
}
