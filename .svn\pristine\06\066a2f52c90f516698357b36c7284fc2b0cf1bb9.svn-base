﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDPoorBlerCellListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TDPoorBlerCellListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCell = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLACCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLogName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBLER = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCCPCH_RSCP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCCPCH_C2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDPCH_C2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxBler = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinBler = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgBler = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxPccpchRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinPccpchRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgPccpchRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxPccpchC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinPccpchC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgPccpchC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxDpchC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinDpchC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgDpchC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCell)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 98);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Visible = false;
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(152, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Visible = false;
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewCell
            // 
            this.ListViewCell.AllColumns.Add(this.olvColumnSN);
            this.ListViewCell.AllColumns.Add(this.olvColumnCellName);
            this.ListViewCell.AllColumns.Add(this.olvColumnLACCI);
            this.ListViewCell.AllColumns.Add(this.olvColumnSample);
            this.ListViewCell.AllColumns.Add(this.olvColumnLogName);
            this.ListViewCell.AllColumns.Add(this.olvColumnBLER);
            this.ListViewCell.AllColumns.Add(this.olvColumnPCCPCH_RSCP);
            this.ListViewCell.AllColumns.Add(this.olvColumnPCCPCH_C2I);
            this.ListViewCell.AllColumns.Add(this.olvColumnDPCH_C2I);
            this.ListViewCell.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewCell.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewCell.AllColumns.Add(this.olvColumnMaxBler);
            this.ListViewCell.AllColumns.Add(this.olvColumnMinBler);
            this.ListViewCell.AllColumns.Add(this.olvColumnAvgBler);
            this.ListViewCell.AllColumns.Add(this.olvColumnMaxPccpchRscp);
            this.ListViewCell.AllColumns.Add(this.olvColumnMinPccpchRscp);
            this.ListViewCell.AllColumns.Add(this.olvColumnAvgPccpchRscp);
            this.ListViewCell.AllColumns.Add(this.olvColumnMaxPccpchC2I);
            this.ListViewCell.AllColumns.Add(this.olvColumnMinPccpchC2I);
            this.ListViewCell.AllColumns.Add(this.olvColumnAvgPccpchC2I);
            this.ListViewCell.AllColumns.Add(this.olvColumnMaxDpchC2I);
            this.ListViewCell.AllColumns.Add(this.olvColumnMinDpchC2I);
            this.ListViewCell.AllColumns.Add(this.olvColumnAvgDpchC2I);
            this.ListViewCell.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnLACCI,
            this.olvColumnSample,
            this.olvColumnLogName,
            this.olvColumnBLER,
            this.olvColumnPCCPCH_RSCP,
            this.olvColumnPCCPCH_C2I,
            this.olvColumnDPCH_C2I,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnMaxBler,
            this.olvColumnMinBler,
            this.olvColumnAvgBler,
            this.olvColumnMaxPccpchRscp,
            this.olvColumnMinPccpchRscp,
            this.olvColumnAvgPccpchRscp,
            this.olvColumnMaxPccpchC2I,
            this.olvColumnMinPccpchC2I,
            this.olvColumnAvgPccpchC2I,
            this.olvColumnMaxDpchC2I,
            this.olvColumnMinDpchC2I,
            this.olvColumnAvgDpchC2I});
            this.ListViewCell.ContextMenuStrip = this.ctxMenu;
            this.ListViewCell.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewCell.FullRowSelect = true;
            this.ListViewCell.GridLines = true;
            this.ListViewCell.HeaderWordWrap = true;
            this.ListViewCell.IsNeedShowOverlay = false;
            this.ListViewCell.Location = new System.Drawing.Point(0, 0);
            this.ListViewCell.Name = "ListViewCell";
            this.ListViewCell.OwnerDraw = true;
            this.ListViewCell.ShowGroups = false;
            this.ListViewCell.Size = new System.Drawing.Size(1347, 502);
            this.ListViewCell.TabIndex = 5;
            this.ListViewCell.UseCompatibleStateImageBehavior = false;
            this.ListViewCell.View = System.Windows.Forms.View.Details;
            this.ListViewCell.VirtualMode = true;
            this.ListViewCell.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 120;
            // 
            // olvColumnLACCI
            // 
            this.olvColumnLACCI.HeaderFont = null;
            this.olvColumnLACCI.Text = "LAC_CI";
            this.olvColumnLACCI.Width = 120;
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "采样点数";
            // 
            // olvColumnLogName
            // 
            this.olvColumnLogName.HeaderFont = null;
            this.olvColumnLogName.Text = "文件名称";
            this.olvColumnLogName.Width = 150;
            // 
            // olvColumnBLER
            // 
            this.olvColumnBLER.HeaderFont = null;
            this.olvColumnBLER.Text = "BLER";
            // 
            // olvColumnPCCPCH_RSCP
            // 
            this.olvColumnPCCPCH_RSCP.HeaderFont = null;
            this.olvColumnPCCPCH_RSCP.Text = "PCCPCH_RSCP";
            this.olvColumnPCCPCH_RSCP.Width = 90;
            // 
            // olvColumnPCCPCH_C2I
            // 
            this.olvColumnPCCPCH_C2I.HeaderFont = null;
            this.olvColumnPCCPCH_C2I.Text = "PCCPCH_C/I";
            this.olvColumnPCCPCH_C2I.Width = 80;
            // 
            // olvColumnDPCH_C2I
            // 
            this.olvColumnDPCH_C2I.HeaderFont = null;
            this.olvColumnDPCH_C2I.Text = "DPCH_C/I";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            // 
            // olvColumnMaxBler
            // 
            this.olvColumnMaxBler.HeaderFont = null;
            this.olvColumnMaxBler.Text = "最大BLER";
            // 
            // olvColumnMinBler
            // 
            this.olvColumnMinBler.HeaderFont = null;
            this.olvColumnMinBler.Text = "最小BLER";
            // 
            // olvColumnAvgBler
            // 
            this.olvColumnAvgBler.HeaderFont = null;
            this.olvColumnAvgBler.Text = "平均BLER";
            // 
            // olvColumnMaxPccpchRscp
            // 
            this.olvColumnMaxPccpchRscp.HeaderFont = null;
            this.olvColumnMaxPccpchRscp.Text = "最大PCCPCH_RSCP";
            this.olvColumnMaxPccpchRscp.Width = 80;
            // 
            // olvColumnMinPccpchRscp
            // 
            this.olvColumnMinPccpchRscp.HeaderFont = null;
            this.olvColumnMinPccpchRscp.Text = "最小PCCPCH_RSCP";
            this.olvColumnMinPccpchRscp.Width = 80;
            // 
            // olvColumnAvgPccpchRscp
            // 
            this.olvColumnAvgPccpchRscp.HeaderFont = null;
            this.olvColumnAvgPccpchRscp.Text = "平均PCCPCH_RSCP";
            this.olvColumnAvgPccpchRscp.Width = 80;
            // 
            // olvColumnMaxPccpchC2I
            // 
            this.olvColumnMaxPccpchC2I.HeaderFont = null;
            this.olvColumnMaxPccpchC2I.Text = "最大PCCPCH_C/I";
            // 
            // olvColumnMinPccpchC2I
            // 
            this.olvColumnMinPccpchC2I.HeaderFont = null;
            this.olvColumnMinPccpchC2I.Text = "最小PCCPCH_C/I";
            // 
            // olvColumnAvgPccpchC2I
            // 
            this.olvColumnAvgPccpchC2I.HeaderFont = null;
            this.olvColumnAvgPccpchC2I.Text = "平均PCCPCH_C/I";
            // 
            // olvColumnMaxDpchC2I
            // 
            this.olvColumnMaxDpchC2I.HeaderFont = null;
            this.olvColumnMaxDpchC2I.Text = "最大DPCH_C/I";
            // 
            // olvColumnMinDpchC2I
            // 
            this.olvColumnMinDpchC2I.HeaderFont = null;
            this.olvColumnMinDpchC2I.Text = "最小DPCH_C/I";
            // 
            // olvColumnAvgDpchC2I
            // 
            this.olvColumnAvgDpchC2I.HeaderFont = null;
            this.olvColumnAvgDpchC2I.Text = "平均DPCH_C/I";
            // 
            // TDPoorBlerCellListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1347, 502);
            this.Controls.Add(this.ListViewCell);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "TDPoorBlerCellListForm";
            this.Text = "高BLER小区";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCell)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewCell;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnLACCI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgPccpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxPccpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnMinPccpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxPccpchC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMinPccpchC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgPccpchC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxBler;
        private BrightIdeasSoftware.OLVColumn olvColumnMinBler;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgBler;
        private BrightIdeasSoftware.OLVColumn olvColumnLogName;
        private BrightIdeasSoftware.OLVColumn olvColumnBLER;
        private BrightIdeasSoftware.OLVColumn olvColumnPCCPCH_RSCP;
        private BrightIdeasSoftware.OLVColumn olvColumnPCCPCH_C2I;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnDPCH_C2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxDpchC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMinDpchC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgDpchC2I;

    }
}