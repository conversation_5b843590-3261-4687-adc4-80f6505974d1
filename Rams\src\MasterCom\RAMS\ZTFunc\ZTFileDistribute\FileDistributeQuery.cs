﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;

using MapWinGIS;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    /*
     * 地市文件分发 for 山西
     * 
     * 1，对用户选定的文件进行回放，判断文件在哪些地市区域出现过
     * 2，对文件的保存路径进行以下替换，以得到文件在出现过的地市的ftp上传路径:
     * 2.1, 使用配置文件config/filedistribute.xml中的ftp上传路径前缀替换配置中的文件保存路径前缀，得到文件省库ftp路径
     * 2.2, 使用地市名称替换配置中的ReplaceTarget项（文件保存路径中出现过的省份名称），得到文件的地市ftp路径
     * 3，向服务端发送FileDistribute请求，由服务端将文件复制到地市的ftp上传目录（服务端不关心省库的ftp路径）
     * 
     * 注意项：
     * 1，客户端需要打开FileDistribute开关
     * 2，首次使用需要先打开分发设置界面进行配置
     * 3，省库文件入库路径与地市文件入库路径除了省份名称跟地市名称有差别外，其他部分必须严格一致
     */
    public class FileDistributeQuery : DIYReplayFileQuery
    {
        public FileDistributeQuery(MainModel mainModel) : base(mainModel)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
        }

        public override string Name
        {
            get { return "地市文件分发"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18029, this.Name);
        }

        protected override bool isValidCondition()
        {
            if (MainModel.User.DBID != -1)
            {
                MessageBox.Show("该功能需要管理员权限", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }

            config = new DistributeFileConfig();
            config.Load();
            if (!config.IsValid)
            {
                FileDistributeConfigForm configForm = new FileDistributeConfigForm();
                if (configForm.ShowDialog() != DialogResult.OK)
                {
                    return false;
                }
                config = new DistributeFileConfig();
                config.Load();
            }
            return true;
        }

        protected override void query()
        {
            DoSomethingBeforeQuery();
            if (cityList == null || cityList.Count == 0)
            {
                return;
            }

            base.query();
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();

            List<ColumnDefItem> columns = null;
            columns = InterfaceManager.GetInstance().GetColumnDefByShowName("ilongitude");
            if (columns != null && columns.Count > 0)
            {
                option.SampleColumns.AddRange(columns);
            }
            columns = InterfaceManager.GetInstance().GetColumnDefByShowName("ilatitude");
            if (columns != null && columns.Count > 0)
            {
                option.SampleColumns.AddRange(columns);
            }

            return option;
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            StartFileReplay(fileInfo);
            base.queryReplayInfo(clientProxy, package, fileInfo);
        }

        protected override void doWithDTData(TestPoint tp)
        {
            int fileId = tp.FileID;
            foreach (DistributeFileCity city in cityList)
            {
                if (city.Mop2.CheckPointInRegion(tp.Longitude, tp.Latitude))
                {
                    city.AddFile(fileId);
                }
            }
        }

        protected override void doPostReplayAction()
        {
            FileDistributeConfirmForm confirmForm = new FileDistributeConfirmForm(this.cityList, Condition.FileInfos);
            if (confirmForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitTextBox.Show("正在进行文件分发...", DoDistributeFiles);
        }

        protected override void fireShowResult()
        {
            if (resultList == null) // 分发确认窗口点击取消后，不会产生分发结果
            {
                return;
            }

            FileDistributeResultForm resultForm = MainModel.GetObjectFromBlackboard(typeof(FileDistributeResultForm).FullName) as FileDistributeResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new FileDistributeResultForm(MainModel);
            }
            resultForm.FillData(resultList);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }

            resultList = null; // 重置
        }

        protected void DoSomethingBeforeQuery()
        {
            WaitBox.Show("正在处理图层信息...", CreateCityList);
        }

        protected void StartFileReplay(FileInfo fileInfo)
        {
            //
        }

        protected string GetValidCityName(string oldName)
        {
            string newName = oldName.TrimEnd('市');
            string[] districtNames;
#if DEBUG
            districtNames = new string[] { "宝安", "龙岗", "南山", "盐田", "罗湖", "福田" };
#else
            districtNames = DistrictManager.GetInstance().DistrictNames;
#endif
            foreach (string name in districtNames)
            {
                if (newName == newName.TrimEnd('市'))
                {
                    return newName;
                }
            }
            return null;
        }

        protected void DoDistributeFiles()
        {
            resultList = new List<DistributeFileItem>();
            try
            {
                foreach (DistributeFileCity city in cityList)
                {
                    List<FileInfo> fileList = city.FileList;
                    foreach (FileInfo file in fileList)
                    {
                        WaitTextBox.Text = string.Format("分发[{0}]至[{1}]", file.Name, city.CityName);

                        DistributeFileItem fileItem = new DistributeFileItem();
                        fileItem.FileInfo = file;
                        fileItem.TargetCityName = city.CityName;

                        string errorMsg = null;
                        string srcFtpPath = config.BuildSourcePath(file.Path, out errorMsg);
                        if (srcFtpPath == null)
                        {
                            fileItem.ResultDesc = string.Format("分发失败: {0}", errorMsg);
                            resultList.Add(fileItem);
                            continue;
                        }
                        fileItem.SourceFtpPath = srcFtpPath;

                        string tarFtpPath = config.BuildTargetPath(srcFtpPath, city.CityName, out errorMsg);
                        if (tarFtpPath == null)
                        {
                            fileItem.ResultDesc = string.Format("分发失败: {0}", errorMsg);
                            resultList.Add(fileItem);
                            continue;
                        }
                        fileItem.TargetFtpPath = tarFtpPath;

                        FileDistributeCommand cmd = new FileDistributeCommand(MainModel);
                        cmd.SrcFileInfo = fileItem.FileInfo;
                        cmd.TarSavepath = fileItem.TargetFtpPath;
                        cmd.Query();
                        if (!cmd.IsSucceed)
                        {
                            fileItem.ResultDesc = "分发失败: 可能是源文件路径或者目标地市入库路径有误";
                        }
                        else
                        {
                            fileItem.ResultDesc = "分发成功";
                        }
                        resultList.Add(fileItem);
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        #region 从预存区域或者图层文件中获取地市区域
        protected void CreateCityList()
        {
            cityList = new List<DistributeFileCity>();
            string errorMsg = null;
            try
            {
                if (MainModel.SearchGeometrys.SelectedResvRegions != null && MainModel.SearchGeometrys.SelectedResvRegions.Count != 0)
                {
                    errorMsg = CreateCityListByResvRegion(ref cityList);
                }
                else
                {
                    errorMsg = CreateCityListByShpfile(ref cityList);
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitBox.Close();

                if (errorMsg != null)
                {
                    MessageBox.Show(errorMsg, "错误", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        protected string CreateCityListByResvRegion(ref List<DistributeFileCity> cityList)
        {
            int iLoop = 0;
            string errorMsg = null;
            foreach (ResvRegion reg in MainModel.SearchGeometrys.SelectedResvRegions)
            {
                WaitBox.ProgressPercent = (++iLoop) * 100 / MainModel.SearchGeometrys.SelectedResvRegions.Count;
                string validCityName = GetValidCityName(reg.RegionName);
                if (validCityName == null)
                {
                    continue;
                }

                DistributeFileCity city = new DistributeFileCity(validCityName, reg);
                cityList.Add(city);
            }
            if (cityList.Count == 0)
            {
                errorMsg = "从预存区域未找到地市名称";
            }
            return errorMsg;
        }

        protected string CreateCityListByShpfile(ref List<DistributeFileCity> cityList)
        {
            string errorMsg = null;
            if (config == null || string.IsNullOrEmpty(config.ShpFileName) || string.IsNullOrEmpty(config.ShpFieldName))
            {
                errorMsg = "图层文件路径或者图层地市字段未配置";
                return errorMsg;
            }

            List<string> fields = ShapeHelper.GetFieldNamesFromFile(config.ShpFileName);
            int fieldIndex = -1;
            if (fields == null || fields.Count == 0 || (fieldIndex = fields.IndexOf(config.ShpFieldName)) == -1)
            {
                errorMsg = "从图层文件中未找到对应的配置字段";
                return errorMsg;
            }

            Shapefile shp = new Shapefile();
            shp.Open(config.ShpFileName, null);
            for (int i = 0; i < shp.NumShapes; ++i)
            {
                WaitBox.ProgressPercent = (i + 1) * 100 / shp.NumShapes;

                MapWinGIS.Shape shape = shp.get_Shape(i);
                object value = shp.get_CellValue(fieldIndex, i);
                string cityName = null;
                if (shape == null || value == null
                    || (cityName = GetValidCityName(value.ToString())) == null)
                {
                    continue;
                }
                DistributeFileCity city = new DistributeFileCity(cityName, shape);
                cityList.Add(city);
            }
            shp.Close();

            if (cityList.Count == 0)
            {
                errorMsg = "从图层文件中未找到地市名称";
            }
            return errorMsg;
        }
        #endregion

        protected DistributeFileConfig config;
        protected List<DistributeFileCity> cityList;
        protected List<DistributeFileItem> resultList;
    }

    public class DistributeFileItem
    {
        public FileInfo FileInfo
        {
            get;
            set;
        }

        public string TargetFtpPath
        {
            get;
            set;
        }

        public string SourceFtpPath
        {
            get;
            set;
        }

        public string TargetCityName
        {
            get;
            set;
        }

        public string ResultDesc
        {
            get;
            set;
        }
    }

    public class DistributeFileConfig
    {
        public bool IsValid
        {
            get
            {
                return !string.IsNullOrEmpty(ReplaceTarget)
                    && !string.IsNullOrEmpty(SavePathPrefix)
                    && !string.IsNullOrEmpty(FtpPathPrefix)
                    && !string.IsNullOrEmpty(ShpFileName)
                    && !string.IsNullOrEmpty(ShpFileName);
            }
        }

        public string ReplaceTarget
        {
            get;
            set;
        }

        public string SavePathPrefix
        {
            get;
            set;
        }

        public string FtpPathPrefix
        {
            get;
            set;
        }

        public string ShpFileName
        {
            get;
            set;
        }

        public string ShpFieldName
        {
            get;
            set;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("ReplaceTarget", ReplaceTarget);
                dic.Add("SavePathPrefix", SavePathPrefix);
                dic.Add("FtpPathPrefix", FtpPathPrefix);
                dic.Add("ShpFileName", ShpFileName);
                dic.Add("ShpFieldName", ShpFieldName);
                return dic;
            }
            set
            {
                if (value.ContainsKey("ReplaceTarget"))
                {
                    ReplaceTarget = value["ReplaceTarget"].ToString();
                }
                if (value.ContainsKey("SavePathPrefix"))
                {
                    SavePathPrefix = value["SavePathPrefix"].ToString();
                }
                if (value.ContainsKey("FtpPathPrefix"))
                {
                    FtpPathPrefix = value["FtpPathPrefix"].ToString();
                }
                if (value.ContainsKey("ShpFileName"))
                {
                    ShpFileName = value["ShpFileName"].ToString();
                }
                if (value.ContainsKey("ShpFieldName"))
                {
                    ShpFieldName = value["ShpFieldName"].ToString();
                }
            }
        }

        public DistributeFileConfig()
        {
        }

        public string BuildSourcePath(string srcSavePath, out string errorMsg)
        {
            errorMsg = null;
            if (srcSavePath.IndexOf(SavePathPrefix) == -1)
            {
                errorMsg = "源文件保存路径前缀匹配失败";
                return null;
            }
            string srcTail = srcSavePath.Substring(SavePathPrefix.Length);
            return System.IO.Path.Combine(FtpPathPrefix, srcTail.TrimStart('\\'));
        }

        public string BuildTargetPath(string srcFtpPath, string tarCityName, out string errorMsg)
        {
            errorMsg = null;
            if (srcFtpPath.IndexOf(ReplaceTarget) == -1)
            {
                errorMsg = string.Format("源文件入库路径未找到替换项[{0}]", ReplaceTarget);
                return null;
            }
            return srcFtpPath.Replace(ReplaceTarget, tarCityName);
        }

        public void Save()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement cfg = configFile.AddConfig("Config");
            configFile.AddItem(cfg, "FileDistribute", Param);
            configFile.Save(xmlFilePath);
        }

        public void Load()
        {
            if (!System.IO.File.Exists(xmlFilePath))
            {
                return;
            }

            XmlConfigFile configFile = new XmlConfigFile(xmlFilePath);
            object value = configFile.GetItemValue("Config", "FileDistribute");
            if (value != null)
            {
                Param = value as Dictionary<string, object>;
            }
        }

        public static string xmlFilePath { get; set; } = Application.StartupPath + "/config/filedistribute.xml";
    }

    public class DistributeFileCity
    {
        public DistributeFileCity(string cityName, MapWinGIS.Shape shape) : this(cityName)
        {
            RegionShape = shape;
            Mop2 = new MTPolygon();
            Mop2.Append(RegionShape);
        }

        public DistributeFileCity(string cityName, ResvRegion reg) : this(cityName)
        {
            RegionShape = reg.Shape;
            Mop2 = reg.GeoOp;
        }

        public DistributeFileCity(DistributeFileCity city) : this(city.CityName)
        {
            RegionShape = city.RegionShape;
            Mop2 = city.Mop2;
        }

        private DistributeFileCity(string cityName)
        {
            CityName = cityName;

            headerManager = DTDataHeaderManager.GetInstance();
            FileSampleCountDic = new Dictionary<int, int>();
        }

        public string CityName
        {
            get;
            set;
        }

        public MapWinGIS.Shape RegionShape
        {
            get;
            set;
        }

        public MTPolygon Mop2
        {
            get;
            set;
        }

        public List<FileInfo> FileList
        {
            get { return new List<FileInfo>(fileInfoDic.Values); }
        }

        public Dictionary<int, int> FileSampleCountDic
        {
            get;
            private set;
        }

        public void AddFile(int fileId)
        {
            if (!FileSampleCountDic.ContainsKey(fileId))
            {
                FileSampleCountDic.Add(fileId, 0);
            }
            ++FileSampleCountDic[fileId];

            if (fileInfoDic.ContainsKey(fileId))
            {
                return;
            }
            DTDataHeader fileInfo = headerManager.GetHeaderByFileID(fileId);
            fileInfoDic.Add(fileId, fileInfo);
        }

        private readonly DTDataHeaderManager headerManager;
        private readonly Dictionary<int, FileInfo> fileInfoDic = new Dictionary<int, FileInfo>();
    }
}
