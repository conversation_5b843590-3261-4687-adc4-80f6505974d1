﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTQueryScanOverlap_TD:DIYAnalyseByPeriodBackgroundBase_Sample
    {
        private static ZTQueryScanOverlap_TD intance = null;
        protected static readonly object lockObj = new object();
        public static ZTQueryScanOverlap_TD GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTQueryScanOverlap_TD();
                    }
                }
            }
            return intance;
        }

        protected ZTQueryScanOverlap_TD()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "最强小区过覆盖分析"; }
        }

        public override string IconName
        {
            get { return "images/覆盖分析/过覆盖.png"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16024, this.Name);
        }

        public override string Description
        {
            get
            {
                return "场强最强的小区-离采样点的最近小区的距离大于设定值 且 最强小区与最近小区场强差大于设定值 视为该点存在过覆盖";
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override bool getConditionBeforeQuery()
        {
            ScanOverlapSettingDlg dlg = new ScanOverlapSettingDlg(rxLevDiffMin, distanceDiffMin);
            dlg.ShowDialog();
            return dlg.DialogResult == System.Windows.Forms.DialogResult.OK;
        }

        protected override void FireShowFormAfterQuery()
        {
            if (overlapPointList.Count == 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的采样点！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ScanOverlapCellForm).FullName);
            ScanOverlapCellForm resultFrm = obj == null ? null : obj as ScanOverlapCellForm;
            if (resultFrm == null || resultFrm.IsDisposed)
            {
                resultFrm = new ScanOverlapCellForm(MainModel);
            }
            resultFrm.FillData(overlapPointList);
            if (!resultFrm.Visible)
            {
                resultFrm.Show(MainModel.MainForm);
            }
            resultFrm.BringToFront();
            MainModel.FireDTDataChanged(this);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_Channel";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)" TD扫频");
            tmpDic.Add("themeName", (object)"TDSCAN_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected int rxLevDiffMin = 10;
        protected int distanceDiffMin = 300;
        protected DTDisplayParameterInfo rxLevParam = DTDisplayParameterManager.GetInstance()["TDSCDMA_SCAN", "PCCPCH_RSCP"];
        protected override void doWithDTData(TestPoint tp)
        {
            TDCellInfo info = new TDCellInfo();
            for (int index = 0; index < 50; index++)//扫频数据，入库时已按rxlev从大到小排序，无需再排序
            {
                float? rxlev = (float?)tp["TDS_PCCPCH_RSCP", index];
                if (rxlev != null && rxlev <= rxLevParam.ValueMax && rxlev >= rxLevParam.ValueMin)
                {
                    setTDCellInfo(tp, info, index, rxlev);
                }
                else
                {
                    break;
                }
            }

            double disDiff = info.dis2MainCell - info.disMin;
            float rxLevDiff = info.mainRxLev - info.nearestCellRxLev;
            //最强小区rxlev-最近小区rxlev大于等于设定值 且 最强小区距离-最近小区距离大于等于等于设定值
            if (disDiff >= distanceDiffMin && rxLevDiff >= rxLevDiffMin)
            {
                ScanOverlapPoint ovlPnt = new ScanOverlapPoint(tp, info.nearestCell, info.disMin, info.nearestCellRxLev, info.mainCell, info.dis2MainCell, info.mainRxLev);
                overlapPointList.Add(ovlPnt);
            }
        }

        private static void setTDCellInfo(TestPoint tp, TDCellInfo info, int index, float? rxlev)
        {
            TDCell cell = tp.GetCell_TDScan(index);
            if (cell != null)
            {
                double distance = cell.GetDistance(tp.Longitude, tp.Latitude);
                if (info.mainCell == null)//主强小区信息
                {
                    info.mainRxLev = (float)rxlev;
                    info.mainCell = cell;
                    info.dis2MainCell = distance;
                }
                else
                {
                    if (cell.Type != TDNodeBType.Indoor && distance < info.disMin && cell != info.mainCell)
                    {//最近小区为室内小区时，过滤掉；取非室内的最近小区
                        info.disMin = distance;
                        info.nearestCell = cell;
                        info.nearestCellRxLev = (float)rxlev;
                    }
                }
            }
        }

        readonly List<ScanOverlapPoint> overlapPointList = new List<ScanOverlapPoint>();

        class TDCellInfo
        {
            public TDCell mainCell = null;//主强小区
            public float mainRxLev = float.NaN;//主强小区rxlev
            public double dis2MainCell = 0;//采样点与主强小区的距离
            public TDCell nearestCell = null;//最近小区
            public double disMin = double.MaxValue;//最近小区距离
            public float nearestCellRxLev = float.NaN;//最近小区rxlev
        }
    }

    public class ZTQueryScanOverlap_WCDMA : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        private static ZTQueryScanOverlap_WCDMA intance = null;
        protected static readonly object lockObj = new object();
        public static ZTQueryScanOverlap_WCDMA GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTQueryScanOverlap_WCDMA();
                    }
                }
            }
            return intance;
        }

        protected ZTQueryScanOverlap_WCDMA()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "WCDMA最强小区过覆盖分析"; }
        }

        public override string IconName
        {
            get { return "images/覆盖分析/过覆盖.png"; }
        }
        
        public override string Description
        {
            get
            {
                return "WCDMA场强最强的小区-离采样点的最近小区的距离大于设定值 且 最强小区与最近小区场强差大于设定值 视为该点存在过覆盖";
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override bool getConditionBeforeQuery()
        {
            ScanOverlapSettingDlg dlg = new ScanOverlapSettingDlg(rxLevDiffMin, distanceDiffMin);
            dlg.ShowDialog();
            return dlg.DialogResult == System.Windows.Forms.DialogResult.OK;
        }

        protected override void FireShowFormAfterQuery()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ScanOverlapCellForm).FullName);
            ScanOverlapCellForm resultFrm = obj == null ? null : obj as ScanOverlapCellForm;
            if (resultFrm == null || resultFrm.IsDisposed)
            {
                resultFrm = new ScanOverlapCellForm(MainModel);
            }
            resultFrm.FillData(overlapPointList);
            if (!resultFrm.Visible)
            {
                resultFrm.Show(MainModel.MainForm);
            }
            resultFrm.BringToFront();
            MainModel.FireDTDataChanged(this);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHTotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHChannel";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHPilot";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)" WCDMA扫频");
            tmpDic.Add("themeName", (object)"WCDAMSCAN_CPICHTotalRSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected int rxLevDiffMin = 10;
        protected int distanceDiffMin = 300;
        protected DTDisplayParameterInfo rxLevParam = DTDisplayParameterManager.GetInstance()["WCDMA_SCAN", "CPICHTotalRSCP"];
        protected override void doWithDTData(TestPoint tp)
        {
            WCellInfo info = new WCellInfo();
            for (int index = 0; index < 50; index++)//扫频数据，入库时已按rxlev从大到小排序，无需再排序
            {
                float? rxlev = (float?)tp["WS_CPICHTotalRSCP", index];
                if (rxlev != null && rxlev <= rxLevParam.ValueMax && rxlev >= rxLevParam.ValueMin)
                {
                    setWCellInfo(tp, info, index, rxlev);
                }
                else
                {
                    break;
                }
            }

            double disDiff = info.dis2MainCell - info.disMin;
            float rxLevDiff = info.mainRxLev - info.nearestCellRxLev;
            //最强小区rxlev-最近小区rxlev大于等于设定值 且 最强小区距离-最近小区距离大于等于等于设定值
            if (disDiff >= distanceDiffMin && rxLevDiff >= rxLevDiffMin)
            {
                ScanOverlapPoint ovlPnt = new ScanOverlapPoint(tp, info.nearestCell, info.disMin, info.nearestCellRxLev, info.mainCell, info.dis2MainCell, info.mainRxLev);
                overlapPointList.Add(ovlPnt);
            }
        }

        private void setWCellInfo(TestPoint tp, WCellInfo info, int index, float? rxlev)
        {
            WCell cell = tp.GetCell_WScan(index);
            if (cell != null)
            {
                double distance = cell.GetDistance(tp.Longitude, tp.Latitude);
                if (info.mainCell == null)//主强小区信息
                {
                    info.mainRxLev = (float)rxlev;
                    info.mainCell = cell;
                    info.dis2MainCell = distance;
                }
                else
                {
                    if (cell.Type != WNodeBType.Indoor
                        && distance < info.disMin && cell != info.mainCell)//最近小区为室内小区时，过滤掉；取非室内的最近小区
                    {
                        info.disMin = distance;
                        info.nearestCell = cell;
                        info.nearestCellRxLev = (float)rxlev;
                    }
                }
            }
        }

        readonly List<ScanOverlapPoint> overlapPointList = new List<ScanOverlapPoint>();

        class WCellInfo
        {
            public WCell mainCell = null;//主强小区
            public float mainRxLev = float.NaN;//主强小区rxlev
            public double dis2MainCell = 0;//采样点与主强小区的距离     
            public WCell nearestCell = null;//最近小区
            public double disMin = double.MaxValue;//最近小区距离
            public float nearestCellRxLev = float.NaN;//最近小区rxlev
        }
    }
}
