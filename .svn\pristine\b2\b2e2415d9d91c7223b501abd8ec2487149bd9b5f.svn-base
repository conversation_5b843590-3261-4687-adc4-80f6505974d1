﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class MoBlockCallQuery : DIYAnalyseByFileBackgroundBase
    {
        List<MoCallInfo> resultList = null;
        MoBlockCallCondition hoCondition = null;

        protected static readonly object lockObj = new object();
        private static MoBlockCallQuery instance = null;
        public static MoBlockCallQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new MoBlockCallQuery();
                    }
                }
            }
            return instance;
        }

        protected MoBlockCallQuery()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
        }

        public override string Name
        {
            get
            {
                return "VOLTE主叫未接通(按区域)";
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 27000, 27012, this.Name);
        }

        MoBlockCallSettingForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new MoBlockCallSettingForm();
            }
            if (setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                resultList = new List<MoCallInfo>();
                setForm.GetCondition(out hoCondition);
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            if (resultList == null || resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的数据");
                return;
            }
            MoBlockCallResultListForm frm = MainModel.GetInstance().CreateResultForm(typeof(MoBlockCallResultListForm)) as MoBlockCallResultListForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
            resultList = null;
        }

        protected override void analyseFiles()
        {
            List<FileInfo> fileList = new List<FileInfo>();
            foreach (FileInfo file in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)file.ServiceType))
                {
                    continue;
                }
                if (file.Momt == (int)MoMtFile.MoFlag)
                {
                    fileList.Add(file);
                }
            }

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (FileInfo info in fileList)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + fileList.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / fileList.Count);

                    condition.FileInfos.Clear();
                    if (info != null)
                    {
                        condition.FileInfos.Add(info);
                    }

                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        MoCallInfo call = null;
        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    moFile = file;
                }
            }

            if (moFile != null)
            {
                dealDTDatas(moFile);
            }
        }

        private void dealDTDatas(DTFileDataManager moFile)
        {
            foreach (DTData data in moFile.DTDatas)
            {
                if (data is TestPoint)
                {
                    if (call != null)
                    {
                        call.AddTP(data as TestPoint);
                    }
                }
                else if (data is Event)
                {
                    if (call != null)
                    {
                        call.AddEvent(data as Event);
                    }
                }
                else if (data is Message)
                {
                    dealMsgInfo(moFile, data);
                }
            }
        }

        private void dealMsgInfo(DTFileDataManager moFile, DTData data)
        {
            Message msg = data as Message;
            if (call != null)
            {
                if ((msg.DateTime - call.BeginTime).TotalSeconds > hoCondition.AfterTime)
                {
                    call.SN = resultList.Count + 1;
                    resultList.Add(call);
                    call = null;
                    return;
                }
                else if (msg.ID == 1097532106 || msg.ID == 1108344832)
                {
                    call = null;
                    return;
                }
                if (msg.ID == (int)MessageManager.Msg_IMS_SIP_INVITE_Session_Progress)
                {
                    call.IsContainISI2SP = "是";
                }
                else if (call.IsContainISI2SP != "是")
                {
                    call.IsContainISI2SP = "否";
                }
                call.AddMsg(msg);
            }
            if (msg.ID == 1107705956)
            {
                if (call != null && (msg.DateTime - call.BeginTime).TotalSeconds <= hoCondition.AfterTime)
                {
                    call.AddMsg(msg);
                }
                else
                {
                    call = new MoCallInfo();
                    call.AddMsg(msg);
                    call.BeginTime = msg.DateTime;
                    call.FileName = moFile.FileName;
                    call.File = moFile.GetFileInfo();
                }
            }
        }
    }

    public class MoBlockCallQuery_FDD : MoBlockCallQuery
    {
        private static MoBlockCallQuery_FDD instance = null;
        public static new MoBlockCallQuery_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    instance = new MoBlockCallQuery_FDD();
                }
            }
            return instance;
        }
        protected MoBlockCallQuery_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get
            {
                return "VOLTE_FDD主叫未接通(按区域)";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 30000, 30035, this.Name);
        }
    }

    public class MoCallInfo
    {
        public int SN { get; set; }
        public string FileName { get; set; }
        public DateTime BeginTime { get; set; }
        public string IsContainISI2SP { get; set; }
        public FileInfo File { get; set; }

        private readonly List<Event> evtList = new List<Event>();
        public void AddEvent(Event evt)
        {
            evtList.Add(evt);
        }
        public List<Event> Events
        {
            get
            {
                return evtList;
            }
        }

        private readonly List<TestPoint> tpList = new List<TestPoint>();
        public void AddTP(TestPoint tp)
        {
            tpList.Add(tp);
        }
        public List<TestPoint> TestPoints
        {
            get
            {
                return tpList;
            }
        }

        private readonly List<Message> msgList = new List<Message>();
        public void AddMsg(Message msg)
        {
            msgList.Add(msg);
        }
        public List<Message> Messages
        {
            get
            {
                return msgList;
            }
        }
    }

    public class MoBlockCallCondition
    {
        public int AfterTime { get; set; }
        public MoBlockCallCondition()
        {
            AfterTime = 1;
        }
    }
}
