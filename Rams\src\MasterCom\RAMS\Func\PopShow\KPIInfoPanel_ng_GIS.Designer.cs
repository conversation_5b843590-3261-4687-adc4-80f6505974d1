﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class KPIInfoPanel_ng_GIS
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram3 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel7 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.PointOptions pointOptions3 = new DevExpress.XtraCharts.PointOptions();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel8 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel9 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.rdbKPICompetition = new System.Windows.Forms.RadioButton();
            this.rdbKPISort = new System.Windows.Forms.RadioButton();
            this.rdbKPIList = new System.Windows.Forms.RadioButton();
            this.exp2Xls = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.cxtMsKPI_ng = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExp2Word = new System.Windows.Forms.ToolStripMenuItem();
            this.cbxReportSel = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxShowType = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.panel2 = new System.Windows.Forms.Panel();
            this.cbxContentType = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.checkedCbxMonth = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.labelMonth = new System.Windows.Forms.Label();
            this.btnLevel0 = new System.Windows.Forms.Button();
            this.btnColor = new System.Windows.Forms.Button();
            this.splitMain = new System.Windows.Forms.SplitContainer();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.pnlKPIContent = new System.Windows.Forms.Panel();
            this.btnSortType = new System.Windows.Forms.Button();
            this.lblKPIName = new System.Windows.Forms.Label();
            this.tabSortAreaType = new System.Windows.Forms.TabControl();
            this.pagSortByCity = new System.Windows.Forms.TabPage();
            this.pagSortByArea = new System.Windows.Forms.TabPage();
            this.tabShow = new System.Windows.Forms.TabControl();
            this.tabPageMap = new System.Windows.Forms.TabPage();
            this.btnLegend = new System.Windows.Forms.Button();
            this.cbxLegend = new System.Windows.Forms.ComboBox();
            this.lstLegend = new System.Windows.Forms.ListBox();
            this.tabPageChart = new System.Windows.Forms.TabPage();
            this.chartControl = new DevExpress.XtraCharts.ChartControl();
            this.toolTip = new System.Windows.Forms.ToolTip(this.components);
            this.mnuSortLegend = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.btnSelectKPI = new System.Windows.Forms.Button();
            this.panel1.SuspendLayout();
            this.cxtMsKPI_ng.SuspendLayout();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxContentType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkedCbxMonth.Properties)).BeginInit();
            this.splitMain.Panel1.SuspendLayout();
            this.splitMain.Panel2.SuspendLayout();
            this.splitMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.pnlKPIContent.SuspendLayout();
            this.tabSortAreaType.SuspendLayout();
            this.tabShow.SuspendLayout();
            this.tabPageMap.SuspendLayout();
            this.tabPageChart.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).BeginInit();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.BackColor = System.Drawing.Color.Silver;
            this.panel1.Controls.Add(this.rdbKPICompetition);
            this.panel1.Controls.Add(this.rdbKPISort);
            this.panel1.Controls.Add(this.rdbKPIList);
            this.panel1.Controls.Add(this.exp2Xls);
            this.panel1.Controls.Add(this.button1);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(990, 30);
            this.panel1.TabIndex = 1;
            // 
            // rdbKPICompetition
            // 
            this.rdbKPICompetition.AutoSize = true;
            this.rdbKPICompetition.Location = new System.Drawing.Point(319, 7);
            this.rdbKPICompetition.Name = "rdbKPICompetition";
            this.rdbKPICompetition.Size = new System.Drawing.Size(71, 16);
            this.rdbKPICompetition.TabIndex = 5;
            this.rdbKPICompetition.Text = "三网对比";
            this.rdbKPICompetition.UseVisualStyleBackColor = true;
            this.rdbKPICompetition.CheckedChanged += new System.EventHandler(this.rdbKPICompetition_CheckedChanged);
            // 
            // rdbKPISort
            // 
            this.rdbKPISort.AutoSize = true;
            this.rdbKPISort.Location = new System.Drawing.Point(218, 7);
            this.rdbKPISort.Name = "rdbKPISort";
            this.rdbKPISort.Size = new System.Drawing.Size(71, 16);
            this.rdbKPISort.TabIndex = 4;
            this.rdbKPISort.Text = "指标排名";
            this.rdbKPISort.UseVisualStyleBackColor = true;
            this.rdbKPISort.CheckedChanged += new System.EventHandler(this.rdbKPISort_CheckedChanged);
            // 
            // rdbKPIList
            // 
            this.rdbKPIList.AutoSize = true;
            this.rdbKPIList.Checked = true;
            this.rdbKPIList.Location = new System.Drawing.Point(123, 7);
            this.rdbKPIList.Name = "rdbKPIList";
            this.rdbKPIList.Size = new System.Drawing.Size(71, 16);
            this.rdbKPIList.TabIndex = 3;
            this.rdbKPIList.TabStop = true;
            this.rdbKPIList.Text = "指标列表";
            this.rdbKPIList.UseVisualStyleBackColor = true;
            this.rdbKPIList.CheckedChanged += new System.EventHandler(this.rdbKPIList_CheckedChanged);
            // 
            // exp2Xls
            // 
            this.exp2Xls.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.exp2Xls.Location = new System.Drawing.Point(831, 3);
            this.exp2Xls.Name = "exp2Xls";
            this.exp2Xls.Size = new System.Drawing.Size(75, 23);
            this.exp2Xls.TabIndex = 2;
            this.exp2Xls.Text = "导出本页";
            this.exp2Xls.UseVisualStyleBackColor = true;
            this.exp2Xls.Click += new System.EventHandler(this.exp2Xls_Click);
            // 
            // button1
            // 
            this.button1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.button1.Location = new System.Drawing.Point(912, 3);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 1;
            this.button1.Text = "导出所有页面";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(19, 9);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(61, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "KPI指标";
            // 
            // cxtMsKPI_ng
            // 
            this.cxtMsKPI_ng.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExp2Word});
            this.cxtMsKPI_ng.Name = "cxtMsKPI_ng";
            this.cxtMsKPI_ng.Size = new System.Drawing.Size(179, 26);
            // 
            // miExp2Word
            // 
            this.miExp2Word.Name = "miExp2Word";
            this.miExp2Word.Size = new System.Drawing.Size(178, 22);
            this.miExp2Word.Text = "导出数据到Excel...";
            this.miExp2Word.Click += new System.EventHandler(this.miExp2Word_Click);
            // 
            // cbxReportSel
            // 
            this.cbxReportSel.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxReportSel.FormattingEnabled = true;
            this.cbxReportSel.Location = new System.Drawing.Point(3, 4);
            this.cbxReportSel.Name = "cbxReportSel";
            this.cbxReportSel.Size = new System.Drawing.Size(226, 20);
            this.cbxReportSel.TabIndex = 4;
            this.cbxReportSel.SelectedIndexChanged += new System.EventHandler(this.cbxReportSel_SelectedIndexChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(242, 9);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 5;
            this.label2.Text = "方式：";
            // 
            // cbxShowType
            // 
            this.cbxShowType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxShowType.FormattingEnabled = true;
            this.cbxShowType.Location = new System.Drawing.Point(279, 4);
            this.cbxShowType.Name = "cbxShowType";
            this.cbxShowType.Size = new System.Drawing.Size(110, 20);
            this.cbxShowType.TabIndex = 4;
            this.cbxShowType.SelectedIndexChanged += new System.EventHandler(this.cbxShowType_SelectedIndexChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(394, 9);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(41, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "内容：";
            // 
            // panel2
            // 
            this.panel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel2.Controls.Add(this.cbxContentType);
            this.panel2.Controls.Add(this.checkedCbxMonth);
            this.panel2.Controls.Add(this.labelMonth);
            this.panel2.Controls.Add(this.btnLevel0);
            this.panel2.Controls.Add(this.btnColor);
            this.panel2.Controls.Add(this.cbxReportSel);
            this.panel2.Controls.Add(this.cbxShowType);
            this.panel2.Controls.Add(this.label2);
            this.panel2.Controls.Add(this.label3);
            this.panel2.Location = new System.Drawing.Point(11, 36);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(971, 27);
            this.panel2.TabIndex = 6;
            // 
            // cbxContentType
            // 
            this.cbxContentType.Location = new System.Drawing.Point(427, 3);
            this.cbxContentType.Name = "cbxContentType";
            this.cbxContentType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxContentType.Properties.Closed += new DevExpress.XtraEditors.Controls.ClosedEventHandler(this.checkedComboBoxEdit1_Properties_Closed);
            this.cbxContentType.Size = new System.Drawing.Size(132, 21);
            this.cbxContentType.TabIndex = 10;
            this.cbxContentType.EditValueChanged += new System.EventHandler(this.cbxContentType_EditValueChanged);
            this.cbxContentType.MouseDown += new System.Windows.Forms.MouseEventHandler(this.cbxContentType_MouseDown);
            // 
            // checkedCbxMonth
            // 
            this.checkedCbxMonth.Location = new System.Drawing.Point(604, 2);
            this.checkedCbxMonth.Name = "checkedCbxMonth";
            this.checkedCbxMonth.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.checkedCbxMonth.Properties.Closed += new DevExpress.XtraEditors.Controls.ClosedEventHandler(this.checkedComboBoxEdit1_Properties_Closed);
            this.checkedCbxMonth.Size = new System.Drawing.Size(211, 21);
            this.checkedCbxMonth.TabIndex = 9;
            this.checkedCbxMonth.Visible = false;
            this.checkedCbxMonth.EditValueChanged += new System.EventHandler(this.checkedCbxMonth_EditValueChanged);
            this.checkedCbxMonth.MouseDown += new System.Windows.Forms.MouseEventHandler(this.checkedCbxMonth_MouseDown);
            // 
            // labelMonth
            // 
            this.labelMonth.AutoSize = true;
            this.labelMonth.Location = new System.Drawing.Point(565, 7);
            this.labelMonth.Name = "labelMonth";
            this.labelMonth.Size = new System.Drawing.Size(41, 12);
            this.labelMonth.TabIndex = 8;
            this.labelMonth.Text = "月份：";
            this.labelMonth.Visible = false;
            // 
            // btnLevel0
            // 
            this.btnLevel0.Anchor = System.Windows.Forms.AnchorStyles.Right;
            this.btnLevel0.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnLevel0.Location = new System.Drawing.Point(910, 4);
            this.btnLevel0.Name = "btnLevel0";
            this.btnLevel0.Size = new System.Drawing.Size(57, 19);
            this.btnLevel0.TabIndex = 0;
            this.btnLevel0.Text = "全省<<";
            this.btnLevel0.UseVisualStyleBackColor = true;
            this.btnLevel0.Click += new System.EventHandler(this.btnLevel0_Click);
            // 
            // btnColor
            // 
            this.btnColor.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnColor.Location = new System.Drawing.Point(823, 2);
            this.btnColor.Name = "btnColor";
            this.btnColor.Size = new System.Drawing.Size(83, 23);
            this.btnColor.TabIndex = 7;
            this.btnColor.Text = "颜色设置";
            this.btnColor.UseVisualStyleBackColor = true;
            this.btnColor.Visible = false;
            this.btnColor.Click += new System.EventHandler(this.btnColor_Click);
            // 
            // splitMain
            // 
            this.splitMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.splitMain.Location = new System.Drawing.Point(11, 69);
            this.splitMain.Name = "splitMain";
            // 
            // splitMain.Panel1
            // 
            this.splitMain.Panel1.Controls.Add(this.dataGridView);
            this.splitMain.Panel1.Controls.Add(this.pnlKPIContent);
            // 
            // splitMain.Panel2
            // 
            this.splitMain.Panel2.Controls.Add(this.tabShow);
            this.splitMain.Size = new System.Drawing.Size(971, 370);
            this.splitMain.SplitterDistance = 438;
            this.splitMain.TabIndex = 7;
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.BackgroundColor = System.Drawing.Color.White;
            this.dataGridView.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.ContextMenuStrip = this.cxtMsKPI_ng;
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.Location = new System.Drawing.Point(0, 56);
            this.dataGridView.MultiSelect = false;
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.Size = new System.Drawing.Size(438, 314);
            this.dataGridView.TabIndex = 5;
            this.dataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellDoubleClick);
            this.dataGridView.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellClick);
            // 
            // pnlKPIContent
            // 
            this.pnlKPIContent.BackColor = System.Drawing.Color.Transparent;
            this.pnlKPIContent.Controls.Add(this.btnSelectKPI);
            this.pnlKPIContent.Controls.Add(this.btnSortType);
            this.pnlKPIContent.Controls.Add(this.lblKPIName);
            this.pnlKPIContent.Controls.Add(this.tabSortAreaType);
            this.pnlKPIContent.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlKPIContent.Location = new System.Drawing.Point(0, 0);
            this.pnlKPIContent.Name = "pnlKPIContent";
            this.pnlKPIContent.Size = new System.Drawing.Size(438, 56);
            this.pnlKPIContent.TabIndex = 4;
            // 
            // btnSortType
            // 
            this.btnSortType.BackColor = System.Drawing.Color.Peru;
            this.btnSortType.FlatAppearance.BorderColor = System.Drawing.Color.White;
            this.btnSortType.FlatAppearance.MouseDownBackColor = System.Drawing.Color.OrangeRed;
            this.btnSortType.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Chocolate;
            this.btnSortType.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnSortType.Location = new System.Drawing.Point(4, 27);
            this.btnSortType.Name = "btnSortType";
            this.btnSortType.Size = new System.Drawing.Size(75, 23);
            this.btnSortType.TabIndex = 5;
            this.btnSortType.Text = "正序/倒序";
            this.btnSortType.UseVisualStyleBackColor = false;
            this.btnSortType.Click += new System.EventHandler(this.btnSortType_Click);
            // 
            // lblKPIName
            // 
            this.lblKPIName.BackColor = System.Drawing.Color.Peru;
            this.lblKPIName.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lblKPIName.Font = new System.Drawing.Font("宋体", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblKPIName.ForeColor = System.Drawing.Color.White;
            this.lblKPIName.Location = new System.Drawing.Point(0, 21);
            this.lblKPIName.Name = "lblKPIName";
            this.lblKPIName.Size = new System.Drawing.Size(438, 35);
            this.lblKPIName.TabIndex = 4;
            this.lblKPIName.Text = "KPI指标";
            this.lblKPIName.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabSortAreaType
            // 
            this.tabSortAreaType.Controls.Add(this.pagSortByCity);
            this.tabSortAreaType.Controls.Add(this.pagSortByArea);
            this.tabSortAreaType.Dock = System.Windows.Forms.DockStyle.Top;
            this.tabSortAreaType.Location = new System.Drawing.Point(0, 0);
            this.tabSortAreaType.Name = "tabSortAreaType";
            this.tabSortAreaType.SelectedIndex = 0;
            this.tabSortAreaType.Size = new System.Drawing.Size(438, 21);
            this.tabSortAreaType.TabIndex = 3;
            this.tabSortAreaType.SelectedIndexChanged += new System.EventHandler(this.tabSortAreaType_SelectedIndexChanged);
            // 
            // pagSortByCity
            // 
            this.pagSortByCity.Location = new System.Drawing.Point(4, 21);
            this.pagSortByCity.Name = "pagSortByCity";
            this.pagSortByCity.Padding = new System.Windows.Forms.Padding(3);
            this.pagSortByCity.Size = new System.Drawing.Size(430, 0);
            this.pagSortByCity.TabIndex = 0;
            this.pagSortByCity.Text = "地市排名";
            this.pagSortByCity.UseVisualStyleBackColor = true;
            // 
            // pagSortByArea
            // 
            this.pagSortByArea.Location = new System.Drawing.Point(4, 21);
            this.pagSortByArea.Name = "pagSortByArea";
            this.pagSortByArea.Padding = new System.Windows.Forms.Padding(3);
            this.pagSortByArea.Size = new System.Drawing.Size(430, 0);
            this.pagSortByArea.TabIndex = 1;
            this.pagSortByArea.Text = "网格排名";
            this.pagSortByArea.UseVisualStyleBackColor = true;
            // 
            // tabShow
            // 
            this.tabShow.Controls.Add(this.tabPageMap);
            this.tabShow.Controls.Add(this.tabPageChart);
            this.tabShow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabShow.Location = new System.Drawing.Point(0, 0);
            this.tabShow.Name = "tabShow";
            this.tabShow.SelectedIndex = 0;
            this.tabShow.Size = new System.Drawing.Size(529, 370);
            this.tabShow.TabIndex = 2;
            this.tabShow.SelectedIndexChanged += new System.EventHandler(this.tabShow_SelectedIndexChanged);
            // 
            // tabPageMap
            // 
            this.tabPageMap.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.tabPageMap.Controls.Add(this.btnLegend);
            this.tabPageMap.Controls.Add(this.cbxLegend);
            this.tabPageMap.Controls.Add(this.lstLegend);
            this.tabPageMap.Location = new System.Drawing.Point(4, 21);
            this.tabPageMap.Name = "tabPageMap";
            this.tabPageMap.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageMap.Size = new System.Drawing.Size(521, 345);
            this.tabPageMap.TabIndex = 0;
            this.tabPageMap.Text = "地图呈现";
            this.tabPageMap.UseVisualStyleBackColor = true;
            // 
            // btnLegend
            // 
            this.btnLegend.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnLegend.Location = new System.Drawing.Point(370, 2);
            this.btnLegend.Name = "btnLegend";
            this.btnLegend.Size = new System.Drawing.Size(20, 20);
            this.btnLegend.TabIndex = 15;
            this.btnLegend.Text = "-";
            this.btnLegend.UseVisualStyleBackColor = true;
            this.btnLegend.Click += new System.EventHandler(this.btnLegend_Click);
            // 
            // cbxLegend
            // 
            this.cbxLegend.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxLegend.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxLegend.FormattingEnabled = true;
            this.cbxLegend.Location = new System.Drawing.Point(390, 2);
            this.cbxLegend.Name = "cbxLegend";
            this.cbxLegend.Size = new System.Drawing.Size(124, 20);
            this.cbxLegend.TabIndex = 17;
            this.cbxLegend.SelectedIndexChanged += new System.EventHandler(this.cbxLegend_SelectedIndexChanged);
            // 
            // lstLegend
            // 
            this.lstLegend.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lstLegend.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.lstLegend.Enabled = false;
            this.lstLegend.FormattingEnabled = true;
            this.lstLegend.ItemHeight = 15;
            this.lstLegend.Location = new System.Drawing.Point(390, 23);
            this.lstLegend.Name = "lstLegend";
            this.lstLegend.Size = new System.Drawing.Size(124, 79);
            this.lstLegend.TabIndex = 16;
            this.lstLegend.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.lstLegend_DrawItem);
            // 
            // tabPageChart
            // 
            this.tabPageChart.Controls.Add(this.chartControl);
            this.tabPageChart.Location = new System.Drawing.Point(4, 21);
            this.tabPageChart.Name = "tabPageChart";
            this.tabPageChart.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageChart.Size = new System.Drawing.Size(521, 345);
            this.tabPageChart.TabIndex = 1;
            this.tabPageChart.Text = "图表";
            this.tabPageChart.UseVisualStyleBackColor = true;
            // 
            // chartControl
            // 
            this.chartControl.AppearanceName = "Northern Lights";
            xyDiagram3.AxisX.AutoScaleBreaks.Enabled = true;
            xyDiagram3.AxisX.AutoScaleBreaks.MaxCount = 2;
            xyDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram3.EnableAxisXScrolling = true;
            xyDiagram3.EnableAxisXZooming = true;
            this.chartControl.Diagram = xyDiagram3;
            this.chartControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl.Location = new System.Drawing.Point(3, 3);
            this.chartControl.Name = "chartControl";
            this.chartControl.PaletteName = "Nature Colors";
            this.chartControl.RuntimeSelection = true;
            sideBySideBarSeriesLabel7.LineVisible = true;
            series5.Label = sideBySideBarSeriesLabel7;
            series5.Name = "Series 1";
            pointOptions3.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Number;
            series5.PointOptions = pointOptions3;
            sideBySideBarSeriesLabel8.LineVisible = true;
            sideBySideBarSeriesLabel8.Visible = false;
            series6.Label = sideBySideBarSeriesLabel8;
            series6.Name = "Series 2";
            this.chartControl.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series5,
        series6};
            sideBySideBarSeriesLabel9.LineVisible = true;
            this.chartControl.SeriesTemplate.Label = sideBySideBarSeriesLabel9;
            this.chartControl.Size = new System.Drawing.Size(515, 339);
            this.chartControl.TabIndex = 3;
            // 
            // mnuSortLegend
            // 
            this.mnuSortLegend.Name = "mnuSortLegend";
            this.mnuSortLegend.Size = new System.Drawing.Size(61, 4);
            // 
            // btnSelectKPI
            // 
            this.btnSelectKPI.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSelectKPI.BackColor = System.Drawing.Color.Peru;
            this.btnSelectKPI.FlatAppearance.BorderColor = System.Drawing.Color.White;
            this.btnSelectKPI.FlatAppearance.MouseDownBackColor = System.Drawing.Color.OrangeRed;
            this.btnSelectKPI.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Chocolate;
            this.btnSelectKPI.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnSelectKPI.Location = new System.Drawing.Point(411, 27);
            this.btnSelectKPI.Name = "btnSelectKPI";
            this.btnSelectKPI.Size = new System.Drawing.Size(24, 23);
            this.btnSelectKPI.TabIndex = 7;
            this.btnSelectKPI.Text = "↓";
            this.btnSelectKPI.UseVisualStyleBackColor = false;
            this.btnSelectKPI.Click += new System.EventHandler(this.btnSelectKPI_Click);
            // 
            // KPIInfoPanel_ng_GIS
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.Transparent;
            this.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.Controls.Add(this.splitMain);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "KPIInfoPanel_ng_GIS";
            this.Size = new System.Drawing.Size(996, 453);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.cxtMsKPI_ng.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxContentType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkedCbxMonth.Properties)).EndInit();
            this.splitMain.Panel1.ResumeLayout(false);
            this.splitMain.Panel2.ResumeLayout(false);
            this.splitMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.pnlKPIContent.ResumeLayout(false);
            this.tabSortAreaType.ResumeLayout(false);
            this.tabShow.ResumeLayout(false);
            this.tabPageMap.ResumeLayout(false);
            this.tabPageChart.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cbxReportSel;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cbxShowType;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.SplitContainer splitMain;
        private System.Windows.Forms.Button btnColor;
        private System.Windows.Forms.Button btnLevel0;
        private System.Windows.Forms.ContextMenuStrip cxtMsKPI_ng;
        private System.Windows.Forms.ToolStripMenuItem miExp2Word;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button exp2Xls;
        private DevExpress.XtraEditors.CheckedComboBoxEdit checkedCbxMonth;
        private System.Windows.Forms.Label labelMonth;
        private DevExpress.XtraEditors.CheckedComboBoxEdit cbxContentType;
        private System.Windows.Forms.TabControl tabShow;
        private System.Windows.Forms.TabPage tabPageMap;
        private System.Windows.Forms.TabPage tabPageChart;
        private DevExpress.XtraCharts.ChartControl chartControl;
        private System.Windows.Forms.ToolTip toolTip;
        private System.Windows.Forms.Button btnLegend;
        private System.Windows.Forms.ComboBox cbxLegend;
        private System.Windows.Forms.ListBox lstLegend;
        private System.Windows.Forms.Panel pnlKPIContent;
        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.RadioButton rdbKPICompetition;
        private System.Windows.Forms.RadioButton rdbKPISort;
        private System.Windows.Forms.RadioButton rdbKPIList;
        private System.Windows.Forms.TabControl tabSortAreaType;
        private System.Windows.Forms.TabPage pagSortByCity;
        private System.Windows.Forms.TabPage pagSortByArea;
        private System.Windows.Forms.Label lblKPIName;
        private System.Windows.Forms.Button btnSortType;
        private System.Windows.Forms.ContextMenuStrip mnuSortLegend;
        private System.Windows.Forms.Button btnSelectKPI;
    }
}
