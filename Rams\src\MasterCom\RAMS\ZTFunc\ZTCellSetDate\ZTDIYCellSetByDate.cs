﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    /// <summary>
    /// 仿照ZTDIYCellSetByFile改写的按时段对比
    /// </summary>
    public class ZTDIYCellSetByDate : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        /// <summary>
        /// 保存最终结果数据
        /// </summary>
        public List<CellSetByDateForm.CellGSMResult> CellSetOfDateResult
        {
            get;
            set;
        }

        //标准时段
        private TimePeriod tpStandard = new TimePeriod();
        //对比时段
        private TimePeriod tpCompare = new TimePeriod();
        //是否是标准时段
        private bool isStandard = true;
        //标准时段测量点总数
        private long standardTPTotalCount = 0;
        //对比时段测量点总数
        private long compareTPTotalCount = 0;
        //小区数（序号）
        private int seqNo = 1;
        //以LAC CI作为key，保存所有小区数据
        private Dictionary<string, CellSetByDateForm.CellGSMResult> cellGSMResult = new Dictionary<string, CellSetByDateForm.CellGSMResult>();   

        #region instance
        private static volatile ZTDIYCellSetByDate instance = null;

        protected ZTDIYCellSetByDate(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public static ZTDIYCellSetByDate GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTDIYCellSetByDate(MainModel.GetInstance());
            }
            return instance;
        }
        #endregion

        /// <summary>
        /// 重写为了按钮不选区域就可以点击
        /// </summary>
        /// <param name="searchGeometrys"></param>
        /// <returns></returns>
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 添加功能点
        /// </summary>
        /// <returns></returns>
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12098, this.Name);
        }

        /// <summary>
        /// 界面显示按钮的注释
        /// </summary>
        public override string Name
        {
            get { return "小区集按时段对比分析"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            return true;
        }

        /// <summary>
        /// 弹出选择时段的窗口
        /// </summary>
        /// <returns>是否确认查询该时段</returns>
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            ZTCellDateSettigDlg dlg = new ZTCellDateSettigDlg();
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            tpStandard = dlg.TPStandard;
            tpCompare = dlg.TPCompare;

            return true;
        }

        #region 重写忽略地区限制
        protected override void AddDIYRegion_Intersect(Package package)
        {
            //
        }

        protected override void AddDIYRegion_Sample(Package package)
        {
            //
        }
        #endregion

        /// <summary>
        /// 暂时这个功能查出的数据好像没有用上
        /// </summary>
        /// <returns></returns>
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "---";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("LAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("CI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("BCCH");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("BSIC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }            
            return cellSetGroup;
        }    

        /// <summary>
        /// 重写父类,修改查询方式,这里需要查询2个时段
        /// </summary>
        /// <param name="o"></param>
        protected override void queryInThread(object o)
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;

                int periodCount = 2;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    WaitBox.Text = "开始统计底层时段[" + tpStandard.GetShortString() + "]内的数据...";
                    WaitBox.ProgressPercent = index / periodCount + 10;
                    queryPeriodInfo(clientProxy, package, tpStandard, false);

                    isStandard = false;
                    WaitBox.Text = "开始统计对比时段[" + tpCompare.GetShortString() + "]内的数据...";
                    WaitBox.ProgressPercent = index / periodCount + 20;
                    queryPeriodInfo(clientProxy, package, tpCompare, false);
                }
                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message + Environment.NewLine + ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 查询前准备
        /// </summary>
        protected override void doSomethingBeforeQueryInThread()
        {
            initDataMap();
        }

        /// <summary>
        /// 初始化变量
        /// </summary>
        protected virtual void initDataMap()
        {
            isStandard = true;
            seqNo = 1;
            standardTPTotalCount = 0;
            compareTPTotalCount = 0;

            CellSetOfDateResult = new List<CellSetByDateForm.CellGSMResult>();
            cellGSMResult = new Dictionary<string, CellSetByDateForm.CellGSMResult>();            
        }
        
        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (tp is TestPointDetail)
                {
                    doWithGSMData(tp);
                }
            }
            catch (System.Exception ex)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.ToString());
            }
        }

        private void doWithGSMData(TestPoint tpPoint)
        {
            Cell cell = tpPoint.GetMainCell_GSM();
            if (cell != null)
            {
                if (tpPoint.Longitude == 0 && tpPoint.Latitude == 0)
                {
                    return;
                }

                addGSMCell(cell, tpStandard.GetShortString(), tpCompare.GetShortString(), cell.LAC, cell.CI, cell.Name);
                return;
            }

            int? lac = (int?)tpPoint["LAC"];
            int? ci = (int?)tpPoint["CI"];
            if (lac != null && ci != null && lac != -255 && ci != -255)
            {
                string key = lac + "_" + ci;
                addGSMCell(key, tpStandard.GetShortString(), tpCompare.GetShortString(), (int)lac, (int)ci, key);
            }
        }

        /// <summary>
        /// 添加小区
        /// </summary>
        /// <param name="cell">小区</param>
        /// <param name="tpPoint">采样点</param>
        /// <param name="standardDate">基准时段</param>
        /// <param name="compareDate">对比时段</param>
        /// <param name="iLAC"></param>
        /// <param name="iCI"></param>
        /// <param name="cellName">小区名</param>
        private void addGSMCell(object cell, string standardDate, string compareDate, int iLAC, int iCI,string cellName)
        {
            CellSetByDateForm.CellGSMResult localGSMResult;
            string sLAC_CI = iLAC + "_" + iCI;

            if (isStandard)
            {
                if (!cellGSMResult.ContainsKey(sLAC_CI))
                {
                    localGSMResult = new CellSetByDateForm.CellGSMResult();
                    localGSMResult.Cell = cell;
                    localGSMResult.SN = seqNo.ToString();
                    localGSMResult.CellName = cellName;
                    localGSMResult.CellType = "GSM";
                    localGSMResult.LAC = iLAC.ToString();
                    localGSMResult.CI = iCI.ToString();
                    localGSMResult.StandardTestPointCount = 1;
                    localGSMResult.StandardDate = standardDate;
                    localGSMResult.CompareDate = compareDate;
                    localGSMResult.CompareTestPointCount = 0;
                    localGSMResult.IsExist = "优化问题";
                    localGSMResult.TestPointCount = 1;

                    cellGSMResult.Add(sLAC_CI, localGSMResult);
                    seqNo++;
                    standardTPTotalCount++;
                }
                else
                {
                    localGSMResult = cellGSMResult[sLAC_CI];
                    localGSMResult.StandardTestPointCount++;
                    localGSMResult.TestPointCount = localGSMResult.StandardTestPointCount;
                    cellGSMResult[sLAC_CI] = localGSMResult;

                    standardTPTotalCount++;
                }
            }
            else 
            {
                if (cellGSMResult.ContainsKey(sLAC_CI))
                {
                    localGSMResult = cellGSMResult[sLAC_CI];
                    localGSMResult.CompareTestPointCount++;
                    localGSMResult.IsExist = "是";
                    localGSMResult.TestPointCount = localGSMResult.StandardTestPointCount + localGSMResult.CompareTestPointCount;

                    compareTPTotalCount++;
                }
            }
        }

        /// <summary>
        /// 查询完后数据处理
        /// </summary>
        protected override void getResultAfterQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return;
            }

            statCell();
        }

        /// <summary>
        /// 将cellGSMResult的数据处理后保存到CellSetOfDateResult中
        /// </summary>
        private void statCell()
        {
            foreach (KeyValuePair<string, CellSetByDateForm.CellGSMResult> CellSetOfDate in cellGSMResult)
            {
                //基准时段采样点总数和占比
                CellSetOfDate.Value.StandardTestPointTotalCount = standardTPTotalCount;
                CellSetOfDate.Value.StandardTestPointRatio = (CellSetOfDate.Value.StandardTestPointCount * 100.0 / standardTPTotalCount).ToString("f2");
                //对比时段采样点总数和占比
                CellSetOfDate.Value.CompareTestPointTotalCount = compareTPTotalCount;
                CellSetOfDate.Value.CompareTestPointRatio = (CellSetOfDate.Value.CompareTestPointTotalCount * 100.0 / compareTPTotalCount).ToString("f2");
                //采样点总数和占比
                CellSetOfDate.Value.TestPointTotalCount = CellSetOfDate.Value.StandardTestPointTotalCount + CellSetOfDate.Value.CompareTestPointTotalCount;
                CellSetOfDate.Value.TestPointRatio = (CellSetOfDate.Value.TestPointCount * 100.0 / CellSetOfDate.Value.TestPointTotalCount).ToString("f2");
                //采样点变化趋势
                CellSetOfDate.Value.ChangeRate = changeRate(CellSetOfDate.Value.StandardTestPointCount, CellSetOfDate.Value.CompareTestPointCount);

                CellSetOfDateResult.Add(CellSetOfDate.Value);
            }
        }
 
        /// <summary>
        /// 查询完数据后调用展示窗口
        /// </summary>
        protected override void FireShowFormAfterQuery()
        {
            CellSetByDateForm formCellSetByDate = MainModel.CreateResultForm(typeof(CellSetByDateForm)) as CellSetByDateForm;
            formCellSetByDate.ShowCellSet(CellSetOfDateResult);
            formCellSetByDate.Visible = true;
            formCellSetByDate.BringToFront();
            releaseData();
        }

        /// <summary>
        /// 释放部分变量
        /// </summary>
        private void releaseData()
        {
            cellGSMResult = null;
            CellSetOfDateResult = null;
        }

        /// <summary>
        /// 计算变化趋势
        /// </summary>
        /// <param name="iStandard">基准时段采样点数</param>
        /// <param name="iCompare">对比时段采样点数</param>
        /// <returns></returns>
        private string changeRate(int iStandard, int iCompare)
        {
            double dResult = 0;
            if (iStandard != 0)
            {
                dResult = (iCompare - iStandard) * 100.0 / iStandard;
            }
            else
            {
                dResult = 100.0;
            }
            return dResult.ToString("f2");
        }
    }
}
