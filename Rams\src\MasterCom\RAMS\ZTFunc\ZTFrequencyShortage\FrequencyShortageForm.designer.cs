﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class FrequencyShortageForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrequencyShortageForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayTestPoint = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.btnConvertToGrid = new DevExpress.XtraEditors.SimpleButton();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDateTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCells = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFreqCountRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayTestPoint,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 70);
            // 
            // miReplayTestPoint
            // 
            this.miReplayTestPoint.Name = "miReplayTestPoint";
            this.miReplayTestPoint.Size = new System.Drawing.Size(152, 22);
            this.miReplayTestPoint.Text = "回放采样点...";
            this.miReplayTestPoint.Click += new System.EventHandler(this.miReplayTestPoint_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel...";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.btnConvertToGrid);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(770, 44);
            this.panelControl1.TabIndex = 2;
            // 
            // btnConvertToGrid
            // 
            this.btnConvertToGrid.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnConvertToGrid.Location = new System.Drawing.Point(631, 12);
            this.btnConvertToGrid.Name = "btnConvertToGrid";
            this.btnConvertToGrid.Size = new System.Drawing.Size(75, 23);
            this.btnConvertToGrid.TabIndex = 0;
            this.btnConvertToGrid.Text = "栅格化";
            this.btnConvertToGrid.Click += new System.EventHandler(this.btnConvertToGrid_Click);
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.ctxMenu;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 44);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(770, 359);
            this.gridControl.TabIndex = 3;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnDateTime,
            this.gridColumnCellCount,
            this.gridColumnCells,
            this.gridColumnFreqCountRate});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            this.gridColumnFileName.Width = 194;
            // 
            // gridColumnDateTime
            // 
            this.gridColumnDateTime.Caption = "时间";
            this.gridColumnDateTime.FieldName = "DateTimeStringWithMillisecond";
            this.gridColumnDateTime.Name = "gridColumnDateTime";
            this.gridColumnDateTime.Visible = true;
            this.gridColumnDateTime.VisibleIndex = 1;
            this.gridColumnDateTime.Width = 131;
            // 
            // gridColumnCellCount
            // 
            this.gridColumnCellCount.Caption = "对象小区数";
            this.gridColumnCellCount.FieldName = "CellCount";
            this.gridColumnCellCount.Name = "gridColumnCellCount";
            this.gridColumnCellCount.Visible = true;
            this.gridColumnCellCount.VisibleIndex = 2;
            // 
            // gridColumnCells
            // 
            this.gridColumnCells.Caption = "对象小区";
            this.gridColumnCells.FieldName = "Cells";
            this.gridColumnCells.Name = "gridColumnCells";
            this.gridColumnCells.Visible = true;
            this.gridColumnCells.VisibleIndex = 3;
            this.gridColumnCells.Width = 148;
            // 
            // gridColumnFreqCountRate
            // 
            this.gridColumnFreqCountRate.Caption = "频点占比";
            this.gridColumnFreqCountRate.FieldName = "FreqCountRate";
            this.gridColumnFreqCountRate.Name = "gridColumnFreqCountRate";
            this.gridColumnFreqCountRate.Visible = true;
            this.gridColumnFreqCountRate.VisibleIndex = 4;
            this.gridColumnFreqCountRate.Width = 131;
            // 
            // FrequencyShortageForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(770, 403);
            this.Controls.Add(this.gridControl);
            this.Controls.Add(this.panelControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "FrequencyShortageForm";
            this.Text = "频率资源不足";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplayTestPoint;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.SimpleButton btnConvertToGrid;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDateTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCells;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFreqCountRate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellCount;

    }
}