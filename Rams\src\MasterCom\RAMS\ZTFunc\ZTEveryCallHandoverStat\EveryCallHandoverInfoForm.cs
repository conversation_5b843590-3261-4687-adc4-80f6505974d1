﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class EveryCallHandoverInfoForm : MinCloseForm
    {
        ToolStripNumericUpDown cellOccupation_tsmud = new ToolStripNumericUpDown();
        ToolStripNumericUpDown handoverTime_tsmud = new ToolStripNumericUpDown();
        public EveryCallHandoverInfoForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;    
            init();
        }
        private void init()
        {
            cellOccupation_tsmud.Increment = 5;
            cellOccupation_tsmud.Value = 10;
            cellOccupation_tsmud.Minimum = 1;
            cellOccupation_tsmud.Maximum = 99999;
            handoverTime_tsmud.Increment = 1;
            handoverTime_tsmud.Value = 3;
            handoverTime_tsmud.Minimum = 1;
            handoverTime_tsmud.Maximum = 30;
            toolStrip1.Items.Insert(1, cellOccupation_tsmud);
            toolStrip1.Items.Insert(4, handoverTime_tsmud);
            cellOccupation_tsmud.ValueChanged += new EventHandler(config_ValueChanged);
            handoverTime_tsmud.ValueChanged += new EventHandler(config_ValueChanged);

            treeListView.ObjectExpandChanged += new BrightIdeasSoftware.TreeListView.ObjectExpandProxy(treeListView_ObjectExpandChanged);
            treeListView.DoubleClick += new EventHandler(treeListView_DoubleClick);
            olvColumnSN.AspectGetter += delegate (object row)
            {
                if (row is FileEveryCallInfoOfTD)
                {
                    FileEveryCallInfoOfTD o = row as FileEveryCallInfoOfTD;
                    return o.SN;
                }
                return null;
            };
            olvColumnName.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.Name;
                }
                else if (row is FileEveryCallInfoOfTD)
                {
                    FileEveryCallInfoOfTD o = row as FileEveryCallInfoOfTD;
                    return o.Name;
                }
                else if (row is OneCallInfoOfTD)
                {
                    OneCallInfoOfTD o = row as OneCallInfoOfTD;
                    return o.Desc;
                }
                return null;
            };

            setCellInfoOfOneCallTD();

            this.treeListView.CanExpandGetter += delegate (object row)
            {
                return row is FileEveryCallInfoOfTD || row is OneCallInfoOfTD;
            };
            this.treeListView.ChildrenGetter += delegate (object x)
            {
                if (x is FileEveryCallInfoOfTD)
                {
                    FileEveryCallInfoOfTD item = x as FileEveryCallInfoOfTD;
                    return item.MatchCalls;
                }
                else if (x is OneCallInfoOfTD)
                {
                    OneCallInfoOfTD item = x as OneCallInfoOfTD;
                    return item.MatchCellInfo;
                }
                return null;
            };
        }

        private void setCellInfoOfOneCallTD()
        {
            olvColumnLAC.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.LAC;
                }
                return null;
            };
            olvColumnCI.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.CI;
                }
                return null;
            };
            olvColumnMileage.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.Milage;
                }
                return null;
            };
            olvColumnOccupation.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.OccupaSec;
                }
                return null;
            };
            olvColumnSpeed.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.Speed;
                }
                return null;
            };
            olvColumnTpCount.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.TestPoints.Count;
                }
                return null;
            };
            olvColumnMaxDistance.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.MaxDistance;
                }
                return null;
            };
            olvColumnAvgDistance.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.AvgDistance;
                }
                return null;
            };
            olvColumnMinRxLev.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.MinRxLev;
                }
                return null;
            };
            olvColumnMinDistance.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.MinDistance;
                }
                return null;
            };
            olvColumnMaxRxLev.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.MaxRxLev;
                }
                return null;
            };
            olvColumnAvgRxLev.AspectGetter += delegate (object row)
            {
                if (row is CellInfoOfOneCallTD)
                {
                    CellInfoOfOneCallTD o = row as CellInfoOfOneCallTD;
                    return o.RxLevAvg;
                }
                return null;
            };
        }

        void treeListView_DoubleClick(object sender, EventArgs e)
        {
            if (treeListView.SelectedObject is OneCallInfoOfTD)
            {
                MainModel.ClearDTData();
                OneCallInfoOfTD cellItem = treeListView.SelectedObject as OneCallInfoOfTD;
                foreach (Event evt in cellItem.Events)
                {
                    MainModel.DTDataManager.Add(evt);
                }
                foreach (TestPoint tp in cellItem.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
            }
            else if (treeListView.SelectedObject is CellInfoOfOneCallTD)
            {
                MainModel.ClearDTData();
                CellInfoOfOneCallTD cellItem = treeListView.SelectedObject as CellInfoOfOneCallTD;
                foreach (TestPoint tp in cellItem.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
                if (cellItem.Cell is TDCell)
                {
                    TDCell cell = cellItem.Cell as TDCell;
                    MainModel.SelectedTDCell = cell;
                    MainModel.MainForm.FireGotoView(cell.Longitude, cell.Latitude);
                }
                else if (cellItem.Cell is Cell)
                {
                    Cell cell = cellItem.Cell as Cell;
                    MainModel.SelectedCell = cell;
                    MainModel.MainForm.FireGotoView(cell.Longitude, cell.Latitude);
                }
            }
        }

        void treeListView_ObjectExpandChanged(object model, bool isExpend)
        {
            if (!isExpend) return;
            treeListView.RefreshObject(model);
            treeListView.SelectedObject = model;
        }
        private void config_ValueChanged(object obj, EventArgs e)
        {
            freshData();
        }
        private void freshData()
        {
            foreach (object o in treeListView.Objects)
            {
                FileEveryCallInfoOfTD fileCall=o as FileEveryCallInfoOfTD;
                if (fileCall!=null)
                {
                    List<OneCallInfoOfTD> calls = fileCall.MatchCalls;
                    foreach (OneCallInfoOfTD call in calls)
                    {
                        treeListView.Collapse(call);
                    }
                }
                treeListView.Collapse(o);
            }
            treeListView.Refresh();

            int handoverTime = (int)handoverTime_tsmud.Value;
            int cellOccupation = (int)cellOccupation_tsmud.Value;
            List<FileEveryCallInfoOfTD> resultList = new List<FileEveryCallInfoOfTD>();
            foreach (FileEveryCallInfoOfTD fileCall in fileCallList)
            {
                fileCall.FilterCall(handoverTime, cellOccupation);
                if (fileCall.MatchCalls.Count > 0)
                {
                    fileCall.SN = resultList.Count + 1;
                    resultList.Add(fileCall);
                }
            }
            treeListView.Roots=resultList;
        }

        private List<FileEveryCallInfoOfTD> fileCallList = null;
        public void FillData(List<FileEveryCallInfoOfTD> list)
        {
            fileCallList = list;
            freshData();
        }

        private void tsBtnExp2Xls_Click(object sender, EventArgs e)
        {
            treeListView.ExpandAll();
            ExcelNPOIManager.ExportToExcel(this.treeListView);
        }

    }
}
