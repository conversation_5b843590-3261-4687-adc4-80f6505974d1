﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Frame
{
    public partial class SrcTextBox : RichTextBox
    {
        private int POS_HEAD_LEN = 6;
        private int HEX_HEAD_LEN = 55;
        private int ROW_LEN = 72;

        public SrcTextBox()
        {
            InitializeComponent();
            this.ReadOnly  = true;
            this.BackColor = Color.White;
        }

        public void ClearSelection()
        {
            int len = Text.Length;
            Select(0, len);
            SelectionBackColor = Color.White;
            SelectionColor     = Color.Black;
        }

        private void HignLight(Int32 bgnPos, Int32 len)
        {
            Select(bgnPos, len);
            SelectionBackColor = Color.Blue;
            SelectionColor     = Color.White;
        }

        public void SetHighLight(Int32 bgnPos, Int32 len)
        {
            ClearSelection();
            if (bgnPos + len > srcCode.Length)
            {
                return;
            }

            int pos1 = bgnPos, len1 = 0, pos2 = 0, len2 = 0;

            len1 = 16 - (pos1 % 16);
            if (len1 > len)
            {
                len1 = len;
            }
            HignLight(ROW_LEN * (pos1 / 16) + POS_HEAD_LEN + (pos1 % 16) * 3, len1 * 3 - 1);
            HignLight(ROW_LEN * (pos1 / 16) + HEX_HEAD_LEN + (pos1 % 16), len1);

            int loopCount = (len - len1) / 16;
            for (int i = 0; i < loopCount; i++)
            {
                int pos = pos1 + len1 + i * 16;
                HignLight(ROW_LEN * (pos / 16) + POS_HEAD_LEN + (pos % 16) * 3, 16 * 3 - 1);
                HignLight(ROW_LEN * (pos / 16) + HEX_HEAD_LEN + (pos % 16), 16);
            }

            len2 = (len - len1) % 16;
            pos2 = bgnPos + len - len2;
            HignLight(ROW_LEN * (pos2 / 16) + POS_HEAD_LEN + (pos2 % 16) * 3, len2 * 3 - 1);
            HignLight(ROW_LEN * (pos2 / 16) + HEX_HEAD_LEN + (pos2 % 16), len2);
        }

        private byte[] srcCode;

        public void SetSrcCode(byte[] _srcCode)
        {
            srcCode = _srcCode;
        }

        private String GetPosString(int bgnPos)
        {
            String tmp = bgnPos.ToString("x");
            if (tmp.Length == 1)
            {
                tmp = "000" + tmp;
            }
            else if (tmp.Length == 2)
            {
                tmp = "00" + tmp;
            }
            else if (tmp.Length == 3)
            {
                tmp = "0" + tmp;
            }
            return tmp + "  ";
        }

        private String ToAssicString(int bgnPos)
        {
            byte byt = srcCode[bgnPos];
            if (byt < 0 || byt>0xa0)
                return ".";
            if (byt >= '0' && byt <= '9')
            {
                return Encoding.Default.GetString(srcCode, bgnPos, 1);
            }
            else if (Char.IsLetter((char)byt))
            {
                return Encoding.Default.GetString(srcCode, bgnPos, 1);
            }
            return ".";
        }

        private String SrcToString(int bgnPos, int len)
        {
            StringBuilder dstStr = new StringBuilder();
            for (int pos = bgnPos; pos < (bgnPos + len); pos++)
            {
                String tmp = ToAssicString(pos);
                dstStr.Append(tmp);
            }
            return dstStr.ToString();
        }

        private String SrcToHexString(int bgnPos, int len)
        {
            StringBuilder dstStr = new StringBuilder();
            for (int pos = bgnPos; pos < (bgnPos + len); pos++)
            {
                String tmp = srcCode[pos].ToString("X");
                if (tmp.Length == 1)
                {
                    tmp = "0" + tmp;
                }
                dstStr.Append(tmp + " ");
            }
            for (int i = 0; i < 16 - len; i++)
            {
                dstStr.Append("   ");
            }
            return dstStr.ToString();
        }


        public void DisplaySrcCode()
        {
            Text = "";
            StringBuilder textTemp = new StringBuilder();
            if(srcCode!=null)
            {
                int loopTimes = srcCode.Length / 16;
                int lastLen = srcCode.Length % 16;
                for (int i = 0; i < loopTimes; i++)
                {
                    textTemp.Append(GetPosString(i * 16) + SrcToHexString(i * 16, 16)
                        + " " + SrcToString(i * 16, 16)
                        + "\r\n");
                }
                textTemp.Append(GetPosString(loopTimes * 16) + SrcToHexString(loopTimes * 16, lastLen)
                   + " " + SrcToString(loopTimes * 16, lastLen));
                Text = textTemp.ToString();
            }
            
        }
    }
}
