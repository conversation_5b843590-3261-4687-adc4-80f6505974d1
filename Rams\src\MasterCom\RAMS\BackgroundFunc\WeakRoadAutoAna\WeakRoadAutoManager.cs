﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class WeakRoadAutoCfgManager
    {
        private static WeakRoadAutoCfgManager instance = null;
        private WeakRoadAutoCfgManager()
        {
            cfgFileName = System.Windows.Forms.Application.StartupPath + "/config/AutoStatsWeakRoad.xml";
            LoadCfg();
        }
        public static WeakRoadAutoCfgManager GetInstance()
        {
            if (instance==null)
            {
                instance = new WeakRoadAutoCfgManager();
            }
            return instance;
        }
        
        public List<WeakRoadReport> Reports { get; set; } = new List<WeakRoadReport>();
        private List<object> cfgParam
        {
            get
            {
                List<object> rpts = new List<object>();
                foreach (WeakRoadReport rpt in Reports)
                {
                    rpts.Add(rpt.CfgParam);
                }
                return rpts;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                Reports.Clear();
                foreach (object obj in value)
                {
                    WeakRoadReport rpt = new WeakRoadReport();
                    rpt.CfgParam = obj as Dictionary<string, object>;
                    Reports.Add(rpt);
                }
            }
        }

        private readonly string cfgFileName;
        public void LoadCfg()
        {
            if (System.IO.File.Exists(cfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(cfgFileName);
                cfgParam = configFile.GetItemValue("WeakRoadCfg", "Reports") as List<object>;
            }
        }
        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("WeakRoadCfg");
            xmlFile.AddItem(cfgE, "Reports", this.cfgParam);
            xmlFile.Save(cfgFileName);
        }
    }

    public class WeakRoadAutoFuncManager
    {
        readonly MainModel mainModel = MainModel.GetInstance();
        private static WeakRoadAutoFuncManager instance = null;
        public static WeakRoadAutoFuncManager GetInstance()
        {
            if (instance==null)
            {
                instance = new WeakRoadAutoFuncManager();
            }
            return instance;
        }

        public void SaveResult_Road(int subFuncID, string projectTypes, FileInfo file, List<BackgroundResult> resultList)
        {
            DiySqlNonQuery queryNon = null;
            string sql = "";
            int fileID = file.ID;
            if (resultList.Count > 0)
            {
                DateTime beginDateTime = JavaDate.GetDateTimeFromMilliseconds(file.BeginTime * 1000L);
                string year = beginDateTime.Year.ToString();
                string month = beginDateTime.Month.ToString();
                if (month.Length == 1)
                {
                    month = "0" + month;
                }
                string yearMonth = year + month;
                int maxID_Road = getMaxID_Road(yearMonth);
                string tableName = "tb_probchk_road_result_" + yearMonth;
                checkTableExists(yearMonth, "tb_probchk_road_result", projectTypes);

                sql = "delete from " + tableName + " where iSubFuncID = " + subFuncID + " and iFileID = " + fileID + " and strProject = '" + projectTypes + "'";
                queryNon = new DiySqlNonQuery(mainModel, sql);
                queryNon.Query();

                BackgroundFuncResultWriter.Instance.WriteRoad(tableName, resultList, maxID_Road);
            }

            sql = "insert into tb_probchk_road_processed_log values(" + subFuncID + "," + fileID + ",'" + projectTypes + "')";
            queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();
        }
        private int getMaxID_Road(string yearMonth)
        {
            string sql = "select isnull(max(iid),0) from tb_probchk_road_result_" + yearMonth;
            DiySqlOneIntValueOnly queryMaxID = new DiySqlOneIntValueOnly(mainModel, sql);
            queryMaxID.Query();
            int maxID = queryMaxID.IntValue;
            return maxID < 0 ? 0 : maxID;
        }
        private void checkTableExists(string yearMonth, string tableNameSrc, string projectTypes)
        {
            string tableName = tableNameSrc + "_" + yearMonth;
            string sql = "exec sp_probchk_table_time_insert '" + tableNameSrc + "','" + yearMonth + "','" + projectTypes + "'";
            DiySqlNonQuery queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();

            sql = "exec sp_adapter_addtable '" + tableName + "', '" + tableNameSrc + "'";
            queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();
        }

        public List<BackgroundResult> GetResult_Road(DateTime sDateTime, DateTime eDateTime, int iSubFuncID, string subFuncName,
    BackgroundStatType statType, string strProject, int districtID)
        {
            int istime = (int)(JavaDate.GetMilliseconds(sDateTime) / 1000);
            int ietime = (int)(JavaDate.GetMilliseconds(eDateTime) / 1000);
            MasterCom.RAMS.Net.BackgroundFuncQueryManager.DIYSQLBackground_Road_GetResult roadGetResultQuery =
              MasterCom.RAMS.Net.BackgroundFuncQueryManager.DIYSQLBackground_Road_GetResult.GetInstanceRoadGetResult();

            roadGetResultQuery.SetCondition(istime, ietime, iSubFuncID, strProject);
            roadGetResultQuery.Query();
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (BackgroundResult_Road item in roadGetResultQuery.ResultList)
            {
                item.SN = bgResultList.Count + 1;
                item.SubFuncName = subFuncName;
                item.StatType = statType;
                item.DistrictDesc = DistrictManager.GetInstance().getDistrictName(districtID);
                bgResultList.Add((BackgroundResult)item);
            }
            return bgResultList;
        }
    }
}
