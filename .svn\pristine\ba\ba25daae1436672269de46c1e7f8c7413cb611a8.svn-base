﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRVideoPlayAnaDlg : BaseDialog
    {
        public NRVideoPlayAnaDlg(NRVideoPlayAnaCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(NRVideoPlayAnaCondition condition)
        {
            numPreLoad.Value = condition.PreLoadTime;
            numPreRebuffer.Value = condition.PreRebufferTime;
        }

        public NRVideoPlayAnaCondition GetCondition()
        {
            NRVideoPlayAnaCondition condition = new NRVideoPlayAnaCondition();
            condition.PreRebufferTime = (int)numPreRebuffer.Value;
            condition.PreLoadTime = (int)numPreLoad.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }
}
