﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteLessTestPointCellAnaBase : LteWeakCellAnaBase
    {
        int minPointCount = 50;
        protected LteLessTestPointCellAnaBase()
            : base()
        {
        }
        public LteLessTestPointCellAnaBase(bool isVoLTE)
            : base(isVoLTE)
        {
        }
        public override string Name
        {
            get { return "低采样小区分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22106, this.Name);
        }

        LessTpCellConditionDlg dlg;
        protected override bool getCondition()
        {
            if (dlg == null)
            {
                dlg = new LessTpCellConditionDlg();
            }
            dlg.SetCondition(minPointCount);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out minPointCount);
            return true;
        }
        protected override void getResultsAfterQuery()
        {
            foreach (LteWeakCellInfo info in lteWeakCellInfoDic.Values)
            {
                if (info.TestPointCount < minPointCount)
                {
                    addToResult(info);
                }
            }
            lteWeakCellInfoDic.Clear();
        }
    }

    public class LteLessTestPointCellAnaByRegion : LteLessTestPointCellAnaBase
    {
        private static LteLessTestPointCellAnaByRegion instance = null;
        public static LteLessTestPointCellAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteLessTestPointCellAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected LteLessTestPointCellAnaByRegion()
            : base(false)
        {
        }
        public LteLessTestPointCellAnaByRegion(bool isVoLTE)
            : base(isVoLTE)
        {
        }
        public override string Name
        {
            get { return "低采样小区分析(按区域)"; }
        }
    }
    public class LteLessTestPointCellAnaByFile : LteLessTestPointCellAnaBase
    {
        private static LteLessTestPointCellAnaByFile intance = null;
        public static LteLessTestPointCellAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LteLessTestPointCellAnaByFile();
                    }
                }
            }
            return intance;
        }

        protected LteLessTestPointCellAnaByFile()
            : base(false)
        {
        }
        public LteLessTestPointCellAnaByFile(bool isVoLTE)
            : base(isVoLTE)
        {
        }
        public override string Name
        {
            get { return "低采样小区分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }
}
