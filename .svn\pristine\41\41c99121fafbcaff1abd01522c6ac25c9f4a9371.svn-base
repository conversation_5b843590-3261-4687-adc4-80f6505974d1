﻿namespace MasterCom.RAMS.Func
{
    partial class LTEBTSInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.Label labelTCH;
            System.Windows.Forms.Label label4;
            System.Windows.Forms.Label label5;
            System.Windows.Forms.Label label6;
            System.Windows.Forms.Label label8;
            System.Windows.Forms.GroupBox groupBox3;
            System.Windows.Forms.GroupBox groupBox2;
            System.Windows.Forms.Label label9;
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label10;
            System.Windows.Forms.Label label7;
            System.Windows.Forms.Label label2;
            System.Windows.Forms.Label label3;
            System.Windows.Forms.Label label12;
            System.Windows.Forms.Label label13;
            System.Windows.Forms.Label label14;
            System.Windows.Forms.Label labelCI;
            System.Windows.Forms.Label labelID;
            System.Windows.Forms.Label label11;
            this.gridCtrlLTE = new DevExpress.XtraGrid.GridControl();
            this.gridViewLTE = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.txtCode = new System.Windows.Forms.TextBox();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.txtFreqList = new System.Windows.Forms.TextBox();
            this.txtDesc = new System.Windows.Forms.TextBox();
            this.txtDirection = new System.Windows.Forms.TextBox();
            this.txtPCI = new System.Windows.Forms.TextBox();
            this.txtTAC = new System.Windows.Forms.TextBox();
            this.txtSectorID = new System.Windows.Forms.TextBox();
            this.txtEarfcn = new System.Windows.Forms.TextBox();
            this.txtECI = new System.Windows.Forms.TextBox();
            this.txtID = new System.Windows.Forms.TextBox();
            this.btnCellLocation = new System.Windows.Forms.Button();
            this.txtType = new System.Windows.Forms.TextBox();
            this.txtName = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.txtBtsId = new System.Windows.Forms.TextBox();
            this.txtBTSDesc = new System.Windows.Forms.TextBox();
            this.txtLat = new System.Windows.Forms.TextBox();
            this.txtLng = new System.Windows.Forms.TextBox();
            labelTCH = new System.Windows.Forms.Label();
            label4 = new System.Windows.Forms.Label();
            label5 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            label8 = new System.Windows.Forms.Label();
            groupBox3 = new System.Windows.Forms.GroupBox();
            groupBox2 = new System.Windows.Forms.GroupBox();
            label9 = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            label10 = new System.Windows.Forms.Label();
            label7 = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            label12 = new System.Windows.Forms.Label();
            label13 = new System.Windows.Forms.Label();
            label14 = new System.Windows.Forms.Label();
            labelCI = new System.Windows.Forms.Label();
            labelID = new System.Windows.Forms.Label();
            label11 = new System.Windows.Forms.Label();
            groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlLTE)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTE)).BeginInit();
            groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelTCH
            // 
            labelTCH.AutoSize = true;
            labelTCH.Location = new System.Drawing.Point(205, 84);
            labelTCH.Name = "labelTCH";
            labelTCH.Size = new System.Drawing.Size(35, 14);
            labelTCH.TabIndex = 30;
            labelTCH.Text = "描述:";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(23, 52);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(35, 14);
            label4.TabIndex = 18;
            label4.Text = "类型:";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(205, 52);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(35, 14);
            label5.TabIndex = 26;
            label5.Text = "经度:";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new System.Drawing.Point(387, 52);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(35, 14);
            label6.TabIndex = 28;
            label6.Text = "纬度:";
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(23, 21);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(35, 14);
            label8.TabIndex = 16;
            label8.Text = "名称:";
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(this.gridCtrlLTE);
            groupBox3.Location = new System.Drawing.Point(14, 139);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new System.Drawing.Size(204, 283);
            groupBox3.TabIndex = 32;
            groupBox3.TabStop = false;
            groupBox3.Text = "&CellList";
            // 
            // gridCtrlLTE
            // 
            this.gridCtrlLTE.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlLTE.Location = new System.Drawing.Point(3, 18);
            this.gridCtrlLTE.MainView = this.gridViewLTE;
            this.gridCtrlLTE.Name = "gridCtrlLTE";
            this.gridCtrlLTE.Size = new System.Drawing.Size(198, 262);
            this.gridCtrlLTE.TabIndex = 2;
            this.gridCtrlLTE.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewLTE});
            // 
            // gridViewLTE
            // 
            this.gridViewLTE.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.gridViewLTE.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gridViewLTE.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.gridViewLTE.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gridViewLTE.Appearance.OddRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.gridViewLTE.Appearance.OddRow.Options.UseBackColor = true;
            this.gridViewLTE.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3});
            this.gridViewLTE.GridControl = this.gridCtrlLTE;
            this.gridViewLTE.Name = "gridViewLTE";
            this.gridViewLTE.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewLTE.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewLTE.OptionsBehavior.AutoSelectAllInEditor = false;
            this.gridViewLTE.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.gridViewLTE.OptionsBehavior.Editable = false;
            this.gridViewLTE.OptionsView.ShowDetailButtons = false;
            this.gridViewLTE.OptionsView.ShowGroupPanel = false;
            this.gridViewLTE.OptionsView.ShowIndicator = false;
            this.gridViewLTE.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridViewLTE_FocusedRowChanged);
            this.gridViewLTE.DoubleClick += new System.EventHandler(this.gridViewLTE_DoubleClick);
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "小区名";
            this.gridColumn3.FieldName = "Name";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 0;
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(label9);
            groupBox2.Controls.Add(label1);
            groupBox2.Controls.Add(this.txtCode);
            groupBox2.Controls.Add(this.txtCellName);
            groupBox2.Controls.Add(this.txtFreqList);
            groupBox2.Controls.Add(this.txtDesc);
            groupBox2.Controls.Add(this.txtDirection);
            groupBox2.Controls.Add(this.txtPCI);
            groupBox2.Controls.Add(this.txtTAC);
            groupBox2.Controls.Add(label10);
            groupBox2.Controls.Add(label7);
            groupBox2.Controls.Add(label2);
            groupBox2.Controls.Add(label3);
            groupBox2.Controls.Add(label12);
            groupBox2.Controls.Add(label13);
            groupBox2.Controls.Add(this.txtSectorID);
            groupBox2.Controls.Add(this.txtEarfcn);
            groupBox2.Controls.Add(this.txtECI);
            groupBox2.Controls.Add(label14);
            groupBox2.Controls.Add(labelCI);
            groupBox2.Controls.Add(this.txtID);
            groupBox2.Controls.Add(labelID);
            groupBox2.Controls.Add(this.btnCellLocation);
            groupBox2.Location = new System.Drawing.Point(226, 139);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new System.Drawing.Size(351, 283);
            groupBox2.TabIndex = 33;
            groupBox2.TabStop = false;
            groupBox2.Text = "Cell Info";
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new System.Drawing.Point(203, 58);
            label9.Name = "label9";
            label9.Size = new System.Drawing.Size(39, 14);
            label9.TabIndex = 14;
            label9.Text = "&Code:";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(35, 27);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(42, 14);
            label1.TabIndex = 12;
            label1.Text = "&Name:";
            // 
            // txtCode
            // 
            this.txtCode.AcceptsReturn = true;
            this.txtCode.BackColor = System.Drawing.SystemColors.Window;
            this.txtCode.Location = new System.Drawing.Point(247, 55);
            this.txtCode.Name = "txtCode";
            this.txtCode.ReadOnly = true;
            this.txtCode.Size = new System.Drawing.Size(93, 22);
            this.txtCode.TabIndex = 15;
            this.txtCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // txtCellName
            // 
            this.txtCellName.BackColor = System.Drawing.SystemColors.Window;
            this.txtCellName.Location = new System.Drawing.Point(83, 23);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.ReadOnly = true;
            this.txtCellName.Size = new System.Drawing.Size(256, 22);
            this.txtCellName.TabIndex = 13;
            this.txtCellName.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // txtFreqList
            // 
            this.txtFreqList.BackColor = System.Drawing.SystemColors.Window;
            this.txtFreqList.Location = new System.Drawing.Point(82, 212);
            this.txtFreqList.Name = "txtFreqList";
            this.txtFreqList.ReadOnly = true;
            this.txtFreqList.Size = new System.Drawing.Size(257, 22);
            this.txtFreqList.TabIndex = 24;
            // 
            // txtDesc
            // 
            this.txtDesc.BackColor = System.Drawing.SystemColors.Window;
            this.txtDesc.Location = new System.Drawing.Point(82, 181);
            this.txtDesc.Name = "txtDesc";
            this.txtDesc.ReadOnly = true;
            this.txtDesc.Size = new System.Drawing.Size(257, 22);
            this.txtDesc.TabIndex = 23;
            // 
            // txtDirection
            // 
            this.txtDirection.BackColor = System.Drawing.SystemColors.Window;
            this.txtDirection.Location = new System.Drawing.Point(83, 149);
            this.txtDirection.Name = "txtDirection";
            this.txtDirection.ReadOnly = true;
            this.txtDirection.Size = new System.Drawing.Size(93, 22);
            this.txtDirection.TabIndex = 22;
            // 
            // txtPCI
            // 
            this.txtPCI.BackColor = System.Drawing.SystemColors.Window;
            this.txtPCI.Location = new System.Drawing.Point(83, 118);
            this.txtPCI.Name = "txtPCI";
            this.txtPCI.ReadOnly = true;
            this.txtPCI.Size = new System.Drawing.Size(93, 22);
            this.txtPCI.TabIndex = 26;
            // 
            // txtTAC
            // 
            this.txtTAC.BackColor = System.Drawing.SystemColors.Window;
            this.txtTAC.Location = new System.Drawing.Point(83, 86);
            this.txtTAC.Name = "txtTAC";
            this.txtTAC.ReadOnly = true;
            this.txtTAC.Size = new System.Drawing.Size(93, 22);
            this.txtTAC.TabIndex = 25;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new System.Drawing.Point(7, 216);
            label10.Name = "label10";
            label10.Size = new System.Drawing.Size(53, 14);
            label10.TabIndex = 20;
            label10.Text = "&FreqList:";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new System.Drawing.Point(35, 184);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(35, 14);
            label7.TabIndex = 19;
            label7.Text = "&描述:";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(21, 153);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(47, 14);
            label2.TabIndex = 17;
            label2.Text = "&方向角:";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(189, 153);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(47, 14);
            label3.TabIndex = 16;
            label3.Text = "&扇区号:";
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(42, 121);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(29, 14);
            label12.TabIndex = 21;
            label12.Text = "&PCI:";
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.Location = new System.Drawing.Point(42, 90);
            label13.Name = "label13";
            label13.Size = new System.Drawing.Size(34, 14);
            label13.TabIndex = 18;
            label13.Text = "&TAC:";
            // 
            // txtSectorID
            // 
            this.txtSectorID.BackColor = System.Drawing.SystemColors.Window;
            this.txtSectorID.Location = new System.Drawing.Point(247, 149);
            this.txtSectorID.Name = "txtSectorID";
            this.txtSectorID.ReadOnly = true;
            this.txtSectorID.Size = new System.Drawing.Size(93, 22);
            this.txtSectorID.TabIndex = 29;
            // 
            // txtEarfcn
            // 
            this.txtEarfcn.BackColor = System.Drawing.SystemColors.Window;
            this.txtEarfcn.Location = new System.Drawing.Point(247, 118);
            this.txtEarfcn.Name = "txtEarfcn";
            this.txtEarfcn.ReadOnly = true;
            this.txtEarfcn.Size = new System.Drawing.Size(93, 22);
            this.txtEarfcn.TabIndex = 30;
            // 
            // txtECI
            // 
            this.txtECI.BackColor = System.Drawing.SystemColors.Window;
            this.txtECI.Location = new System.Drawing.Point(247, 86);
            this.txtECI.Name = "txtECI";
            this.txtECI.ReadOnly = true;
            this.txtECI.Size = new System.Drawing.Size(93, 22);
            this.txtECI.TabIndex = 31;
            // 
            // label14
            // 
            label14.AutoSize = true;
            label14.Location = new System.Drawing.Point(189, 121);
            label14.Name = "label14";
            label14.Size = new System.Drawing.Size(54, 14);
            label14.TabIndex = 27;
            label14.Text = "&EARFCN:";
            // 
            // labelCI
            // 
            labelCI.AutoSize = true;
            labelCI.Location = new System.Drawing.Point(210, 90);
            labelCI.Name = "labelCI";
            labelCI.Size = new System.Drawing.Size(29, 14);
            labelCI.TabIndex = 28;
            labelCI.Text = "&ECI:";
            // 
            // txtID
            // 
            this.txtID.BackColor = System.Drawing.SystemColors.Window;
            this.txtID.Location = new System.Drawing.Point(83, 55);
            this.txtID.Name = "txtID";
            this.txtID.ReadOnly = true;
            this.txtID.Size = new System.Drawing.Size(93, 22);
            this.txtID.TabIndex = 11;
            // 
            // labelID
            // 
            labelID.AutoSize = true;
            labelID.Location = new System.Drawing.Point(49, 58);
            labelID.Name = "labelID";
            labelID.Size = new System.Drawing.Size(23, 14);
            labelID.TabIndex = 10;
            labelID.Text = "&ID:";
            // 
            // btnCellLocation
            // 
            this.btnCellLocation.Location = new System.Drawing.Point(213, 244);
            this.btnCellLocation.Name = "btnCellLocation";
            this.btnCellLocation.Size = new System.Drawing.Size(127, 27);
            this.btnCellLocation.TabIndex = 8;
            this.btnCellLocation.Text = "&Location Cell";
            this.btnCellLocation.UseVisualStyleBackColor = true;
            this.btnCellLocation.Click += new System.EventHandler(this.btnCellLocation_Click);
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new System.Drawing.Point(23, 84);
            label11.Name = "label11";
            label11.Size = new System.Drawing.Size(45, 14);
            label11.TabIndex = 31;
            label11.Text = "BTSID:";
            // 
            // txtType
            // 
            this.txtType.BackColor = System.Drawing.SystemColors.Window;
            this.txtType.Location = new System.Drawing.Point(75, 48);
            this.txtType.Name = "txtType";
            this.txtType.ReadOnly = true;
            this.txtType.Size = new System.Drawing.Size(111, 22);
            this.txtType.TabIndex = 19;
            // 
            // txtName
            // 
            this.txtName.BackColor = System.Drawing.SystemColors.Window;
            this.txtName.Location = new System.Drawing.Point(75, 17);
            this.txtName.Name = "txtName";
            this.txtName.ReadOnly = true;
            this.txtName.Size = new System.Drawing.Size(476, 22);
            this.txtName.TabIndex = 17;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(label11);
            this.groupBox1.Controls.Add(this.txtBtsId);
            this.groupBox1.Controls.Add(this.txtBTSDesc);
            this.groupBox1.Controls.Add(this.txtName);
            this.groupBox1.Controls.Add(label8);
            this.groupBox1.Controls.Add(label6);
            this.groupBox1.Controls.Add(labelTCH);
            this.groupBox1.Controls.Add(label5);
            this.groupBox1.Controls.Add(label4);
            this.groupBox1.Controls.Add(this.txtLat);
            this.groupBox1.Controls.Add(this.txtLng);
            this.groupBox1.Controls.Add(this.txtType);
            this.groupBox1.Location = new System.Drawing.Point(14, 14);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(563, 118);
            this.groupBox1.TabIndex = 34;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Info";
            // 
            // txtBtsId
            // 
            this.txtBtsId.BackColor = System.Drawing.SystemColors.Window;
            this.txtBtsId.Location = new System.Drawing.Point(75, 80);
            this.txtBtsId.Name = "txtBtsId";
            this.txtBtsId.ReadOnly = true;
            this.txtBtsId.Size = new System.Drawing.Size(111, 22);
            this.txtBtsId.TabIndex = 32;
            // 
            // txtBTSDesc
            // 
            this.txtBTSDesc.BackColor = System.Drawing.SystemColors.Window;
            this.txtBTSDesc.Location = new System.Drawing.Point(257, 80);
            this.txtBTSDesc.Name = "txtBTSDesc";
            this.txtBTSDesc.ReadOnly = true;
            this.txtBTSDesc.Size = new System.Drawing.Size(295, 22);
            this.txtBTSDesc.TabIndex = 17;
            // 
            // txtLat
            // 
            this.txtLat.BackColor = System.Drawing.SystemColors.Window;
            this.txtLat.Location = new System.Drawing.Point(440, 49);
            this.txtLat.Name = "txtLat";
            this.txtLat.ReadOnly = true;
            this.txtLat.Size = new System.Drawing.Size(111, 22);
            this.txtLat.TabIndex = 19;
            // 
            // txtLng
            // 
            this.txtLng.BackColor = System.Drawing.SystemColors.Window;
            this.txtLng.Location = new System.Drawing.Point(257, 49);
            this.txtLng.Name = "txtLng";
            this.txtLng.ReadOnly = true;
            this.txtLng.Size = new System.Drawing.Size(111, 22);
            this.txtLng.TabIndex = 19;
            // 
            // LTEBTSInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(589, 434);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(groupBox2);
            this.Controls.Add(groupBox3);
            this.MaximizeBox = false;
            this.Name = "LTEBTSInfoForm";
            this.Text = "LTE基站详细信息";
            groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlLTE)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewLTE)).EndInit();
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TextBox txtType;
        private System.Windows.Forms.TextBox txtName;
        private System.Windows.Forms.Button btnCellLocation;
        private DevExpress.XtraGrid.GridControl gridCtrlLTE;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewLTE;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private System.Windows.Forms.TextBox txtCode;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.TextBox txtFreqList;
        private System.Windows.Forms.TextBox txtDesc;
        private System.Windows.Forms.TextBox txtDirection;
        private System.Windows.Forms.TextBox txtPCI;
        private System.Windows.Forms.TextBox txtTAC;
        private System.Windows.Forms.TextBox txtSectorID;
        private System.Windows.Forms.TextBox txtEarfcn;
        private System.Windows.Forms.TextBox txtECI;
        private System.Windows.Forms.TextBox txtID;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox txtBTSDesc;
        private System.Windows.Forms.TextBox txtLat;
        private System.Windows.Forms.TextBox txtLng;
        private System.Windows.Forms.TextBox txtBtsId;
    }
}