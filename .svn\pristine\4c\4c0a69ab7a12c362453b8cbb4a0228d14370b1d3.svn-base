﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using MasterCom.RAMS.ZTQuery;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.Net
{
    public class DIYInjectionGridQuery : QueryBase
    {
        public List<StreetInjectInfo> totalInfoResultList { get; set; }
        public GridMatrix<InjectGridUnit> injectGridMatrix { get; set; }
        public bool bBySelectedCarrierArea { get; set; }
        public CarrierAreaInjectionInfo carrierAreaInjectionInfo { get; set; }
        private readonly int carrierID = -1;
        private readonly ResvRegion rr = null;

        public DIYInjectionGridQuery(MainModel mainModel)
            : base(mainModel)
        {
            totalInfoResultList = new List<StreetInjectInfo>();
            bBySelectedCarrierArea = false;
            carrierAreaInjectionInfo = new CarrierAreaInjectionInfo();
        }

        public DIYInjectionGridQuery(MainModel mainModel, int carrierID, ResvRegion rr)
            : this(mainModel)
        {
            this.carrierID = carrierID;
            this.rr = rr;
            bBySelectedCarrierArea = true;
        }

        public override string Name
        {
            get { return "道路测试渗透率查询"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18005, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
                MainModel.StreetInjectMultiTables = false;
                if (MainModel.MultiGeometrys && Condition.Geometorys.SelectedResvRegions != null && condition.Geometorys.SelectedResvRegions.Count > 0)//
                {
                    MainModel.StreetInjectResvRegions = condition.Geometorys.SelectedResvRegions;
                }
                else
                {
                    MainModel.StreetInjectResvRegions.Clear();
                    ResvRegion resvRegion = new ResvRegion();
                    resvRegion.RegionName = "区域";
                    resvRegion.Shape = Condition.Geometorys.Region;
                    MainModel.StreetInjectResvRegions.Add(resvRegion);
                }
                if (bBySelectedCarrierArea)
                {
                    /**
                    carrierAreaInjectionInfo.StreetInjectColorMatrix = injGridMatrix;

                    int rowCount = injGridMatrix.GetLength(0);
                    int columnCount = injGridMatrix.GetLength(1);
                    DbRect bounds = new DbRect();
                    if (rowCount > 0 && columnCount > 0)
                    {
                        bounds.x1 = injGridMatrix[0, 0].ltLong;
                        bounds.x2 = injGridMatrix[rowCount - 1, columnCount - 1].ltLong + GRID_SPAN_LONG;
                        bounds.y1 = injGridMatrix[rowCount - 1, columnCount - 1].ltLat - GRID_SPAN_LAT;
                        bounds.y2 = injGridMatrix[0, 0].ltLat;
                    }
                    MapFormStreetInjectAnaLayer layer = MainModel.MainForm.GetMapForm().GetStreetInjectAnaLayer();
                    foreach (StreetInjectInfo info in totalInfoResultList)
                    {
                        info.carrierID = this.carrierID;
                        info.AreaName = this.rr.areaName;
                        carrierAreaInjectionInfo.TotalInfoResultList.Add(info as StreetInjectInfo);
                    }*///
                }
                else
                {
                    MapWinGIS.Shape geometryReg = condition.Geometorys.Region;
                    MainModel.LastSearchGeometry = geometryReg;
                    MainModel.CurStreetInjectMatrix = injectGridMatrix;
                    MainModel.TotalStreetInjInfoResultList = totalInfoResultList;
                    MainModel.FireStreetInjectQueried(this);
                    MainModel.RefreshLegend();
                }
            }
        }

        protected virtual void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;

                foreach (TimePeriod period in condition.Periods)
                {
                    WaitBox.Text = "开始统计查询栅格数据用以进行渗透率计算...";
                    prepareQueryPackage(package, period);
                    prepareNeededInfo(package);
                    clientProxy.Send();
                    recieveInfo(clientProxy);
                }

                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                WaitBox.Text = "数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
                MainModel.CurStreetInjectMatrix = null;
            }
            catch (Exception e)
            {
                log.Error("Error:" + e.Message);
            }
            finally
            {
                WaitBox.Close();
            }
            MapFormStreetInjectAnaLayer.NeedFreshFullImg = true;
        }

        protected void prepareNeededInfo(Package package)
        {
            package.Content.AddParam("1,1,1");
        }

        protected void recieveInfo(ClientProxy clientProxy)
        {
            if (injectGridMatrix == null)
            {
                injectGridMatrix = new GridMatrix<InjectGridUnit>();
            }
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                GridPartParam gpp = null;
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = "正在从服务器接收数据...";
                }
                recved = true;
                InjectionGridInfo info = new InjectionGridInfo();
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_GPRS
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_AMR
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_PS
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_TDSCDMA_VP
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_AMR
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PS
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_VP
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_WCDMA_PSHS
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_CDMA_V
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_CDMA_D
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_CDMA2000_D
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_GSM
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_TDSCDMA
                      || package.Content.Type == ResponseType.KPI_LTE_AMR
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_LTE_TOPN
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_LTE_FDD_AMR
                      || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_NR
                      || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_NR
                      || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM)
                {
                    gpp = getGridPartParam(package, curImgColumnDef, info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }
                setInjectGridUnit(gpp, info);

                setProgressPercent(ref counter, ref curPercent);
            }
        }

        class InjectionGridInfo
        {
            public int FileID { get; set; }
            public double Duration { get; set; }
            public double Distance { get; set; }
        }

        private GridPartParam getGridPartParam(Package package, List<StatImgDefItem> curImgColumnDef, InjectionGridInfo info)
        {
            GridPartParam gpp;
            double longi = package.Content.GetParamDouble();
            double lati = package.Content.GetParamDouble();
            gpp = new GridPartParam();
            gpp.LTLng = longi;
            gpp.LTLat = lati;
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                DataItem dItemFileid = null;
                if (cellStatInfoDic.TryGetValue("0806", out dItemFileid) || cellStatInfoDic.TryGetValue("80040006", out dItemFileid))
                {
                    info.Distance += double.Parse(dItemFileid.Value.ToString());
                }
                if (cellStatInfoDic.TryGetValue("0805", out dItemFileid) || cellStatInfoDic.TryGetValue("80040005", out dItemFileid))
                {
                    info.Duration += double.Parse(dItemFileid.Value.ToString());
                }
                if (cellStatInfoDic.TryGetValue("0801", out dItemFileid) || cellStatInfoDic.TryGetValue("80040001", out dItemFileid))
                {
                    info.FileID = (int)dItemFileid.Value;
                    break;
                }
            }

            return gpp;
        }

        private void setInjectGridUnit(GridPartParam gpp, InjectionGridInfo info)
        {
            if (gpp != null && isValidPoint(gpp))
            {
                InjectGridUnit igu = injectGridMatrix[gpp.RowIdx, gpp.ColIdx];
                if (igu == null)
                {
                    igu = new InjectGridUnit();
                    igu.LTLng = gpp.LTLng;
                    igu.LTLat = gpp.LTLat;
                    injectGridMatrix[gpp.RowIdx, gpp.ColIdx] = igu;
                    igu.Status++;
                }
                igu.repeatCount++;
                igu.fileRepeatDic[info.FileID] = true;
                igu.TestDistance += info.Distance;
                igu.TestDuration += info.Duration;
            }
        }

        protected virtual void prepareQueryPackage(Package package, TimePeriod period)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (condition.ServiceTypes.Contains(55))
            {
                if (condition.ServiceTypes.Count == 1)
                {
                    //仅选择NB SCAN时才查询NB扫频渗透率
                    package.Content.Type = RequestType.REQTYPE_DIY_STATI_NB_SCAN_GRID;
                }
                else
                {
                    condition.ServiceTypes.Remove(55);
                    package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_GRID;
                }
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_GRID;
            }
            package.Content.PrepareAddParam();
            AddDIYPeriod(package, period);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            if (bBySelectedCarrierArea)
            {
                List<int> lstCarrierId = new List<int>();
                lstCarrierId.Add(this.carrierID);
                AddDIYCarrierType(package, lstCarrierId);
                AddDIYRoundedRegion(package, rr.Shape);
            }
            else
            {
                AddDIYCarrierType(package, condition.CarrierTypes);
                AddDIYRoundedRegion(package, condition.Geometorys.Region);
            }
            AddDIYFileFilter(package, condition);
            AddDIYFileID(package);
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYEndOpFlag(package);
        }

        protected virtual bool isValidPoint(GridPartParam data)
        {
            return true;
        }

        /// <summary>
        /// 传送圆整后的区域
        /// </summary>
        /// <param name="package"></param>
        /// <param name="fg"></param>
        protected void AddDIYRoundedRegion(Package package, MapWinGIS.Shape fg)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            DbRect rect = GridHelper.RoundRectByDefaultSizeGrid(MapOperation.GetShapeBounds(fg));
            package.Content.AddParam(rect.x1);
            package.Content.AddParam(rect.y2);
            package.Content.AddParam(rect.x2);
            package.Content.AddParam(rect.y1);
        }

        protected void AddDIYFileID(Package package)
        {
            try
            {
                if (condition.FileInfos != null && condition.FileInfos.Count > 0)
                {
                    package.Content.AddParam((byte)OpOptionDef.InSelect);
                    package.Content.AddParam("0,1,1");
                    StringBuilder strFileIds = new StringBuilder();
                    foreach (FileInfo fi in condition.FileInfos)
                    {
                        strFileIds.Append("" + fi.ID + ",");
                    }
                    package.Content.AddParam(strFileIds.ToString() + "0");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
    }
}
