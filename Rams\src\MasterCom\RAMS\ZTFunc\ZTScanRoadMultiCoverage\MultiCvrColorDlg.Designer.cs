﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class MultiCvrColorDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MultiCvrColorDlg));
            this.label1 = new System.Windows.Forms.Label();
            this.colorEditInvalidPoint = new DevExpress.XtraEditors.ColorEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.colorEdit1800Invalidate = new DevExpress.XtraEditors.ColorEdit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEditInvalidPoint.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit1800Invalidate.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(48, 234);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(71, 12);
            this.label1.TabIndex = 7;
            this.label1.Text = "无效点颜色:";
            // 
            // colorEditInvalidPoint
            // 
            this.colorEditInvalidPoint.EditValue = System.Drawing.Color.Gray;
            this.colorEditInvalidPoint.Location = new System.Drawing.Point(125, 231);
            this.colorEditInvalidPoint.Name = "colorEditInvalidPoint";
            this.colorEditInvalidPoint.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEditInvalidPoint.Properties.ShowWebColors = false;
            this.colorEditInvalidPoint.Size = new System.Drawing.Size(50, 21);
            this.colorEditInvalidPoint.TabIndex = 8;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(36, 262);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(83, 12);
            this.label2.TabIndex = 7;
            this.label2.Text = "非主控点颜色:";
            // 
            // colorEdit1800Invalidate
            // 
            this.colorEdit1800Invalidate.EditValue = System.Drawing.Color.Olive;
            this.colorEdit1800Invalidate.Location = new System.Drawing.Point(125, 258);
            this.colorEdit1800Invalidate.Name = "colorEdit1800Invalidate";
            this.colorEdit1800Invalidate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEdit1800Invalidate.Properties.ShowWebColors = false;
            this.colorEdit1800Invalidate.Size = new System.Drawing.Size(50, 21);
            this.colorEdit1800Invalidate.TabIndex = 8;
            // 
            // MultiCvrColorDlg
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("MultiCvrColorDlg.Appearance.Image")));
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(384, 281);
            this.Controls.Add(this.colorEdit1800Invalidate);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.colorEditInvalidPoint);
            this.Controls.Add(this.label1);
            this.Name = "MultiCvrColorDlg";
            this.Text = "颜色设置";
            this.Controls.SetChildIndex(this.label1, 0);
            this.Controls.SetChildIndex(this.colorEditInvalidPoint, 0);
            this.Controls.SetChildIndex(this.label2, 0);
            this.Controls.SetChildIndex(this.colorEdit1800Invalidate, 0);
            ((System.ComponentModel.ISupportInitialize)(this.colorEditInvalidPoint.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorEdit1800Invalidate.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.ColorEdit colorEditInvalidPoint;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.ColorEdit colorEdit1800Invalidate;
    }
}