﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using CQTLibrary.PublicItem;
using MasterCom.Util;
using CQTLibrary.CqtZTFunc;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public class QueryCQTComplainTrack : QueryBase 
    {
        public QueryCQTComplainTrack(MainModel mainModel)
            : base(mainModel)
        {
            mainmodel = mainModel;
        }
        public int Roundid { get; set; }

        private readonly MainModel mainmodel;
         
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return "CQT投诉跟踪"; }
        }

        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }

        /// <summary>
        /// 判断是否选择区域
        /// </summary>
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21029, this.Name);//临时
        }
        
        public List<ComplainResult> ComplainResultList { get; set; }
        public Dictionary<AreaKey, AreaDetail> CqtDetailDic { get; set; }
        public Dictionary<string, AreaDetail> InitcqtDetailDic { get; set; }
        public List<CQTLibrary.PublicItem.ParaColumnItem> Paraitem { get; set; }
        public CQTLibrary.RAMS.NET.MainModel CqtModel { get; set; }
        public List<TimePeriod> TpList { get; set; }
        /// <summary>
        /// 查询接口
        /// </summary>
        protected override void query()
        {
            MainModel.QueryCondition = this.condition;
            WaitBox.Text = "准备查询...";
            WaitBox.CanCancel = true;
            WaitBox.Show("开始接收统计数据...", queryStatData);
            FrmCQTComplainOverview overviewFrm = new FrmCQTComplainOverview(mainmodel);
            overviewFrm.InitcqtDetailDic = InitcqtDetailDic;
            overviewFrm.TpList = TpList;
            overviewFrm.Paraitem = Paraitem;
            overviewFrm.CqtModel = this.CqtModel;
            overviewFrm.ComplainResultList = this.ComplainResultList;
            overviewFrm.gridControl1.DataSource = ComplainResultList;
            overviewFrm.gridControl1.RefreshDataSource();
            overviewFrm.Show();
        }

        /// <summary>
        /// 查询统计数据
        /// </summary>
        protected void queryStatData()
        {
            try
            {
                QueryCondition queryCond = this.condition;
                TpList = queryCond.Periods;
                CqtModel = new CQTLibrary.RAMS.NET.MainModel();
                CqtModel.DistrictID = MainModel.DistrictID;
                CqtModel.UserName = MainModel.User.LoginName;
                CqtModel.UserPass = MainModel.User.Password;
                CqtModel.ServerIP = MainModel.Server.IP;
                CqtModel.ServerPort = MainModel.Server.Port;
                CQTLibrary.DbManage.IOSearch io = new CQTLibrary.DbManage.IOSearch();
                string cfgFileName = Application.StartupPath + @"\userData\CqtFormulaCfg.xml";
                Paraitem = io.getConfigXml(cfgFileName);
                ComplainAna compAna = new ComplainAna();
                WaitBox.Text = "正在初始化数据...";
                WaitBox.ProgressPercent = 20;
                CqtDetailDic = compAna.intCqtDetailInfo(CqtModel);
                InitcqtDetailDic = compAna.intCqtDetailInfo(CqtModel, CqtDetailDic);
                WaitBox.Text = "正在统计数据...";
                WaitBox.ProgressPercent = 60;
                ComplainResultList = compAna.complainZtFunc(TpList[0].BeginTime, TpList[0].EndTime, CqtModel, Paraitem, CqtDetailDic);
                WaitBox.Text = "统计完成...";
                WaitBox.ProgressPercent = 90;
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }
    }
}
