﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYQueryTAUHandoverTooMuchLTENew : ZTDIYQueryHandoverTooMuchNew
    {
        private static ZTDIYQueryTAUHandoverTooMuchLTENew intance = null;
        public new static ZTDIYQueryTAUHandoverTooMuchLTENew GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYQueryTAUHandoverTooMuchLTENew();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYQueryTAUHandoverTooMuchLTENew()
            : base()
        {
            MainModel.NeedType = true;
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get
            {
                return "跟踪区频繁更新-按区域(LTE新版)";
            }
        }

        protected override void initStatEventIDs()
        {
            statEventIDs.Clear();
            statEventIDs.Add(853);
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                fileDataManager.Events = filterEvents(fileDataManager.Events);
                HandoverFileDataManagerNew curHandoverFile = HandoverAndReselectionManagerNew.GetHandoverTooMuchResult(fileDataManager,
                timeLimit, distanceLimit, handoverCount);
                if (curHandoverFile.HandoverTimes > 0)
                {
                    curHandoverFile.fmnger.TestPoints = filterTestPointsByEvents(curHandoverFile.Events, fileDataManager.TestPoints);
                    HandoverAndReselectionManagerNew.GetHandoverToMuchDetails(curHandoverFile, curHandoverFile.fmnger);
                    curHandoverFile.Index = handoverFileList.Count + 1;
                    handoverFileList.Add(curHandoverFile);
                    handoverEvents.AddRange(curHandoverFile.Events);
                }

                if (!MainModel.IsBackground)
                {
                    fileDataManager.ClearDTDatas();
                    fileDataManager.ClearTestPoints();
                }
            }
        }

        protected override void fireShowForm()
        {
            ZTTAUHandoverTooMuchNewForm frm = null;
            frm = MainModel.CreateResultForm(typeof(ZTTAUHandoverTooMuchNewForm)) as ZTTAUHandoverTooMuchNewForm;
            frm.FillData(handoverFileList, handoverEvents);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class ZTDIYQueryTAUHandoverTooMuchLTE_FDDNew : ZTDIYQueryTAUHandoverTooMuchLTENew
    {
        private static ZTDIYQueryTAUHandoverTooMuchLTE_FDDNew instance = null;
        public static new ZTDIYQueryTAUHandoverTooMuchLTE_FDDNew GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYQueryTAUHandoverTooMuchLTE_FDDNew();
                    }
                }
            }
            return instance;
        }

        protected ZTDIYQueryTAUHandoverTooMuchLTE_FDDNew()
            : base()
        {
            carrierID = CarrierType.ChinaUnicom;
        }

        public override string Name
        {
            get
            {
                return "跟踪区频繁更新-按区域(LTE_FDD新版)";
            }
        }

        protected override void initStatEventIDs()
        {
            statEventIDs.Clear();
            statEventIDs.Add(3172);
        }
    }

    public class ZTDIYQueryTAUHandoverTooMuchByFileNew : ZTDIYQueryTAUHandoverTooMuchLTENew
    {
        public ZTDIYQueryTAUHandoverTooMuchByFileNew()
            : base()
        {
        }
        private static ZTDIYQueryTAUHandoverTooMuchByFileNew intance = null;
        public static new ZTDIYQueryTAUHandoverTooMuchByFileNew GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYQueryTAUHandoverTooMuchByFileNew();
                    }
                }
            }
            return intance;
        }
        public override string Name
        {
            get { return "跟踪区频繁更新-按文件(LTE新版)"; }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }
        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
    }
}
