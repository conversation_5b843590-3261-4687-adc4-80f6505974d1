﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using AxMapWinGIS;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using System.Drawing.Drawing2D;
using System.IO;

namespace MasterCom.RAMS.MapControlTool
{
    class MapControlToolAddLineMark
    {
        private readonly MapOperation mapOP;
        private readonly MapForm mf;
        private readonly AxMap mapControl;
        private bool isActive = false;

        public MapControlToolAddLineMark(MapForm mapForm, AxMap map)
        {
            mapControl = map;
            mapOP = new MapOperation(mapControl);
            mf = mapForm;
            MasterCom.RAMS.Model.MainModel.GetInstance().ClearDataEvent += new EventHandler(mapForm_ClearDataEvent);
        }

        void mapForm_ClearDataEvent(object sender, EventArgs e)
        {
            //
        }

        public void Activate()
        {
            if (!isActive)
            {
                mapControl.MouseDownEvent += mapControl_MouseDownEvent;
                mapControl.DblClick += mapControl_DbClick;
            }
            isActive = true;
            mapControl.CursorMode = tkCursorMode.cmNone;
            mapControl.MapCursor = tkCursor.crsrCross;
            graphics = mapControl.CreateGraphics();
        }

        public void Deactivate()
        {
            if (!isActive) return;
            mapControl.MouseDownEvent -= mapControl_MouseDownEvent;
            mapControl.DblClick -= mapControl_DbClick;
            isActive = false;
            graphics.Dispose();
            mf.SetStatusLabelZoom("");
        }

        private void mapControl_DbClick(object sender, EventArgs e)
        {
            if (!isActive)
            {
                return;
            }

            FinishDraw();
        }

        private void mapControl_MouseDownEvent(object sender, _DMapEvents_MouseDownEvent e)
        {
            if (!isActive)
            {
                return;
            }

            double x = 0, y = 0;
            mapControl.PixelToProj(e.x, e.y, ref x, ref y);
            points.Add(new DbPoint(x, y));

            if (e.button == 1)
            {
                ProcessMouseLeftDown();
            }
            else if (e.button == 2 && points.Count >= 2)
            {
                ProcessMouseRightDown();
            }
            else
            {
                points.Clear();
            }
        }

        private readonly DbPoint lastPoint = new DbPoint(0, 0);
        private void mapControl_MouseMove(object sender, _DMapEvents_MouseMoveEvent e)
        {
            if (!isActive)
            {
                return;
            }

            List<DbPoint> tmps = new List<DbPoint>(points);
            double x = 0, y = 0;
            mapControl.PixelToProj(e.x, e.y, ref x, ref y);
            lastPoint.x = x; lastPoint.y = y;
            tmps.Add(lastPoint);

            mapControl.Refresh();
            DrawMapWinGISLine(tmps);
        }

        private void ProcessMouseLeftDown()
        {
            if (points.Count == 1)
            {
                mapControl.MouseMoveEvent += mapControl_MouseMove;
                return;
            }

            List<DbPoint> tmps = new List<DbPoint>(points);
            DrawMapWinGISLine(tmps);
        }

        private void ProcessMouseRightDown()
        {
            FinishDraw();
        }

        private void FinishDraw()
        {
            if (points.Count < 2)
            {
                return;
            }

            mapControl.MouseMoveEvent -= mapControl_MouseMove;
            LineMarkCreateDlg dlg = new LineMarkCreateDlg(mf, points, distanceTotal);
            dlg.ShowDialog();
            points.Clear();
        }

        private double distanceTotal = 0;
        private void DrawMapWinGISLine(List<DbPoint> points)
        {
            if (points.Count < 2)
            {
                return;
            }

            PointF[] pArr;
            mapOP.ToDisplay(points.ToArray(), out pArr);
            graphics.DrawLines(linePen, pArr);

            distanceTotal = 0;
            for (int i = 1; i < points.Count; i++)
            {
                double distance = MasterCom.Util.MathFuncs.GetDistance(points[i].x, points[i].y, points[i - 1].x, points[i - 1].y);
                distanceTotal += distance;
            }
            mf.SetStatusLabelZoom(string.Format("总长:{0:F0}米", distanceTotal));
        }

        private readonly Pen linePen = new Pen(Color.Green, 2);
        private readonly List<DbPoint> points = new List<DbPoint>();
        Graphics graphics;
    }
}
