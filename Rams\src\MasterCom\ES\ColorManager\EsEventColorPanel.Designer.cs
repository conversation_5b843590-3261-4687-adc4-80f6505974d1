﻿namespace MasterCom.ES.ColorManager
{
    partial class EsEventColorPanel
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listViewComplex = new BrightIdeasSoftware.FilterObjectListView();
            this.name_olvc = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn1 = new BrightIdeasSoftware.OLVColumn();
            this.color_olvC = new BrightIdeasSoftware.OLVColumn();
            this.des_olvc = new BrightIdeasSoftware.OLVColumn();
            this.size_olvc = new BrightIdeasSoftware.OLVColumn();
            this.menu_cms = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.up_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.down_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.del_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1 = new System.Windows.Forms.Panel();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.config_tsb = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.tip_tssl = new System.Windows.Forms.ToolStripLabel();
            this.label1 = new System.Windows.Forms.Label();
            this.str_rtb = new System.Windows.Forms.RichTextBox();
            this.add_b = new System.Windows.Forms.Button();
            this.strs_b = new System.Windows.Forms.Button();
            this.color_tcp = new System.Windows.Forms.ColorControls.TabbedColorPicker();
            this.panel2 = new System.Windows.Forms.Panel();
            this.refresh_b = new System.Windows.Forms.Button();
            this.name_tb = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.listViewComplex)).BeginInit();
            this.menu_cms.SuspendLayout();
            this.panel1.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // listViewComplex
            // 
            this.listViewComplex.AllColumns.Add(this.name_olvc);
            this.listViewComplex.AllColumns.Add(this.olvColumn1);
            this.listViewComplex.AllColumns.Add(this.color_olvC);
            this.listViewComplex.AllColumns.Add(this.des_olvc);
            this.listViewComplex.AllColumns.Add(this.size_olvc);
            this.listViewComplex.AllowColumnReorder = true;
            this.listViewComplex.AllowDrop = true;
            this.listViewComplex.AlternateRowBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(240)))), ((int)(((byte)(220)))));
            this.listViewComplex.BackColor = System.Drawing.SystemColors.Window;
            this.listViewComplex.CellEditActivation = BrightIdeasSoftware.ObjectListView.CellEditActivateMode.DoubleClick;
            this.listViewComplex.CheckBoxes = true;
            this.listViewComplex.CheckedAspectName = "IsChecked";
            this.listViewComplex.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.name_olvc,
            this.olvColumn1,
            this.color_olvC,
            this.des_olvc,
            this.size_olvc});
            this.listViewComplex.ContextMenuStrip = this.menu_cms;
            this.listViewComplex.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewComplex.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewComplex.EmptyListMsg = "列表没有数据";
            this.listViewComplex.FullRowSelect = true;
            this.listViewComplex.GridLines = true;
            this.listViewComplex.GroupWithItemCountFormat = "{0} ({1} 条)";
            this.listViewComplex.GroupWithItemCountSingularFormat = "{0} ({1} 条)";
            this.listViewComplex.HeaderWordWrap = true;
            this.listViewComplex.HideSelection = false;
            this.listViewComplex.IsNeedShowOverlay = false;
            this.listViewComplex.IsSimpleDragSource = true;
            this.listViewComplex.IsSimpleDropSink = true;
            this.listViewComplex.Location = new System.Drawing.Point(0, 0);
            this.listViewComplex.MenuLabelGroupBy = "\'{0}\'分组";
            this.listViewComplex.MenuLabelLockGroupingOn = "锁定分组 \'{0}\'";
            this.listViewComplex.MenuLabelSortAscending = "\'{0}\'升序";
            this.listViewComplex.MenuLabelSortDescending = "\'{0}\'降序";
            this.listViewComplex.MenuLabelTurnOffGroups = "关闭分组";
            this.listViewComplex.MenuLabelUnlockGroupingOn = "解锁分组 \'{0}\'";
            this.listViewComplex.MenuLabelUnsort = "取消排序";
            this.listViewComplex.MultiSelect = false;
            this.listViewComplex.Name = "listViewComplex";
            this.listViewComplex.OverlayImage.Alignment = System.Drawing.ContentAlignment.BottomLeft;
            this.listViewComplex.OverlayText.Alignment = System.Drawing.ContentAlignment.TopRight;
            this.listViewComplex.OverlayText.BorderColor = System.Drawing.Color.DarkRed;
            this.listViewComplex.OverlayText.BorderWidth = 4F;
            this.listViewComplex.OverlayText.InsetX = 10;
            this.listViewComplex.OverlayText.InsetY = 35;
            this.listViewComplex.OverlayText.Rotation = 20;
            this.listViewComplex.OverlayText.Text = "";
            this.listViewComplex.OverlayText.TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.listViewComplex.OwnerDraw = true;
            this.listViewComplex.ShowCommandMenuOnRightClick = true;
            this.listViewComplex.ShowGroups = false;
            this.listViewComplex.ShowImagesOnSubItems = true;
            this.listViewComplex.ShowItemCountOnGroups = true;
            this.listViewComplex.ShowItemToolTips = true;
            this.listViewComplex.ShowSortIndicators = false;
            this.listViewComplex.Size = new System.Drawing.Size(621, 283);
            this.listViewComplex.SpaceBetweenGroups = 20;
            this.listViewComplex.TabIndex = 4;
            this.listViewComplex.UseAlternatingBackColors = true;
            this.listViewComplex.UseCompatibleStateImageBehavior = false;
            this.listViewComplex.UseHotItem = true;
            this.listViewComplex.UseTranslucentHotItem = true;
            this.listViewComplex.UseTranslucentSelection = true;
            this.listViewComplex.View = System.Windows.Forms.View.Details;
            this.listViewComplex.SelectedIndexChanged += new System.EventHandler(this.listViewComplex_SelectedIndexChanged);
            // 
            // name_olvc
            // 
            this.name_olvc.AspectName = "Name";
            this.name_olvc.HeaderFont = null;
            this.name_olvc.Text = "问题名称";
            this.name_olvc.Width = 113;
            // 
            // olvColumn1
            // 
            this.olvColumn1.AspectName = "Expression";
            this.olvColumn1.HeaderFont = null;
            this.olvColumn1.IsTileViewColumn = true;
            this.olvColumn1.Text = "表达式";
            this.olvColumn1.Width = 283;
            // 
            // color_olvC
            // 
            this.color_olvC.AspectName = "Color";
            this.color_olvC.HeaderFont = null;
            this.color_olvC.Text = "颜色";
            // 
            // des_olvc
            // 
            this.des_olvc.AspectName = "Des";
            this.des_olvc.HeaderFont = null;
            this.des_olvc.Text = "问题描述";
            this.des_olvc.Width = 130;
            // 
            // size_olvc
            // 
            this.size_olvc.AspectName = "Size";
            this.size_olvc.HeaderFont = null;
            this.size_olvc.Text = "大小";
            // 
            // menu_cms
            // 
            this.menu_cms.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.up_tsmi,
            this.down_tsmi,
            this.toolStripSeparator1,
            this.del_tsmi});
            this.menu_cms.Name = "menu_cms";
            this.menu_cms.Size = new System.Drawing.Size(101, 76);
            this.menu_cms.Opening += new System.ComponentModel.CancelEventHandler(this.menu_cms_Opening);
            // 
            // up_tsmi
            // 
            this.up_tsmi.Name = "up_tsmi";
            this.up_tsmi.Size = new System.Drawing.Size(100, 22);
            this.up_tsmi.Text = "上移";
            this.up_tsmi.Click += new System.EventHandler(this.up_tsmi_Click);
            // 
            // down_tsmi
            // 
            this.down_tsmi.Name = "down_tsmi";
            this.down_tsmi.Size = new System.Drawing.Size(100, 22);
            this.down_tsmi.Text = "下移";
            this.down_tsmi.Click += new System.EventHandler(this.down_tsmi_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(97, 6);
            // 
            // del_tsmi
            // 
            this.del_tsmi.Name = "del_tsmi";
            this.del_tsmi.Size = new System.Drawing.Size(100, 22);
            this.del_tsmi.Text = "删除";
            this.del_tsmi.Click += new System.EventHandler(this.del_tsmi_Click);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.toolStrip1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1.Location = new System.Drawing.Point(0, 307);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(621, 23);
            this.panel1.TabIndex = 5;
            // 
            // toolStrip1
            // 
            this.toolStrip1.BackColor = System.Drawing.SystemColors.Control;
            this.toolStrip1.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.config_tsb,
            this.toolStripSeparator2,
            this.tip_tssl});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(621, 25);
            this.toolStrip1.TabIndex = 2;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // config_tsb
            // 
            this.config_tsb.CheckOnClick = true;
            this.config_tsb.Image = global::MasterCom.RAMS.Properties.Resources.frozen;
            this.config_tsb.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.config_tsb.Name = "config_tsb";
            this.config_tsb.Size = new System.Drawing.Size(76, 22);
            this.config_tsb.Text = "配置模式";
            this.config_tsb.CheckedChanged += new System.EventHandler(this.config_tsb_CheckedChanged);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(6, 25);
            // 
            // tip_tssl
            // 
            this.tip_tssl.Name = "tip_tssl";
            this.tip_tssl.Size = new System.Drawing.Size(32, 22);
            this.tip_tssl.Text = "就绪";
            this.tip_tssl.Visible = false;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Dock = System.Windows.Forms.DockStyle.Left;
            this.label1.Location = new System.Drawing.Point(111, 0);
            this.label1.Name = "label1";
            this.label1.Padding = new System.Windows.Forms.Padding(0, 5, 0, 0);
            this.label1.Size = new System.Drawing.Size(53, 17);
            this.label1.TabIndex = 6;
            this.label1.Text = "表达式：";
            // 
            // str_rtb
            // 
            this.str_rtb.Dock = System.Windows.Forms.DockStyle.Fill;
            this.str_rtb.Location = new System.Drawing.Point(164, 0);
            this.str_rtb.Multiline = false;
            this.str_rtb.Name = "str_rtb";
            this.str_rtb.Size = new System.Drawing.Size(457, 24);
            this.str_rtb.TabIndex = 0;
            this.str_rtb.Text = "";
            this.str_rtb.TextChanged += new System.EventHandler(this.str_rtb_TextChanged);
            // 
            // add_b
            // 
            this.add_b.Dock = System.Windows.Forms.DockStyle.Right;
            this.add_b.Enabled = false;
            this.add_b.Location = new System.Drawing.Point(545, 0);
            this.add_b.Name = "add_b";
            this.add_b.Size = new System.Drawing.Size(38, 24);
            this.add_b.TabIndex = 1;
            this.add_b.Text = "添加";
            this.add_b.UseVisualStyleBackColor = true;
            this.add_b.Click += new System.EventHandler(this.add_b_Click);
            // 
            // strs_b
            // 
            this.strs_b.Dock = System.Windows.Forms.DockStyle.Right;
            this.strs_b.Location = new System.Drawing.Point(478, 0);
            this.strs_b.Name = "strs_b";
            this.strs_b.Size = new System.Drawing.Size(22, 24);
            this.strs_b.TabIndex = 3;
            this.strs_b.Text = ">";
            this.strs_b.UseVisualStyleBackColor = true;
            this.strs_b.Click += new System.EventHandler(this.strs_b_Click);
            // 
            // color_tcp
            // 
            this.color_tcp.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.color_tcp.Dock = System.Windows.Forms.DockStyle.Right;
            this.color_tcp.Location = new System.Drawing.Point(500, 0);
            this.color_tcp.Name = "color_tcp";
            this.color_tcp.Size = new System.Drawing.Size(45, 24);
            this.color_tcp.TabIndex = 5;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.strs_b);
            this.panel2.Controls.Add(this.color_tcp);
            this.panel2.Controls.Add(this.add_b);
            this.panel2.Controls.Add(this.refresh_b);
            this.panel2.Controls.Add(this.str_rtb);
            this.panel2.Controls.Add(this.label1);
            this.panel2.Controls.Add(this.name_tb);
            this.panel2.Controls.Add(this.label2);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel2.Location = new System.Drawing.Point(0, 283);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(621, 24);
            this.panel2.TabIndex = 6;
            this.panel2.Visible = false;
            // 
            // refresh_b
            // 
            this.refresh_b.Dock = System.Windows.Forms.DockStyle.Right;
            this.refresh_b.Location = new System.Drawing.Point(583, 0);
            this.refresh_b.Name = "refresh_b";
            this.refresh_b.Size = new System.Drawing.Size(38, 24);
            this.refresh_b.TabIndex = 9;
            this.refresh_b.Text = "刷新";
            this.refresh_b.UseVisualStyleBackColor = true;
            this.refresh_b.Click += new System.EventHandler(this.refresh_b_Click);
            // 
            // name_tb
            // 
            this.name_tb.Dock = System.Windows.Forms.DockStyle.Left;
            this.name_tb.Location = new System.Drawing.Point(41, 0);
            this.name_tb.Name = "name_tb";
            this.name_tb.Size = new System.Drawing.Size(70, 21);
            this.name_tb.TabIndex = 8;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Dock = System.Windows.Forms.DockStyle.Left;
            this.label2.Location = new System.Drawing.Point(0, 0);
            this.label2.Name = "label2";
            this.label2.Padding = new System.Windows.Forms.Padding(0, 5, 0, 0);
            this.label2.Size = new System.Drawing.Size(41, 17);
            this.label2.TabIndex = 7;
            this.label2.Text = "名称：";
            // 
            // EsEventColorPanel
            // 
            this.Controls.Add(this.listViewComplex);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "EsEventColorPanel";
            this.Size = new System.Drawing.Size(621, 330);
            ((System.ComponentModel.ISupportInitialize)(this.listViewComplex)).EndInit();
            this.menu_cms.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.FilterObjectListView listViewComplex;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
        private System.Windows.Forms.Panel panel1;
        private BrightIdeasSoftware.OLVColumn color_olvC;
        private BrightIdeasSoftware.OLVColumn des_olvc;
        private System.Windows.Forms.ContextMenuStrip menu_cms;
        private System.Windows.Forms.ToolStripMenuItem del_tsmi;
        private System.Windows.Forms.ToolStripMenuItem up_tsmi;
        private System.Windows.Forms.ToolStripMenuItem down_tsmi;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private BrightIdeasSoftware.OLVColumn size_olvc;
        private BrightIdeasSoftware.OLVColumn name_olvc;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripButton config_tsb;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RichTextBox str_rtb;
        private System.Windows.Forms.Button add_b;
        private System.Windows.Forms.Button strs_b;
        private System.Windows.Forms.ColorControls.TabbedColorPicker color_tcp;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.TextBox name_tb;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ToolStripLabel tip_tssl;
        private System.Windows.Forms.Button refresh_b;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
    }
}