﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CompeteBasePanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.dateTimePickerStart = new System.Windows.Forms.DateTimePicker();
            this.label1 = new System.Windows.Forms.Label();
            this.dateTimePickerEnd = new System.Windows.Forms.DateTimePicker();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.radioGroupFileFilter = new DevExpress.XtraEditors.RadioGroup();
            this.textBoxFileName = new System.Windows.Forms.TextBox();
            this.checkEditFilter = new DevExpress.XtraEditors.CheckEdit();
            this.attributePanelProj = new MasterCom.RAMS.ZTFunc.AttributePanel();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupFileFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditFilter.Properties)).BeginInit();
            this.tableLayoutPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.dateTimePickerStart);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.dateTimePickerEnd);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Location = new System.Drawing.Point(3, 3);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(278, 76);
            this.groupBox2.TabIndex = 8;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "时间";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(15, 53);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "结束时间：";
            // 
            // dateTimePickerStart
            // 
            this.dateTimePickerStart.Location = new System.Drawing.Point(85, 20);
            this.dateTimePickerStart.Name = "dateTimePickerStart";
            this.dateTimePickerStart.Size = new System.Drawing.Size(187, 21);
            this.dateTimePickerStart.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(15, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "开始时间：";
            // 
            // dateTimePickerEnd
            // 
            this.dateTimePickerEnd.Location = new System.Drawing.Point(85, 47);
            this.dateTimePickerEnd.Name = "dateTimePickerEnd";
            this.dateTimePickerEnd.Size = new System.Drawing.Size(187, 21);
            this.dateTimePickerEnd.TabIndex = 0;
            // 
            // groupControl1
            // 
            this.groupControl1.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.groupControl1.Appearance.Options.UseBackColor = true;
            this.groupControl1.Controls.Add(this.radioGroupFileFilter);
            this.groupControl1.Controls.Add(this.textBoxFileName);
            this.groupControl1.Controls.Add(this.checkEditFilter);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(3, 260);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(278, 76);
            this.groupControl1.TabIndex = 10;
            // 
            // radioGroupFileFilter
            // 
            this.radioGroupFileFilter.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.radioGroupFileFilter.EditValue = true;
            this.radioGroupFileFilter.Enabled = false;
            this.radioGroupFileFilter.Location = new System.Drawing.Point(4, 25);
            this.radioGroupFileFilter.Margin = new System.Windows.Forms.Padding(2);
            this.radioGroupFileFilter.Name = "radioGroupFileFilter";
            this.radioGroupFileFilter.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.radioGroupFileFilter.Properties.Appearance.Options.UseBackColor = true;
            this.radioGroupFileFilter.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.radioGroupFileFilter.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "文件名"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "路径"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "备注"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "标记"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "高级")});
            this.radioGroupFileFilter.Size = new System.Drawing.Size(287, 24);
            this.radioGroupFileFilter.TabIndex = 3;
            // 
            // textBoxFileName
            // 
            this.textBoxFileName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFileName.Location = new System.Drawing.Point(7, 51);
            this.textBoxFileName.Name = "textBoxFileName";
            this.textBoxFileName.Size = new System.Drawing.Size(266, 21);
            this.textBoxFileName.TabIndex = 1;
            // 
            // checkEditFilter
            // 
            this.checkEditFilter.Location = new System.Drawing.Point(5, 4);
            this.checkEditFilter.Name = "checkEditFilter";
            this.checkEditFilter.Properties.Caption = "文件筛选：";
            this.checkEditFilter.Size = new System.Drawing.Size(75, 19);
            this.checkEditFilter.TabIndex = 0;
            this.checkEditFilter.CheckedChanged += new System.EventHandler(this.checkEditFilter_CheckedChanged);
            // 
            // attributePanelProj
            // 
            this.attributePanelProj.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.attributePanelProj.Dock = System.Windows.Forms.DockStyle.Fill;
            this.attributePanelProj.Location = new System.Drawing.Point(3, 85);
            this.attributePanelProj.Name = "attributePanelProj";
            this.attributePanelProj.Size = new System.Drawing.Size(278, 169);
            this.attributePanelProj.TabIndex = 9;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.groupBox2, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.groupControl1, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.attributePanelProj, 0, 1);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 3;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 82F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 82F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(284, 339);
            this.tableLayoutPanel1.TabIndex = 11;
            // 
            // CompeteBasePanel
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.Controls.Add(this.tableLayoutPanel1);
            this.Name = "CompeteBasePanel";
            this.Size = new System.Drawing.Size(284, 339);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupFileFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditFilter.Properties)).EndInit();
            this.tableLayoutPanel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private AttributePanel attributePanelProj;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.DateTimePicker dateTimePickerStart;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DateTimePicker dateTimePickerEnd;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.RadioGroup radioGroupFileFilter;
        private System.Windows.Forms.TextBox textBoxFileName;
        private DevExpress.XtraEditors.CheckEdit checkEditFilter;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
    }
}
