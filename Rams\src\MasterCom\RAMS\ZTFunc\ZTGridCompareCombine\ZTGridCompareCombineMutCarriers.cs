﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;


namespace MasterCom.RAMS.Net
{
    public class ZTGridCompareCombineMutCarriers : ZTGridCompareCombineBase
    {
        public ZTGridCompareCombineMutCarriers(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "速率同时劣于联通电信路段(5M至25M)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {            
			return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22075, "查询");        
		}
        public GridMatrix<ColorUnit> ydCurGridColorUnitMatrix { get; set; }
        public GridMatrix<ColorUnit> ltCurGridColorUnitMatrix { get; set; }
        public GridMatrix<ColorUnit> dxCurGridColorUnitMatrix { get; set; }
        /// <summary>
        /// 设置查询指标
        /// </summary>
        /// <returns></returns>
        protected override bool getConditionBeforeQuery()
        {
            if (condition.CarrierTypes.Count < 3)
            {
                MessageBox.Show("运营商选择有误，需勾选全部运营商");
                return false;
            }
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            isShowSetForm = true;
            List<string> formulaSet = new List<string>();
            formulaSet.Add(strTDDDownTime);
            formulaSet.Add(strTDDDownSize);
            formulaSet.Add(strTDDSampleNum);
            formulaSet.Add(strFDDDownTime);
            formulaSet.Add(strFDDDownSize);
            formulaSet.Add(strFDDSampleNum);
            statImgIDSet = getTriadIDIgnoreServiceType(formulaSet);
            return true;
        }

        /// <summary>
        /// 重写三个运营商的数据获取
        /// </summary>
        /// <param name="o"></param>
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;

                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                WaitBox.Text = strCityName + " 开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();


                ydCurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                ltCurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                dxCurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                foreach (TimePeriod period in condition.Periods)
                {
                    int[] iCarrList = {1,2,3 };
                    condition.CarrierTypes.Clear();
                    foreach (int iCarr in iCarrList)
                    {
                        condition.CarrierTypes.Add(iCarr);
                        if (iCarr == 1)
                        {
                            strCarrName = " 移动 ";
                            queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                            ydCurGridColorUnitMatrix = MainModel.CurGridColorUnitMatrix;
                        }
                        else if (iCarr == 2)
                        {
                            strCarrName = " 联通 ";
                            queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                            ltCurGridColorUnitMatrix = MainModel.CurGridColorUnitMatrix;
                        }
                        else if (iCarr == 3)
                        {
                            strCarrName = " 电信 ";
                            queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                            dxCurGridColorUnitMatrix = MainModel.CurGridColorUnitMatrix;
                        }
                        condition.CarrierTypes.Clear();
                        MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                    }
                    condition.CarrierTypes.AddRange(iCarrList);
                }
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;

                WaitBox.Text = strCityName + " 数据获取完毕，进行对比处理...";
                doCompare();
                WaitBox.Text = strCityName + " 对比完毕，进行栅格汇聚处理...";
                doCombine();
                gridFileNameListDic.Clear();

                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        /// <summary>
        /// 重写对比过程
        /// </summary>
        protected override void doCompare()
        {
            gridCountInfo = new GridCountInfo();
            List<CenterLongLat> gridCenterLongLat = new List<CenterLongLat>();
            foreach (ColorUnit cu in ydCurGridColorUnitMatrix)
            {
                MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cu.CenterLng, cu.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType != "" && gridName.strGridName != "")
                {
                    gridCountInfo.IHostGridCount++;
                    CenterLongLat ccl = new CenterLongLat(cu.CenterLng, cu.CenterLat);
                    if (!gridCenterLongLat.Contains(ccl))
                    {
                        gridCenterLongLat.Add(ccl);
                    }
                }
            }
            foreach (ColorUnit cu in ltCurGridColorUnitMatrix)
            {
                MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cu.CenterLng, cu.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType != "" && gridName.strGridName != "")
                {
                    gridCountInfo.IGuestGridCount++;
                    CenterLongLat ccl = new CenterLongLat(cu.CenterLng, cu.CenterLat);
                    if (!gridCenterLongLat.Contains(ccl))
                    {
                        gridCenterLongLat.Add(ccl);
                    }
                }
            }
            foreach (ColorUnit cu in dxCurGridColorUnitMatrix)
            {
                MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cu.CenterLng, cu.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType != "" && gridName.strGridName != "")
                {
                    gridCountInfo.IGuestDXGridCount++;
                    CenterLongLat ccl = new CenterLongLat(cu.CenterLng, cu.CenterLat);
                    if (!gridCenterLongLat.Contains(ccl))
                    {
                        gridCenterLongLat.Add(ccl);
                    }
                }
            }
            gridCountInfo.IAllGridCount = gridCenterLongLat.Count;
            gridCenterLongLat.Clear();
            foreach (ColorUnit cuYD in ydCurGridColorUnitMatrix)
            {
                MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cuYD.CenterLng, cuYD.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType == "" || gridName.strGridName == "")
                {
                    continue;
                }
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(cuYD.CenterLng, cuYD.CenterLat, out rAt, out cAt);
                ColorUnit cuLT = ltCurGridColorUnitMatrix[rAt, cAt];
                ColorUnit cuDX = dxCurGridColorUnitMatrix[rAt, cAt];
               
                if (cuYD == null || (cuLT == null && cuDX == null))
                {
                    continue;
                }
                StatDataLTE dataStatTDD = cuYD.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
                StatDataLTE_FDD dataStatFDD_LT = null;
                if (cuLT != null)
                {
                    dataStatFDD_LT = cuLT.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
                }
                StatDataLTE_FDD dataStatFDD_DX = null;
                if (cuDX != null)
                {
                    dataStatFDD_DX = cuDX.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
                }
                if (dataStatTDD == null || (dataStatFDD_LT == null && dataStatFDD_DX == null))
                {
                    continue;
                }

                GridFormalValeInfo gridFormalValeInfo = new GridFormalValeInfo();
                gridFormalValeInfo.dHostDownTime = cuYD.DataHub.CalcValueByFormula(strTDDDownTime);
                gridFormalValeInfo.dHostDownSize = cuYD.DataHub.CalcValueByFormula(strTDDDownSize);
                if (gridFormalValeInfo.dHostDownTime > 0 && gridFormalValeInfo.dHostDownSize >= 0)
                {
                    gridFormalValeInfo.dHostDownSpeed = gridFormalValeInfo.dHostDownSize / gridFormalValeInfo.dHostDownTime;
                }
                if (gridFormalValeInfo.dHostDownSpeed < 5 || gridFormalValeInfo.dHostDownSpeed > 25)
                {
                    continue;
                }
                if (dataStatFDD_LT != null)
                {
                    gridFormalValeInfo.dGuestDownTime = cuLT.DataHub.CalcValueByFormula(strFDDDownTime);
                    gridFormalValeInfo.dGuestDownSize = cuLT.DataHub.CalcValueByFormula(strFDDDownSize);
                    if (gridFormalValeInfo.dGuestDownTime > 0 && gridFormalValeInfo.dGuestDownSize >= 0)
                    {
                        gridFormalValeInfo.dGuestDownSpeed = gridFormalValeInfo.dGuestDownSize / gridFormalValeInfo.dGuestDownTime;
                    }
                }
                if (dataStatFDD_DX != null)
                {
                    gridFormalValeInfo.dGuestDownTime_dx = cuDX.DataHub.CalcValueByFormula(strFDDDownTime);
                    gridFormalValeInfo.dGuestDownSize_dx = cuDX.DataHub.CalcValueByFormula(strFDDDownSize);
                    if (gridFormalValeInfo.dGuestDownTime_dx > 0 && gridFormalValeInfo.dGuestDownSize_dx >= 0)
                    {
                        gridFormalValeInfo.dGuestDownSpeed_dx = gridFormalValeInfo.dGuestDownSize_dx / gridFormalValeInfo.dGuestDownTime_dx;
                    }
                }

                if (gridFormalValeInfo.dHostDownTime > 0 && (gridFormalValeInfo.dGuestDownTime > 0 || gridFormalValeInfo.dGuestDownTime_dx > 0))
                {
                    gridCountInfo.ICompareGridCount++;
                }

                if (gridFormalValeInfo.dHostDownTime <= 0 || gridFormalValeInfo.dGuestDownTime <= 0 || gridFormalValeInfo.dGuestDownTime_dx <= 0)
                {
                    continue;
                }

                if (gridFormalValeInfo.dHostDownSpeed < gridFormalValeInfo.dGuestDownSpeed * dPercent
                    && gridFormalValeInfo.dHostDownSpeed < gridFormalValeInfo.dGuestDownSpeed_dx * dPercent)
                {
                    GridColorUnit griCu = new GridColorUnit();
                    griCu.CuUnit = cuYD;
                    griCu.DHostSpeed = gridFormalValeInfo.dHostDownSpeed;
                    griCu.DGuestSpeed = gridFormalValeInfo.dGuestDownSpeed;
                    griCu.DGuestSpeed_dx = gridFormalValeInfo.dGuestDownSpeed_dx;
                    griCu.DHostMo = gridFormalValeInfo.dHostDownSize;
                    griCu.DHostBase = gridFormalValeInfo.dHostDownTime;
                    griCu.DGuestMo = gridFormalValeInfo.dGuestDownSize;
                    griCu.DGuestMo_dx = gridFormalValeInfo.dGuestDownSize_dx;
                    griCu.DGuestBase = gridFormalValeInfo.dGuestDownTime;
                    griCu.DGuestBase_dx = gridFormalValeInfo.dGuestDownTime_dx;
                    griCu.CuUnit.DataHub = null;//降低内存
                    if (!gridColorUnit.ContainsKey(gridName))
                    {
                        gridColorUnit[gridName] = new GridMatrix<GridColorUnit>();
                    }
                    gridColorUnit[gridName][rAt, cAt] = griCu;
                }
                cuYD.DataHub = null;
            }
        }
        /// <summary>
        /// 重写转化过程
        /// </summary>
        protected override GridCompareCombineInfo doChangGridCompareResult(GridCompareCombineBlock block)
        {
            if (block.Grids.Count == 0)
            {
                return null;
            }
            GridCompareCombineInfo gridItem = new GridCompareCombineInfo();
            double sumLng = 0;
            double sumLat = 0;
            double sumHostMo = 0;
            double sumHostBase = 0;
            double sumGuestMo = 0;
            double sumGuestBase = 0;
            double sumGuestMo_dx = 0;
            double sumGuestBase_dx = 0;

            StringBuilder sbLng = new StringBuilder();
            StringBuilder sbLat = new StringBuilder();
            foreach (GridColorUnit gridCU in block.Grids)
            {
                sumLng += gridCU.CuUnit.CenterLng;
                sumLat += gridCU.CuUnit.CenterLat;
                sbLng.Append(gridCU.CuUnit.CenterLng + ";");
                sbLat.Append(gridCU.CuUnit.CenterLat + ";");
                sumHostMo += gridCU.DHostMo;
                sumHostBase += gridCU.DHostBase;
                sumGuestMo += gridCU.DGuestMo;
                sumGuestBase += gridCU.DGuestBase;
                sumGuestMo_dx += gridCU.DGuestMo_dx;
                sumGuestBase_dx += gridCU.DGuestBase_dx;

                setGridStrFileName(gridItem, gridCU);
            }
            gridItem.StrLngList = sbLng.ToString();
            gridItem.StrLatList = sbLat.ToString();

            double dHostMeanVale = -999;
            double dGuestMeanVale = -999;
            double dGuestMeanVale_dx = -999;
            if (sumHostBase != 0)
            {
                dHostMeanVale = sumHostMo / sumHostBase;
            }
            if (sumGuestBase != 0)
            {
                dGuestMeanVale = sumGuestMo / sumGuestBase;
            }
            if (sumGuestBase_dx != 0)
            {
                dGuestMeanVale_dx = sumGuestMo_dx / sumGuestBase_dx;
            }
            gridItem.StrCompareInfo = "劣于联通,同时劣于电信";
            gridItem.StrProblemInfo = "移动下载速率：" + dHostMeanVale.ToString("0.00") + "M，联通下载速率："
                + dGuestMeanVale.ToString("0.00") + "M，电信下载速率：" + dGuestMeanVale_dx.ToString("0.00")
                + "M，连续栅格个数：" + block.Grids.Count + "个";
            gridItem.DLng = sumLng / block.Grids.Count;
            gridItem.DLat = sumLat / block.Grids.Count;
            gridItem.StrProblemType = "速率问题";
            return gridItem;
        }

        private void setGridStrFileName(GridCompareCombineInfo gridItem, GridColorUnit gridCU)
        {
            CenterLongLat cll = new CenterLongLat(gridCU.CuUnit.CenterLng, gridCU.CuUnit.CenterLat);
            if (gridFileNameListDic.ContainsKey(cll))
            {
                gridItem.StrFileName = "";
                StringBuilder sb = new StringBuilder();
                foreach (string strFileName in gridFileNameListDic[cll])
                {
                    if (strFileName.Contains("上传"))
                    {
                        continue;
                    }
                    if (!gridItem.StrFileName.Contains(strFileName))
                    {
                        sb.Append(strFileName + ";");
                    }
                    gridItem.StrFileName = sb.ToString();
                }
            }
        }

        /// <summary>
        /// 显示结果集
        /// </summary>
        protected override void fireShowResult()
        {
            if (cityGridCompareInfoDic.Count == 0)
            {
                MessageBox.Show("没有结果");
                return;
            }
            GridCompareCombineMutForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(GridCompareCombineMutForm).FullName);
            showForm = obj == null ? null : obj as GridCompareCombineMutForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new GridCompareCombineMutForm(MainModel);
            }
            List<GridCompareCombineInfo> gridInfoList = new List<GridCompareCombineInfo>();
            foreach (string strCity in cityGridCompareInfoDic.Keys)
            {
                gridInfoList.AddRange(cityGridCompareInfoDic[strCity]);
            }
            cityGridCompareInfoDic.Clear();
            showForm.FillData(gridInfoList);
            showForm.Show(MainModel.MainForm);
        }
    }
}
