﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.AnaZT
{
    public class IdleLeakNeighboursQuery : QueryBase
    {
        public static double GRID_SPAN_LONG { get; set; } = CD.ATOM_SPAN_LONG;//当前运算中使用的值
        public static double GRID_SPAN_LAT { get; set; } = CD.ATOM_SPAN_LAT;//当前运算中使用的值

        public IdleLeakNeighboursQuery(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "Idle邻区缺失核查(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12067, this.Name);
        }
        
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        protected override void query()
        {
            
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            try
            {
                
                SetIdleScanLostDlg fDlg = new SetIdleScanLostDlg();
                if (fDlg.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                int gridSize;
                int scanDBOffset = 0;
                fDlg.GetSettingFilterRet(out gridSize,out scanDBOffset);
                GRID_SPAN_LONG = CD.ATOM_SPAN_LONG * gridSize / 40.0;
                GRID_SPAN_LAT = CD.ATOM_SPAN_LAT * gridSize / 40.0;
                IdleCellsInGrid[,] idleMatrix = getIdleGridMatrix();
                DbRect bounds = getBoundsOfQuery();
                if (idleMatrix == null || idleMatrix.Length == 0)
                {
                    return;
                }
                PrepareGatherIdleGridQuery prepareIdleGridQuery = new PrepareGatherIdleGridQuery(MainModel,idleMatrix,bounds);
                prepareIdleGridQuery.SetQueryCondition(GetQueryCondition());
                prepareIdleGridQuery.Query();
                GatherScanOnIdleGridQuery gatherScanOnIdleQuery = new GatherScanOnIdleGridQuery(MainModel, idleMatrix, bounds);
                gatherScanOnIdleQuery.SetQueryCondition(GetQueryCondition());
                gatherScanOnIdleQuery.SetScanDBOffset(scanDBOffset);
                gatherScanOnIdleQuery.Query();
                MainModel.IdleNeibScanLostMatrix = idleMatrix;
                MainModel.FireIdleMatrixQueried(this);
            }
            finally
            {
                //continue
            }
        }
        private IdleCellsInGrid[,] getIdleGridMatrix()
        {
            DbRect bounds = getBoundsOfQuery();
            int columnCount = (int)(bounds.Width() / GRID_SPAN_LONG + 0.5);
            int rowCount = (int)(bounds.Height() / GRID_SPAN_LAT + 0.5);
            IdleCellsInGrid[,] colorMatrix = null;
            if (columnCount > 90000 || rowCount > 90000)
            {
                MessageBox.Show("范围过大，请缩小查询范围!");
                return new IdleCellsInGrid[0, 0];
            }
            else
            {
                colorMatrix = new IdleCellsInGrid[rowCount, columnCount];
            }
            //左上角
            colorMatrix[0, 0] = new IdleCellsInGrid();
            IdleCellsInGrid cu = colorMatrix[0, 0];
            cu.ltLong = bounds.x1 + 0 * GRID_SPAN_LONG;
            cu.brLong = cu.ltLong + GRID_SPAN_LONG;
            cu.ltLat = bounds.y2 - 0 * GRID_SPAN_LAT;
            cu.brLat = cu.ltLat - GRID_SPAN_LAT;
            //右下角
            colorMatrix[rowCount - 1, columnCount - 1] = new IdleCellsInGrid();
            cu = colorMatrix[rowCount - 1, columnCount - 1];
            cu.ltLong = bounds.x1 + (columnCount - 1) * GRID_SPAN_LONG;
            cu.brLong = cu.ltLong + GRID_SPAN_LONG;
            cu.ltLat = bounds.y2 - (rowCount - 1) * GRID_SPAN_LAT;
            cu.brLat = cu.ltLat - GRID_SPAN_LAT;
            return colorMatrix;
        }
        private  DbRect getBoundsOfQuery()
        {
            DbRect origBounds = Condition.Geometorys.RegionBounds;
            DbRect tarBounds = new DbRect();
            tarBounds.x1 = 0.0000001 * ((int)(origBounds.x1 * 10000000) / ((int)(GRID_SPAN_LONG * 10000000))) * ((int)(GRID_SPAN_LONG * 10000000));
            tarBounds.x2 = 0.0000001 * ((int)(origBounds.x2 * 10000000) / ((int)(GRID_SPAN_LONG * 10000000))) * ((int)(GRID_SPAN_LONG * 10000000));
            tarBounds.y2 = 0.0000001 * ((int)(origBounds.y2 * 10000000) / ((int)(GRID_SPAN_LAT * 10000000))) * ((int)(GRID_SPAN_LAT * 10000000));
            tarBounds.y1 = 0.0000001 * ((int)(origBounds.y1 * 10000000) / ((int)(GRID_SPAN_LAT * 10000000))) * ((int)(GRID_SPAN_LAT * 10000000));
            return tarBounds;
        }
    }
    public class IdleCellsInGrid
    {
        public double ltLong { get; set; }
        public double ltLat { get; set; }
        public double brLong { get; set; }
        public double brLat { get; set; }

        public float _MaxIdleRxlev { get; set; } = -140;
        /// <summary>
        /// Idle测试到的小区
        /// </summary>
        public Dictionary<string, CellRemData> idleCellsDic { get; set; } = new Dictionary<string, CellRemData>();
        /// <summary>
        /// 扫频扫到的，但Idle没有测试到的，并且扫频扫到的强度大于最强Idle小区
        /// </summary>
        public Dictionary<string, CellRemData> lostScannedCellsDic { get; set; } = new Dictionary<string, CellRemData>();
        internal void GatherCell(string cellname, short rxlev)
        {
            CellRemData remData = null;
            if(!idleCellsDic.TryGetValue(cellname,out remData))
            {
                remData = new CellRemData();
                idleCellsDic[cellname] = remData;
            }
            remData.rxlevMean = (remData.rxlevMean * remData.sampleCount + rxlev) / (remData.sampleCount + 1);
            remData.sampleCount++;
        }
        internal void GatherScannedLost(string cellname, short rxlev)
        {
            CellRemData remData = null;
            if (!lostScannedCellsDic.TryGetValue(cellname, out remData))
            {
                remData = new CellRemData();
                lostScannedCellsDic[cellname] = remData;
            }
            remData.rxlevMean = (remData.rxlevMean * remData.sampleCount + rxlev) / (remData.sampleCount + 1);
            remData.sampleCount++;
        }
        /// <summary>
        /// 准备好Idle数据后调用此函数，找出最大的rxlev
        /// </summary>
        internal void updateMaxIdleRxlev()
        {
            foreach(string cellname in idleCellsDic.Keys)
            {
                float rxlev = idleCellsDic[cellname].rxlevMean;
                if(rxlev>_MaxIdleRxlev)
                {
                    _MaxIdleRxlev = rxlev;
                }
            }
        }

        
    };
    public class CellRemData
    {
        public float rxlevMean { get; set; } = 0;
        public int sampleCount { get; set; }
    };
}
