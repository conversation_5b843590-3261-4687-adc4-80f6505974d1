﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTPilotFreqPolluteByRegion_LTEScan :ZTPilotFreqPolluteByRegion_LTE
   {
        private static ZTPilotFreqPolluteByRegion_LTEScan intance = null;
        public new static ZTPilotFreqPolluteByRegion_LTEScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTPilotFreqPolluteByRegion_LTEScan();
                    }
                }
            }
            return intance;
        }

        protected ZTPilotFreqPolluteByRegion_LTEScan()
            : base()
        {
        }

        public ZTPilotFreqPolluteByRegion_LTEScan(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "导频污染分析_LTEScan"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23011, this.Name);
        }

        protected override bool validTestPoint(TestPoint tp)
        {
            if (!(tp is ScanTestPoint_LTE || tp is ScanTestPoint_NBIOT))
            {
                return false;
            }
            Dictionary<LTECell, float> cellRscpDic = new Dictionary<LTECell, float>();
            Dictionary<LTECell, float?> cellSinrDic = new Dictionary<LTECell, float?>();

            setCellInfo(tp, cellRscpDic, cellSinrDic);

            //邻区场强
            setNCellInfo(tp, cellRscpDic, cellSinrDic);

            float maxRscp = float.MinValue;
            LTECell maxCell = null;
            List<LTECell> cells = new List<LTECell>();
            bool isvalid = getMaxCell(cellRscpDic, ref maxRscp, ref maxCell, cells);
            if (!isvalid)
            {
                return false;
            }

            if (cells.Count < cellCountThreshold)
            {
                saveGoodSample(tp);
                return false;
            }
            else
            {
                List<LTECellOfPilotFrequencyPolluteBlock> cellList = new List<LTECellOfPilotFrequencyPolluteBlock>();
                foreach (LTECell c in cells)
                {
                    float value = cellRscpDic[c];
                    cellList.Add(new LTECellOfPilotFrequencyPolluteBlock(cellList.Count + 1, c, value, cellSinrDic[c]));
                }
                gatherBlock(cellList, tp, maxCell.EARFCN);
            }
            return true;
        }

        private void setNCellInfo(TestPoint tp, Dictionary<LTECell, float> cellRscpDic, Dictionary<LTECell, float?> cellSinrDic)
        {
            LTECell cell;
            for (int i = 0; i < 10; i++)
            {
                float? nRscp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
                if (nRscp == null)
                {
                    break;
                }
                cell = tp.GetCell_LTEScan(i);
                if (cell != null && nRscp >= filterPCCPCH_RSCPMin && nRscp <= filterPCCPCH_RSCPMax)
                {
                    cellRscpDic[cell] = (float)nRscp;
                    cellSinrDic[cell] = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", i];
                }
            }
        }

        private void setCellInfo(TestPoint tp, Dictionary<LTECell, float> cellRscpDic, Dictionary<LTECell, float?> cellSinrDic)
        {
            LTECell cell = tp.GetCell_LTEScan(0);
            //主服场强
            float? rsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", 0];
            if (cell != null && rsrp != null && rsrp >= filterPCCPCH_RSCPMin && rsrp <= filterPCCPCH_RSCPMax)
            {
                cellRscpDic[cell] = (float)rsrp;
                cellSinrDic[cell] = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", 0];
            }
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSSINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", "PilotFrequencyPollute");
            tmpDic.Add("themeName", "LTESCAN_TopN_PSS_RP");
            tmpDic.Add("columnsDef", columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
       
        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["FilterPCCPCH_RSCPMin"] = filterPCCPCH_RSCPMin;
                param["FilterPCCPCH_RSCPMax"] = filterPCCPCH_RSCPMax;
                param["PilotFrequencyPolluteBlockRadius"] = PilotFrequencyPolluteBlockRadius;
                param["SampleCountLimit"] = sampleCountLimit;
                param["CellCountThreshold"] = cellCountThreshold;
                param["RxLevDValueThreshold"] = rxLevDValueThreshold;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("FilterPCCPCH_RSCPMin"))
                {
                    filterPCCPCH_RSCPMin = int.Parse(param["FilterPCCPCH_RSCPMin"].ToString());
                }
                if (param.ContainsKey("FilterPCCPCH_RSCPMax"))
                {
                    filterPCCPCH_RSCPMax = int.Parse(param["FilterPCCPCH_RSCPMax"].ToString());
                }
                if (param.ContainsKey("PilotFrequencyPolluteBlockRadius"))
                {
                    PilotFrequencyPolluteBlockRadius = int.Parse(param["PilotFrequencyPolluteBlockRadius"].ToString());
                }
                if (param.ContainsKey("SampleCountLimit"))
                {
                    sampleCountLimit = int.Parse(param["SampleCountLimit"].ToString());
                }
                if (param.ContainsKey("CellCountThreshold"))
                {
                    cellCountThreshold = int.Parse(param["CellCountThreshold"].ToString());
                }
                if (param.ContainsKey("RxLevDValueThreshold"))
                {
                    rxLevDValueThreshold = int.Parse(param["RxLevDValueThreshold"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return null;
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (PilotFrequencyPolluteBlock block in MainModel.CurPilotFrequencyPolluteBlockList)
            {
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Region(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int badTestPointCount = bgResult.GetImageValueInt();
                int goodTestPointCount = bgResult.GetImageValueInt();
                StringBuilder sb = new StringBuilder();
                sb.Append("总采样点数：");
                sb.Append(badTestPointCount + goodTestPointCount);
                sb.Append("\r\n");
                sb.Append("异常采样点数：");
                sb.Append(badTestPointCount);
                sb.Append("\r\n");
                sb.Append("异常采样点比例：");
                sb.Append(Math.Round(100.0 * badTestPointCount / (badTestPointCount + goodTestPointCount), 2));
                sb.Append("%");
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }
}
