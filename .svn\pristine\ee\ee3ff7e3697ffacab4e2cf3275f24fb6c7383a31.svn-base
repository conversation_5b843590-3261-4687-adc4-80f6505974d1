﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class QueryAndReplayFileInfo : DIYAnalyseFilesOneByOneByRegion
    {
        readonly int recentFileCount = -1;//查询最近几个文件，值小于1时查询所有
        public List<DTFileDataManager> DTFiles { get; set; } = new List<DTFileDataManager>();//按开始时间倒叙排放的DTFileDataManager集合
        public QueryAndReplayFileInfo()
            : base(MainModel.GetInstance())
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;//默认不回放事件
        }
        public QueryAndReplayFileInfo(int recentFileCount)
            : this()
        {
            this.recentFileCount = recentFileCount;
        }
        public override string Name { get { return "查询并回放文件信息"; } }
        protected override bool getCondition()
        {
            return true;
        }
        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;

            queryFileToAnalyse();
            analyseFiles();

            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            DTFiles.Clear();
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                MainModel.FileInfos.Sort(FileInfo.GetCompareByBeginTimeDesc());
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }

                    if (recentFileCount > 0 && files.Count >= recentFileCount)
                    {
                        break;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();
                foreach (FileInfo fileInfo in files)
                {
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (mainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
            }
            catch (Exception e)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(e);
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
            }
        }
        protected override void doStatWithQuery()
        {
            DTFiles.AddRange(MainModel.DTDataManager.FileDataManagers);
        }
    }
}