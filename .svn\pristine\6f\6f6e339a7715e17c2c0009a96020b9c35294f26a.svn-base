﻿namespace MasterCom.RAMS.Func.SystemSetting
{
    partial class RoadRelatedProperties
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.radioByMwgSelf = new System.Windows.Forms.RadioButton();
            this.radioByGrid = new System.Windows.Forms.RadioButton();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.cbxDistrictName = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnGridNow = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxRoadNameCol = new DevExpress.XtraEditors.ComboBoxEdit();
            this.edtLayerPath = new DevExpress.XtraEditors.TextEdit();
            this.radioSpecifiedLayer = new System.Windows.Forms.RadioButton();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.radioAllRoadLayer = new System.Windows.Forms.RadioButton();
            this.btnSelectPath = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxDistrictName.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxRoadNameCol.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtLayerPath.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // radioByMwgSelf
            // 
            this.radioByMwgSelf.AutoSize = true;
            this.radioByMwgSelf.Checked = true;
            this.radioByMwgSelf.Location = new System.Drawing.Point(17, 66);
            this.radioByMwgSelf.Name = "radioByMwgSelf";
            this.radioByMwgSelf.Size = new System.Drawing.Size(253, 18);
            this.radioByMwgSelf.TabIndex = 0;
            this.radioByMwgSelf.TabStop = true;
            this.radioByMwgSelf.Text = "非栅格化关联（关联速度慢，内存占用小）";
            this.radioByMwgSelf.UseVisualStyleBackColor = true;
            this.radioByMwgSelf.CheckedChanged += new System.EventHandler(this.radioByMwgSelf_CheckedChanged);
            // 
            // radioByGrid
            // 
            this.radioByGrid.AutoSize = true;
            this.radioByGrid.Location = new System.Drawing.Point(17, 90);
            this.radioByGrid.Name = "radioByGrid";
            this.radioByGrid.Size = new System.Drawing.Size(204, 18);
            this.radioByGrid.TabIndex = 0;
            this.radioByGrid.Text = "栅格化道路关联（40*40米栅格）";
            this.radioByGrid.UseVisualStyleBackColor = true;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.cbxDistrictName);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Controls.Add(this.btnGridNow);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Controls.Add(this.label1);
            this.groupControl1.Controls.Add(this.radioByGrid);
            this.groupControl1.Controls.Add(this.radioByMwgSelf);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(464, 296);
            this.groupControl1.TabIndex = 3;
            this.groupControl1.Text = "道路名称关联设置";
            // 
            // cbxDistrictName
            // 
            this.cbxDistrictName.Location = new System.Drawing.Point(58, 35);
            this.cbxDistrictName.Name = "cbxDistrictName";
            this.cbxDistrictName.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxDistrictName.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxDistrictName.Size = new System.Drawing.Size(100, 21);
            this.cbxDistrictName.TabIndex = 6;
            this.cbxDistrictName.SelectedIndexChanged += new System.EventHandler(this.cbxDistrictName_SelectedIndexChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(16, 38);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 14);
            this.labelControl1.TabIndex = 7;
            this.labelControl1.Text = "地市：";
            // 
            // btnGridNow
            // 
            this.btnGridNow.Location = new System.Drawing.Point(348, 107);
            this.btnGridNow.Name = "btnGridNow";
            this.btnGridNow.Size = new System.Drawing.Size(78, 23);
            this.btnGridNow.TabIndex = 2;
            this.btnGridNow.Text = "立即栅格化";
            this.btnGridNow.Click += new System.EventHandler(this.btnGridNow_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxRoadNameCol);
            this.groupBox1.Controls.Add(this.edtLayerPath);
            this.groupBox1.Controls.Add(this.radioSpecifiedLayer);
            this.groupBox1.Controls.Add(this.labelControl4);
            this.groupBox1.Controls.Add(this.labelControl3);
            this.groupBox1.Controls.Add(this.radioAllRoadLayer);
            this.groupBox1.Controls.Add(this.btnSelectPath);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Enabled = false;
            this.groupBox1.Location = new System.Drawing.Point(31, 138);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(401, 129);
            this.groupBox1.TabIndex = 5;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "栅格化道路图层选择";
            // 
            // cbxRoadNameCol
            // 
            this.cbxRoadNameCol.Location = new System.Drawing.Point(65, 93);
            this.cbxRoadNameCol.Name = "cbxRoadNameCol";
            this.cbxRoadNameCol.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxRoadNameCol.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxRoadNameCol.Size = new System.Drawing.Size(100, 21);
            this.cbxRoadNameCol.TabIndex = 4;
            this.cbxRoadNameCol.SelectedIndexChanged += new System.EventHandler(this.cbxRoadNameCol_SelectedIndexChanged);
            // 
            // edtLayerPath
            // 
            this.edtLayerPath.Location = new System.Drawing.Point(65, 66);
            this.edtLayerPath.Name = "edtLayerPath";
            this.edtLayerPath.Properties.ReadOnly = true;
            this.edtLayerPath.Size = new System.Drawing.Size(289, 21);
            this.edtLayerPath.TabIndex = 1;
            // 
            // radioSpecifiedLayer
            // 
            this.radioSpecifiedLayer.AutoSize = true;
            this.radioSpecifiedLayer.Checked = true;
            this.radioSpecifiedLayer.Location = new System.Drawing.Point(8, 45);
            this.radioSpecifiedLayer.Name = "radioSpecifiedLayer";
            this.radioSpecifiedLayer.Size = new System.Drawing.Size(97, 18);
            this.radioSpecifiedLayer.TabIndex = 0;
            this.radioSpecifiedLayer.TabStop = true;
            this.radioSpecifiedLayer.Text = "指定道路图层";
            this.radioSpecifiedLayer.UseVisualStyleBackColor = true;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(171, 97);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(108, 14);
            this.labelControl4.TabIndex = 3;
            this.labelControl4.Text = "（道路名称所在列）";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(27, 96);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(36, 14);
            this.labelControl3.TabIndex = 3;
            this.labelControl3.Text = "列名：";
            // 
            // radioAllRoadLayer
            // 
            this.radioAllRoadLayer.AutoSize = true;
            this.radioAllRoadLayer.Location = new System.Drawing.Point(8, 21);
            this.radioAllRoadLayer.Name = "radioAllRoadLayer";
            this.radioAllRoadLayer.Size = new System.Drawing.Size(97, 18);
            this.radioAllRoadLayer.TabIndex = 0;
            this.radioAllRoadLayer.Text = "所有道路图层";
            this.radioAllRoadLayer.UseVisualStyleBackColor = true;
            this.radioAllRoadLayer.CheckedChanged += new System.EventHandler(this.radioAllRoadLayer_CheckedChanged);
            // 
            // btnSelectPath
            // 
            this.btnSelectPath.Location = new System.Drawing.Point(360, 65);
            this.btnSelectPath.Name = "btnSelectPath";
            this.btnSelectPath.Size = new System.Drawing.Size(35, 23);
            this.btnSelectPath.TabIndex = 2;
            this.btnSelectPath.Text = "...";
            this.btnSelectPath.Click += new System.EventHandler(this.btnSelectPath_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(27, 69);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(36, 14);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "路径：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(28, 111);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(310, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "（关联速度快，视道路图层大小需额外占用5~50M内存）";
            // 
            // RoadRelatedProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl1);
            this.Name = "RoadRelatedProperties";
            this.Size = new System.Drawing.Size(464, 296);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxDistrictName.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxRoadNameCol.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtLayerPath.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.RadioButton radioByMwgSelf;
        private System.Windows.Forms.RadioButton radioByGrid;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.ComboBoxEdit cbxRoadNameCol;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SimpleButton btnSelectPath;
        private DevExpress.XtraEditors.TextEdit edtLayerPath;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton radioSpecifiedLayer;
        private System.Windows.Forms.RadioButton radioAllRoadLayer;
        private DevExpress.XtraEditors.SimpleButton btnGridNow;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.ComboBoxEdit cbxDistrictName;
    }
}
