﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;

using MasterCom.RAMS.ZTFunc.LteSignalImsi;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ReplayFileByImsiQueryWithOneIMSI : DIYReplayFileQuery
    {
        public ReplayFileByImsiQueryWithOneIMSI(MainModel mainModel)
            : base(mainModel)
        {

            this.DoWithDTDataEvent += DoWithDTData;
            this.DoWithDTEventEvent += DoWithDTEvent;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            object imsi = tp["signal_IMSI"];
            return imsi != null && IsContainImsi(imsi.ToString());
        }

        protected override bool isValidEvent(Event e)
        {
            object imsi = e["Value3"];
            return imsi != null && IsContainImsi(imsi.ToString());
        }

        protected virtual List<ImsiFile> QueryImsiFiles()
        {
            ImsiFileQuery query = new ImsiFileQuery();
            return query.QueryFilesForEach(Condition.FileInfos);
        }

        protected virtual void DoSomethingBeforeQuery(List<ImsiFile> selectedFiles)
        {
        }

        private bool IsContainImsi(string imsi)
        {
            return curImsiSel == imsi;
        }

       
        private string curImsiSel = "";
        //private Dictionary<string, int> imsiFileDic;

        internal void SetCurrentIMSI(long imsi)
        {
            curImsiSel = imsi.ToString();
        }
        private SearchGeometrys curSearchGeo = null;
        internal void setWithinGeometry(SearchGeometrys searchGeometrys)
        {
            this.curSearchGeo = searchGeometrys;
        }
        private void DoWithDTEvent(Event evt)
        {
            if (curSearchGeo != null)
            {
                DbRect rect = curSearchGeo.RegionBounds;
                if (rect == null)
                {
                    return;
                }
                bool ok = false;
                double x = evt.Longitude;
                double y = evt.Latitude;
                ok = curSearchGeo.GeoOp.Contains(x, y);
                int retryCount = 0;
                while (!ok && retryCount < 8)
                {
                    getLongLat(evt.Longitude, evt.Latitude, ref x, ref y, retryCount);
                    ok = curSearchGeo.GeoOp.Contains(x, y);
                    retryCount++;
                }
                if (ok)
                {
                    evt.Longitude = x;
                    evt.Latitude = y;
                }
                else
                {
                    evt.Longitude = 0.5 * (rect.x1 + rect.x2);
                    evt.Latitude = 0.5 * (rect.y1 + rect.y2);
                }
                evt.Tag = curSearchGeo;

                //TestPoints.Add(tp);
            }
        }
        private void DoWithDTData(TestPoint tp)
        {
            if (curSearchGeo != null)
            {
                DbRect rect = curSearchGeo.RegionBounds;
                if (rect == null)
                {
                    return;
                }
                bool ok = false;
                double x = tp.Longitude;
                double y = tp.Latitude;
                ok = curSearchGeo.GeoOp.Contains(x, y);
                int retryCount = 0;
                while (!ok && retryCount < 8)
                {
                    getLongLat(tp.Longitude, tp.Latitude, ref x, ref y, retryCount);
                    ok = curSearchGeo.GeoOp.Contains(x, y);
                    retryCount++;
                }
                if (ok)
                {
                    tp.Longitude = x;
                    tp.Latitude = y;
                }
                else
                {
                    tp.Longitude = 0.5 * (rect.x1 + rect.x2);
                    tp.Latitude = 0.5 * (rect.y1 + rect.y2);
                }
                if (tp is SignalTestPoint)
                {
                    (tp as SignalTestPoint).queryGeometry = curSearchGeo;
                }

                //TestPoints.Add(tp);
            }
        }

        private void getLongLat(double longitude, double latitude, ref double x, ref double y, int retryCount)
        {
            if (retryCount == 0)
            {
                x = longitude - CD.ATOM_SPAN_LONG;
                y = latitude - CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 1)
            {
                x = longitude - CD.ATOM_SPAN_LONG;
                y = latitude + CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 2)
            {
                x = longitude + CD.ATOM_SPAN_LONG;
                y = latitude - CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 3)
            {
                x = longitude + CD.ATOM_SPAN_LONG;
                y = latitude + CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 4)
            {
                x = longitude - 0.5 * CD.ATOM_SPAN_LONG;
                y = latitude - 0.5 * CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 5)
            {
                x = longitude - 0.5 * CD.ATOM_SPAN_LONG;
                y = latitude + 0.5 * CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 6)
            {
                x = longitude + 0.5 * CD.ATOM_SPAN_LONG;
                y = latitude - 0.5 * CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 7)
            {
                x = longitude + 0.5 * CD.ATOM_SPAN_LONG;
                y = latitude + 0.5 * CD.ATOM_SPAN_LAT;
            }
        }
    }
}
