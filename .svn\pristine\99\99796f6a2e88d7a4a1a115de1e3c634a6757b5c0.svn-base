﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRNCellLevelHigherInfo
    {
        public string CellName { get; set; } = "";
        public int EARFCN { get; set; }
        public int PCI { get; set; }
        public int TAC { get; set; }
        public long NCI { get; set; }
        public string RSRP { get; set; }
  
        public int NEARFCN { get; set; }
        public int NPCI { get; set; }
        public int NTAC { get; set; }
        public long NNCI { get; set; }
        public string NRSRP { get; set; }

        public string RSRPDiff { get; set; }

        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string Time { get; set; }
        public bool IsHighRsrpPoint { get; set; }
        public string IsHighRsrpPointDesc { get; set; }
        public TestPoint TestPoint { get; set; } = new TestPoint();

        public void Calculate()
        {
            Time = TestPoint.DateTimeStringWithMillisecond;
            if (IsHighRsrpPoint)
            {
                IsHighRsrpPointDesc = "是";
            }
            else
            {
                IsHighRsrpPointDesc = "否";
            }
        }
    }

    public class NRNCellLevelHigherResultInfo
    {
        public string FileName { get; set; }
        public DateTime ErrorStartTime { get; set; }
        public double Distance { get; set; }
        public double Duration { get; set; }
        public string Rate { get; set; }
        public int TPCount { get; set; }
        public List<NRNCellLevelHigherInfo> TPResList { get; set; }

        public void Calculate()
        {
            foreach (var item in TPResList)
            {
                item.Calculate();
            }
        }
    }
}
