﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellComparisonForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.ColumnCellName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnLacCi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnBcchBsic = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnScanSampleCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnDtSampleCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnAvgRxlevScan = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnAvgRxlevDt = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnScanPct = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnDTPct = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnDifferPct = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_exportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.radioButton900 = new System.Windows.Forms.RadioButton();
            this.radioButton1800 = new System.Windows.Forms.RadioButton();
            this.radioButtonAll = new System.Windows.Forms.RadioButton();
            this.buttonChangeChannel = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.textBoxCurChannel = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.AllowUserToOrderColumns = true;
            this.dataGridView.AllowUserToResizeRows = false;
            this.dataGridView.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridView.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.DisplayedCells;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ColumnCellName,
            this.ColumnLacCi,
            this.ColumnBcchBsic,
            this.ColumnScanSampleCount,
            this.ColumnDtSampleCount,
            this.ColumnAvgRxlevScan,
            this.ColumnAvgRxlevDt,
            this.ColumnScanPct,
            this.ColumnDTPct,
            this.ColumnDifferPct});
            this.dataGridView.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridView.Location = new System.Drawing.Point(0, 56);
            this.dataGridView.MultiSelect = false;
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView.Size = new System.Drawing.Size(1148, 453);
            this.dataGridView.TabIndex = 0;
            this.dataGridView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.dataGridView_MouseDoubleClick);
            // 
            // ColumnCellName
            // 
            this.ColumnCellName.HeaderText = "小区名称";
            this.ColumnCellName.Name = "ColumnCellName";
            this.ColumnCellName.ReadOnly = true;
            // 
            // ColumnLacCi
            // 
            this.ColumnLacCi.HeaderText = "小区LAC_CI";
            this.ColumnLacCi.Name = "ColumnLacCi";
            this.ColumnLacCi.ReadOnly = true;
            // 
            // ColumnBcchBsic
            // 
            this.ColumnBcchBsic.HeaderText = "小区BCCH_BSIC";
            this.ColumnBcchBsic.Name = "ColumnBcchBsic";
            this.ColumnBcchBsic.ReadOnly = true;
            // 
            // ColumnScanSampleCount
            // 
            this.ColumnScanSampleCount.HeaderText = "扫频采样点数目";
            this.ColumnScanSampleCount.Name = "ColumnScanSampleCount";
            this.ColumnScanSampleCount.ReadOnly = true;
            // 
            // ColumnDtSampleCount
            // 
            this.ColumnDtSampleCount.HeaderText = "DT采样点数目";
            this.ColumnDtSampleCount.Name = "ColumnDtSampleCount";
            this.ColumnDtSampleCount.ReadOnly = true;
            // 
            // ColumnAvgRxlevScan
            // 
            this.ColumnAvgRxlevScan.HeaderText = "扫频平均电平(dB)";
            this.ColumnAvgRxlevScan.Name = "ColumnAvgRxlevScan";
            this.ColumnAvgRxlevScan.ReadOnly = true;
            // 
            // ColumnAvgRxlevDt
            // 
            this.ColumnAvgRxlevDt.HeaderText = "DT平均电平(dB)";
            this.ColumnAvgRxlevDt.Name = "ColumnAvgRxlevDt";
            this.ColumnAvgRxlevDt.ReadOnly = true;
            // 
            // ColumnScanPct
            // 
            this.ColumnScanPct.HeaderText = "扫频占比(%)";
            this.ColumnScanPct.Name = "ColumnScanPct";
            this.ColumnScanPct.ReadOnly = true;
            // 
            // ColumnDTPct
            // 
            this.ColumnDTPct.HeaderText = "DT占比(%)";
            this.ColumnDTPct.Name = "ColumnDTPct";
            this.ColumnDTPct.ReadOnly = true;
            // 
            // ColumnDifferPct
            // 
            this.ColumnDifferPct.HeaderText = "占比差值(%)";
            this.ColumnDifferPct.Name = "ColumnDifferPct";
            this.ColumnDifferPct.ReadOnly = true;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_exportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // mi_exportExcel
            // 
            this.mi_exportExcel.Name = "mi_exportExcel";
            this.mi_exportExcel.Size = new System.Drawing.Size(129, 22);
            this.mi_exportExcel.Text = "导出Excel";
            this.mi_exportExcel.Click += new System.EventHandler(this.mi_exportExcel_Click);
            // 
            // radioButton900
            // 
            this.radioButton900.AutoSize = true;
            this.radioButton900.Location = new System.Drawing.Point(614, 19);
            this.radioButton900.Name = "radioButton900";
            this.radioButton900.Size = new System.Drawing.Size(46, 18);
            this.radioButton900.TabIndex = 1;
            this.radioButton900.TabStop = true;
            this.radioButton900.Text = "900";
            this.radioButton900.UseVisualStyleBackColor = true;
            // 
            // radioButton1800
            // 
            this.radioButton1800.AutoSize = true;
            this.radioButton1800.Location = new System.Drawing.Point(752, 17);
            this.radioButton1800.Name = "radioButton1800";
            this.radioButton1800.Size = new System.Drawing.Size(53, 18);
            this.radioButton1800.TabIndex = 2;
            this.radioButton1800.TabStop = true;
            this.radioButton1800.Text = "1800";
            this.radioButton1800.UseVisualStyleBackColor = true;
            // 
            // radioButtonAll
            // 
            this.radioButtonAll.AutoSize = true;
            this.radioButtonAll.Location = new System.Drawing.Point(890, 17);
            this.radioButtonAll.Name = "radioButtonAll";
            this.radioButtonAll.Size = new System.Drawing.Size(82, 18);
            this.radioButtonAll.TabIndex = 3;
            this.radioButtonAll.TabStop = true;
            this.radioButtonAll.Text = "900&&1800";
            this.radioButtonAll.UseVisualStyleBackColor = true;
            // 
            // buttonChangeChannel
            // 
            this.buttonChangeChannel.Location = new System.Drawing.Point(1036, 13);
            this.buttonChangeChannel.Name = "buttonChangeChannel";
            this.buttonChangeChannel.Size = new System.Drawing.Size(87, 27);
            this.buttonChangeChannel.TabIndex = 4;
            this.buttonChangeChannel.Text = "改变频段";
            this.buttonChangeChannel.UseVisualStyleBackColor = true;
            this.buttonChangeChannel.Click += new System.EventHandler(this.buttonChangeChannel_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.BackColor = System.Drawing.SystemColors.Window;
            this.groupBox1.Controls.Add(this.textBoxCurChannel);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.radioButton900);
            this.groupBox1.Controls.Add(this.radioButton1800);
            this.groupBox1.Controls.Add(this.buttonChangeChannel);
            this.groupBox1.Controls.Add(this.radioButtonAll);
            this.groupBox1.Location = new System.Drawing.Point(0, 5);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1148, 44);
            this.groupBox1.TabIndex = 5;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "频段";
            // 
            // textBoxCurChannel
            // 
            this.textBoxCurChannel.Location = new System.Drawing.Point(100, 14);
            this.textBoxCurChannel.Name = "textBoxCurChannel";
            this.textBoxCurChannel.ReadOnly = true;
            this.textBoxCurChannel.Size = new System.Drawing.Size(116, 22);
            this.textBoxCurChannel.TabIndex = 6;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(59, 20);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(31, 14);
            this.label1.TabIndex = 5;
            this.label1.Text = "当前";
            // 
            // CellComparisonForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1148, 509);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.dataGridView);
            this.Name = "CellComparisonForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "小区占用对比结果";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.CellComparisonForm_FormClosed);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem mi_exportExcel;
        private System.Windows.Forms.RadioButton radioButton900;
        private System.Windows.Forms.RadioButton radioButton1800;
        private System.Windows.Forms.RadioButton radioButtonAll;
        private System.Windows.Forms.Button buttonChangeChannel;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox textBoxCurChannel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnCellName;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnLacCi;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnBcchBsic;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnScanSampleCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnDtSampleCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnAvgRxlevScan;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnAvgRxlevDt;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnScanPct;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnDTPct;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnDifferPct;
    }
}