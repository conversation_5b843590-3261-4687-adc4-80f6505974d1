﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTSCellNCellInfo
{
    public class SCellInfo
    {
        public ICell Cell { get; set; }
        public string FileName { get; set; }
        public List<NCellInfo> NCells { get; set; } = new List<NCellInfo>();

        public void AddNCell(TestPoint tp, float signalLev, ICell cell, float nCellSignalLev)
        {
            NCellInfo nCell = NCells.Find(
                delegate(NCellInfo x) { return x.Cell == cell; });
            if (nCell == null)
            {
                nCell = new NCellInfo(tp, this.Cell, signalLev, cell, nCellSignalLev);
                NCells.Add(nCell);
            }
            else
            {
                nCell.AddSignalLev(tp, signalLev, nCellSignalLev);
            }
        }
    }
}
