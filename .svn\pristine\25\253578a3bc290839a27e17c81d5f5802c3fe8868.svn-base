﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors;
using System.IO;

namespace MasterCom.RAMS.Func
{
    public partial class NewWorkSheetForm : BaseForm
    {
        public NewWorkSheetForm(WorkSpace ws)
        {
            InitializeComponent();
            tbSheetName.Text = ws.getNextWorkSheetDefaultText();
            fillWSTemplateTreeView(ws);
            radioGroup.SelectedIndex = 1;
        }

        private void fillWSTemplateTreeView(WorkSpace ws)
        {
            treeViewTemplateWS.Nodes.Clear();
            foreach (WorkSheet sheet in ws.WorkSheetTemplates)
            {
                TreeNode sNode = new TreeNode(sheet.Text);
                sNode.NodeFont = treeViewTemplateWS.Font;
                sNode.Tag = sheet;
                treeViewTemplateWS.Nodes.Add(sNode);
                foreach (ChildFormConfig formCfg in sheet.ChildFormConfigs)
                {
                    TreeNode fNode = new TreeNode(formCfg.Text);
                    sNode.Nodes.Add(fNode);
                    fNode.NodeFont = treeViewTemplateWS.Font;
                }
            }
            treeViewTemplateWS.CollapseAll();
            if (ws.WorkSheetTemplates.Count==0)
            {
                if (File.Exists(ws.templatePath))
                {
                    labelControl1.Text = "没有已保存的工作台模板！";
                }
                else
                {
                    labelControl1.Text = "不存在工作台模板配置文件：" + ws.templatePath;
                }
            }
        }

        private void radioGroup_SelectedIndexChanged(object sender, EventArgs e)
        {
            splitContainerControl1.Panel2.Enabled = radioGroup.SelectedIndex == 1;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (radioGroup.SelectedIndex == 1 && treeViewTemplateWS.SelectedNode == null)
            {
                MessageBox.Show("请选中一个节点！");
                return;
            }
            if (tbSheetName.Text.Trim().Length>0)
            {
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("请填写工作台名称！");
            }
        }

        public WorkSheet GetWorkSheet()
        {
            WorkSheet workSheet = null;
            if (radioGroup.SelectedIndex == 0)
            {
                workSheet = new WorkSheet();
                workSheet.Text = tbSheetName.Text;
            }
            else if (radioGroup.SelectedIndex == 1)
            {
                if (treeViewTemplateWS.SelectedNode.Parent == null)
                {
                    workSheet = treeViewTemplateWS.SelectedNode.Tag as WorkSheet;
                }
                else
                {//子节点
                    workSheet = treeViewTemplateWS.SelectedNode.Parent.Tag as WorkSheet;
                    workSheet.ActiveChildFormIndex = treeViewTemplateWS.SelectedNode.Index;
                }
                workSheet.Text = tbSheetName.Text;
            }
            return workSheet;
        }

        private void treeViewTemplateWS_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (radioGroup.SelectedIndex == 1 && e.Node != null&&tbSheetName.Text.Trim().Length>0)
            {
                    DialogResult = DialogResult.OK;
            }
        }

        private void treeViewTemplateWS_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Parent == null)
            {
                tbSheetName.Text = (e.Node.Tag as WorkSheet).Text;
            }
            else
            {//子节点
                tbSheetName.Text = (e.Node.Parent.Tag as WorkSheet).Text;
            }
        }

    }
}
