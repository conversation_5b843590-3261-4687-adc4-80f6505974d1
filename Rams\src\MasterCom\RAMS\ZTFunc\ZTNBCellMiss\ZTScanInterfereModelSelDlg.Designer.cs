﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanInterfereModelSelDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.rbCo = new System.Windows.Forms.RadioButton();
            this.rbAdj = new System.Windows.Forms.RadioButton();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.lblBCCH = new System.Windows.Forms.Label();
            this.numBCCHDValue = new System.Windows.Forms.NumericUpDown();
            this.cbxInterferenceType = new System.Windows.Forms.ComboBox();
            ((System.ComponentModel.ISupportInitialize)(this.numBCCHDValue)).BeginInit();
            this.SuspendLayout();
            // 
            // rbCo
            // 
            this.rbCo.AutoSize = true;
            this.rbCo.Checked = true;
            this.rbCo.Location = new System.Drawing.Point(29, 28);
            this.rbCo.Name = "rbCo";
            this.rbCo.Size = new System.Drawing.Size(47, 16);
            this.rbCo.TabIndex = 0;
            this.rbCo.TabStop = true;
            this.rbCo.Text = "同频";
            this.rbCo.UseVisualStyleBackColor = true;
            this.rbCo.CheckedChanged += new System.EventHandler(this.rb_CheckedChanged);
            // 
            // rbAdj
            // 
            this.rbAdj.AutoSize = true;
            this.rbAdj.Location = new System.Drawing.Point(29, 59);
            this.rbAdj.Name = "rbAdj";
            this.rbAdj.Size = new System.Drawing.Size(47, 16);
            this.rbAdj.TabIndex = 1;
            this.rbAdj.Text = "邻频";
            this.rbAdj.UseVisualStyleBackColor = true;
            this.rbAdj.CheckedChanged += new System.EventHandler(this.rb_CheckedChanged);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(27, 100);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(128, 100);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // lblBCCH
            // 
            this.lblBCCH.AutoSize = true;
            this.lblBCCH.Location = new System.Drawing.Point(91, 61);
            this.lblBCCH.Name = "lblBCCH";
            this.lblBCCH.Size = new System.Drawing.Size(65, 12);
            this.lblBCCH.TabIndex = 4;
            this.lblBCCH.Text = "BCCH差值≤";
            // 
            // numBCCHDValue
            // 
            this.numBCCHDValue.Location = new System.Drawing.Point(162, 57);
            this.numBCCHDValue.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBCCHDValue.Name = "numBCCHDValue";
            this.numBCCHDValue.Size = new System.Drawing.Size(39, 21);
            this.numBCCHDValue.TabIndex = 5;
            this.numBCCHDValue.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // cbxInterferenceType
            // 
            this.cbxInterferenceType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxInterferenceType.FormattingEnabled = true;
            this.cbxInterferenceType.Items.AddRange(new object[] {
            "BCCH&TCH",
            "BCCH Only",
            "TCH Only"});
            this.cbxInterferenceType.Location = new System.Drawing.Point(93, 24);
            this.cbxInterferenceType.Name = "cbxInterferenceType";
            this.cbxInterferenceType.Size = new System.Drawing.Size(108, 20);
            this.cbxInterferenceType.TabIndex = 6;
            // 
            // ZTScanInterfereModelSelDlg
            // 
            this.AcceptButton = this.btnOK;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(234, 144);
            this.Controls.Add(this.cbxInterferenceType);
            this.Controls.Add(this.numBCCHDValue);
            this.Controls.Add(this.lblBCCH);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.rbAdj);
            this.Controls.Add(this.rbCo);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ZTScanInterfereModelSelDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "扫频同邻频干扰查询选择";
            ((System.ComponentModel.ISupportInitialize)(this.numBCCHDValue)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.RadioButton rbCo;
        private System.Windows.Forms.RadioButton rbAdj;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label lblBCCH;
        private System.Windows.Forms.NumericUpDown numBCCHDValue;
        private System.Windows.Forms.ComboBox cbxInterferenceType;
    }
}