﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.NebulaForm;

namespace MasterCom.RAMS.Func
{
    class NebulaReplay : DIYAnalyseByFileBackgroundBase
    {
        public NebulaReplay()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = false;
            this.IncludeEvent = false;
        }
        public override string Name
        {
            get
            {
                return "象限分布视图";
            }
        }
        
        WorkSheet workSheet = null;
        ChildFormConfig childFormConfig = null;
        private bool selectChileForm()
        {
            foreach (WorkSheet ws in this.MainModel.WorkSpace.WorkSheets)
            {
                foreach (ChildFormConfig cfc in ws.ChildFormConfigs)
                {
                    if (cfc.Text == "象限分布视图")
                    {
                        this.workSheet = ws;
                        this.childFormConfig = cfc;
                        return true;
                    }
                }
            }
            return false;
        }
        protected override bool getCondition()
        {
            if (!this.selectChileForm())
            {
                this.MainModel.MainForm.GetNodeFromText(this.MainModel.MainForm.treeViewMenuItem.Nodes, "象限分布视图");
                if (!this.selectChileForm())
                {
                    MessageBox.Show("启动象限分布视图失败!");
                    return false;
                }
            }
            SettingReplay dlg = new SettingReplay();
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            return true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11035, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }
        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }
        readonly ChartSerialInfo serialInfoX = new ChartSerialInfo();
        readonly ChartSerialInfo seeialInfoY = new ChartSerialInfo();
        protected override void getReadyBeforeQuery()
        { 
            XYData.IsBigDataReplaying = true;
            XYData.ListDataXY = new List<DataXY>();
            serialInfoX.DisplayParam = DTDisplayParameterManager.GetInstance()[XYData.Sys, XYData.TargetX, 0];  //x轴信息
            serialInfoX.MS = 0;
            seeialInfoY.DisplayParam = DTDisplayParameterManager.GetInstance()[XYData.Sys, XYData.TargetY, 0];  //y轴信息
            seeialInfoY.MS = 0;
        }
        protected override void fireShowForm()
        {
            XYData.IsBigDataReplaying = false;
            this.MainModel.MainForm.activeChildForm(workSheet, childFormConfig);
            XYData.XsmallChanged = true;
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
        readonly Random random = new Random();
        protected override void doStatWithQuery()
        {
            float unitX = 0;
            float unitY = 0;
            if (XYData.Xlarge - XYData.Xsmall > 0)
            {
                unitX = (800) / (XYData.Xlarge - XYData.Xsmall);
            }
            if (XYData.Ylarge - XYData.Ysmall > 0)
            {
                unitY = (700) / (XYData.Ylarge - XYData.Ysmall);
            }

            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in file.TestPoints)
                {
                    if (!isValidTestPoint(tp))
                    {
                        continue;
                    }

                    float? valuex = serialInfoX.GetValue(tp);
                    float? valuey = seeialInfoY.GetValue(tp);
                    int randomX = random.Next(0, (int)unitX);
                    int randomY = random.Next(0, (int)unitY);
                    if (valuex == null || valuey == null)
                    {
                        continue;
                    }

                    addListDataXY(unitX, unitY, valuex, valuey, randomX, randomY);
                }
            }
        }

        private void addListDataXY(float unitX, float unitY, float? valuex, float? valuey, int randomX, int randomY)
        {
            DataXY dtxy = new DataXY();
            dtxy.Xvalue = (float)valuex;
            dtxy.Yvalue = (float)valuey;
            //x轴的随机数
            if (dtxy.Xvalue <= XYData.Xsmall + 0.5)
            {
                dtxy.Xrandom = (float)randomX;
            }
            else if (dtxy.Xvalue >= XYData.Xlarge - 0.5)
            {
                dtxy.Xrandom = -(float)randomX;
            }
            else
            {
                dtxy.Xrandom = (float)(randomX - (int)unitX / 2);
            }
            //y轴的随机数
            if (dtxy.Yvalue <= XYData.Ysmall + 0.5)
            {
                dtxy.Yrandom = (float)randomY;
            }
            else if (dtxy.Yvalue >= XYData.Ylarge - 0.5)
            {
                dtxy.Yrandom = -(float)randomY;
            }
            else
            {
                dtxy.Yrandom = (float)(randomY - (int)unitY / 2);
            }
            XYData.ListDataXY.Add(dtxy);
        }
    }
}
