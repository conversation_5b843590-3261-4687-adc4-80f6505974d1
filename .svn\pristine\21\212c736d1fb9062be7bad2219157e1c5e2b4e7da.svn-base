﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellChangeFreqSettingDlg : BaseForm
    {
        private CellFreqCondition condition;
        public CellChangeFreqSettingDlg()
        {
            InitializeComponent();
            Init();
        }

        public void Init()
        {
            spinEdiRadius.Value = 1500;
            condition = new CellFreqCondition((int)spinEdiRadius.Value, cbxIndoorCell.Checked,false);
        }

        public void SetCondition(CellFreqCondition condition)
        {
            if (condition != null)
            {
                spinEdiRadius.Value = condition.Radius;
                cbxIndoorCell.Checked = condition.IsExcludeIndoorCell;
                cbxFrequencyType.SelectedIndex=condition.IsMatchingByTCH?1:0;
                listBoxControlFreq.Items.Clear();
                foreach (FreqRange freq in condition.FreqList)
                {
                    listBoxControlFreq.Items.Add(freq);
                }
            }
        }

        public void GetCondition(out CellFreqCondition condition)
        {
            condition = this.condition;
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            if (listBoxControlFreq.Items.Count <= 0)
            {
                MessageBox.Show("需至少设置一个频段！");
                return;
            }
            bool isMatchingByTCH = false;
            if (cbxFrequencyType.Text == "TCH")
            {
                isMatchingByTCH = true;
            }
            this.condition = new CellFreqCondition((int)spinEdiRadius.Value, cbxIndoorCell.Checked,isMatchingByTCH);
            foreach (FreqRange freq in listBoxControlFreq.Items)
            {
                this.condition.FreqList.Add(freq);
            }
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void simpleButtonAddFreq_Click(object sender, EventArgs e)
        {
            FreqRange freq = new FreqRange((int)spinEditStart.Value, (int)spinEditEnd.Value);
            freq.Name = freq.FreqStart + "-" + freq.FreqEnd;
            if (!freq.IsLegal())
            {
                XtraMessageBox.Show("频段输入有误！");
                return;
            }
            foreach (object o in listBoxControlFreq.Items)
            {
                if (freq.Equals(o))
                {
                    return;
                }
            }
            listBoxControlFreq.Items.Add(freq);
        }

        private void simpleButtonDel_Click(object sender, EventArgs e)
        {
            if (listBoxControlFreq.SelectedItem != null)
            {
                listBoxControlFreq.Items.Remove(listBoxControlFreq.SelectedItem);
            }
        }
    }

    public class FreqRange
    {
        public string Name { get; set; }
        public int FreqStart { get; set; }
        public int FreqEnd { get; set; }

        public FreqRange(int freqStart,int freqEnd)
        {
            this.FreqStart = freqStart;
            this.FreqEnd = freqEnd;
        }

        public FreqRange(string name, int freqStart, int freqEnd)
        {
            this.Name = name;
            this.FreqStart = freqStart;
            this.FreqEnd = freqEnd;
        }

        public bool IsLegal()
        {
            return this.FreqStart <= this.FreqEnd;
        }

        public bool BInRange(int ivalue)
        {
            return ivalue >= FreqStart && ivalue <= FreqEnd;
        }

        public override string ToString()
        {
            return Name;
        }

        public override bool Equals(object obj)
        {
            FreqRange freq = obj as FreqRange;
            return (this.FreqStart == freq.FreqStart && this.FreqEnd == freq.FreqEnd);
        }
        public override int GetHashCode()
        {
            return (this.FreqStart + "-" + this.FreqEnd).GetHashCode();
        }
    }

    public class CellFreqCondition
    {
        public int Radius { get; set; }
        public bool IsExcludeIndoorCell { get; set; }
        public List<FreqRange> FreqList { get; set; } = new List<FreqRange>();
        public MapWinGIS.Shape Geometry { get; set; }
        public bool IsMatchingByTCH { get; set; }

        public CellFreqCondition(int radius, bool isExcludeIndoorCell,bool isMatchingByTCH)
        {
            this.Radius = radius;
            this.IsExcludeIndoorCell = isExcludeIndoorCell;
            this.IsMatchingByTCH = isMatchingByTCH;
        }
    }
}