﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using System.Collections.ObjectModel;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.ES.Data
{
    public class CfgDataProvider : DataProvider
    {
        public CfgDataProvider()
        {
            FuncInfo fi = new FuncInfo("问题小区为室内站", "返回参数：int 1表示室内站 0表示非室内站", isIndoorStation, false);
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("与问题小区的距离", "返回参数：int 与问题小区的距离米", getDistanceToServCell, false);
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("获取主服务小区名称", "返回参数：string 主服务小区名称", getServCellName, false);
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("通过经纬度等获取小区名称", "返回参数：string 小区名称", getCellNameByLongLatBcchBsic, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("经度double"));
            fi.ParamDescList.Add(new ParamInfo("纬度double"));
            fi.ParamDescList.Add(new ParamInfo("bcch/ScellUARFCN(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("bsic/ScellCPI(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("通过LACCI获取小区名称", "返回参数：string 小区名称", getCellNameByLacCi, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("LAC(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("CI(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("通过经纬度等获取与小区距离", "返回参数：距离", getDistanceByCellName, false);
            fi.ParamDescList.Add(new ParamInfo("经度double"));
            fi.ParamDescList.Add(new ParamInfo("纬度double"));
            fi.ParamDescList.Add(new ParamInfo("小区名称(string)"));
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("通过经纬度获取与小区的夹角", "返回参数：夹角0-180  -99999异常或无法判断", getDirectionToCell, false);
            fi.ParamDescList.Add(new ParamInfo("经度double"));
            fi.ParamDescList.Add(new ParamInfo("纬度double"));
            fi.ParamDescList.Add(new ParamInfo("小区名称(string)"));
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("获取某小区的理想覆盖半径", "输入：1 小区名，2 参考基站个数 返回参数", getDesignedRadius, false);
            fi.ParamDescList.Add(new ParamInfo("小区名称(string)"));
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("判断小区间是否有邻区关系配置", "表明需要核查小区2是否在小区1的邻区关系中 输入：1 时间点毫秒，2 小区1名称，3 小区2名称  输出返回： 1-在  0-不在 -999999无法判定", checkCellHasNeighbour, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("小区1名称(string)"));
            fi.ParamDescList.Add(new ParamInfo("小区2名称(string)"));
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("获取更好的目标900小区", "输入：1 经度，2 纬度，3 方向值 4排除小区名  输出返回：小区名", getBetter900TargetCell, false);
            fi.ParamDescList.Add(new ParamInfo("经度double"));
            fi.ParamDescList.Add(new ParamInfo("纬度double"));
            fi.ParamDescList.Add(new ParamInfo("方向角度值int"));
            fi.ParamDescList.Add(new ParamInfo("小区名称(string)"));
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("获取更好的目标1800小区", "输入：1 经度，2 纬度，3 方向值 4排除小区名  输出返回：小区名", getBetter1800TargetCell, false);
            fi.ParamDescList.Add(new ParamInfo("经度double"));
            fi.ParamDescList.Add(new ParamInfo("纬度double"));
            fi.ParamDescList.Add(new ParamInfo("方向角度值int"));
            fi.ParamDescList.Add(new ParamInfo("小区名称(string)"));
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("是否存在隔小区覆盖情况", "输入：1 经度，2 纬度，3 小区名  输出返回：1 存在 0不存在", hasOverlapCell, false);
            fi.ParamDescList.Add(new ParamInfo("经度double"));
            fi.ParamDescList.Add(new ParamInfo("纬度double"));
            fi.ParamDescList.Add(new ParamInfo("小区名称(string)"));
            this.funcInfoDic[fi.Name] = fi;
            fi = new FuncInfo("获得某小区的参数", "输入：1 时间点毫秒，2 参数，3 小区名  输出返回：参数值", getCellParam, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("参数(string)"));
            fi.ParamDescList.Add(new ParamInfo("小区名称(string)"));
            this.funcInfoDic[fi.Name] = fi;
        }
        public override string Name
        {
            get { return "工参数据"; }
        }
        public override void fireDataFill(object objData)
        {
            if (objData is Event)
            {
                this.evt = objData as Event;
            }
        }
        #region 对外提供的调用函数
        private void getCellNameByLacCi(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                string s = arg.param;
                string[] args = s.Split(',');
                judgeParamCount(args, 3);
                judgeParamTime(args, 0);
                int lac = judgeParamLac(args, 1);
                int ci = judgeParamCi(args, 2);
                arg.str = "";
                if (evt != null)
                {
                    setCellName(arg, lac, ci);
                }
            }
        }

        private void setCellName(CommonEventArgs arg, int lac, int ci)
        {
            Cell cell = CellManager.GetInstance().GetCell(evt.DateTime, (ushort)lac, (ushort)ci);
            if (cell != null)
            {
                arg.str = cell.Name;
            }
            else
            {
                TDCell tdcell = CellManager.GetInstance().GetTDCell(evt.DateTime, lac, ci);
                if (tdcell != null)
                {
                    arg.str = tdcell.Name;
                }
                else
                {
                    WCell wcell = CellManager.GetInstance().GetWCell(evt.DateTime, lac, ci);
                    if (wcell != null)
                    {
                        arg.str = wcell.Name;
                    }
                }
            }
        }

        #region 判断输入参数
        private void judgeParamCount(string[] args, int count)
        {
            if (args.Length != count)
            {
                throw (new Exception("输入参数个数有误！"));
            }
        }

        private double judgeParamTime(string[] args, int index)
        {
            double timeValueAt;
            if (!double.TryParse(args[index], out timeValueAt))
            {
                throw (new Exception(string.Format("输入时间点有误！参数{0}需为long表示的时间毫秒值", index + 1)));
            }

            return timeValueAt;
        }

        private int judgeParamLac(string[] args, int index)
        {
            int lac = 0;
            if (!int.TryParse(args[index], out lac))
            {
                throw (new Exception(string.Format("输入参数个数有误！参数{0}需为整型lac", index + 1)));
            }

            return lac;
        }

        private int judgeParamCi(string[] args, int index)
        {
            int ci = 0;
            if (!int.TryParse(args[index], out ci))
            {
                throw (new Exception(string.Format("输入参数个数有误！参数{0}需为整型ci", index + 1)));
            }

            return ci;
        }

        private double judgeParamLongitude(string[] args, int index)
        {
            double longitude = 0;
            if (!double.TryParse(args[index], out longitude))
            {
                throw (new Exception(string.Format("输入参数个数有误！参数{0}需为经度", index + 1)));
            }

            return longitude;
        }

        private double judgeParamLatitude(string[] args, int index)
        {
            double latitude = 0;
            if (!double.TryParse(args[index], out latitude))
            {
                throw (new Exception(string.Format("输入参数个数有误！参数{0}需为纬度", index + 1)));
            }

            return latitude;
        }

        private void judgeParamDirection(string[] args, int index)
        {
            double dir = 0;
            if (!double.TryParse(args[index], out dir))
            {
                throw (new Exception(string.Format("输入参数个数有误！参数{0}需为方向角度值", index + 1)));
            }
        }

        private int judgeParamNearestCellCount(string[] args, int index)
        {
            int nearestCellCount = 0;
            if (!int.TryParse(args[index], out nearestCellCount))
            {
                throw (new Exception(string.Format("参数输入有误，参数{0}为参考基站个数！", index + 1)));
            }
            return nearestCellCount;
        }

        private int judgeParamBcch(string[] args, int index)
        {
            int bcch = 0;
            if (!int.TryParse(args[index], out bcch))
            {
                throw (new Exception(string.Format("输入参数个数有误！参数{0}需为整型bcch", index + 1)));
            }
            return bcch;
        }

        private int judgeParamBsic(string[] args, int index)
        {
            int bsic = 0;
            if (!int.TryParse(args[index], out bsic))
            {
                throw (new Exception(string.Format("输入参数个数有误！参数{0}需为整型bsic", index + 1)));
            }
            return bsic;
        }
        #endregion

        private void checkCellHasNeighbour(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                string s = arg.param;
                string[] args = s.Split(',');
                judgeParamCount(args, 3);
                judgeParamTime(args, 0);
                string cellName1 = args[1];
                string cellName2 = args[2];
                arg.ret = -99999;
                Cell cell = CellManager.GetInstance().GetCellByName(cellName1);
                if (cell != null)
                {
                    if (cell.GetNeighbourCells().Count == 0)
                    {
                        QueryCellNeighbourInfo queryNeighbour = new QueryCellNeighbourInfo(MainModel.GetInstance(), cell.ID);
                        queryNeighbour.Query();
                    }
                    foreach (Cell nc in cell.GetNeighbourCells())
                    {
                        if (nc.Name.Equals(cellName2))
                        {
                            arg.ret = 1;
                            return;
                        }
                    }
                    arg.ret = 0;
                }
            }
        }

        private void getBetter900TargetCell(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                string s = arg.param;
                string[] args = s.Split(',');
                judgeParamCount(args, 4);
                double longitude = judgeParamLongitude(args, 0);
                double latitude = judgeParamLatitude(args, 1);
                judgeParamDirection(args, 2);
                string cellName = args[3];
                arg.ret = double.NaN;
                arg.str = "";
                List<Cell> cells = CellManager.GetInstance().GetCurrentCells();
                double minDist = 999999;
                Cell tarCell = null;
                foreach (Cell cell in cells)
                {
                    setTarCell(longitude, latitude, cellName, ref minDist, ref tarCell, cell, BTSBandType.GSM900);
                }
                if (tarCell != null)
                {
                    arg.str = tarCell.Name;
                }
            }
        }

        private void setTarCell(double longitude, double latitude, string cellName, ref double minDist, ref Cell tarCell, Cell cell, BTSBandType type)
        {
            if (cell.Type == BTSType.Outdoor && cell.DirectionType == AntennaDirectionType.Beam && cell.Name != cellName && cell.BandType == type)
            {
                double dist = MathFuncs.GetDistance(cell.Longitude, cell.Latitude, longitude, latitude);
                if (dist < CD.MAX_COV_DISTANCE_GSM) //在一定距离内
                {
                    bool inCover = MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, longitude, latitude, cell.Direction);
                    if (inCover && dist < minDist)//在覆盖角度内
                    {
                        tarCell = cell;
                        minDist = dist;
                    }
                }
            }
        }

        private void getBetter1800TargetCell(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                string s = arg.param;
                string[] args = s.Split(',');
                judgeParamCount(args, 4);
                double longitude = judgeParamLongitude(args, 0);
                double latitude = judgeParamLatitude(args, 1);
                judgeParamDirection(args, 2);
                string cellName = args[3];
                arg.ret = double.NaN;
                arg.str = "";
                List<Cell> cells = CellManager.GetInstance().GetCurrentCells();
                double minDist = 999999;
                Cell tarCell = null;
                foreach (Cell cell in cells)
                {
                    setTarCell(longitude, latitude, cellName, ref minDist, ref tarCell, cell, BTSBandType.DSC1800);
                }
                if (tarCell != null)
                {
                    arg.str = tarCell.Name;
                }
            }
        }

        private void getCellParam(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                string s = arg.param;
                string[] args = s.Split(',');
                judgeParamCount(args, 3);
                judgeParamTime(args, 0);
                string param = args[1].ToUpper();
                string cellName = args[2];
                setCellInfo(arg, param, cellName);
            }
        }

        private void setCellInfo(CommonEventArgs arg, string param, string cellName)
        {
            Cell curCell = CellManager.GetInstance().GetCellByName(cellName);
            if (curCell != null)
            {
                setCellInfo(arg, param, curCell);
            }
            else
            {
                TDCell tdcurCell = CellManager.GetInstance().GetTDCellByName(cellName);
                if (tdcurCell != null)
                {
                    setTDCellInfo(arg, param, tdcurCell);
                }
                else
                {
                    WCell wcurCell = CellManager.GetInstance().GetWCellByName(cellName);
                    if (wcurCell != null)
                    {
                        setWCellInfo(arg, param, wcurCell);
                    }
                }
            }
        }

        private void setCellInfo(CommonEventArgs arg, string param, Cell curCell)
        {
            if (param.Equals("MSC名称"))
            {
                arg.str = curCell.BelongBTS.BelongBSC.BelongMSC.Name;
            }
            else if (param.Equals("BSC名称"))
            {
                arg.str = curCell.BelongBTS.BelongBSC.Name;
            }
            else if (param.Equals("小区类型"))
            {
                //Outdoor = 1,
                //Upper,
                //Indoor,
                //Other
                arg.str = curCell.Type.ToString();
            }
            else if (param.Equals("BANDTYPE"))
            {
                //GSM900 = 1,
                //DSC1800,
                //Other
                arg.str = curCell.BandType.ToString();
            }
            else if (param.Equals("小区描述"))
            {
                arg.str = curCell.DetailInfo;
            }
            else if (param.Equals("小区编码"))
            {
                arg.str = curCell.Code;
            }
            else if (param.Equals("HOP"))
            {
                arg.str = curCell.HOP ? "是" : "否";
            }
            else if (param.Equals("TCH"))
            {
                arg.str = curCell.TCHDescription;
            }
            ///==数值
            else if (param.Equals("LAC"))
            {
                arg.ret = curCell.LAC;
            }
            else if (param.Equals("CI"))
            {
                arg.ret = curCell.CI;
            }
            else if (param.Equals("BCCH"))
            {
                arg.ret = curCell.BCCH;
            }
            else if (param.Equals("BSIC"))
            {
                arg.ret = curCell.BSIC;
            }
        }

        private void setTDCellInfo(CommonEventArgs arg, string param, TDCell tdcurCell)
        {
            if (param.Equals("MSC名称"))
            {
                arg.str = tdcurCell.BelongBTS.BelongBSC.BelongMSC.Name;
            }
            else if (param.Equals("BSC名称"))
            {
                arg.str = tdcurCell.BelongBTS.BelongBSC.Name;
            }
            else if (param.Equals("小区类型"))
            {
                //Outdoor = 1,
                //Upper,
                //Indoor,
                //Other
                arg.str = tdcurCell.Type.ToString();
            }
            else if (param.Equals("小区描述"))
            {
                arg.str = tdcurCell.DetailInfo;
            }
            else if (param.Equals("小区编码"))
            {
                arg.str = tdcurCell.Code;
            }
            ///==数值
            else if (param.Equals("LAC"))
            {
                arg.ret = tdcurCell.LAC;
            }
            else if (param.Equals("CI"))
            {
                arg.ret = tdcurCell.CI;
            }
            else if (param.Equals("FREQ"))
            {
                arg.ret = tdcurCell.FREQ;
            }
            else if (param.Equals("CPI"))
            {
                arg.ret = tdcurCell.CPI;
            }
        }

        private void setWCellInfo(CommonEventArgs arg, string param, WCell wcurCell)
        {
            if (param.Equals("MSC名称"))
            {
                arg.str = wcurCell.BelongNodeBs[0].BelongRNC.BelongMGW.Name;
            }
            else if (param.Equals("BSC名称"))
            {
                arg.str = wcurCell.BelongNodeBs[0].BelongRNC.Name;
            }
            else if (param.Equals("小区类型"))
            {
                //Outdoor = 1,
                //Upper,
                //Indoor,
                //Other
                arg.str = wcurCell.Type.ToString();
            }
            else if (param.Equals("小区描述"))
            {
                arg.str = wcurCell.DetailInfo;
            }
            else if (param.Equals("小区编码"))
            {
                arg.str = wcurCell.Code;
            }
            ///==数值
            else if (param.Equals("LAC"))
            {
                arg.ret = wcurCell.LAC;
            }
            else if (param.Equals("CI"))
            {
                arg.ret = wcurCell.CI;
            }
            else if (param.Equals("UARFCN"))
            {
                arg.ret = wcurCell.UARFCN;
            }
            else if (param.Equals("PSC"))
            {
                arg.ret = wcurCell.PSC;
            }
        }

        private void hasOverlapCell(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                string s = arg.param;
                string[] args = s.Split(',');
                judgeParamCount(args, 3);
                double longitude = judgeParamLongitude(args, 0);
                double latitude = judgeParamLatitude(args, 1);
                string cellName = args[2];
                arg.ret = 0;
                setInCoverArg(arg, longitude, latitude, cellName);
            }
        }

        private void setInCoverArg(CommonEventArgs arg, double longitude, double latitude, string cellName)
        {
            Cell curCell = CellManager.GetInstance().GetCellByName(cellName);
            if (curCell != null)
            {
                judgeCellInCover(arg, longitude, latitude, curCell);
            }
            else
            {
                TDCell curTDCell = CellManager.GetInstance().GetTDCellByName(cellName);
                if (curTDCell != null)
                {
                    judgeTDCellInCover(arg, longitude, latitude, curTDCell);
                }
                else
                {
                    WCell curWCell = CellManager.GetInstance().GetWCellByName(cellName);
                    if (curWCell != null)
                    {
                        judgeWCellInCover(arg, longitude, latitude, curWCell);
                    }
                }
            }
        }

        private void judgeCellInCover(CommonEventArgs arg, double longitude, double latitude, Cell curCell)
        {
            double minLong = curCell.Longitude < longitude ? curCell.Longitude : longitude;
            double maxLong = curCell.Longitude < longitude ? longitude : curCell.Longitude;
            double minLat = curCell.Latitude < latitude ? curCell.Latitude : latitude;
            double maxLat = curCell.Latitude < latitude ? latitude : curCell.Latitude;
            List<Cell> cells = CellManager.GetInstance().GetCurrentCells();
            foreach (Cell cell in cells)
            {
                if (cell.Longitude > minLong && cell.Longitude < maxLong && cell.Latitude > minLat && cell.Latitude < maxLat)
                {
                    bool inCover = MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, longitude, latitude, cell.Direction);
                    if (inCover)//在覆盖角度内
                    {
                        arg.ret = 1;
                    }
                }
            }
        }

        private void judgeTDCellInCover(CommonEventArgs arg, double longitude, double latitude, TDCell curTDCell)
        {
            double minLong = curTDCell.Longitude < longitude ? curTDCell.Longitude : longitude;
            double maxLong = curTDCell.Longitude < longitude ? longitude : curTDCell.Longitude;
            double minLat = curTDCell.Latitude < latitude ? curTDCell.Latitude : latitude;
            double maxLat = curTDCell.Latitude < latitude ? latitude : curTDCell.Latitude;
            List<TDCell> tdcells = CellManager.GetInstance().GetCurrentTDCells();
            foreach (TDCell tdcell in tdcells)
            {
                if (tdcell.Longitude > minLong && tdcell.Longitude < maxLong && tdcell.Latitude > minLat && tdcell.Latitude < maxLat)
                {
                    bool inCover = MathFuncs.JudgePoint(tdcell.Longitude, tdcell.Latitude, longitude, latitude, tdcell.Direction);
                    if (inCover)//在覆盖角度内
                    {
                        arg.ret = 1;
                    }
                }
            }
        }

        private void judgeWCellInCover(CommonEventArgs arg, double longitude, double latitude, WCell curWCell)
        {
            double minLong = curWCell.Longitude < longitude ? curWCell.Longitude : longitude;
            double maxLong = curWCell.Longitude < longitude ? longitude : curWCell.Longitude;
            double minLat = curWCell.Latitude < latitude ? curWCell.Latitude : latitude;
            double maxLat = curWCell.Latitude < latitude ? latitude : curWCell.Latitude;
            ReadOnlyCollection<WCell> wcells = CellManager.GetInstance().GetCurrentWCellsReadOnly();
            foreach (WCell wcell in wcells)
            {
                if (wcell.Longitude > minLong && wcell.Longitude < maxLong && wcell.Latitude > minLat && wcell.Latitude < maxLat)
                {
                    bool inCover = MathFuncs.JudgePoint(wcell.Longitude, wcell.Latitude, longitude, latitude, (int)wcell.Direction);
                    if (inCover)//在覆盖角度内
                    {
                        arg.ret = 1;
                    }
                }
            }
        }

        private void getDesignedRadius(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                string s = arg.param;
                string[] args = s.Split(',');
                judgeParamCount(args, 2);
                int nearestCellCount = judgeParamNearestCellCount(args, 1);
                string cellName = args[0];
                
                arg.ret = -99999;
                Cell cell = CellManager.GetInstance().GetCellByName(cellName);
                if (cell != null)
                {
                    arg.ret = CalculateRadius(cell, nearestCellCount,false);
                }
                else
                {
                    TDCell tdcell = CellManager.GetInstance().GetTDCellByName(cellName);
                    if (tdcell != null)
                    {
                        arg.ret = CalculateRadius(tdcell, nearestCellCount);
                    }
                    else//WCell
                    {
                        WCell wcell = CellManager.GetInstance().GetWCellByName(cellName);
                        if (wcell != null)
                        {
                            arg.ret = CalculateRadius(wcell, nearestCellCount);
                        }
                    }
                }
            }
        }

        private void getDirectionToCell(object sender, EventArgs e)
        {
            CommonEventArgs arg = e as CommonEventArgs;
            if (arg == null)
            {
                return;
            }

            double longitude;
            double latitude;
            string cellName;
            getCondByArgParam(arg.param, out longitude, out latitude, out cellName);

            arg.ret = -99999;
            Cell cell = CellManager.GetInstance().GetCellByName(cellName);
            if (cell != null)
            {
                if (cell.DirectionType == AntennaDirectionType.Beam)
                {
                    arg.ret = getCellWithPointAngle(cell, longitude, latitude);
                }
            }
            else
            {
                TDCell tdcell = CellManager.GetInstance().GetTDCellByName(cellName);
                if (tdcell != null)
                {
                    arg.ret = getCellWithPointAngle(tdcell, longitude, latitude);
                }
                else
                {
                    WCell wcell = CellManager.GetInstance().GetWCellByName(cellName);
                    arg.ret = getCellWithPointAngle(wcell, longitude, latitude);
                }
            }
        }

        private void getCondByArgParam(string strParam, out double longitude, out double latitude
            , out string cellName)
        {
            string[] args = strParam.Split(',');
            if (args.Length != 3)
            {
                throw (new Exception("输入参数个数有误！"));
            }
            longitude = 0;
            if (!double.TryParse(args[0], out longitude))
            {
                throw (new Exception("输入参数个数有误！参数1需为经度"));
            }
            latitude = 0;
            if (!double.TryParse(args[1], out latitude))
            {
                throw (new Exception("输入参数个数有误！参数2需为经度"));
            }

            cellName = args[2];
        }

        private int getCellWithPointAngle(ICell icell, double longitude, double latitude)
        {
            int angle = -99999;
            if (icell != null)
            {
                double cellLong = icell.Longitude;
                double cellLat = icell.Latitude;
                int dir = MathFuncs.getAngleFromPointToPoint(cellLong, cellLat, longitude, latitude);
                angle = Math.Abs(dir - icell.Direction);
                if (angle > 180)
                {
                    return angle - 180;
                }
                else
                {
                    return angle;
                }
            }

            return angle;
        }

        private void getDistanceByCellName(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                string s = arg.param;
                string[] args = s.Split(',');
                judgeParamCount(args, 3);
                double longitude = judgeParamLongitude(args, 0);
                double latitude = judgeParamLatitude(args, 1);
                string cellName = args[2];
                arg.ret = -99999;
                Cell cell = CellManager.GetInstance().GetCellByName(cellName);
                if (cell != null)
                {
                    arg.ret = (int)cell.GetDistance(longitude, latitude);
                }
                else
                {
                    TDCell tdcell = CellManager.GetInstance().GetTDCellByName(cellName);
                    if (tdcell != null)
                    {
                        arg.ret = (int)tdcell.GetDistance(longitude, latitude);
                    }
                    else
                    {
                        WCell wcell = CellManager.GetInstance().GetWCellByName(cellName);
                        if (wcell != null)
                        {
                            arg.ret = (int)wcell.GetDistance(longitude, latitude);
                        }
                    }
                }
            }
        }

        private void getCellNameByLongLatBcchBsic(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                string s = arg.param;
                string[] args = s.Split(',');
                judgeParamCount(args, 5);
                double timeValueAt = judgeParamTime(args, 0);
                double longitude = judgeParamLongitude(args, 1);
                double latitude = judgeParamLatitude(args, 2);
                int bcch = judgeParamBcch(args, 3);
                int bsic = judgeParamBsic(args, 4);
                arg.ret = double.NaN;
                arg.str = "";
                if (evt != null)
                {
                    setCellName(arg, timeValueAt, longitude, latitude, bcch, bsic);
                }
            }
        }

        private void setCellName(CommonEventArgs arg, double timeValueAt, double longitude, double latitude, int bcch, int bsic)
        {
            DateTime time = JavaDate.GetDateTimeFromMilliseconds((long)timeValueAt);
            Cell cell = CellManager.GetInstance().GetNearestCell(time, (short)bcch, (byte)bsic, longitude, latitude);
            if (cell != null)
            {
                arg.str = cell.Name;
            }
            else
            {
                TDCell tdcell = CellManager.GetInstance().GetNearestTDCell(evt.DateTime, (short)bcch, (short)bsic, longitude, latitude);
                if (tdcell != null)
                {
                    arg.str = tdcell.Name;
                }
                else
                {
                    WCell wcell = CellManager.GetInstance().GetNearestWCell(evt.DateTime, (short)bcch, (short)bsic, longitude, latitude);
                    if (wcell != null)
                    {
                        arg.str = wcell.Name;
                    }
                }
            }
        }

        private void getServCellName(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                setServCellName(e);
            }
        }

        private void setServCellName(EventArgs e)
        {
            CommonEventArgs arg = e as CommonEventArgs;
            if (evt != null)
            {
                Cell cell = CellManager.GetInstance().GetCell(evt.DateTime, (ushort)(int)evt["LAC"], (ushort)(int)evt["CI"]);
                if (cell != null)
                {
                    arg.str = cell.Name;
                }
                else
                {
                    TDCell tdcell = CellManager.GetInstance().GetTDCell(evt.DateTime, (int)evt["LAC"], (int)evt["CI"]);
                    if (tdcell != null)
                    {
                        arg.str = tdcell.Name;
                    }
                    else
                    {
                        WCell wcell = CellManager.GetInstance().GetWCell(evt.DateTime, (int)evt["LAC"], (int)evt["CI"]);
                        if (wcell != null)
                        {
                            arg.str = wcell.Name;
                        }
                    }
                }
            }
        }

        private void isIndoorStation(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt != null)
                {
                    setArgType(arg);
                }
            }
        }

        private void setArgType(CommonEventArgs arg)
        {
            Cell cell = CellManager.GetInstance().GetCell(evt.DateTime, (ushort)(int)evt["LAC"], (ushort)(int)evt["CI"]);
            if (cell != null)
            {
                setCellType(arg, cell);
            }
            else// To find TD
            {
                TDCell tdcell = CellManager.GetInstance().GetTDCell(evt.DateTime, (ushort)(int)evt["LAC"], (ushort)(int)evt["CI"]);
                if (tdcell != null)
                {
                    setTDCellType(arg, tdcell);
                }
                else //To find W
                {
                    WCell wcell = CellManager.GetInstance().GetWCell(evt.DateTime, (short)(int)evt["LAC"], (short)(int)evt["CI"]);
                    if (wcell != null && wcell.BelongNodeBs.Count > 0)
                    {
                        setWCellType(arg, wcell);
                    }
                }
            }
        }

        private void setCellType(CommonEventArgs arg, Cell cell)
        {
            if (cell.BelongBTS.Type == BTSType.Indoor)
            {
                arg.ret = 1;
            }
            else
            {
                arg.ret = 0;
            }
        }

        private void setTDCellType(CommonEventArgs arg, TDCell tdcell)
        {
            if (tdcell.BelongBTS.Type == TDNodeBType.Indoor)
            {
                arg.ret = 1;
            }
            else
            {
                arg.ret = 0;
            }
        }

        private void setWCellType(CommonEventArgs arg, WCell wcell)
        {
            if (wcell.BelongNodeBs[0].Type == WNodeBType.Indoor)
            {
                arg.ret = 1;
            }
            else
            {
                arg.ret = 0;
            }
        }

        private void getDistanceToServCell(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt != null)
                {
                    setArgDistance(arg);
                }
            }
        }

        private void setArgDistance(CommonEventArgs arg)
        {
            Cell cell = CellManager.GetInstance().GetCell(evt.DateTime, (ushort)(int)evt["LAC"], (ushort)(int)evt["CI"]);
            if (cell != null)
            {
                arg.ret = (int)cell.GetDistance(evt.Longitude, evt.Latitude);
            }
            else
            {
                TDCell tdcell = CellManager.GetInstance().GetTDCell(evt.DateTime, (ushort)(int)evt["LAC"], (ushort)(int)evt["CI"]);
                if (tdcell != null)
                {
                    arg.ret = (int)tdcell.GetDistance(evt.Longitude, evt.Latitude);
                }
                else// w to find
                {
                    WCell wcell = CellManager.GetInstance().GetWCell(evt.DateTime, (ushort)(int)evt["LAC"], (ushort)(int)evt["CI"]);
                    if (wcell != null)
                    {
                        arg.ret = (int)wcell.GetDistance(evt.Longitude, evt.Latitude);
                    }
                    else
                    {
                        arg.ret = -1;
                    }
                }
            }
        }
        #endregion
        /// <summary>
        /// 获得某小区的理想覆盖半径
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        public static double CalculateRadius(WCell cell, int nearestCellCount)
        {
            IEnumerable<WNodeB> btsList = CellManager.GetInstance().GetCurrentWNodeBs();
            CalculateCell calCell = new CalculateCell(new CellInfo(cell), 0);
            return CalculateRadius(cell, btsList, nearestCellCount, ref calCell);
        }

        public static double CalculateRadius(WCell cell, IEnumerable<WNodeB> btsList, int nearestCellCount, ref CalculateCell calCell)
        {
            ReadOnlyCollection<WAntenna> antennaList = cell.Antennas;
            List<CellDistanceRem> distList = new List<CellDistanceRem>();
            string btsName = cell.BelongNodeBs.Count > 0 ? cell.BelongNodeBs[0].Name : "";
            foreach (WAntenna antenna in antennaList)
            {
                foreach (WNodeB bts in btsList)
                {
                    if (bts.Type == WNodeBType.Indoor || bts.Name == btsName || bts.Cells.Count == 0)
                    {
                        continue;
                    }
                    CellDistanceRem rem = getValidBts(antenna.Longitude, antenna.Latitude, antenna.Direction, bts, CD.MAX_COV_DISTANCE_W);
                    if (rem != null)
                    {
                        distList.Add(rem);
                    }
                }
            }
            GetCalCellInfo(distList, nearestCellCount, ref calCell);
            return calCell.Distance;
        }

        /// <summary>
        /// 获得某小区的理想覆盖半径
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        public static double CalculateRadius(TDCell cell, int nearestCellCount)
        {
            List<TDNodeB> btsList = CellManager.GetInstance().GetCurrentTDBTSs();

            CalculateCell calCell = new CalculateCell(new CellInfo(cell),0);
            return CalculateRadius(cell, btsList, nearestCellCount, ref calCell);
        }

        public static double CalculateRadius(TDCell cell, List<TDNodeB> btsList, int nearestCellCount, ref CalculateCell calCell)
        {
            List<CellDistanceRem> tdCellRemList = new List<CellDistanceRem>();
            string btsName = cell.BelongBTS.Name;
            TDAntenna antenna = cell.Antenna;

            foreach (TDNodeB bts in btsList)
            {
                if (bts.Type == TDNodeBType.Indoor || bts.Name == btsName || bts.Cells.Count == 0)
                {
                    continue;
                }
                CellDistanceRem rem = getValidBts(antenna.Longitude, antenna.Latitude, antenna.Direction, bts, CD.MAX_COV_DISTANCE_TD);
                if (rem != null)
                {
                    tdCellRemList.Add(rem);
                }
            }

            GetCalCellInfo(tdCellRemList, nearestCellCount, ref calCell);
            return calCell.Distance;
        }

        /// <summary>
        /// 获得某小区的理想覆盖半径
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="nearestCellCount"></param>
        /// <param name="btsList"></param>
        /// <returns></returns>
        public static double CalculateRadius(LTECell cell, List<LTEBTS> btsList, int nearestCellCount, ref CalculateCell calCell)
        {
            List<CellDistanceRem> lteCelList = new List<CellDistanceRem>();
            string btsName = cell.BelongBTS.Name;
            LTEAntenna antenna = cell.Antennas[0];

            foreach (LTEBTS bts in btsList)
            {
                if (bts.Name == btsName || bts.Cells.Count == 0 || bts.Type == LTEBTSType.Indoor)
                {
                    continue;
                }
                CellDistanceRem rem = getValidBts(antenna.Longitude, antenna.Latitude, antenna.Direction, bts, CD.MAX_COV_DISTANCE_LTE);
                if (rem != null)
                {
                    lteCelList.Add(rem);
                }
            }

            GetCalCellInfo(lteCelList, nearestCellCount, ref calCell);
            return calCell.Distance;
        }

        /// <summary>
        /// 获得某小区的理想覆盖半径
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        public static double CalculateRadius(LTECell cell, int nearestCellCount)
        {
            List<LTEBTS> btsList = CellManager.GetInstance().GetCurrentLTEBTSs();

            CalculateCell calCell = new CalculateCell(new CellInfo(cell), 0);
            return CalculateRadius(cell, btsList, nearestCellCount,ref calCell);
        }

        public static double CalculateRadius(NRCell cell, int nearestCellCount)
        {
            List<NRBTS> btsList = CellManager.GetInstance().GetCurrentNRBTSs();

            CalculateCell calCell = new CalculateCell(new CellInfo(cell), 0);
            return CalculateRadius(cell, btsList, nearestCellCount, ref calCell);
        }

        public static double CalculateRadius(NRCell cell, List<NRBTS> btsList, int nearestCellCount, ref CalculateCell calCell)
        {
            List<CellDistanceRem> lteCelList = new List<CellDistanceRem>();
            string btsName = cell.BelongBTS.Name;
            if (cell.Antennas.Count == 0)
            {
                return 0;
            }
            NRAntenna antenna = cell.Antennas[0];

            foreach (NRBTS bts in btsList)
            {
                if (bts.Name == btsName || bts.Cells.Count == 0 || bts.Type == NRBTSType.Indoor)
                {
                    continue;
                }
                CellDistanceRem rem = getValidBts(antenna.Longitude, antenna.Latitude, antenna.Direction, bts, CD.MAX_COV_DISTANCE_NR);
                if (rem != null)
                {
                    lteCelList.Add(rem);
                }
            }

            GetCalCellInfo(lteCelList, nearestCellCount, ref calCell);
            return calCell.Distance;
        }

        /// <summary>
        /// 获得某小区的理想覆盖半径 added by wj
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        public static double CalculateRadius(Cell cell, int nearestCellCount, bool isOnlySameBand)
        {
            List<BTS> btsList = CellManager.GetInstance().GetCurrentBTSs();
            CalculateCell calCell = new CalculateCell(null, 0);
            return CalculateRadius(cell, btsList, nearestCellCount, isOnlySameBand,ref calCell);
        }

        public static double CalculateRadius(Cell cell, List<BTS> indoorBtsSet, int nearestCellCount, bool isOnlySameBand, ref CalculateCell calCell)
        {
            if (indoorBtsSet == null)
            {
                throw (new Exception("indoorBtsSet 不能为null"));
            }
            List<CellDistanceRem> distList = new List<CellDistanceRem>();
            string btsName = cell.BelongBTS.Name;
            Antenna antenna = cell.Antennas[0];

            foreach (BTS bts in indoorBtsSet)
            {
                if (bts.Type == BTSType.Indoor || bts.Name == btsName || bts.Cells.Count == 0)
                {
                    continue;
                }
                if (isOnlySameBand && cell.BelongBTS.BandType != bts.BandType)   
                {
                    //只看同频段的基站
                    //频段类型不同
                    continue;
                }

                CellDistanceRem rem = getValidBts(antenna.Longitude, antenna.Latitude, antenna.Direction, bts, CD.MAX_COV_DISTANCE_GSM);
                if (rem !=null)
                {
                    distList.Add(rem);
                }
            }
            GetCalCellInfo(distList, nearestCellCount, ref calCell);
            return calCell.Distance;
        }

        public static double CalculateRadius(Cell cell, int nearestCellCount, bool isOnlySameBand, out string realNearestCell)
        {
            List<BTS> btsList = CellManager.GetInstance().GetCurrentBTSs();
            CalculateCell calCell = new CalculateCell(null, 0);
            return CalculateRadius(cell, btsList, nearestCellCount, isOnlySameBand, ref calCell, out realNearestCell);
        }

        public static double CalculateRadius(Cell cell, List<BTS> indoorBtsSet, int nearestCellCount, bool isOnlySameBand, ref CalculateCell calCell, out string realNearestCell)
        {
            if (indoorBtsSet == null)
            {
                throw (new Exception("indoorBtsSet 不能为null"));
            }
            List<CellDistanceRem> distList = new List<CellDistanceRem>();
            string btsName = cell.BelongBTS.Name;
            Antenna antenna = cell.Antennas[0];

            foreach (BTS bts in indoorBtsSet)
            {
                if (bts.Type != BTSType.Indoor && bts.Name != btsName && bts.Cells.Count != 0 
                    && (!isOnlySameBand || cell.BelongBTS.BandType == bts.BandType))
                {
                    CellDistanceRem rem = getValidBts(antenna.Longitude, antenna.Latitude, antenna.Direction, bts, CD.MAX_COV_DISTANCE_GSM);
                    if (rem != null)
                    {
                        distList.Add(rem);
                    }
                }
            }
            GetCalCellInfo(distList, nearestCellCount, ref calCell, out realNearestCell);
            return calCell.Distance;
        }

        public static void GetCalCellInfo(List<CellDistanceRem> distList, int nearestCellCount, ref CalculateCell calCell, out string realNearestCell)
        {
            realNearestCell = "";
            double meanDistance;
            if (distList.Count > 0)
            {
                distList.Sort();

                List<CellDistanceRem> recordedList = getRecordedList(distList, nearestCellCount, out realNearestCell, out meanDistance);
                if (recordedList.Count > 0)
                {
                    meanDistance = meanDistance / recordedList.Count;

                    calCell.NearestBtsName = recordedList[0].btsName;
                    calCell.NearestBtsDis = recordedList[0].distance;
                    calCell.FarthestBtsName = recordedList[recordedList.Count - 1].btsName;
                    calCell.FarthestBtsDis = recordedList[recordedList.Count - 1].distance;
                }
                else
                {
                    meanDistance = 0;
                }
            }
            else
            {
                meanDistance = 0;
            }
            calCell.Distance = meanDistance;
        }

        private static List<CellDistanceRem> getRecordedList(List<CellDistanceRem> distList, int nearestCellCount, out string realNearestCell, out double meanDistance)
        {
            realNearestCell = "";
            meanDistance = 0;
            List<CellDistanceRem> recordedList = new List<CellDistanceRem>();
            for (int i = 0; i < distList.Count; i++)
            {
                CellDistanceRem cRem = distList[i];
                bool isValid = true;//与已经被选的是否距离都在 50以上？
                for (int check = 0; check < recordedList.Count; check++)
                {
                    CellDistanceRem cc = recordedList[check];
                    if (MathFuncs.GetDistance(cc.longitude, cc.latitude, cRem.longitude, cRem.latitude) < 50)
                    {
                        isValid = false;
                        break;
                    }
                }
                if (isValid)
                {
                    meanDistance = meanDistance + distList[i].distance;
                    recordedList.Add(cRem);
                    realNearestCell = string.Format(realNearestCell + cRem.btsName + "|");
                }
                if (recordedList.Count >= nearestCellCount)
                {
                    break;
                }
            }
            return recordedList;
        }

        private static CellDistanceRem getValidBts(double antennaLongitude, double antennaLatitude, short antennaDirection
            , ISite bts, int maxDistance)
        {
            CellDistanceRem rem = null;
            double btsLongitude = bts.Longitude;
            double btsLatitude = bts.Latitude;
            double minLong = antennaLongitude - 0.06;
            double maxLong = antennaLongitude + 0.06;
            double minLat = antennaLatitude - 0.06;
            double maxLat = antennaLatitude + 0.06;

            if ((btsLongitude >= minLong) && (btsLongitude <= maxLong) &&
                (btsLatitude <= maxLat) && (btsLatitude >= minLat))
            {
                double distance = MathFuncs.GetDistance(antennaLongitude, antennaLatitude, btsLongitude, btsLatitude);
                if ((distance < maxDistance) && (distance > 50))//限制最近距离
                {
                    bool inCover = MathFuncs.JudgePoint(antennaLongitude, antennaLatitude, btsLongitude, btsLatitude, antennaDirection);
                    if (inCover)
                    {
                        rem = new CellDistanceRem();
                        rem.btsName = bts.Name;
                        rem.distance = distance;
                        rem.longitude = bts.Longitude;
                        rem.latitude = bts.Latitude;
                    }
                }
            }

            return rem;
        }

        public static void GetCalCellInfo(List<CellDistanceRem> distList, int nearestCellCount, ref CalculateCell calCell)
        {
            double meanDistance = 0;
            if (distList.Count > 0)
            {
                distList.Sort();

                List<CellDistanceRem> recordedList = new List<CellDistanceRem>();
                meanDistance = addRecordedList(distList, nearestCellCount, meanDistance, recordedList);
                if (recordedList.Count > 0)
                {
                    meanDistance = meanDistance / recordedList.Count;

                    calCell.NearestBtsName = recordedList[0].btsName;
                    calCell.NearestBtsDis = recordedList[0].distance;
                    calCell.FarthestBtsName = recordedList[recordedList.Count - 1].btsName;
                    calCell.FarthestBtsDis = recordedList[recordedList.Count - 1].distance;
                }
                else
                {
                    meanDistance = 0;
                }
            }
            else
            {
                meanDistance = 0;
            }
            calCell.Distance = meanDistance;
        }

        private static double addRecordedList(List<CellDistanceRem> distList, int nearestCellCount, double meanDistance, List<CellDistanceRem> recordedList)
        {
            for (int i = 0; i < distList.Count; i++)
            {
                CellDistanceRem cRem = distList[i];
                bool isValid = true;//与已经被选的是否距离都在 50以上？
                for (int check = 0; check < recordedList.Count; check++)
                {
                    CellDistanceRem cc = recordedList[check];
                    if (MathFuncs.GetDistance(cc.longitude, cc.latitude, cRem.longitude, cRem.latitude) < 50)
                    {
                        isValid = false;
                        break;
                    }
                }
                if (isValid)
                {
                    meanDistance = meanDistance + distList[i].distance;
                    recordedList.Add(cRem);
                }
                if (recordedList.Count >= nearestCellCount)
                {
                    break;
                }
            }

            return meanDistance;
        }

        /// <summary>
        /// 获得某小区的最近三个基站的名称
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        public static string GetNearestBTSs(Cell cell, int nearestCellCount)
        {
            List<BTS> btsList = CellManager.GetInstance().GetCurrentBTSs();
            List<CellDistanceBTSName> nearestList = new List<CellDistanceBTSName>();
            string btsName = cell.BelongBTS.Name;
            Antenna antenna = cell.Antennas[0];
            double minLong = antenna.Longitude - 0.06;
            double maxLong = antenna.Longitude + 0.06;
            double minLat = antenna.Latitude - 0.06;
            double maxLat = antenna.Latitude + 0.06;

            foreach (BTS bts in btsList)
            {
                if (bts.Type != BTSType.Indoor && bts.Name != btsName && bts.Cells.Count != 0)
                {
                    addNearestList(nearestList, antenna, minLong, maxLong, minLat, maxLat, bts);
                }
            }

            StringBuilder sbBTSs = getsbBTSs(nearestCellCount, nearestList);
            return sbBTSs.ToString();
        }

        private static void addNearestList(List<CellDistanceBTSName> nearestList, Antenna antenna, double minLong, double maxLong, double minLat, double maxLat, BTS bts)
        {
            if ((bts.Longitude >= minLong) && (bts.Longitude <= maxLong) &&
                (bts.Latitude <= maxLat) && (bts.Latitude >= minLat))
            {
                double distance = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude);
                if ((distance < CD.MAX_COV_DISTANCE_TD) && (distance > 50))
                {
                    bool inCover = MathFuncs.JudgePoint(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude, antenna.Direction);
                    if (inCover)
                    {
                        CellDistanceBTSName btsItem = new CellDistanceBTSName();
                        btsItem.distance = distance;
                        btsItem.name = bts.Name;
                        nearestList.Add(btsItem);
                    }
                }
            }
        }

        /// <summary>
        /// 获得某小区的最近三个基站的名称
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        public static string GetNearestBTSs(TDCell cell, int nearestCellCount)
        {
            List<TDNodeB> btsList = CellManager.GetInstance().GetCurrentTDBTSs();
            List<CellDistanceBTSName> nearestList = new List<CellDistanceBTSName>();
            string btsName = cell.BelongBTS.Name;
            TDAntenna antenna = cell.Antenna;
            double minLong = antenna.Longitude - 0.06;
            double maxLong = antenna.Longitude + 0.06;
            double minLat = antenna.Latitude - 0.06;
            double maxLat = antenna.Latitude + 0.06;

            foreach (TDNodeB bts in btsList)
            {
                if (bts.Type != TDNodeBType.Indoor && bts.Name != btsName && bts.Cells.Count != 0)
                {
                    addNearestList(nearestList, antenna, minLong, maxLong, minLat, maxLat, bts);
                }
            }

            StringBuilder sbBTSs = getsbBTSs(nearestCellCount, nearestList);
            return sbBTSs.ToString();
        }

        private static void addNearestList(List<CellDistanceBTSName> nearestList, TDAntenna antenna, double minLong, double maxLong, double minLat, double maxLat, TDNodeB bts)
        {
            if ((bts.Longitude >= minLong) && (bts.Longitude <= maxLong) &&
                (bts.Latitude <= maxLat) && (bts.Latitude >= minLat))
            {
                double distance = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude);
                if ((distance < CD.MAX_COV_DISTANCE_TD) && (distance > 50))
                {
                    bool inCover = MathFuncs.JudgePoint(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude, antenna.Direction);
                    if (inCover)
                    {
                        CellDistanceBTSName btsItem = new CellDistanceBTSName();
                        btsItem.distance = distance;
                        btsItem.name = bts.Name;
                        nearestList.Add(btsItem);
                    }
                }
            }
        }

        /// <summary>
        /// 获得某小区的最近三个基站的名称
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        public static string GetNearestBTSs(WCell cell, int nearestCellCount)
        {
            ReadOnlyCollection<WNodeB> btsList = CellManager.GetInstance().GetCurrentWNodeBs();
            ReadOnlyCollection<WAntenna> antennaList = cell.Antennas; List<CellDistanceBTSName> nearestList = new List<CellDistanceBTSName>();
            string btsName = cell.BelongNodeBs.Count > 0 ? cell.BelongNodeBs[0].Name : "";
            foreach (WAntenna antenna in antennaList)
            {
                double minLong = antenna.Longitude - 0.06;
                double maxLong = antenna.Longitude + 0.06;
                double minLat = antenna.Latitude - 0.06;
                double maxLat = antenna.Latitude + 0.06;

                foreach (WNodeB bts in btsList)
                {
                    if (bts.Type != WNodeBType.Indoor && bts.Name != btsName && bts.Cells.Count != 0)
                    {
                        addNearestList(nearestList, antenna, minLong, maxLong, minLat, maxLat, bts);
                    }
                }
            }

            StringBuilder sbBTSs = getsbBTSs(nearestCellCount, nearestList);
            return sbBTSs.ToString();
        }

        private static void addNearestList(List<CellDistanceBTSName> nearestList, WAntenna antenna, double minLong, double maxLong, double minLat, double maxLat, WNodeB bts)
        {
            if ((bts.Longitude >= minLong) && (bts.Longitude <= maxLong) &&
                (bts.Latitude <= maxLat) && (bts.Latitude >= minLat))
            {
                double distance = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude);
                if ((distance < CD.MAX_COV_DISTANCE_TD) && (distance > 50))
                {
                    bool inCover = MathFuncs.JudgePoint(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude, (int)antenna.Direction);
                    if (inCover)
                    {
                        CellDistanceBTSName btsItem = new CellDistanceBTSName();
                        btsItem.distance = distance;
                        btsItem.name = bts.Name;
                        nearestList.Add(btsItem);
                    }
                }
            }
        }

        /// <summary>
        /// 获得某小区的最近三个基站的名称
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        public static string GetNearestBTSs(LTECell cell, int nearestCellCount)
        {
            List<LTEBTS> btsList = CellManager.GetInstance().GetCurrentLTEBTSs();
            List<CellDistanceBTSName> nearestList = new List<CellDistanceBTSName>();
            string btsName = cell.BelongBTS.Name;
            LTEAntenna antenna = cell.Antennas[0];
            double minLong = antenna.Longitude - 0.06;
            double maxLong = antenna.Longitude + 0.06;
            double minLat = antenna.Latitude - 0.06;
            double maxLat = antenna.Latitude + 0.06;

            foreach (LTEBTS bts in btsList)
            {
                if (bts.Type != LTEBTSType.Indoor && bts.Name != btsName && bts.Cells.Count != 0)
                {
                    addNearestList(nearestList, antenna, minLong, maxLong, minLat, maxLat, bts);
                }
            }

            StringBuilder sbBTSs = getsbBTSs(nearestCellCount, nearestList);
            return sbBTSs.ToString();
        }

        private static StringBuilder getsbBTSs(int nearestCellCount, List<CellDistanceBTSName> nearestList)
        {
            StringBuilder sbBTSs = new StringBuilder();
            if (nearestList.Count > 0)
            {
                nearestList.Sort(CellDistanceBTSName.GetCompareByDistance());
                int counter = 0;
                for (int i = 0; i < nearestList.Count; i++)
                {
                    counter++;
                    sbBTSs.Append(nearestList[i].name);
                    sbBTSs.Append("|");
                    if (counter >= nearestCellCount)
                    {
                        break;
                    }
                }
            }

            return sbBTSs;
        }

        private static void addNearestList(List<CellDistanceBTSName> nearestList, LTEAntenna antenna, double minLong, double maxLong, double minLat, double maxLat, LTEBTS bts)
        {
            if ((bts.Longitude >= minLong) && (bts.Longitude <= maxLong) &&
                (bts.Latitude <= maxLat) && (bts.Latitude >= minLat))
            {
                double distance = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude);
                if ((distance < CD.MAX_COV_DISTANCE_TD) && (distance > 50))
                {
                    bool inCover = MathFuncs.JudgePoint(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude, (int)antenna.Direction);
                    if (inCover)
                    {
                        CellDistanceBTSName btsItem = new CellDistanceBTSName();
                        btsItem.distance = distance;
                        btsItem.name = bts.Name;
                        nearestList.Add(btsItem);
                    }
                }
            }
        }

        public static string GetNearestBTSs(NRCell cell, int nearestCellCount)
        {
            List<NRBTS> btsList = CellManager.GetInstance().GetCurrentNRBTSs();
            List<CellDistanceBTSName> nearestList = new List<CellDistanceBTSName>();
            string btsName = cell.BelongBTS.Name;
            NRAntenna antenna = cell.Antennas[0];
            double minLong = antenna.Longitude - 0.06;
            double maxLong = antenna.Longitude + 0.06;
            double minLat = antenna.Latitude - 0.06;
            double maxLat = antenna.Latitude + 0.06;

            foreach (NRBTS bts in btsList)
            {
                if (bts.Type != NRBTSType.Indoor && bts.Name != btsName && bts.Cells.Count != 0)
                {
                    addNearestList(nearestList, antenna, minLong, maxLong, minLat, maxLat, bts);
                }
            }

            StringBuilder sbBTSs = getsbBTSs(nearestCellCount, nearestList);
            return sbBTSs.ToString();
        }

        private static void addNearestList(List<CellDistanceBTSName> nearestList, NRAntenna antenna, double minLong, double maxLong, double minLat, double maxLat, NRBTS bts)
        {
            if ((bts.Longitude >= minLong) && (bts.Longitude <= maxLong) &&
                (bts.Latitude <= maxLat) && (bts.Latitude >= minLat))
            {
                double distance = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude);
                if ((distance < CD.MAX_COV_DISTANCE_NR) && (distance > 50))
                {
                    bool inCover = MathFuncs.JudgePoint(antenna.Longitude, antenna.Latitude, bts.Longitude, bts.Latitude, (int)antenna.Direction);
                    if (inCover)
                    {
                        CellDistanceBTSName btsItem = new CellDistanceBTSName();
                        btsItem.distance = distance;
                        btsItem.name = bts.Name;
                        nearestList.Add(btsItem);
                    }
                }
            }
        }

        private Event evt = null;
    }

    public class CellDistanceRem : IComparable<CellDistanceRem>
    {
        public string btsName { get; set; }
        public double distance { get; set; }
        public double longitude { get; set; }
        public double latitude { get; set; }
        #region IComparable<CellDistanceRem> 成员

        public int CompareTo(CellDistanceRem other)
        {
            return this.distance.CompareTo(other.distance);
        }

        #endregion
    }

    public class CellDistanceBTSName
    {
        public double distance { get; set; }
        public string name { get; set; }

        public static IComparer<CellDistanceBTSName> GetCompareByDistance()
        {
            if (comparerByDistance == null)
            {
                comparerByDistance = new ComparerByDistance();
            }
            return comparerByDistance;
        }
        public class ComparerByDistance : IComparer<CellDistanceBTSName>
        {
            public int Compare(CellDistanceBTSName x, CellDistanceBTSName y)
            {
                return (int)(x.distance - y.distance);
            }
        }
        private static IComparer<CellDistanceBTSName> comparerByDistance;
    }
}
