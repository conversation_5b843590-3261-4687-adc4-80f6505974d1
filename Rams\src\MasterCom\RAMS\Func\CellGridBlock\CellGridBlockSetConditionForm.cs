﻿using MasterCom.RAMS.Frame;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class CellGridBlockSetConditionForm : BaseDialog
    {
        public CellGridBlockSetConditionForm()
        {
            InitializeComponent();
            DIYSQLCellGridType cgType = new DIYSQLCellGridType();
            cgType.Query();
            foreach (string type in cgType.Types)
            {
                chkTypes.Items.Add(type);
            }
            for (int i = 0; i < chkTypes.Items.Count; i++)
            {
                chkTypes.SetItemChecked(i, true);
            }
            chkAll.Checked = true;
        }

        public CellGridCondition GetCondition()
        {
            CellGridCondition condition = new CellGridCondition();
            for (int i = 0; i < chkTypes.Items.Count; i++)
            {
                if (chkTypes.CheckedItems.Contains(chkTypes.Items[i]))
                {
                    condition.AddType(chkTypes.Items[i].ToString());
                }
            }

            condition.RSRP141 = lb141.BackColor;
            condition.RSRP110 = lb110.BackColor;
            condition.RSRP105 = lb105.BackColor;
            condition.RSRP100 = lb100.BackColor;
            condition.RSRP95 = lb95.BackColor;
            condition.RSRP80 = lb80.BackColor;
            condition.RSRP75 = lb75.BackColor;
            condition.RSRP40 = lb40.BackColor;

            LabColorText lct141 = new LabColorText("-141 <=RSRP<= -110", lb141.BackColor);
            LabColorText lct110 = new LabColorText("-110 <=RSRP<= -105", lb110.BackColor);
            LabColorText lct105 = new LabColorText("-105 <=RSRP<= -100", lb105.BackColor);
            LabColorText lct100 = new LabColorText("-100 <=RSRP<= -95", lb100.BackColor);
            LabColorText lct95 = new LabColorText("-95 <=RSRP<= -80", lb95.BackColor);
            LabColorText lct80 = new LabColorText("-80 <=RSRP<= -75", lb80.BackColor);
            LabColorText lct75 = new LabColorText("-75 <=RSRP<= -40", lb75.BackColor);
            LabColorText lct40 = new LabColorText("-40 <=RSRP<= 25", lb40.BackColor);

            condition.AddLb(lct141);
            condition.AddLb(lct110);
            condition.AddLb(lct105);
            condition.AddLb(lct100);
            condition.AddLb(lct95);
            condition.AddLb(lct80);
            condition.AddLb(lct75);
            condition.AddLb(lct40);

            MainModel.RefreshCellGridLegend(ref condition);
            
            return condition;
        }

        public CellGridCondition GetColor()
        {
            CellGridCondition condition = new CellGridCondition();

            condition.RSRP141 = lb141.BackColor;
            condition.RSRP110 = lb110.BackColor;
            condition.RSRP105 = lb105.BackColor;
            condition.RSRP100 = lb100.BackColor;
            condition.RSRP95 = lb95.BackColor;
            condition.RSRP80 = lb80.BackColor;
            condition.RSRP75 = lb75.BackColor;
            condition.RSRP40 = lb40.BackColor;

            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (chkTypes.Items.Count <= 0)
            {
                MessageBox.Show("请先配置栅格问题点类型！");
                return;
            }
            if (chkTypes.CheckedItems.Count <= 0)
            {
                MessageBox.Show("请至少选择一项类型");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void btnCancle_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void chkAll_CheckedChanged(object sender, EventArgs e)
        {
            bool allChecked = chkAll.Checked;
            for (int i = 0; i < chkTypes.Items.Count; i++)
            {
                chkTypes.SetItemChecked(i, allChecked);
            }
        }
    }
}
