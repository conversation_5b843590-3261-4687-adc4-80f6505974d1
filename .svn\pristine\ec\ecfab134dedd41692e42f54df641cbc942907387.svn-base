﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public partial class MapLTECellLayerPlanningPropertis : MTLayerPropUserControl
    {
        public MapLTECellLayerPlanningPropertis()
        {
            InitializeComponent();
        }

        private MapLTECellLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as MapLTECellLayer;
            if (layer == null)
            {
                return;
            }
            Text = "Planning";

            colorGsm.ColorChanged -= new EventHandler(colorGsm_ColorChanged);
            colorTd.ColorChanged -= new EventHandler(colorTd_ColorChanged);
            colorLte.ColorChanged -= new EventHandler(colorLte_ColorChanged);

            colorGsm.Color = layer.ColorNeighbourCell;
            colorTd.Color = layer.ColorNeighbourTDCell;
            colorLte.Color = layer.ColorNeighbourLTECell;
            colorGsm.ColorChanged += new EventHandler(colorGsm_ColorChanged);
            colorTd.ColorChanged += new EventHandler(colorTd_ColorChanged);
            colorLte.ColorChanged += new EventHandler(colorLte_ColorChanged);
        }


        void colorLte_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorNeighbourLTECell = colorLte.Color;
        }

        void colorTd_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorNeighbourTDCell = colorTd.Color;
        }

        void colorGsm_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorNeighbourCell = colorGsm.Color;
        }
    }
}
