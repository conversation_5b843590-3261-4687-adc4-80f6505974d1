﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class CellGridDetailResultForm : MinCloseForm
    {
        public CellGridDetailResultForm()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }

        private MapForm mapForm = null;
        private List<CellGridWithCell> resultList = null;

        private void init()
        {
            this.ListViewCG.CanExpandGetter = delegate(object x)
            {
                return x is CellGridWithCell;
            };
            this.ListViewCG.ChildrenGetter = delegate(object x)
            {
                if (x is CellGridWithCell)
                {
                    CellGridWithCell cgw = x as CellGridWithCell;
                    return cgw.CellGridDetails;
                }
                return "";
            };
            this.olvColumnSN.AspectGetter = delegate(object row)
            {
                if (row is CellGridWithCell)
                {
                    CellGridWithCell cgw = row as CellGridWithCell;
                    return cgw.SN;
                }
                return "";
            };
            this.olvColumnChecked.AspectGetter = delegate(object row)
            {
                if (row is CellGridWithCell)
                {
                    CellGridWithCell cgw = row as CellGridWithCell;
                    return cgw.isChecked;
                }
                return "";
            };
            this.olvColumnLAC.AspectGetter = delegate(object row)
            {
                if (row is CellGridWithCell)
                {
                    CellGridWithCell cgw = row as CellGridWithCell;
                    return cgw.LAC;
                }
                return "";
            };
            this.olvColumnCI.AspectGetter = delegate(object row)
            {
                if (row is CellGridWithCell)
                {
                    CellGridWithCell cgw = row as CellGridWithCell;
                    return cgw.CI;
                }
                return "";
            };
            this.olvColumnServCell.AspectGetter = delegate(object row)
            {
                if (row is CellGridWithCell)
                {
                    CellGridWithCell cgw = row as CellGridWithCell;
                    return cgw.Cell;
                }
                return "";
            };
            this.olvColumnTlLng.AspectGetter = delegate(object row)
            {
                if (row is CellGridDetailInfo)
                {
                    CellGridDetailInfo detail = row as CellGridDetailInfo;
                    return (double)detail.TlLongitude / 10000000;
                }
                return "";
            };
            this.olvColumnTlLat.AspectGetter = delegate(object row)
            {
                if (row is CellGridDetailInfo)
                {
                    CellGridDetailInfo detail = row as CellGridDetailInfo;
                    return (double)detail.TlLatitude / 10000000;
                }
                return "";
            };
            this.olvColumnBrLng.AspectGetter = delegate(object row)
            {
                if (row is CellGridDetailInfo)
                {
                    CellGridDetailInfo detail = row as CellGridDetailInfo;
                    return (double)detail.BrLongitude / 10000000;
                }
                return "";
            };
            this.olvColumnBrLat.AspectGetter = delegate(object row)
            {
                if (row is CellGridDetailInfo)
                {
                    CellGridDetailInfo detail = row as CellGridDetailInfo;
                    return (double)detail.BrLatitude / 10000000;
                }
                return "";
            };
            this.olvColumnAvgRSRP.AspectGetter = delegate(object row)
            {
                if (row is CellGridDetailInfo)
                {
                    CellGridDetailInfo detail = row as CellGridDetailInfo;
                    return detail.AvgRSRP;
                }
                return "";
            };
        }


        CellGridDetailLayer layer = null;
        public void FillData(List<CellGridWithCell> ResultList)
        {
            this.resultList = new List<CellGridWithCell>();
            this.resultList = ResultList;

            this.ListViewCG.RebuildColumns();
            this.ListViewCG.ClearObjects();
            this.ListViewCG.SetObjects(resultList);

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }

            makeSureLayerVisible();

            CellGridBlockSetConditionForm setFrm = new CellGridBlockSetConditionForm();
            layer.conn = setFrm.GetCondition();
            foreach (CellGridWithCell cg in resultList)
            {
                layer.cgw.Add(cg);
                MainModel.SelectedLTECells.Add(cg.CellGridDetails[0].lteCell);
            }
        }

        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            mf.clearData();
            layer = mf.GetLayerBase(typeof(CellGridDetailLayer)) as CellGridDetailLayer;
        }

        private void ListViewCG_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            BrightIdeasSoftware.OLVListItem row = e.Item as BrightIdeasSoftware.OLVListItem;
            if (row.RowObject is CellGridWithCell)
            {
                CellGridWithCell cgw = row.RowObject as CellGridWithCell;
                if (cgw.isChecked)
                {
                    cgw.isChecked = false;
                    layer.cgw.Remove(cgw);
                    MainModel.SelectedLTECells.Remove(cgw.CellGridDetails[0].lteCell);
                }
                else
                {
                    cgw.isChecked = true;
                    layer.cgw.Add(cgw);
                    MainModel.SelectedLTECells.Add(cgw.CellGridDetails[0].lteCell);
                }
            }
            mapForm.updateMap();
        }

        private void ListViewCG_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            MainModel.SelectedLTECells.Clear();
            if (ListViewCG.SelectedObject is CellGridDetailInfo)
            {
                CellGridDetailInfo detail = ListViewCG.SelectedObject as CellGridDetailInfo;
                double minLng = Math.Min(detail.TlLongitude, detail.BrLongitude);
                double maxLng = Math.Max(detail.TlLongitude, detail.BrLongitude);
                double minLat = Math.Min(detail.TlLatitude, detail.BrLatitude);
                double maxLat = Math.Max(detail.TlLatitude, detail.BrLatitude);
                MasterCom.MTGis.DbRect rect = new MTGis.DbRect(minLng / 10000000, minLat / 10000000, maxLng / 10000000, maxLat / 10000000);
                mapForm.GoToView(rect);
                mapForm.updateMap();
            }
            else if (ListViewCG.SelectedObject is CellGridWithCell)
            {
                CellGridWithCell cgw = ListViewCG.SelectedObject as CellGridWithCell;
                if (cgw.isChecked)
                {
                    MainModel.SelectedLTECell = cgw.CellGridDetails[0].lteCell;
                    mapForm.goToSelectedCellView(cgw.CellGridDetails[0].lteCell.Longitude, cgw.CellGridDetails[0].lteCell.Latitude);
                    mapForm.updateMap();
                }
            }
        }
    }
}
