﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.IO;

namespace MasterCom.RAMS.CQT
{
    public class CQTKPIReportCfgManager
    {
        private static CQTKPIReportCfgManager instance = null;
        public static string FolderName { get; set; } = Application.StartupPath + @"\config\templates\";
        public static CQTKPIReportCfgManager GetInstance()
        {
            if (instance == null)
            {
                instance = new CQTKPIReportCfgManager();
            }
            return instance;
        }

        private CQTKPIReportCfgManager()
        {

        }

        public List<CQTKPIReport> Reports { get; set; } = new List<CQTKPIReport>();
        public void SaveAllReport()
        {
            foreach (CQTKPIReport rpt in Reports)
            {
                rpt.Save();
            }
        }

        public void LoadByDefault()
        {
            Load(FolderName);
        }

        public void Load(string folderName)
        {
            Reports = new List<CQTKPIReport>();
            DirectoryInfo folder = new DirectoryInfo(folderName);
            foreach (FileInfo fileInfo in folder.GetFiles("*" + CQTKPIReport.ReportExtension))
            {
                CQTKPIReport rpt = CQTKPIReport.Open(fileInfo.FullName);
                if (rpt != null)
                {
                    Reports.Add(rpt);
                }
            }
        }
    }
}
