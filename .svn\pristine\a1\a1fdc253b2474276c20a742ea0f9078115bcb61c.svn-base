﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc
{
    class FddSmallAcpFtpDownload : FddIndoorAcpFtpDownload
    {
        public FddSmallAcpFtpDownload()
        {
            resultGrid = new string[3, 3, 1];
            int idx = 16;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 1, 0] = "q" + (++row).ToString();
                resultGrid[i, 2, 0] = "q" + (++row).ToString();
            }
        }

        protected override void SaveResult(FileInfo fileInfo, CellKpi kpiCell, int colIndex, int sectorID)
        {
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            SetValue(result, sectorID, 0, colIndex, kpiCell.AvgRsrp);
            SetValue(result, sectorID, 1, colIndex, kpiCell.AvgSinr);
            SetValue(result, sectorID, 2, colIndex, kpiCell.AvgDLSpeed);
        }
    }

    class FddSmallAcpFtpUpload : FddIndoorAcpFtpUpload
    {
        public FddSmallAcpFtpUpload()
        {
            resultGrid = new string[3, 3, 1];
            int idx = 19;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 1, 0] = "q" + (++row).ToString();
                resultGrid[i, 2, 0] = "q" + (++row).ToString();
            }
        }
    }

    class FddSmallAcpHandover : FddIndoorAcpHandover
    {
        public FddSmallAcpHandover()
        {
            evtRequList = new List<int> { 850, 898 };
            evtSuccList = new List<int> { 851, 899 };

            resultGrid = new string[3, 1, 2];
            int idx = 26;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 0, 1] = "x" + row.ToString();
            }
        }
    }

    class FddSmallAcpCsfbRate : FddIndoorAcpCsfbRate
    {
        public FddSmallAcpCsfbRate()
        {
            resultGrid = new string[3, 1, 2];
            int idx = 12;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 0, 1] = "x" + row.ToString();
            }
        }
    }

    class FddSmallAcpRrcRate : FddIndoorAcpRrcRate
    {
        public FddSmallAcpRrcRate()
        {
            evtRequList = new List<int>() { 855 };
            evtSuccList = new List<int>() { 856 };

            resultGrid = new string[3, 1, 2];
            int idx = 7;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 0, 1] = "x" + row.ToString();
            }
        }
    }

    class FddSmallAcpErabRate : FddIndoorAcpErabRate
    {
        public FddSmallAcpErabRate()
        {
            evtRequList = new List<int>() { 858 };
            evtSuccList = new List<int>() { 859 };

            resultGrid = new string[3, 1, 2];
            int idx = 8;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 0, 1] = "x" + row.ToString();
            }
        }
    }

    class FddSmallAcpAccRate : FddIndoorAcpAccRate
    {
        public FddSmallAcpAccRate()
        {
            evtRequList = new List<int>() { 22 };
            evtSuccList = new List<int>() { 23 };

            resultGrid = new string[3, 1, 2];
            int idx = 9;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 0, 1] = "x" + row.ToString();
            }
        }
    }

    class FddSmallAcp24ReselectRate : FddIndoorAcp24ReselectRate
    {
        public FddSmallAcp24ReselectRate()
        {
            evtRequList = new List<int>() { 852 };
            evtSuccList = new List<int>() { 853 };

            resultGrid = new string[3, 1, 2];
            int idx = 10;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 0, 1] = "x" + row.ToString();
            }
        }
    }

    class FddSmallAcpVolteVoiceMo : FddIndoorAcpVolteVoiceMo
    {
        public FddSmallAcpVolteVoiceMo()
        {
            evtRequList = new List<int>() { 1070 };
            evtSuccList = new List<int>() { 1072 };

            resultGrid = new string[3, 1, 2];
            int idx = 13;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 0, 1] = "x" + row.ToString();
            }
        }
    }

    class FddSmallAcpVolteVoiceMt : FddIndoorAcpVolteVoiceMt
    {
        public FddSmallAcpVolteVoiceMt()
        {
            evtRequList = new List<int>() { 1071 };
            evtSuccList = new List<int>() { 1073 };

            resultGrid = new string[3, 1, 2];
            int idx = 14;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
                resultGrid[i, 0, 1] = "x" + row.ToString();
            }
        }
    }

    class FddSmallAcpLeakOutLock : FddIndoorAcpLeakOutLock
    {
        public FddSmallAcpLeakOutLock()
        {
            resultGrid = new string[3, 1, 1];
            int idx = 24;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "q" + row.ToString();
            }
        }
    }

    class FddSmallAcpLeakOutScan : FddIndoorAcpLeakOutScan
    {
        public FddSmallAcpLeakOutScan()
        {
            resultGrid = new string[3, 1, 1];
            int idx = 24;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "AD" + row.ToString();
            }
        }
    }

    class FddSmallAcpLeveling : FddIndoorAcpLeveling
    {
        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }
            FillResultToSheet(eBook, 5, sectorIDs);
        }
    }

    class FddSmallAcpDotCoverage : FddIndoorAcpLeveling
    {
        readonly Dictionary<string, int> floorDic = new Dictionary<string, int>();

        public FddSmallAcpDotCoverage()
        {
            resultGrid = new string[1, 1, 10] {
                {
                    { "a3", "b3", "c3", "d3", "e3", "f3", "g3", "h3", "i3", "j3" },
                },
            };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("楼_") && fileInfo.Name.Contains("下载");
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string pci, int sectorID)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            //对文件名进行处理,获取天线位置和楼层
            string location;
            string floor;
            if (getFloorLocation(fileInfo.Name, out location, out floor))
            {
                if (!floorDic.ContainsKey(floor))
                {
                    floorDic.Add(floor, floorDic.Count);
                }

                int count = floorDic.Count;
                int index = count + 2;
                resultDic[count - 1] = new string[10] { "a" + index, "b" + index, "c" + index, "d" + index, "e" + index, "f" + index, "g" + index, "h" + index, "i" + index, "j" + index };

                AnaFile(fileInfo, fileManager, pci, location, floor);
            }
        }

        /// <summary>
        /// 按命名规范获取天线位置和楼层
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="location"></param>
        /// <param name="floor"></param>
        /// <returns></returns>
        protected bool getFloorLocation(string fileName, out string location, out string floor)
        {
            string[] names = fileName.Split('_');
            //根据命名规则定义2是时间,3是基站名,4是楼层,5是PCI,6是业务名,7是测试位置,8是天线位置
            if (names.Length == 8)
            {
                location = names[7].Substring(0, names[7].IndexOf('.'));
                floor = names[3];
                return true;
            }
            location = "";
            floor = "";
            return false;
        }

        private void AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, string location, string floor)
        {
            CellKpi targetKpiCell = new CellKpi(PCI);

            LTECell targetCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (getValidTestPoint(targetCell, tp))
                {
                    targetKpiCell.AddPoint(tp);
                }
            }
            targetKpiCell.CalcResult();

            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName,
                    resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            SetValue(result, fileInfo.Name, targetKpiCell, floorDic[floor], location, floor);
        }

        private void SetValue(LteTestAcceptResult result, string fileName, CellKpi cellKPI, int sectorID, string location, string floor)
        {
            result.SetValue(sectorID, 0, 0, location, true);
            result.SetValue(sectorID, 0, 1, floor, true);

            if (fileName.Contains("室内中间"))
            {
                result.SetValue(sectorID, 0, 2, cellKPI.AvgRsrp, true);
                result.SetValue(sectorID, 0, 3, cellKPI.AvgSinr, true);
            }
            else if (fileName.Contains("室内面向"))
            {
                result.SetValue(sectorID, 0, 4, cellKPI.AvgRsrp, true);
                result.SetValue(sectorID, 0, 5, cellKPI.AvgSinr, true);
            }
            else if (fileName.Contains("室内背向"))
            {
                result.SetValue(sectorID, 0, 6, cellKPI.AvgRsrp, true);
                result.SetValue(sectorID, 0, 7, cellKPI.AvgSinr, true);
            }
            else if (fileName.Contains("走廊面向"))
            {
                result.SetValue(sectorID, 0, 8, cellKPI.AvgRsrp, true);
            }
            else if (fileName.Contains("走廊背向"))
            {
                result.SetValue(sectorID, 0, 9, cellKPI.AvgRsrp, true);
            }
        }

        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }
            FillResultToSheet(eBook, 4, sectorIDs);
        }

        protected override void FillResultToSheet(Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int i = 0; i < resultDic.Count; ++i)
            {
                int sid = i;
                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    for (int col = 0; col < resultGrid.GetLength(2); ++col)
                    {
                        object value = result.GetValue(sid, row, col);
                        if (value is Dictionary<bool, double>)
                        {
                            Dictionary<bool, double> dic = value as Dictionary<bool, double>;
                            InsertExcelValue(eBook, sheetIndex, resultDic[i][col], dic[true], true);
                        }
                        else
                        {
                            InsertExcelValue(eBook, sheetIndex, resultDic[i][col], value);
                        }
                    }
                }
            }
        }

        protected new class CellKpi
        {
            public CellKpi(string PCI)
            {
                this.PCI = PCI;
            }

            public string PCI
            {
                get;
                private set;
            }

            public int PointCount
            {
                get;
                private set;
            }

            public double AvgRsrp
            {
                get;
                private set;
            }

            public double AvgSinr
            {
                get;
                private set;
            }

            public void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }
            }

            public void CalcResult()
            {
                AvgRsrp = cntRsrp == 0 ? double.MinValue : Math.Round(sumRsrp / cntRsrp, 2);
                AvgSinr = cntSinr == 0 ? double.MinValue : Math.Round(sumSinr / cntSinr, 2);
            }

            private double sumRsrp;
            private int cntRsrp;

            private double sumSinr;
            private int cntSinr;
        }
    }

    class FddSmallAcpCellName : FddIndoorAcpCellName
    {
        public FddSmallAcpCellName()
        {
            resultGrid = new string[3, 1, 1];
            int idx = 6;
            int step = 22;
            for (int i = 0; i < 3; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "a" + row.ToString();
            }
        }
    }

    /// <summary>
    /// 查询其他平台推送的规划数据 一个站只执行一次
    class FddSmallAcpCellPlanPara : FddIndoorAcpCellPlanPara
    {
        public FddSmallAcpCellPlanPara()
        {
            resultGrid = new string[3, 27, 1];
            int colIdx = 8;
            int step = 8;
            for (int i = 0; i < 3; i++)
            {
                #region 基站描述
                resultGrid[i, 0, 0] = "e3";
                resultGrid[i, 1, 0] = "z3";
                resultGrid[i, 2, 0] = "e5";
                resultGrid[i, 3, 0] = "z5";
                resultGrid[i, 4, 0] = "e9";
                resultGrid[i, 5, 0] = "z9";
                resultGrid[i, 6, 0] = "e11";
                resultGrid[i, 7, 0] = "z11";
                #endregion
                #region 基站参数
                resultGrid[i, 8, 0] = "h15";
                resultGrid[i, 9, 0] = "h16";
                resultGrid[i, 10, 0] = "h17";
                resultGrid[i, 11, 0] = "h18";
                #endregion

                string col = ExcelHelper.GetColNameByIndex(colIdx + step * i);
                #region 工程参数
                resultGrid[i, 12, 0] = col + 19;
                int row = 20;
                for (int j = 13; j < 20; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
                #endregion
                #region 网优参数
                resultGrid[i, 20, 0] = col + 28;
                row = 32;
                for (int j = 21; j < 25; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
                resultGrid[i, 25, 0] = col + 43;
                resultGrid[i, 26, 0] = col + 44;
                #endregion
            }
        }
        
        protected override void saveResult(FileInfo fileInfo, List<FddPlanBtsCellData> btsCellData)
        {
            LteTestAcceptResult result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);

            int sectorID = 0;
            foreach (FddPlanBtsCellData cellData in btsCellData)
            {
                int row = 0;
                #region 基站描述
                result.SetValue(sectorID, row++, 0, cellData.BtsName, true);
                string date = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(fileInfo.BeginTime * 1000L).ToShortDateString();
                result.SetValue(sectorID, row++, 0, date, true);
                result.SetValue(sectorID, row++, 0, cellData.ENodeBID, true);
                result.SetValue(sectorID, row++, 0, cellData.CoverScenes, true);//覆盖场景
                string address = btsName.Substring(0, btsName.IndexOf('-'));
                result.SetValue(sectorID, row++, 0, address, true);//地址
                result.SetValue(sectorID, row++, 0, cellData.CoverArea, true);//覆盖面积
                result.SetValue(sectorID, row++, 0, cellData.DeviceType, true);//设备类型
                result.SetValue(sectorID, row++, 0, cellData.CoverRange, true);//覆盖范围
                #endregion
                #region 基站参数
                result.SetValue(sectorID, row++, 0, cellData.Longitude, true);
                result.SetValue(sectorID, row++, 0, cellData.Latitude, true);
                result.SetValue(sectorID, row++, 0, cellData.TAC, true);
                result.SetValue(sectorID, row++, 0, cellData.ENodeBID, true);
                #endregion
                #region 小区工程参数
                string cellDes = string.Format("Cell-{0}(PCI:{1})", (sectorID + 1), cellData.PCI);
                result.SetValue(sectorID, row++, 0, cellDes, true);
                result.SetValue(sectorID, row++, 0, cellData.CarrierInfo, true);
                result.SetValue(sectorID, row++, 0, cellData.CellID, true);
                result.SetValue(sectorID, row++, 0, cellData.PCI, true);
                result.SetValue(sectorID, row++, 0, cellData.FrequencyBand, true);
                result.SetValue(sectorID, row++, 0, cellData.Earfcn, true);
                result.SetValue(sectorID, row++, 0, cellData.CellBroadBand, true);
                result.SetValue(sectorID, row++, 0, cellData.RootSN, true);
                #endregion
                #region 小区网优参数
                result.SetValue(sectorID, row++, 0, cellDes, true);
                result.SetValue(sectorID, row++, 0, cellData.Altitude, true);
                result.SetValue(sectorID, row++, 0, cellData.Direction, true);
                result.SetValue(sectorID, row++, 0, cellData.Downward, true);
                result.SetValue(sectorID, row++, 0, cellData.Downtilt, true);
                result.SetValue(sectorID, row++, 0, cellData.AntennaType, true);
                result.SetValue(sectorID, row, 0, cellData.AntennaGain, true);
                #endregion
            }
        }
    }

    /// <summary>
    /// 查询其他平台推送的实测数据 一个站只执行一次
    /// </summary>
    class FddSmallAcpCellActualPara : FddIndoorAcpCellActualPara
    {
        public FddSmallAcpCellActualPara()
        {
            resultGrid = new string[3, 21, 1];
            int colIdx = 11;
            int step = 8;
            for (int i = 0; i < 3; i++)
            {
                #region 基站参数
                resultGrid[i, 0, 0] = "n17";
                resultGrid[i, 1, 0] = "n18";
                #endregion

                string col = ExcelHelper.GetColNameByIndex(colIdx + step * i);
                #region 工程参数
                int row = 20;
                for (int j = 2; j < 9; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
                #endregion
                #region 网优参数
                row = 29;
                for (int j = 9; j < 21; j++)
                {
                    resultGrid[i, j, 0] = col + (++row).ToString();
                }
                #endregion
            }
        }

        protected override void saveResult(List<FddActualBtsCellData> btsCellData)
        {
            LteTestAcceptResult result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);

            int sectorID = 0;
            foreach (FddActualBtsCellData cellData in btsCellData)
            {
                int row = 0;
                #region 基站参数
                result.SetValue(sectorID, row++, 0, cellData.TAC, true);
                result.SetValue(sectorID, row++, 0, cellData.ENodeBID, true);
                #endregion
                #region 小区工程参数
                result.SetValue(sectorID, row++, 0, cellData.CarrierInfo, true);
                result.SetValue(sectorID, row++, 0, cellData.CellID, true);
                result.SetValue(sectorID, row++, 0, cellData.PCI, true);
                result.SetValue(sectorID, row++, 0, cellData.FrequencyBand, true);
                result.SetValue(sectorID, row++, 0, cellData.Earfcn, true);
                result.SetValue(sectorID, row++, 0, cellData.CellBroadBand, true);
                result.SetValue(sectorID, row++, 0, cellData.RootSN, true);
                #endregion
                #region 小区网优参数
                result.SetValue(sectorID, row++, 0, cellData.RsPower, true);
                result.SetValue(sectorID, row++, 0, cellData.PA, true);
                result.SetValue(sectorID, row++, 0, cellData.PB, true);
                result.SetValue(sectorID, row++, 0, cellData.Altitude, true);
                //数据库缺少方位角和机械下倾角字段
                result.SetValue(sectorID, row++, 0, "", true);
                result.SetValue(sectorID, row++, 0, "", true);
                result.SetValue(sectorID, row++, 0, cellData.Downtilt, true);
                result.SetValue(sectorID, row++, 0, cellData.PDCCH, true);
                result.SetValue(sectorID, row++, 0, cellData.VOLTE, true);
                result.SetValue(sectorID, row++, 0, cellData.SRVCC, true);
                result.SetValue(sectorID, row++, 0, cellData.A2Threshold, true);
                result.SetValue(sectorID, row, 0, cellData.A4Threshold, true);
                #endregion
            }
        }
    }

    class FddSmallAcpPerformance : FddIndoorAcpPerformance
    {
        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 6, sectorIDs);
        }
    }

    class FddSmallAcpAlarm : FddIndoorAcpAlarm
    {
        public override void FillResult(Excel.Workbook eBook, List<int> sectorIDs)
        {
            FillResultToSheet(eBook, 6, sectorIDs);
        }
    }
}
