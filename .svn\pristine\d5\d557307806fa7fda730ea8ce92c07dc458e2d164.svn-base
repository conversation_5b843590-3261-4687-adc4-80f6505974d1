﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakCoverEventForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnIndex = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDateTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCode = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBCCH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBSIC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnStreetDesc = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPCCPCH_C2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDPCH_RSCP = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDPCH_C2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDPCH_ISCP = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBLER = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.olvColumnTxPower = new BrightIdeasSoftware.OLVColumn();
            this.toolStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel1});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(680, 25);
            this.toolStrip1.TabIndex = 0;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(104, 22);
            this.toolStripLabel1.Text = "弱覆盖场强门限：";
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnIndex);
            this.objectListView.AllColumns.Add(this.olvColumnName);
            this.objectListView.AllColumns.Add(this.olvColumnDateTime);
            this.objectListView.AllColumns.Add(this.olvColumnLongitude);
            this.objectListView.AllColumns.Add(this.olvColumnLatitude);
            this.objectListView.AllColumns.Add(this.olvColumnCellName);
            this.objectListView.AllColumns.Add(this.olvColumnCode);
            this.objectListView.AllColumns.Add(this.olvColumnLAC);
            this.objectListView.AllColumns.Add(this.olvColumnCI);
            this.objectListView.AllColumns.Add(this.olvColumnBCCH);
            this.objectListView.AllColumns.Add(this.olvColumnBSIC);
            this.objectListView.AllColumns.Add(this.olvColumnRxLev);
            this.objectListView.AllColumns.Add(this.olvColumnStreetDesc);
            this.objectListView.AllColumns.Add(this.olvColumnFileName);
            this.objectListView.AllColumns.Add(this.olvColumnPCCPCH_C2I);
            this.objectListView.AllColumns.Add(this.olvColumnDPCH_RSCP);
            this.objectListView.AllColumns.Add(this.olvColumnDPCH_C2I);
            this.objectListView.AllColumns.Add(this.olvColumnDPCH_ISCP);
            this.objectListView.AllColumns.Add(this.olvColumnBLER);
            this.objectListView.AllColumns.Add(this.olvColumnTxPower);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnIndex,
            this.olvColumnName,
            this.olvColumnDateTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnCellName,
            this.olvColumnCode,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnBCCH,
            this.olvColumnBSIC,
            this.olvColumnRxLev,
            this.olvColumnStreetDesc,
            this.olvColumnFileName,
            this.olvColumnPCCPCH_C2I,
            this.olvColumnDPCH_RSCP,
            this.olvColumnDPCH_C2I,
            this.olvColumnDPCH_ISCP,
            this.olvColumnBLER,
            this.olvColumnTxPower});
            this.objectListView.ContextMenuStrip = this.contextMenuStrip;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.HeaderWordWrap = true;
            this.objectListView.Location = new System.Drawing.Point(0, 25);
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(680, 324);
            this.objectListView.TabIndex = 4;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            this.objectListView.DoubleClick += new System.EventHandler(this.objectListView_DoubleClick);
            // 
            // olvColumnIndex
            // 
            this.olvColumnIndex.HeaderFont = null;
            this.olvColumnIndex.Text = "序号";
            this.olvColumnIndex.Width = 40;
            // 
            // olvColumnName
            // 
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.Text = "事件名称";
            this.olvColumnName.Width = 80;
            // 
            // olvColumnDateTime
            // 
            this.olvColumnDateTime.HeaderFont = null;
            this.olvColumnDateTime.Text = "时间";
            this.olvColumnDateTime.Width = 100;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名";
            this.olvColumnCellName.Width = 80;
            // 
            // olvColumnCode
            // 
            this.olvColumnCode.HeaderFont = null;
            this.olvColumnCode.Text = "Code";
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnBCCH
            // 
            this.olvColumnBCCH.HeaderFont = null;
            this.olvColumnBCCH.Text = "主频点";
            // 
            // olvColumnBSIC
            // 
            this.olvColumnBSIC.HeaderFont = null;
            this.olvColumnBSIC.Text = "扰码";
            // 
            // olvColumnRxLev
            // 
            this.olvColumnRxLev.HeaderFont = null;
            this.olvColumnRxLev.Text = "掉话前电平";
            // 
            // olvColumnStreetDesc
            // 
            this.olvColumnStreetDesc.HeaderFont = null;
            this.olvColumnStreetDesc.Text = "街道名称";
            this.olvColumnStreetDesc.Width = 100;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            this.olvColumnFileName.Width = 100;
            // 
            // olvColumnPCCPCH_C2I
            // 
            this.olvColumnPCCPCH_C2I.HeaderFont = null;
            this.olvColumnPCCPCH_C2I.Text = "掉话前PCCPCH_C/I";
            this.olvColumnPCCPCH_C2I.Width = 90;
            // 
            // olvColumnDPCH_RSCP
            // 
            this.olvColumnDPCH_RSCP.HeaderFont = null;
            this.olvColumnDPCH_RSCP.Text = "掉话前DPCH_RSCP";
            this.olvColumnDPCH_RSCP.Width = 90;
            // 
            // olvColumnDPCH_C2I
            // 
            this.olvColumnDPCH_C2I.HeaderFont = null;
            this.olvColumnDPCH_C2I.Text = "掉话前DPCH_C/I";
            this.olvColumnDPCH_C2I.Width = 90;
            // 
            // olvColumnDPCH_ISCP
            // 
            this.olvColumnDPCH_ISCP.HeaderFont = null;
            this.olvColumnDPCH_ISCP.Text = "掉话前DPCH_ISCP";
            this.olvColumnDPCH_ISCP.Width = 90;
            // 
            // olvColumnBLER
            // 
            this.olvColumnBLER.HeaderFont = null;
            this.olvColumnBLER.Text = "掉话前BLER";
            this.olvColumnBLER.Width = 90;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvent,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 48);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(129, 22);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // olvColumnTxPower
            // 
            this.olvColumnTxPower.HeaderFont = null;
            this.olvColumnTxPower.Text = "TxPower";
            // 
            // WeakCoverEventForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(680, 349);
            this.Controls.Add(this.objectListView);
            this.Controls.Add(this.toolStrip1);
            this.Name = "WeakCoverEventForm";
            this.Text = "弱覆盖掉话";
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStrip1;
        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnIndex;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private BrightIdeasSoftware.OLVColumn olvColumnDateTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCode;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev;
        private BrightIdeasSoftware.OLVColumn olvColumnStreetDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private BrightIdeasSoftware.OLVColumn olvColumnPCCPCH_C2I;
        private BrightIdeasSoftware.OLVColumn olvColumnDPCH_RSCP;
        private BrightIdeasSoftware.OLVColumn olvColumnDPCH_C2I;
        private BrightIdeasSoftware.OLVColumn olvColumnDPCH_ISCP;
        private BrightIdeasSoftware.OLVColumn olvColumnBLER;
        private BrightIdeasSoftware.OLVColumn olvColumnTxPower;

    }
}