﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedCellForm_NR : MinCloseForm
    {
        public LowSpeedCellForm_NR()
            : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
            tsMenuExportExcel.Click += ExportExcel_Click;
            gridControl.DoubleClick += gridControl_DoubleClick;
        }

        public void FillData(List<LowSpeedCell_NR> lowSpeedCellList)
        {
            gridControl.DataSource = lowSpeedCellList;
            gridControl.RefreshDataSource();
        }

        private void ExportExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            addHeader(rows);
            addExcelContent(rows);
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void addHeader(List<NPOIRow> rows)
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue("小区名");
            row.AddCellValue("TAC");
            row.AddCellValue("NCI");
            row.AddCellValue("CellID");
            row.AddCellValue("EARFCN");
            row.AddCellValue("PCI");
            row.AddCellValue("问题采样点数");
            row.AddCellValue("采样点总数");
            row.AddCellValue("问题点占比");
            row.AddCellValue("问题点平均SS-RSRP");
            row.AddCellValue("问题点平均SS-SINR");
            row.AddCellValue("所有点平均SS-RSRP");
            row.AddCellValue("所有点平均SS-SINR");
            row.AddCellValue("问题点平均LTE-RSRP");
            row.AddCellValue("问题点平均LTE-SINR");
            row.AddCellValue("所有点平均LTE-RSRP");
            row.AddCellValue("所有点平均LTE-SINR");

            row.AddCellValue("速率类型");
            row.AddCellValue("问题采样点数");
            row.AddCellValue("采样点总数");
            row.AddCellValue("问题点占比");
            row.AddCellValue("问题点平均速率(Mbps)");
            row.AddCellValue("问题点平均SS-RSRP");
            row.AddCellValue("问题点平均SS-SINR");
            row.AddCellValue("所有点平均速率(Mbps)");
            row.AddCellValue("所有点平均SS-RSRP");
            row.AddCellValue("所有点平均SS-SINR"); 
            row.AddCellValue("问题点平均LTE-RSRP");
            row.AddCellValue("问题点平均LTE-SINR");
            row.AddCellValue("所有点平均LTE-RSRP");
            row.AddCellValue("所有点平均LTE-SINR");

            rows.Add(row);
        }

        private void addExcelContent(List<NPOIRow> rows)
        {
            List<LowSpeedCell_NR> cellList = gridControl.DataSource as List<LowSpeedCell_NR>;
            foreach (LowSpeedCell_NR cell in cellList)
            {
                NPOIRow row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(cell.CellName);
                row.AddCellValue(cell.Tac);
                row.AddCellValue(cell.Nci);
                row.AddCellValue(cell.CellID);
                row.AddCellValue(cell.Arfcn);
                row.AddCellValue(cell.Pci);
                row.AddCellValue(cell.SampleCount);
                row.AddCellValue(cell.ProblemCount);
                row.AddCellValue(cell.ProblemRate);
                row.AddCellValue(cell.ProbRsrp.Avg);
                row.AddCellValue(cell.ProbSinr.Avg);
                row.AddCellValue(cell.AllRsrp.Avg);
                row.AddCellValue(cell.AllSinr.Avg);
                row.AddCellValue(cell.ProbLteRsrp.Avg);
                row.AddCellValue(cell.ProbLteSinr.Avg);
                row.AddCellValue(cell.AllLteRsrp.Avg);
                row.AddCellValue(cell.AllLteSinr.Avg);

                foreach (LowSpeedInfo speed in cell.SpeedInfoList)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(speed.Type);
                    subRow.AddCellValue(speed.SampleCount);
                    subRow.AddCellValue(speed.ProblemCount);
                    subRow.AddCellValue(speed.ProblemRate);
                    subRow.AddCellValue(speed.ProbSpeed.Avg);
                    subRow.AddCellValue(speed.ProbRsrp.Avg);
                    subRow.AddCellValue(speed.ProbSinr.Avg);
                    subRow.AddCellValue(speed.AllSpeed.Avg);
                    subRow.AddCellValue(speed.AllRsrp.Avg);
                    subRow.AddCellValue(speed.AllSinr.Avg);
                    subRow.AddCellValue(speed.ProbLteRsrp.Avg);
                    subRow.AddCellValue(speed.ProbLteSinr.Avg);
                    subRow.AddCellValue(speed.AllLteRsrp.Avg);
                    subRow.AddCellValue(speed.AllLteSinr.Avg);
                }
            }
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                LowSpeedCell_NR item = gridView.GetRow(gridView.GetSelectedRows()[0]) as LowSpeedCell_NR;
                MainModel.MainForm.GetMapForm().GoToView(item.Longitude, item.Latitude);
                if (item.TestPoints.Count == 0)
                {
                    return;
                }

                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in item.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
            }
        }
    }
}
