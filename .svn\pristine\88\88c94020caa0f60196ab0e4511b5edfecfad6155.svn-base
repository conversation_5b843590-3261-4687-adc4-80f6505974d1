﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using System.Collections;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors;
using MasterCom.MTGis;
using System.IO;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTPrivateNetCellAnalysisDlg : BaseDialog
    {
        /// <summary>
        /// 地市列表,用于匹配采样点所属地市
        /// </summary>
        private List<ResvRegion> resvRegionList = new List<ResvRegion>();
        /// <summary>
        /// 专网小区
        /// </summary>
        private List<PrivateNetCell> privateNetCell = new List<PrivateNetCell>();

        public ZTPrivateNetCellAnalysisDlg(PrivateNetCellAnalysisCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        #region 单例
        protected static readonly object lockObj = new object();
        private static ZTPrivateNetCellAnalysisDlg intance = null;
        public static ZTPrivateNetCellAnalysisDlg GetInstance(PrivateNetCellAnalysisCondition condition)
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTPrivateNetCellAnalysisDlg(condition);
                    }
                }
            }
            return intance;
        }
        #endregion

        private void setCondition(PrivateNetCellAnalysisCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            privateNetCell = condition.PrivateNetCell;
            resvRegionList = condition.ResvRegionList;
            labelFileName.Text = condition.FileName;
            labelPrivateCellCount.Text = privateNetCell.Count.ToString();
            txtLayerFolder.Text = condition.LayerPath;
        }

        public PrivateNetCellAnalysisCondition GetConditon()
        {
            PrivateNetCellAnalysisCondition condition = new PrivateNetCellAnalysisCondition();
            condition.PrivateNetCell = privateNetCell;
            condition.ResvRegionList = resvRegionList;
            condition.FileName = labelFileName.Text;
            condition.LayerPath = txtLayerFolder.Text;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (txtLayerFolder.Text == "")
            {
                MessageBox.Show("地市图层未选择");
            }
            else if (resvRegionList.Count <= 0)
            {
                MessageBox.Show("地市图层未加载成功,请重新选择");
            }
            else if (privateNetCell.Count <= 0)
            {
                MessageBox.Show("专网小区不能为空,请先导入专网小区工参");
            }
            else
            {
                this.DialogResult = DialogResult.OK;
            }
        }

        #region 加载工参
        private void btnImport_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.InitialDirectory = "";
            openFileDialog.Filter = "Excel files |*.xls;*.xlsx|all files (*.*)|*.*";
            openFileDialog.RestoreDirectory = true;
            openFileDialog.FilterIndex = 1;
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                WaitBox.Show("正在导入工参", importPrivateNetCell, new object[] { openFileDialog.SafeFileName, openFileDialog.FileName });
            }
        }

        private void importPrivateNetCell(object args)
        {
            object[] theArg = args as object[];
            privateNetCell.Clear();

            string xlsFilePath = theArg[1].ToString();
            try
            {
                DataSet ds = ExcelNPOIManager.ImportFromExcel(xlsFilePath);
                DataTable datatable = ds.Tables[0];

                if (datatable.Columns.Count < 8)
                {
                    MessageBox.Show("缺少字段");
                }
                else if (datatable.Columns.Count > 8)
                {
                    MessageBox.Show("过多字段");
                }
                else if (datatable.Rows.Count > 0)
                {
                    addRowData(datatable);

                    string fileName = theArg[0].ToString();
                    labelFileName.Text = fileName;

                    labelPrivateCellCount.Text = privateNetCell.Count.ToString();
                }
            }
            catch (Exception ex)
            {
                labelFileName.Text = "";
                privateNetCell.Clear();
                MessageBox.Show("导入存在异常," + ex.Message);
            }
            finally
            {
                WaitBox.Close();
            }
        }
        
        private void addRowData(DataTable datatable)
        {
            for (int i = 0; i < datatable.Rows.Count; i++)
            {
                DataRow dr = datatable.Rows[i];
                PrivateNetCell lteCell = new PrivateNetCell();
                //第一列是区域名
                lteCell.AreaName = getRowData(dr[0], false);
                //第二列是路段名
                lteCell.RoadName = getRowData(dr[1], false);
                //第三列是网络类型
                lteCell.NetType = getRowData(dr[2], false);
                //第四列是TAC
                lteCell.TAC = Convert.ToInt32(getRowData(dr[3], true));
                //第五列是ECI
                lteCell.ECI = Convert.ToInt32(getRowData(dr[4], true));
                LTEBTS bts = new LTEBTS();
                //第六列是经度
                bts.Longitude = Convert.ToDouble(getRowData(dr[5], true));
                //第七列是纬度
                bts.Latitude = Convert.ToDouble(getRowData(dr[6], true));
                //第八列是小区名
                lteCell.CellName = getRowData(dr[7], false);
                privateNetCell.Add(lteCell);

                WaitBox.ProgressPercent = i * 100 / datatable.Rows.Count;
            }
        }

        private string getRowData(object data, bool isNumeric)
        {
            if (data != DBNull.Value && data.ToString() != "")
            {
                return data.ToString();
            }
            if (isNumeric)
            {
                return "0";
            }
            return "";
        }

        private void btnDownload_Click(object sender, EventArgs e)
        {
            #region 下载模板
            List<NPOIRow> datas = new List<NPOIRow>();
            List<object> cols = new List<object>();
            cols.Add("strareaname");
            cols.Add("strroadname");
            cols.Add("strnettype");
            cols.Add("ilac");
            cols.Add("ici");
            cols.Add("ilongitude");
            cols.Add("ilatitude");
            cols.Add("strcomment");
            NPOIRow nr = new NPOIRow();
            nr.cellValues = cols;
            datas.Add(nr);

            List<object> secValue1 = new List<object>();
            secValue1.Add("北部新区");
            secValue1.Add("成渝高铁");
            secValue1.Add("LTE");
            secValue1.Add("13304");
            secValue1.Add("39048705");
            secValue1.Add("1064722220");
            secValue1.Add("296502770");
            secValue1.Add("北部新区斑竹林隧道拉远-HLHA");
            NPOIRow nr1 = new NPOIRow();
            nr1.cellValues = secValue1;
            datas.Add(nr1);
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(datas);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("下载模板失败（error：" + ex.Message + "）");
            }
            #endregion
        }
        #endregion

        #region 加载图层
        private void btnLayerFolder_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.InitialDirectory = "";
            openFileDialog.Filter = "Shape Files |*.shp;|all files (*.*)|*.*";
            openFileDialog.RestoreDirectory = true;
            openFileDialog.FilterIndex = 1;
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtLayerFolder.Text = openFileDialog.FileName;
                //获取图层
                try
                {
                    resvRegionList = getRegionList(txtLayerFolder.Text);
                    if (resvRegionList.Count <= 0)
                    {
                        MessageBox.Show("地市图层未加载成功,请重新选择");
                    }
                }
                catch
                {
                    MessageBox.Show("图层加载出错");
                }
            }
        }

        /// <summary>
        /// 根据地图中的shp文件获取当前地市的地市区域列表
        /// </summary>
        /// <returns></returns>
        private List<ResvRegion> getRegionList(string layerPath)
        {
            List<ResvRegion> regionList = new List<ResvRegion>();
            if (File.Exists(layerPath))
            {
                System.IO.FileInfo file = new System.IO.FileInfo(layerPath);
                string nameColumnName = "NAME";

                string typeName = file.Name;
                if (typeName.IndexOf('.') >= 0)
                {
                    typeName = typeName.Substring(0, typeName.IndexOf('.'));
                }

                MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
                try
                {
                    if (!table.Open(file.FullName, null))
                    {
                        return new List<ResvRegion>();
                    }

                    getShapeFromFile(regionList, nameColumnName, typeName, table);
                    return regionList;
                }
                catch
                {
                    return new List<ResvRegion>();
                }
                finally
                {
                    table.Close();
                }
            }
            return new List<ResvRegion>();
        }

        private static void getShapeFromFile(List<ResvRegion> regionList, string nameColumnName, string typeName, MapWinGIS.Shapefile table)
        {
            int nmFldIndex = MapOperation.GetColumnFieldIndex(table, nameColumnName);
            for (int i = 0; i < table.NumShapes; i++)
            {
                MapWinGIS.Shape geome = table.get_Shape(i);
                if (geome != null)
                {
                    String namestr = table.get_CellValue(nmFldIndex, i).ToString();
                    if (namestr != null && namestr.Trim().Length > 0)
                    {
                        addValidRegion(regionList, typeName, geome, namestr);
                    }
                }
            }
        }

        private static void addValidRegion(List<ResvRegion> regionList, string typeName, MapWinGIS.Shape geome, string namestr)
        {
            if (geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGON
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGONZ
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINE
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINEZ)
            {
                ResvRegion region = new ResvRegion();
                region.RootNodeName = typeName;
                region.RegionName = namestr;
                region.Shape = geome;

                regionList.Add(region);
            }
        }
        #endregion
    }

    public class PrivateNetCellAnalysisCondition
    {
        /// <summary>
        /// 专网工参小区
        /// </summary>
        public List<PrivateNetCell> PrivateNetCell { get; set; } = new List<PrivateNetCell>();

        public string FileName { get; set; } = "";
        public string LayerPath { get; set; } = "";

        /// <summary>
        /// 当前区域的地市列表,用于匹配采样点所属地市
        /// </summary>
        public List<ResvRegion> ResvRegionList { get; set; }
    }

    /// <summary>
    /// 专网工参小区
    /// </summary>
    public class PrivateNetCell
    {
        public string AreaName { get; set; }
        public string RoadName { get; set; }
        public string NetType { get; set; }
        public int TAC { get; set; }
        public int ECI { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string CellName { get; set; }
    }
}
