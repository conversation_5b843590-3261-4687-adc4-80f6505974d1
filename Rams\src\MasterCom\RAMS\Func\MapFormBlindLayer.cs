using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class MapFormBlindLayer : CustomDrawLayer
    {
        public MapFormBlindLayer(MapOperation oper, string name)
            : base(oper, name)
        {
        }

        public Color BackgroundColor { get; set; } = Color.White;

        public int Transparence { get; set; }

        public override void GetObjectData(System.Runtime.Serialization.SerializationInfo info, System.Runtime.Serialization.StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue("BackgroundColor", BackgroundColor);
            info.AddValue("Transparence", Transparence);
        }

        public override void SetObjectData(System.Runtime.Serialization.SerializationInfo info, System.Runtime.Serialization.StreamingContext context)
        {
            base.SetObjectData(info, context);
            BackgroundColor = (Color)info.GetValue("BackgroundColor", typeof(Color));
            Transparence = (int)info.GetValue("Transparence", typeof(int));
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(Transparence, BackgroundColor)), updateRect);
        }

        public new Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundColorR"] = (int)BackgroundColor.R;
                param["BackgroundColorG"] = (int)BackgroundColor.G;
                param["BackgroundColorB"] = (int)BackgroundColor.B;
                param["Transparence"] = Transparence;
                return param;
            }
            set
            {
                BackgroundColor = Color.FromArgb((int)value["BackgroundColorR"], (int)value["BackgroundColorG"], (int)value["BackgroundColorB"]);
                Transparence = (int)value["Transparence"];
            }
        }
    }
}
