﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class RelayChart : UserControl
    {
        public RelayChart()
        {
            InitializeComponent();
            penMiddle.DashStyle = System.Drawing.Drawing2D.DashStyle.DashDotDot;
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer |
                    ControlStyles.ResizeRedraw |
                    ControlStyles.AllPaintingInWmPaint, true);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            if (TerminalEntitySetList.Count < 2)
            {
                return;
            }

            #region 绘制柱线
            int eachEntityGap = (Width - padLeft - padRight) / (TerminalEntitySetList.Count - 1);
            for (int c = 0; c < TerminalEntitySetList.Count; c++)
            {
                TerminalEntitySet entitySet = TerminalEntitySetList[c];
                SizeF sizeF = e.Graphics.MeasureString(entitySet.Name, fontTitle);
                int logoMiddleX = padLeft + eachEntityGap * c;
                if (entitySet.ImgIcon != null)
                {

                    Rectangle rect = new Rectangle();
                    rect.X = logoMiddleX - 30;
                    rect.Y = 0;
                    rect.Width = 60;
                    rect.Height = 60;
                    e.Graphics.DrawImage(entitySet.ImgIcon, rect);
                }
                e.Graphics.DrawString(entitySet.Name, fontTitle, brushTitle, logoMiddleX - sizeF.Width / 2, padTop - sizeF.Height - 1);
                e.Graphics.DrawLine(penLineV, logoMiddleX, padTop, logoMiddleX, Height);
            }
            for (int c = 0; c < TerminalEntitySetList.Count - 1; c++)
            {
                int centerLineX = padLeft + eachEntityGap * c + eachEntityGap / 2;
                e.Graphics.DrawLine(penMiddle, centerLineX, padTop, centerLineX, Height);
            }

            int height = padTop - heightInterval / 2;
            #endregion

            #region 绘制

            if (RelayInfos != null)
            {
                draw(e, eachEntityGap, height);
                MinimumSize = new Size(0, padTop + heightInterval * 5);

            }
            else
            {
                MinimumSize = new Size(MinimumSize.Width, padTop);
            }
            #endregion
        }

        private void draw(PaintEventArgs e, int eachEntityGap, int height)
        {
            int maxnumber = RelayInfos.Count;
            heightInterval = 30;
            fontUp = new Font(new FontFamily("宋体"), 8, FontStyle.Regular);
            fontDown = new Font(new FontFamily("宋体"), 7, FontStyle.Regular);
            if (maxnumber > 0)
            {
                foreach (RelayInfo relayInfo in RelayInfos)
                {
                    if (relayInfo.RelayDeco == null || relayInfo.RelayDeco.direction == RelayInfo.Direction.Unknown)
                    {
                        continue;
                    }

                    height += heightInterval;
                    int entityIndex = relayInfo.RelayDeco.stackLayerIndex;
                    int leftAt = 0;
                    int rightAt = 0;
                    getPosition(eachEntityGap, relayInfo, entityIndex, out leftAt, out rightAt);

                    int middleAt = (leftAt + rightAt) / 2;
                    //文字坐标
                    int textAt = drawRelay(e, height, relayInfo, leftAt, rightAt, middleAt);

                    int width = middleAt - leftAt > 0 ? middleAt - leftAt : 5;
                    Rectangle rectX = new Rectangle(leftAt, height - 15, width, 30);
                    relayInfo.drawPlaceRect = rectX;
                    drawString(e, height, relayInfo, textAt);
                }
            }
        }

        private void getPosition(int eachEntityGap, RelayInfo relayInfo, int entityIndex, out int leftAt, out int rightAt)
        {
            AbisSignalInfo tempAbisSignalInfo = relayInfo as AbisSignalInfo;
            if (tempAbisSignalInfo != null && tempAbisSignalInfo.relativeESD != null
                && tempAbisSignalInfo.relativeESD.OPC != 0)//暂时使用OPC来区分是GSM还是TD
            {
                leftAt = padLeft + eachEntityGap * (entityIndex + 1);
                rightAt = padLeft + eachEntityGap * (entityIndex + 2);
            }
            else
            {
                leftAt = padLeft + eachEntityGap * entityIndex;
                rightAt = padLeft + eachEntityGap * (entityIndex + 1);
            }
        }

        private int drawRelay(PaintEventArgs e, int height, RelayInfo relayInfo, int leftAt, int rightAt, int middleAt)
        {
            int textAt;
            Pen thePen = relayInfo.RelayDeco.color == Color.Empty ? penLineH : new Pen(relayInfo.RelayDeco.color, 2);
            if (relayInfo.RelayDeco.direction == RelayInfo.Direction.Right)
            {
                textAt = (leftAt + middleAt) / 2;
                e.Graphics.DrawLine(thePen, leftAt, height, middleAt, height);
                e.Graphics.DrawLine(thePen, middleAt - 8 - 1, height - 4, middleAt - 1, height);
                e.Graphics.DrawLine(thePen, middleAt - 8 - 1, height + 4 - 1, middleAt - 1, height);
            }
            else
            {
                textAt = (middleAt + rightAt) / 2;
                e.Graphics.DrawLine(thePen, middleAt, height, rightAt, height);
                e.Graphics.DrawLine(thePen, middleAt, height, middleAt + 8, height - 4);
                e.Graphics.DrawLine(thePen, middleAt, height - 1, middleAt + 8, height + 4);
            }

            return textAt;
        }

        private void drawString(PaintEventArgs e, int height, RelayInfo relayInfo, int textAt)
        {
            if (relayInfo.UpString != null)
            {
                string showText = relayInfo.UpString;
                if (relayInfo.RelayDeco.shortName != "")
                {
                    showText = relayInfo.RelayDeco.shortName;
                }
                SizeF sizeF = e.Graphics.MeasureString(showText, fontUp);
                e.Graphics.DrawString(showText, fontUp, brushUp, textAt - sizeF.Width / 2, height - sizeF.Height - 1);
            }
            if (relayInfo.DownString != null)
            {
                SizeF sizeF = e.Graphics.MeasureString(relayInfo.DownString, fontDown);
                e.Graphics.DrawString(relayInfo.DownString, fontDown, brushDown, textAt - sizeF.Width / 2, height + 1);
            }
        }

        #region 公共字段
        public List<RelayInfo> RelayInfos { get; set; }

        public List<TerminalEntitySet> TerminalEntitySetList { get; set; }
        #endregion

        #region 默认值
        private int padTop = 75;

        private int padLeft = 30;

        private int padRight = 30;

        private int heightInterval = 20;

        private Pen penLineH = new Pen(Color.Green, 2);

        private Pen penLineV = new Pen(Color.Blue, 2);

        private Pen penMiddle = new Pen(Color.FromArgb(200, 150, 0), 2);

        private Font fontTitle = new Font(new FontFamily("宋体"), 10, FontStyle.Bold);

        private Font fontUp = new Font(new FontFamily("宋体"), 10, FontStyle.Regular);

        private Font fontDown = new Font(new FontFamily("宋体"), 8, FontStyle.Regular);

        private Brush brushTitle = Brushes.DarkBlue;

        private Brush brushUp = Brushes.Black;

        private Brush brushDown = Brushes.Black;
        #endregion

        #region 窗体大小变化
        private void RelayChart_Resize(object sender, EventArgs e)
        {
            Invalidate();
        }
        #endregion
    }

}
