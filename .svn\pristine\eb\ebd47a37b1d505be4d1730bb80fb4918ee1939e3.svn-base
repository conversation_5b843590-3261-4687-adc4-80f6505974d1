﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationKpiAccept_SX : DIYAnalyseByCellBackgroundBaseByFile
    {
        public string WebservicePath { get; set; } = "";
        readonly Dictionary<int, BtsKpiAcceptInfo_SX> btsKpiInfoDic = new Dictionary<int, BtsKpiAcceptInfo_SX>();

        protected static readonly object lockObj = new object();
        private static StationKpiAccept_SX intance = null;
        public static StationKpiAccept_SX GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new StationKpiAccept_SX();
                    }
                }
            }
            return intance;
        }
        protected StationKpiAccept_SX()
            : base(MainModel.GetInstance())
        {
            if (intance != null)
            {
                return;
            }
            this.isIgnoreExport = true;
        }
        public override string Name
        {
            get
            {
                return "单站验收（与App对接版）";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18045, "查询");
        }
        public static bool IsWebserviceCanConnect(string webservicePath, out string strTips)
        {
            if (string.IsNullOrEmpty(webservicePath))
            {
                strTips = "Webservice地址不能为空！";
                return false;
            }
            else
            {
                try
                {
                    object[] args = new object[3];
                    args[0] = "1";
                    args[1] = "测试";
                    args[2] = "<result>接口连接测试</result>";

                    object result = WebServiceHelper.InvokeWebService(webservicePath, "GetResult", args);
                    strTips = result == null ? "调用接口失败" : result.ToString();
                    if (strTips.Contains("成功"))
                    {
                        return true;
                    }
                    return false;
                }
                catch(Exception ee)
                {
                    strTips = "调用接口失败:" + ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace;
                    return false;
                }
            }
        }

        protected override bool getCondition()
        {
            FilterEventByRegion = false;
            this.Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTE));
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTE));

            return true;
        }
        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                int eci; 
                int enodeBid;
                if (!getEciInfoByCfg(dtFile, out eci, out enodeBid))//先直接从文件名获取eci和enodebid
                {
                    reportBackgroundInfo(string.Format("{0}未匹配到目标小区", dtFile.FileName));
                    continue;
                }

                KpiRateInfo_SX rrcKpiInfo = new KpiRateInfo_SX();
                KpiRateInfo_SX erabKpiInfo = new KpiRateInfo_SX();

                foreach (Event evt in dtFile.Events)
                {
                    switch (evt.ID)
                    {
                        case (int)EnumEventID_SX.RrcSetupRequest:
                            rrcKpiInfo.RequestCnt++;
                            break;
                        case (int)EnumEventID_SX.RrcSetupSuccess:
                            rrcKpiInfo.SuccessCount++;
                            break;
                        case (int)EnumEventID_SX.ErabSetupRequest:
                            erabKpiInfo.RequestCnt++;
                            break;
                        case (int)EnumEventID_SX.ErabSetupSuccess:
                            erabKpiInfo.SuccessCount++;
                            break;
                    }
                }

                saveToResult(eci, enodeBid, dtFile.GetFileInfo(), rrcKpiInfo, erabKpiInfo);
            }
            MainModel.DTDataManager.Clear();
        }
        protected bool getEciInfoByFileName(DTFileDataManager fileManager, out int eci, out int enodeBid)
        {
            enodeBid = 0;
            string[] strArr = fileManager.FileName.Split('-');
            if (int.TryParse(strArr[0], out eci))
            {
                enodeBid = (eci / 256);
                return true;
            }
            return false;
        }

        #region 匹配工参获取测试小区
        protected bool getEciInfoByCfg(DTFileDataManager fileManager, out int eci, out int enodeBid)
        {
            LTECell testCell = getTestCell(fileManager);
            if (testCell != null)
            {
                eci = testCell.ECI;
                enodeBid = testCell.BelongBTS.BTSID;
                return true;
            }
            else
            {
                eci = 0;
                enodeBid = 0;
                return false;
            }
        }
        protected LTECell getTestCell(DTFileDataManager fileManager)
        {
            LTECell testCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null
                    && (fileManager.FileName.Contains(cell.ECI.ToString())
                        || fileManager.FileName.Contains(cell.Name)))
                {
                    testCell = cell;
                    break;
                }
            }
            return testCell;
        }
        protected LTECell getTpSrcCell(TestPoint tp)
        {
            LTECell cell = null;
            if (tp is LTETestPointDetail)
            {
                cell = CellManager.GetInstance().GetNearestLTECellByTACCI(tp.DateTime, (int?)(ushort?)tp["lte_TAC"], (int?)tp["lte_ECI"]
                    , tp.Longitude, tp.Latitude);

                int? earfcn = (int?)tp["lte_EARFCN"];
                int? pci = (int?)(short?)tp["lte_PCI"];

                if (cell == null)
                {
                    cell = getLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude, tp.FileName);
                }
                else if (earfcn != null && pci != null
                    && (cell.EARFCN != (int)earfcn || cell.PCI != (int)pci))
                {
                    cell = getLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude, tp.FileName);
                }
            }
            return cell;
        }
        protected LTECell getLTECellByEARFCNPCI(DateTime time, int? earfcn, int? pci, double longitude
            , double latitude, string fileName)
        {
            if (earfcn == null || pci == null)
            {
                return null;
            }
            List<LTECell> cells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(time, earfcn, pci);
            if (cells != null && cells.Count > 0)
            {
                #region 先根据文件名匹配小区，匹配不到再取最近的一个小区
                if (!string.IsNullOrEmpty(fileName))
                {
                    LTECell validCell = getValidCell(time, longitude, latitude, fileName, cells);
                    if (validCell != null)
                    {
                        return validCell;
                    }
                }
                #endregion

                return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(time, earfcn, pci, longitude, latitude);
            }
            return null;
        }

        private LTECell getValidCell(DateTime time, double longitude, double latitude, string fileName, List<LTECell> cells)
        {
            foreach (LTECell cell in cells)
            {
                if (cell.ValidPeriod.Contains(time))
                {
                    if (longitude > 0 && latitude > 0 && CellManager.GetInstance().SystemConfigInfo.distLimit
                        && cell.GetDistance(longitude, latitude) > CD.MAX_COV_DISTANCE_LTE)//距离限制设置
                    {
                        continue;
                    }
                    if (fileName.Contains(cell.ECI.ToString()) || fileName.Contains(cell.Name))
                    {
                        return cell;
                    }
                }
            }
            return null;
        }
        #endregion

        protected void saveToResult(int eci, int enodeBid, FileInfo file
            , KpiRateInfo_SX rrcKpiInfo, KpiRateInfo_SX erabKpiInfo)
        {
            BtsKpiAcceptInfo_SX btsKpiInfo;
            if (!btsKpiInfoDic.TryGetValue(enodeBid, out btsKpiInfo))
            {
                btsKpiInfo = new BtsKpiAcceptInfo_SX(enodeBid);
                btsKpiInfoDic.Add(enodeBid, btsKpiInfo);
            }
            btsKpiInfo.FileList.Add(file);

            CellKpiAcceptInfo_SX cellKpiInfo;
            if (!btsKpiInfo.CellKpiAcceptInfoDic.TryGetValue(eci, out cellKpiInfo))
            {
                cellKpiInfo = new CellKpiAcceptInfo_SX(eci);
                btsKpiInfo.CellKpiAcceptInfoDic.Add(eci, cellKpiInfo);
            }
            cellKpiInfo.AddData(rrcKpiInfo, erabKpiInfo);
        }
        protected override void doSomethingAfterAnalyseFiles()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStopRequest)
            {
                sendResultToWebservice(btsKpiInfoDic);
            }
        }
        protected void sendResultToWebservice(Dictionary<int, BtsKpiAcceptInfo_SX> resultDic)
        {
            if (resultDic == null || resultDic.Count <= 0)
            {
                reportBackgroundInfo("未分析到验收信息");
                return;
            }

            try
            {
                foreach (BtsKpiAcceptInfo_SX btsKpiInfo in resultDic.Values)
                {
                    StringBuilder strb = new StringBuilder();
                    foreach (CellKpiAcceptInfo_SX cellKpiInfo in btsKpiInfo.CellKpiAcceptInfoDic.Values)
                    {
                        strb.Append(string.Format(@"<eci={0},RrcSetupSuccess:{1},RrcSetupSuccessRate:{2}
,ErabSetupSuccess:{3},ErabSetupSuccessRate:{4},AccessSuccess:{5},AccessSuccessRate:{6}/>"
                            , cellKpiInfo.ECI, cellKpiInfo.RrcKpiInfo.SuccessCount, cellKpiInfo.RrcKpiInfo.SuccessRate
                            , cellKpiInfo.ErabKpiInfo.SuccessCount, cellKpiInfo.ErabKpiInfo.SuccessRate
                            , cellKpiInfo.AccessSuccessCount, cellKpiInfo.AccessSuccessRate));
                    }

                    object[] args = new object[3];
                    args[0] = btsKpiInfo.ENodeBID.ToString();
                    args[1] = curAnaFileInfo.DistrictName;
                    args[2] = "<result>" + strb.ToString() + "</result>";
                    reportBackgroundInfo(string.Format("开始调用接口发送验收信息:{0},{1},{2}", args[0], args[1], args[2]));

                    object result = WebServiceHelper.InvokeWebService(WebservicePath, "GetResult", args);
                    string resultDes = result == null ? "调用接口失败" : result.ToString();
                    if (resultDes.Contains("成功"))
                    {
                        saveBtsFileInfoToDB(btsKpiInfo.FileList);
                    }

                    reportBackgroundInfo(string.Format("发送站点{0}的验收信息：{1}", btsKpiInfo.ENodeBID, resultDes));
                }
            }
            catch (Exception ex)
            {
                reportBackgroundError(ex);
            }
            finally
            {
                resultDic.Clear();
            }
        }

        //将已分析过且发送指标成功的文件信息保存到数据库，下次就不再分析该文件
        private void saveBtsFileInfoToDB(List<FileInfo> fileList)
        {
            if (fileList == null || fileList.Count <= 0)
            {
                return;
            }

            int funcId = GetSubFuncID();
            List<BackgroundResult> resultList = new List<BackgroundResult>();
            foreach (FileInfo fi in fileList)
            {
                BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(funcId, fi, resultList);
            }
        }

        #region Background
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.单站验收; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Clear();
                ignoreParamKeys.Add("WebservicePath");

                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["WebservicePath"] = WebservicePath;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("WebservicePath"))
                {
                    WebservicePath = (string)param["WebservicePath"];
                }
            }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new StationKpiAcceptPropertiesSX_LTE(this);
            }
        }

        protected override void saveBackgroundData()
        {
            //
        }
        protected override void getFilesForAnalyse()
        {
            base.getFilesForAnalyse();
            MainModel.FileInfos.Sort(comparerByTimeDesc);
        }
        protected override void getBackgroundData()
        {
            //
        }
        protected override void initBackgroundImageDesc()
        {
            //
        }

        private readonly ComparerByBeginTime comparerByTimeDesc = new ComparerByBeginTime();
        public class ComparerByBeginTime : IComparer<FileInfo>
        {
            public int Compare(FileInfo x, FileInfo y)
            {
                return y.BeginTime.CompareTo(x.BeginTime);
            }
        }
        #endregion
    }
    public enum EnumEventID_SX
    {
        RrcSetupRequest = 855,
        RrcSetupSuccess = 856,
        ErabSetupRequest = 858,
        ErabSetupSuccess = 859,
        AccessRequest = 22,
        AccessSuccess = 23,
    }
}
