﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MsgParamCheckDlg : BaseForm
    {
        bool isSelectAll = true;
        List<MsgParamSetting> paramList;
        public MsgParamCheckDlg(List<MsgParamSetting> paramList)
        {
            InitializeComponent();
            this.paramList = new List<MsgParamSetting>(paramList);
            fillChkList();
        }
        private void fillChkList()
        {
            this.gridControl1.DataSource = this.paramList;
            this.gridControl1.RefreshDataSource();
        }
        public List<MsgParamSetting> GetCondition()
        {
            return gridViewDetail.DataSource as List<MsgParamSetting>;
        }

        private void btnselectAll_Click(object sender, EventArgs e)
        {
            if (isSelectAll)
            {
                btnselectAll.Text = "清空";
            }
            else
            {
                btnselectAll.Text = "全选";
            }
            foreach (MsgParamSetting item in gridViewDetail.DataSource as List<MsgParamSetting>)
            {
                item.IsChecked = isSelectAll;
            }
            this.gridControl1.DataSource = this.paramList;
            this.gridControl1.RefreshDataSource();
            isSelectAll = !isSelectAll;
        }

        private void ToolStripSet_Click(object sender, EventArgs e)
        {
            MsgParamSetting selectParam = gridViewDetail.GetFocusedRow() as MsgParamSetting;
            if (selectParam != null)
            {
                ParamValueSetDlg dlg = new ParamValueSetDlg(selectParam);
                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    //selectParam = dlg.GetParamSetting();
                }
                dlg.Close();
            }
        }
    }
}
