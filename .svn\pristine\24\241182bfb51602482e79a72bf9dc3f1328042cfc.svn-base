﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTKPIColumnPanel : UserControl
    {
        private CQTKPIReportColumn curColumn;
        private float kpiValueMin;
        private float kpiValueMax;
        private double scoreMin;
        private double scoreMax;
        public CQTKPIColumnPanel()
        {
            InitializeComponent();
            kpiFormulaEditor.SubmitFormula += new EventHandler(kpiFormulaEditor_SubmitFormula);
        }

        void kpiFormulaEditor_SubmitFormula(object sender, EventArgs e)
        {
            if (e is KPIFormulaEditor.SubmitFormulaEventArgs)
            {
                KPIFormulaEditor.SubmitFormulaEventArgs kpiE = e as KPIFormulaEditor.SubmitFormulaEventArgs;
                textBoxFormula.Text = kpiE.Formula;
            }
        }

        public void SetColumn(CQTKPIReportColumn column)
        {
            seKpiRangeMin.ValueChanged -= seKpiRangeMin_EditValueChanged;
            seKpiRangeMax.ValueChanged -= seKpiRangeMax_EditValueChanged;
            seScoreRangeMin.ValueChanged -= seScoreRangeMin_EditValueChanged;
            seScoreRangeMax.ValueChanged -= seScoreRangeMax_EditValueChanged;
            curColumn = column;
            if (column == null)
            {
                this.Enabled = false;
                return;
            }

            this.Enabled = true;
            curColumn = column;
            textEditName.Text = column.Name;
            kpiValueMin = column.KPIValueRangeMin;
            seKpiRangeMin.Value = (decimal)kpiValueMin;
            kpiValueMax = column.KPIValueRangeMax;
            seKpiRangeMax.Value = (decimal)kpiValueMax;

            scoreMin = column.ScoreRangeMin;
            seScoreRangeMin.Value = (decimal)scoreMin;
            scoreMax = column.ScoreRangeMax;
            seScoreRangeMax.Value = (decimal)scoreMax;

            radioScoreType.SelectedIndex = (int)column.ScoreScheme.ScoreOrderType;
            scoreColorRangeSettingPanel.SetRange(column.ScoreScheme.ScoreColorRanges, kpiValueMin, kpiValueMax, scoreMin, scoreMax, column.ScoreScheme.ScoreOrderType);
            textBoxFormula.Text = column.Formula;
            seKpiRangeMin.ValueChanged += seKpiRangeMin_EditValueChanged;
            seKpiRangeMax.ValueChanged += seKpiRangeMax_EditValueChanged;
            seScoreRangeMin.ValueChanged += seScoreRangeMin_EditValueChanged;
            seScoreRangeMax.ValueChanged += seScoreRangeMax_EditValueChanged;
        }

        ScoreOrderType scoreType;
        private void refreshRange()
        {
            if (curColumn == null)
            {
                return;
            }
            curColumn.ScoreRangeMin = scoreMin;
            curColumn.ScoreRangeMax= scoreMax;
            curColumn.KPIValueRangeMin= kpiValueMin;
            curColumn.KPIValueRangeMax  = kpiValueMax;
            curColumn.ScoreScheme.ScoreOrderType = scoreType;
            scoreColorRangeSettingPanel.SetRange(curColumn.ScoreScheme.ScoreColorRanges, kpiValueMin, kpiValueMax, scoreMin, scoreMax, curColumn.ScoreScheme.ScoreOrderType);
        }

#region 控件操作
        private void seKpiRangeMin_EditValueChanged(object sender, EventArgs e)
        {
            if (seKpiRangeMin.Value > seKpiRangeMax.Value)
            {
                seKpiRangeMin.Value = (decimal)kpiValueMin;
            }
            else
            {
                kpiValueMin = (float)seKpiRangeMin.Value;
            }
            refreshRange();
        }

        private void seKpiRangeMax_EditValueChanged(object sender, EventArgs e)
        {
            if (seKpiRangeMax.Value < seKpiRangeMin.Value)
            {
                seKpiRangeMax.Value = (decimal)kpiValueMax;
            }
            else
            {
                kpiValueMax = (float)seKpiRangeMax.Value;
            }
            refreshRange();
        }

        private void seScoreRangeMin_EditValueChanged(object sender, EventArgs e)
        {
            if (seScoreRangeMin.Value > seScoreRangeMax.Value)
            {
                seScoreRangeMin.Value = (decimal)scoreMin;
            }
            else
            {
                scoreMin = (double)seScoreRangeMin.Value;
            }
            refreshRange();
        }

        private void seScoreRangeMax_EditValueChanged(object sender, EventArgs e)
        {
            if (seScoreRangeMax.Value < seScoreRangeMin.Value)
            {
                seScoreRangeMax.Value = (decimal)scoreMax;
            }
            else
            {
                scoreMax = (double)seScoreRangeMax.Value;
            }
            refreshRange();
        }

        private void textEditName_EditValueChanged(object sender, EventArgs e)
        {
            if (curColumn!=null)
            {
                curColumn.Name = textEditName.Text;
            }
        }

        private void radioScoreType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (curColumn != null)
            {
                scoreType = (ScoreOrderType)radioScoreType.SelectedIndex;
                refreshRange();
            }
        }
#endregion

        private void textBoxFormula_TextChanged(object sender, EventArgs e)
        {
            if (curColumn != null)
            {
                curColumn.Formula = textBoxFormula.Text;
            }
        }

        private void textBoxFormula_TextChanged_1(object sender, EventArgs e)
        {
            if (curColumn!=null)
            {
                curColumn.Formula = textBoxFormula.Text;
            }
        }

    }
}
