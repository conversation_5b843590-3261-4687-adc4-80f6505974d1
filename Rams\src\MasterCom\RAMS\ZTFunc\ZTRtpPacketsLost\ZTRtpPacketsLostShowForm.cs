﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTRtpPacketsLostShowForm : MinCloseForm
    {
        public ZTRtpPacketsLostShowForm()
            : base()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            DisposeWhenClose = true;
        }

        private MapForm mapForm = null;

        public void FillData(List<ZTRtpPacketsLostFileInfo> resultList)
        {
            gridControlRTP.DataSource = resultList;
            gridControlRTP.RefreshDataSource();

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void gridViewMessageInfo_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object row = gv.GetRow(gv.GetSelectedRows()[0]);
            if (row is ZTRtpPacketsLostMessageInfo)
            {
                MainModel.ClearDTData();
                ZTRtpPacketsLostMessageInfo lost = row as ZTRtpPacketsLostMessageInfo;

                foreach (TestPoint tp in lost.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.DTDataManager.Add(lost.StartMsg);

                MainModel.MainForm.GetMapForm().GoToView(lost.Longitude, lost.Latitude, 6000);

                this.MainModel.IsFileReplayByCompareMode = false;
                this.MainModel.FireDTDataChanged(this);
                this.MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");    //渲染图标
            }
        }

        private void miExportRTPExcel_Click(object sender, EventArgs e)
        {
            List<ZTRtpPacketsLostFileInfo> viewList = gridControlRTP.DataSource as List<ZTRtpPacketsLostFileInfo>;
            if (viewList == null)
            {
                return;
            }

            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("文件名");
            title.Add("编号");
            title.Add("文件区域");
            title.Add("预存区域");
            title.Add("道路名称");
            title.Add("起呼时间（completetime）");
            title.Add("起呼时间（handsetstime）");
            title.Add("丢包方向");
            title.Add("丢包起点时间（computertime）");
            title.Add("丢包起点时间（handsetstime）");
            title.Add("经度");
            title.Add("纬度");
            title.Add("RTP丢包数");
            title.Add("丢包持续时间(秒)(handsetstime)");
            title.Add("丢包原因分析");
            title.Add("MOS打分时间(computertime)");
            title.Add("当前MOS值(computertime)");
            title.Add("平均MOS值(computertime)");
            title.Add("丢包前占用的小区");
            title.Add("丢包前占用的小区RSRP");
            title.Add("丢包前占用的小区SINR");
            title.Add("丢包前占用的小区路损");
            title.Add("丢包前占用的小区pdsch_bler");
            title.Add("丢包前占用的小区pusch_bler");
            title.Add("丢包前占用的小区uetxpower");
            title.Add("丢包前占用的小区RB占用数");
            title.Add("丢包前占用的频点EARFCN");
            title.Add("PDCCH_UL_Grant_Count");
            title.Add("PDCCH_DL_Grant_Count");
            title.Add("MCSCode0_DL");
            title.Add("MCSCode1_DL");
            title.Add("MCS_UL");
            title.Add("PDSCH_Code0_BLER");
            title.Add("PDSCH_Code1_BLER");
            title.Add("PDCCH_CCE_Start");
            title.Add("PDCCH_CCEs_Number");
            title.Add("上行QPSK");
            title.Add("上行QAM16");
            title.Add("上行QAM64");
            title.Add("PUSCH_prb_num_slot");
            title.Add("上行初传BLER");
            title.Add("TM传输模");
            title.Add("rank");
            title.Add("下行QPSKCode0");
            title.Add("下行QPSKCode1");
            title.Add("下行QAM16Code0");
            title.Add("下行QAM16Code1");
            title.Add("下行QAM64Code0");
            title.Add("下行QAM64Code1");
            title.Add("PDSCH_prb_num_slot");
            title.Add("下行初传BLER");
            title.Add("下行初传BLERCode0");
            title.Add("下行初传BLERCode1");
            content.Add(title);

            foreach (ZTRtpPacketsLostFileInfo fileInfo in viewList)
            {
                List<object> fstRow = new List<object>();
                fstRow.Add(fileInfo.FileName);
                content.Add(fstRow);
                foreach (ZTRtpPacketsLostMessageInfo messageInfo in fileInfo.MessageInfos)
                {
                    List<object> row = new List<object>();
                    row.Add(messageInfo.FileName);
                    row.Add(messageInfo.SN);
                    row.Add(messageInfo.Area);
                    row.Add(messageInfo.Grid);
                    row.Add(messageInfo.RoadName);
                    row.Add(messageInfo.SCallCompleteTimeStr);
                    row.Add(messageInfo.SCallTimeStr);
                    row.Add(messageInfo.Direction);
                    row.Add(messageInfo.SLossCompleteTimeStr);
                    row.Add(messageInfo.SLossTimeStr);
                    row.Add(messageInfo.Longitude);
                    row.Add(messageInfo.Latitude);
                    row.Add(messageInfo.LossNumber);
                    row.Add(messageInfo.LossTime);
                    row.Add(messageInfo.LossReason);
                    row.Add(messageInfo.MosTime);
                    row.Add(messageInfo.MosVal);
                    row.Add(messageInfo.MosAveVal);
                    row.Add(messageInfo.LossCellName);
                    row.Add(messageInfo.RSRP);
                    row.Add(messageInfo.SINR);
                    row.Add(messageInfo.PathLoss);
                    row.Add(messageInfo.Pdsch_bler);
                    row.Add(messageInfo.Pusch_bler);
                    row.Add(messageInfo.Uetxpower);
                    row.Add(messageInfo.RBCount);
                    row.Add(messageInfo.EARFCN);
                    row.Add(messageInfo.PDCCH_UL_Grant_Count);
                    row.Add(messageInfo.PDCCH_DL_Grant_Count);
                    row.Add(messageInfo.MCSCode0_DL);
                    row.Add(messageInfo.MCSCode1_DL);
                    row.Add(messageInfo.MCS_UL);
                    row.Add(messageInfo.PDSCH_Code0_BLER);
                    row.Add(messageInfo.PDSCH_Code1_BLER);
                    row.Add(messageInfo.PDCCH_CCE_Start);
                    row.Add(messageInfo.PDCCH_CCEs_Number);
                    row.Add(messageInfo.Times_QPSK_UL);
                    row.Add(messageInfo.Times_QAM16_UL);
                    row.Add(messageInfo.Times_QAM64_UL);
                    row.Add(messageInfo.PUSCH_PRb_Num_slot);
                    row.Add(messageInfo.PUSCH_Initial_BLER);
                    row.Add(messageInfo.Transmission_Mode);
                    row.Add(messageInfo.Rank);
                    row.Add(messageInfo.Times_QPSK_DLCode0);
                    row.Add(messageInfo.Times_QPSK_DLCode1);
                    row.Add(messageInfo.Times_QAM16_DLCode0);
                    row.Add(messageInfo.Times_QAM16_DLCode1);
                    row.Add(messageInfo.Times_QAM64_DLCode0);
                    row.Add(messageInfo.Times_QAM64_DLCode1);
                    row.Add(messageInfo.PDSCH_PRb_Num_slot);
                    row.Add(messageInfo.PDSCH_Init_BLER);
                    row.Add(messageInfo.PDSCH_Init_BLERCode0);
                    row.Add(messageInfo.PDSCH_Init_BLERCode1);
                    content.Add(row);
                }
            }

            ExcelNPOIManager.ExportToExcel(content);
        }

        private void miShowAllRTPNode_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gridViewRTP.RowCount; i++)
            {
                gridViewRTP.ExpandMasterRow(i);
            }
        }

        private void miHideAllRTPNode_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gridViewRTP.RowCount; i++)
            {
                gridViewRTP.CollapseMasterRow(i);
            }
        }
    }
}

