﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.Util
{
    public class ConfigDataInfo
    {

    }

    public abstract class ConfigHelper<U>
        where U : ConfigDataInfo, new()
    {
        public abstract string ConfigPath { get; }
        public abstract string LogPath { get; }
        public abstract string LogName { get; }
        public string ErrMsg { get; protected set; }

        public virtual U LoadConfig()
        {
            ErrMsg = "";
            U configInfo = new U();

            try
            {
                if (!File.Exists(ConfigPath))
                {
                    SaveConfig(configInfo);
                    //DoAfterFstSave(configInfo);
                    //return configInfo;
                }

                var xcfg = new XmlConfigFile(ConfigPath);
                if (xcfg.Load())
                {
                    loadConfig(xcfg, configInfo);
                }
                return configInfo;
            }
            catch
            {
                return new U();
            }
        }

        protected virtual void loadConfig(XmlConfigFile xcfg, U configInfo)
        {
            throw (new Exception("加载配置未实现..."));
        }

        public abstract void SaveConfig(U configInfo);

        public virtual void DoAfterFstSave(U configInfo)
        { 
        
        }

        #region 获取有效数据方法
        protected T getValidData<T>(XmlConfigFile xcfg, XmlElement config, string name, T defaultData)
        {
            object obj = xcfg.GetItemValue(config, name);
            if (obj != null)
            {
                return (T)obj;
            }
            return defaultData;
        }

        protected string getValidPath(XmlConfigFile xcfg, XmlElement config, string name)
        {
            object obj = xcfg.GetItemValue(config, name);
            if (obj != null)
            {
                string path = obj.ToString();
                if (File.Exists(path))
                {
                    return path;
                }
            }
            return "";
        }

        protected string getValidDirectory(XmlConfigFile xcfg, XmlElement config, string name)
        {
            object obj = xcfg.GetItemValue(config, name);
            if (obj != null)
            {
                string path = obj.ToString();
                if (Directory.Exists(path))
                {
                    return path;
                }
            }
            return "";
        }
        #endregion

        #region Log
        public bool EnableLog { get; set; } = true;
        protected string curLogPath = "";
        protected string curDate = "";
        public void WriteLog(string info, string type = "Info")
        {
            if (!EnableLog)
            {
                return;
            }

            string path = getLogPath();
            if (!File.Exists(path))
            {
                File.Create(path).Close();
            }
            using (StreamWriter sw = File.AppendText(path))
            {
                sw.Write($"[{DateTime.Now}][{type}][{info}]\r\n");
                sw.Flush();
                sw.Close();
            }
        }

        public void WriteLogWithWaitBox(string info, string type = "Info")
        {
            WriteLog(info, type);
            WaitBox.Text = info;
        }

        public void WriteLogWithMsgBox(Exception ex, string info = "", string type = "Error")
        {
            if (ex != null)
            {
                info += ex.Message + ex.Source + ex.StackTrace;
            }

            WriteLogWithMsgBox(info, type);
        }

        public void WriteLogWithMsgBox(string info, string type = "Error")
        {
            WriteLog(info, type);
            MessageBox.Show(info);
        }

        protected string getLogPath()
        {
            string strDate = DateTime.Now.ToString("yyyyMMdd");
            if (curDate == strDate)
            {
                return curLogPath;
            }
            curDate = strDate;
            StringBuilder sb = new StringBuilder(Application.StartupPath);
            sb.Append(LogPath);
            string directory = sb.ToString();
            sb.Append(strDate);
            sb.Append(LogName);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            curLogPath = sb.ToString();
            return curLogPath.ToString();
        }
        #endregion
    }
}
