﻿using DevExpress.XtraCharts;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRBlockCallAnaForm : MinCloseForm
    {
        //NRBlockCallAnaInfo clickedCallInfo = new NRBlockCallAnaInfo();
        public NRBlockCallAnaForm()
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        private List<NRBlockCallInfo> calls = null;
        internal void FillData(List<NRBlockCallInfo> calls, NRBlockCallAnaCondtion blockCallCond)
        {
            this.calls = calls;
            colSinr.Caption = string.Format("掉话前{0}秒平均SINR", blockCallCond.PoorSinrSec);
            colRsrp.Caption = string.Format("掉话前{0}秒平均RSRP", blockCallCond.WeakRsrpSec);
            colLteSinr.Caption = string.Format("掉话前{0}秒平均Lte SINR", blockCallCond.PoorSinrSec);
            colLteRsrp.Caption = string.Format("掉话前{0}秒平均Lte RSRP", blockCallCond.WeakRsrpSec);
            colHoNum.Caption = string.Format("掉话前{0}秒切换次数", blockCallCond.HoSec);

            makeSummary();
         
            gridControl.DataSource = calls;
            gridControl.RefreshDataSource();
        }

        private void makeSummary()
        {
            Dictionary<string, int> causeDic = new Dictionary<string, int>();
            foreach (string name in Enum.GetNames(typeof(ENRBlockCallCause)))
            {
                causeDic[name] = 0;
            }
            int blockNum = 0;
            foreach (NRBlockCallInfo blockCall in calls)
            {
                foreach (NRBlockCallAnaInfo call in blockCall.MoMtCalls)
                {
                    if (call.IsBlockCall)
                    {
                        blockNum++;
                        causeDic[call.BlockCause.ToString()]++;
                    }
                }
            }
            DataTable tb = new DataTable();
            tb.Columns.Add("原因", typeof(string));
            tb.Columns.Add("掉话个数", typeof(int));
            tb.Columns.Add("占比(%)", typeof(double));

            Series mainSer = chartMain.Series[0];
            mainSer.Points.Clear();
            foreach (KeyValuePair<string, int> pair in causeDic)
            {
                DataRow row = tb.NewRow();
                row["原因"] = pair.Key;
                row["掉话个数"] = pair.Value;
                double per = Math.Round(100.0 * pair.Value / blockNum, 2);
                row["占比(%)"] = per;
                tb.Rows.Add(row);
                SeriesPoint pnt = new SeriesPoint(pair.Key, per);
                mainSer.Points.Add(pnt);
            }
            DataRow srow = tb.NewRow();
            srow["原因"] = "汇总";
            srow["掉话个数"] = blockNum;
            srow["占比(%)"] = 100;
            tb.Rows.Add(srow);
            gridSummary.DataSource = tb;
            viewSummary.PopulateColumns();
            viewSummary.BestFitColumns();
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            object info = gridView.GetRow(gridView.FocusedRowHandle);
            if (info == null)
            {
                return;
            }
            if (info is NRBlockCallInfo)
            {
                MainModel.ClearDTData();
                NRBlockCallInfo dc = info as NRBlockCallInfo;
                foreach (NRBlockCallAnaInfo call in dc.MoMtCalls)
                {
                    addDTData(call);
                }
                MainModel.IsFileReplayByCompareMode = true;
                MainModel.FireDTDataChanged(this);
                MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
            }
            else if (info is NRBlockCallAnaInfo)
            {
                MainModel.ClearDTData();
                NRBlockCallAnaInfo call = info as NRBlockCallAnaInfo;
                addDTData(call);
                MainModel.IsFileReplayByCompareMode = false;
                MainModel.FireDTDataChanged(this);
                MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
            }
        }

        private void addDTData(NRBlockCallAnaInfo call)
        {
            foreach (TestPoint tp in call.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (Event evt in call.Events)
            {
                MainModel.DTDataManager.Add(evt);
            }
            foreach (Model.Message msg in call.Messages)
            {
                MainModel.DTDataManager.Add(msg);
            }
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            rows.Add(row);
            row.AddCellValue("序号");
            row.AddCellValue("文件名");
            row.AddCellValue("主叫/被叫");
            row.AddCellValue("是否未接通");
            row.AddCellValue("未接通原因");
            row.AddCellValue("未接通经度");
            row.AddCellValue("未接通纬度");
            row.AddCellValue("未接通时间");
            row.AddCellValue("异常信令");
            row.AddCellValue(colRsrp.Caption);
            row.AddCellValue(colSinr.Caption);
            row.AddCellValue(colLteRsrp.Caption);
            row.AddCellValue(colLteSinr.Caption);
            row.AddCellValue("高重叠覆盖占比(%)");
            row.AddCellValue(colHoNum.Caption);
            row.AddCellValue("是否过滤");

            foreach (NRBlockCallInfo dc in calls)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(dc.SN);
                foreach (NRBlockCallAnaInfo call in dc.MoMtCalls)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(call.FileName);
                    subRow.AddCellValue(call.MoMtDesc);
                    subRow.AddCellValue(call.IsBlockCall);
                    subRow.AddCellValue(call.BlockCause);
                    if (call.IsBlockCall)
                    {
                        subRow.AddCellValue(call.BlockEvt.Longitude);
                        subRow.AddCellValue(call.BlockEvt.Latitude);
                    }
                    else
                    {
                        subRow.AddCellValue("");
                        subRow.AddCellValue("");
                    }
                    subRow.AddCellValue(call.BlockTime);
                    subRow.AddCellValue(call.ErrorMsgName);
                    subRow.AddCellValue(call.RsrpInfo.Avg);
                    subRow.AddCellValue(call.SinrInfo.Avg);
                    subRow.AddCellValue(call.LteRsrpInfo.Avg);
                    subRow.AddCellValue(call.LteSinrInfo.Avg);
                    subRow.AddCellValue(call.MultiCvrInfo.Avg);
                    subRow.AddCellValue(call.HoNum);
                    subRow.AddCellValue(call.IsFilterDesc);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void miExportXlsSum_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(viewSummary);
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gridView.RowCount; i++)
            {
                gridView.ExpandMasterRow(i);
            }
        }

        private void toolStripMenuItem3_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gridView.RowCount; i++)
            {
                gridView.CollapseMasterRow(i);
            }
        }
    }
}
