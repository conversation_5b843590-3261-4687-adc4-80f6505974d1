﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.NOP.BatchImport;

namespace MasterCom.RAMS.NOP
{
    public class CsfbBatchImportQuery : BatchImportQueryBase
    {
        public CsfbBatchImportQuery(MainModel mainModel) : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "批量导入"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 29000, 29012, this.Name);
        }
        protected override TaskOrderQuerier CreateTaskQuerier()
        {
            return new CsfbTaskQuerier(MainModel);
        }

        protected override void ValidateImportItem(BatchImportItem item, bool isSubmit)
        {
            base.ValidateImportItem(item, isSubmit);
            if (!item.IsValid)
            {
                return;
            }

            if (item.NopTask.CurrentStatusName != "已接单")
            {
                item.IsValid = false;
                item.InvalidReason = string.Format("当前状态[{0}]的工单不支持批量修改", item.NopTask.CurrentStatusName);
                return;
            }

            item.IsValid = CheckField(item, "优化人员")
                && CheckField(item, "优化人员联系方式")
                && CheckField(item, "地市分析原因")
                && CheckField(item, "优化措施");

            if (isSubmit)
            {
                item.NopTask.AddValue("工单回单时间", DateTime.Now);
            }
            item.NopTask.CurrentStatus = isSubmit ? 3 : item.NopTask.CurrentStatus;
        }

        private bool CheckField(BatchImportItem item, string fieldName)
        {
            object value = item.FileRow[fieldName];
            if (Convert.IsDBNull(value) || value.ToString() == "")
            {
                item.IsValid = false;
                item.InvalidReason = string.Format("未填写字段[{0}]", fieldName);
            }
            else
            {
                item.IsValid = true;
                item.NopTask.AddValue(fieldName, value.ToString());
            }
            return item.IsValid;
        }
    }
}
