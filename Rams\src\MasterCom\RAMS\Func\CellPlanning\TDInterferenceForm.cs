using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class TDInterferenceForm : Form
    {
        MainModel mainModel;
        public TDInterferenceForm(MainModel mainModel, int freq)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            textBoxFreq.Text = freq.ToString();
            findInterference(freq);
        }

        private List<TDCell> coFreqTDCells;
        private Dictionary<TDCell, List<int>> tempCoFreq = new Dictionary<TDCell, List<int>>();

        private void findInterference(int freq)
        {
            tempCoFreq.Clear();

            if (MapTDCellLayer.DrawCurrent)
            {
                coFreqTDCells = CellManager.GetInstance().GetCo_FreqCurrent(freq);
            }
            else
            {
                coFreqTDCells = CellManager.GetInstance().GetCo_Freq(freq, MapTDCellLayer.CurShowTimeAt);
            }
            tempCoFreq = CellManager.GetInstance().GetDicCo_Freq(coFreqTDCells);

            listBoxTDCell.DataSource = coFreqTDCells;
            listBoxTDCell.DisplayMember = "Name";
        }

        private void listBoxTDCell_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxTDCell.SelectedItem != null)
            {
                TDCell tdCell = listBoxTDCell.SelectedItem as TDCell;
                textBoxID.Text = tdCell.ID.ToString();
                textBoxName.Text = tdCell.Name.ToString();
                textBoxCPI.Text = tdCell.CPI.ToString();
                textBoxFreq.Text = tdCell.FREQ.ToString();
            }
        }

        private void buttonInfo_Click(object sender, EventArgs e)
        {
            new TDCellInfoForm(mainModel, (TDCell)listBoxTDCell.SelectedItem).Show(Owner);
        }

        private void buttonLocation_Click(object sender, EventArgs e)
        {
            mainModel.SelectedTDCell = (TDCell)listBoxTDCell.SelectedItem;
            mainModel.FireSelectedCellChanged(this);
        }
    }
}