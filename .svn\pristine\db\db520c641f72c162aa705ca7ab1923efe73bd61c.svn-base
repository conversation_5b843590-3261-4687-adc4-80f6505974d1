﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Runtime.Serialization;
using MasterCom.RAMS.Model;
using System.Drawing;
using MasterCom.MControls;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable()]
    public class MapFormBlackBlock_PointLayer : CustomDrawLayer
    {
        static MapFormBlackBlock_PointLayer()
        {
        }
        public MapFormBlackBlock_PointLayer(MapOperation Map, string alias)
            : base(Map, alias)
        {
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            if (MainModel.BlackBlockPointsDic.Count > 0)
            {
                foreach (BlackBlock_Point item in MainModel.BlackBlockPointsDic.Values)
                {
                    draw(item, dRect, graphics);
                }
            }
        }

        private void draw(BlackBlock_Point bbPoint, DbRect dRect, Graphics graphics)
        {
            if (bbPoint.WithIn(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                Color color = Color.Red;
                if (color != Color.Empty)
                {
                    Brush brush = new SolidBrush(color);
                    DbPoint ltPoint = new DbPoint(bbPoint.Longitude - 0.002, bbPoint.Latitude + 0.0018);
                    DbPoint brPoint = new DbPoint(bbPoint.Longitude + 0.002, bbPoint.Latitude - 0.0018);
                    PointF pointLT;
                    this.Map.ToDisplay(ltPoint, out pointLT);
                    PointF pointBR;
                    this.Map.ToDisplay(brPoint, out pointBR);

                    graphics.FillEllipse(brush, pointLT.X, pointLT.Y, pointBR.X - pointLT.X, pointBR.Y - pointLT.Y);
                }
            }
        }
    }
}
