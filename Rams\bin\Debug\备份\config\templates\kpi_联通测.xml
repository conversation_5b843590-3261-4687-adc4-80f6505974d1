<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">联通测</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_0801}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">文件ID</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">覆盖率</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D013D}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item typeName="String" key="FileNameFilter" />
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColInfo" />
      <Item typeName="IList" key="ColWidth" />
    </Item>
  </Config>
</Configs>