﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class PKAlghirithmOption : MasterCom.RAMS.Func.BaseForm
    {
        private ValueRange vr;

        public PKAlghirithmOption()
        {
            InitializeComponent();

            comboBoxEditHost.SelectedIndexChanged += new EventHandler(comboBoxEditHost_SelectedIndexChanged);

            comboBoxEditHost.Properties.Items.Clear();
            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                comboBoxEditHost.Properties.Items.Add(car);
            }
            comboBoxEditHost.SelectedItem = ECarrier.移动;
            rangeValueDiff.RangeAll = new MasterCom.RAMS.Chris.Util.Range(-10000, true, 10000, true);
        }

        private void comboBoxEditHost_SelectedIndexChanged(object sender, EventArgs e)
        {
            comboBoxEditGuest.Properties.Items.Clear();
            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                if(car != (ECarrier)comboBoxEditHost.SelectedItem)
                    comboBoxEditGuest.Properties.Items.Add(car);
            }

            if (comboBoxEditGuest.Properties.Items.Count > 0)
                comboBoxEditGuest.SelectedIndex = 0;
        }

        public void SetAttribute(ValueRange vr)
        {
            this.vr = vr;
            if (vr == null) return;

            comboBoxEditHost.SelectedItem = vr.HostCarrier;
            comboBoxEditGuest.SelectedItem = vr.GuestCarrier;
            rangeValueDiff.Range = vr.Range;
        }

        public ValueRange GetAttribute()
        {
            if(vr == null)
                vr = new ValueRange();
            vr.HostCarrier = (ECarrier)comboBoxEditHost.SelectedItem;
            vr.GuestCarrier = (ECarrier)comboBoxEditGuest.SelectedItem;
            vr.Range = rangeValueDiff.Range;

            return vr;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
