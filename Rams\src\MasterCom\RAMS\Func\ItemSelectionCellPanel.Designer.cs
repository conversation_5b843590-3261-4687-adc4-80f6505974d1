﻿namespace MasterCom.RAMS.Func
{
    partial class ItemSelectionCellPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ItemSelectionCellPanel));
            this.btnOk = new System.Windows.Forms.Button();
            this.txtKeyword = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.queryBtn = new System.Windows.Forms.Button();
            this.tabCtrl = new System.Windows.Forms.TabControl();
            this.tabPageGSM = new System.Windows.Forms.TabPage();
            this.gridCtrlSelected = new DevExpress.XtraGrid.GridControl();
            this.gvSelected = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.treeListGSM = new DevExpress.XtraTreeList.TreeList();
            this.colKey = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tabPageTD = new System.Windows.Forms.TabPage();
            this.gridCtrlTD = new DevExpress.XtraGrid.GridControl();
            this.gvTD = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.treeListTD = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tabPageLTE = new System.Windows.Forms.TabPage();
            this.treeListLTE = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn2 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.gridCtrlLTE = new DevExpress.XtraGrid.GridControl();
            this.gvLTE = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnReverse = new System.Windows.Forms.Button();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tabCtrl.SuspendLayout();
            this.tabPageGSM.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlSelected)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSelected)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListGSM)).BeginInit();
            this.tabPageTD.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTD)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTD)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListTD)).BeginInit();
            this.tabPageLTE.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListLTE)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlLTE)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvLTE)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOk
            // 
            this.btnOk.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.btnOk.Location = new System.Drawing.Point(473, 371);
            this.btnOk.Name = "btnOk";
            this.btnOk.Size = new System.Drawing.Size(56, 23);
            this.btnOk.TabIndex = 0;
            this.btnOk.Text = "确定";
            this.btnOk.UseVisualStyleBackColor = true;
            this.btnOk.Click += new System.EventHandler(this.btnOk_Click);
            // 
            // txtKeyword
            // 
            this.txtKeyword.Location = new System.Drawing.Point(50, 373);
            this.txtKeyword.Name = "txtKeyword";
            this.txtKeyword.Size = new System.Drawing.Size(167, 21);
            this.txtKeyword.TabIndex = 5;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(9, 376);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(35, 12);
            this.label1.TabIndex = 6;
            this.label1.Text = "过滤:";
            // 
            // groupBox1
            // 
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(427, 397);
            this.groupBox1.TabIndex = 7;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "小区";
            // 
            // queryBtn
            // 
            this.queryBtn.Location = new System.Drawing.Point(223, 371);
            this.queryBtn.Name = "queryBtn";
            this.queryBtn.Size = new System.Drawing.Size(45, 23);
            this.queryBtn.TabIndex = 7;
            this.queryBtn.Text = "查找";
            this.queryBtn.UseVisualStyleBackColor = true;
            this.queryBtn.Click += new System.EventHandler(this.queryBut_Click);
            // 
            // tabCtrl
            // 
            this.tabCtrl.Controls.Add(this.tabPageGSM);
            this.tabCtrl.Controls.Add(this.tabPageTD);
            this.tabCtrl.Controls.Add(this.tabPageLTE);
            this.tabCtrl.Location = new System.Drawing.Point(3, 3);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedIndex = 0;
            this.tabCtrl.Size = new System.Drawing.Size(533, 362);
            this.tabCtrl.TabIndex = 12;
            // 
            // tabPageGSM
            // 
            this.tabPageGSM.Controls.Add(this.gridCtrlSelected);
            this.tabPageGSM.Controls.Add(this.treeListGSM);
            this.tabPageGSM.Location = new System.Drawing.Point(4, 22);
            this.tabPageGSM.Name = "tabPageGSM";
            this.tabPageGSM.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageGSM.Size = new System.Drawing.Size(525, 336);
            this.tabPageGSM.TabIndex = 0;
            this.tabPageGSM.Text = "GSM";
            this.tabPageGSM.UseVisualStyleBackColor = true;
            // 
            // gridCtrlSelected
            // 
            this.gridCtrlSelected.Location = new System.Drawing.Point(216, 3);
            this.gridCtrlSelected.MainView = this.gvSelected;
            this.gridCtrlSelected.Name = "gridCtrlSelected";
            this.gridCtrlSelected.ShowOnlyPredefinedDetails = true;
            this.gridCtrlSelected.Size = new System.Drawing.Size(306, 330);
            this.gridCtrlSelected.TabIndex = 22;
            this.gridCtrlSelected.UseEmbeddedNavigator = true;
            this.gridCtrlSelected.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSelected});
            // 
            // gvSelected
            // 
            this.gvSelected.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2});
            this.gvSelected.GridControl = this.gridCtrlSelected;
            this.gvSelected.Name = "gvSelected";
            this.gvSelected.OptionsBehavior.Editable = false;
            this.gvSelected.OptionsView.ShowColumnHeaders = false;
            this.gvSelected.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "名称";
            this.gridColumn2.FieldName = "Name";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            // 
            // treeListGSM
            // 
            this.treeListGSM.Appearance.Empty.BackColor = System.Drawing.Color.Ivory;
            this.treeListGSM.Appearance.Empty.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListGSM.Appearance.Empty.Options.UseBackColor = true;
            this.treeListGSM.Appearance.Empty.Options.UseForeColor = true;
            this.treeListGSM.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(253)))), ((int)(((byte)(245)))), ((int)(((byte)(230)))));
            this.treeListGSM.Appearance.EvenRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListGSM.Appearance.EvenRow.Options.UseBackColor = true;
            this.treeListGSM.Appearance.EvenRow.Options.UseForeColor = true;
            this.treeListGSM.Appearance.FooterPanel.BackColor = System.Drawing.Color.NavajoWhite;
            this.treeListGSM.Appearance.FooterPanel.BorderColor = System.Drawing.Color.NavajoWhite;
            this.treeListGSM.Appearance.FooterPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListGSM.Appearance.FooterPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListGSM.Appearance.FooterPanel.Options.UseBackColor = true;
            this.treeListGSM.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.treeListGSM.Appearance.FooterPanel.Options.UseFont = true;
            this.treeListGSM.Appearance.FooterPanel.Options.UseForeColor = true;
            this.treeListGSM.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListGSM.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListGSM.Appearance.GroupButton.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListGSM.Appearance.GroupButton.Options.UseBackColor = true;
            this.treeListGSM.Appearance.GroupButton.Options.UseBorderColor = true;
            this.treeListGSM.Appearance.GroupButton.Options.UseForeColor = true;
            this.treeListGSM.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListGSM.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListGSM.Appearance.GroupFooter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListGSM.Appearance.GroupFooter.Options.UseBackColor = true;
            this.treeListGSM.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.treeListGSM.Appearance.GroupFooter.Options.UseForeColor = true;
            this.treeListGSM.Appearance.HeaderPanel.BackColor = System.Drawing.Color.BurlyWood;
            this.treeListGSM.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.BurlyWood;
            this.treeListGSM.Appearance.HeaderPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListGSM.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListGSM.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.treeListGSM.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.treeListGSM.Appearance.HeaderPanel.Options.UseFont = true;
            this.treeListGSM.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.treeListGSM.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.White;
            this.treeListGSM.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.treeListGSM.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListGSM.Appearance.HorzLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListGSM.Appearance.HorzLine.Options.UseBackColor = true;
            this.treeListGSM.Appearance.HorzLine.Options.UseForeColor = true;
            this.treeListGSM.Appearance.OddRow.BackColor = System.Drawing.Color.Bisque;
            this.treeListGSM.Appearance.OddRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListGSM.Appearance.OddRow.Options.UseBackColor = true;
            this.treeListGSM.Appearance.OddRow.Options.UseForeColor = true;
            this.treeListGSM.Appearance.Preview.BackColor = System.Drawing.Color.Cornsilk;
            this.treeListGSM.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(255)))));
            this.treeListGSM.Appearance.Preview.Options.UseBackColor = true;
            this.treeListGSM.Appearance.Preview.Options.UseForeColor = true;
            this.treeListGSM.Appearance.Preview.Options.UseTextOptions = true;
            this.treeListGSM.Appearance.Preview.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.treeListGSM.Appearance.Row.BackColor = System.Drawing.Color.Ivory;
            this.treeListGSM.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(25)))), ((int)(((byte)(25)))), ((int)(((byte)(112)))));
            this.treeListGSM.Appearance.Row.Options.UseBackColor = true;
            this.treeListGSM.Appearance.Row.Options.UseForeColor = true;
            this.treeListGSM.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(138)))));
            this.treeListGSM.Appearance.SelectedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListGSM.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeListGSM.Appearance.SelectedRow.Options.UseForeColor = true;
            this.treeListGSM.Appearance.TreeLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListGSM.Appearance.TreeLine.Options.UseForeColor = true;
            this.treeListGSM.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListGSM.Appearance.VertLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListGSM.Appearance.VertLine.Options.UseBackColor = true;
            this.treeListGSM.Appearance.VertLine.Options.UseForeColor = true;
            this.treeListGSM.Appearance.VertLine.Options.UseTextOptions = true;
            this.treeListGSM.Appearance.VertLine.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.treeListGSM.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("treeListGSM.BackgroundImage")));
            this.treeListGSM.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colKey});
            this.treeListGSM.HorzScrollVisibility = DevExpress.XtraTreeList.ScrollVisibility.Always;
            this.treeListGSM.KeyFieldName = "";
            this.treeListGSM.Location = new System.Drawing.Point(3, 3);
            this.treeListGSM.Name = "treeListGSM";
            this.treeListGSM.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListGSM.OptionsBehavior.AutoChangeParent = false;
            this.treeListGSM.OptionsBehavior.AutoNodeHeight = false;
            this.treeListGSM.OptionsBehavior.AutoSelectAllInEditor = false;
            this.treeListGSM.OptionsBehavior.CloseEditorOnLostFocus = false;
            this.treeListGSM.OptionsBehavior.Editable = false;
            this.treeListGSM.OptionsBehavior.KeepSelectedOnClick = false;
            this.treeListGSM.OptionsBehavior.ResizeNodes = false;
            this.treeListGSM.OptionsBehavior.SmartMouseHover = false;
            this.treeListGSM.OptionsMenu.EnableFooterMenu = false;
            this.treeListGSM.OptionsPrint.PrintHorzLines = false;
            this.treeListGSM.OptionsPrint.PrintVertLines = false;
            this.treeListGSM.OptionsPrint.UsePrintStyles = true;
            this.treeListGSM.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeListGSM.OptionsView.AutoWidth = false;
            this.treeListGSM.OptionsView.ShowCheckBoxes = true;
            this.treeListGSM.OptionsView.ShowColumns = false;
            this.treeListGSM.OptionsView.ShowFocusedFrame = false;
            this.treeListGSM.OptionsView.ShowHorzLines = false;
            this.treeListGSM.OptionsView.ShowIndicator = false;
            this.treeListGSM.OptionsView.ShowVertLines = false;
            this.treeListGSM.Size = new System.Drawing.Size(207, 330);
            this.treeListGSM.TabIndex = 20;
            // 
            // colKey
            // 
            this.colKey.Caption = "Registry Keys";
            this.colKey.FieldName = "Registry Keys";
            this.colKey.MinWidth = 36;
            this.colKey.Name = "colKey";
            this.colKey.SummaryFooterStrFormat = "Count keys = {0}";
            this.colKey.Visible = true;
            this.colKey.VisibleIndex = 0;
            this.colKey.Width = 200;
            // 
            // tabPageTD
            // 
            this.tabPageTD.Controls.Add(this.gridCtrlTD);
            this.tabPageTD.Controls.Add(this.treeListTD);
            this.tabPageTD.Location = new System.Drawing.Point(4, 22);
            this.tabPageTD.Name = "tabPageTD";
            this.tabPageTD.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageTD.Size = new System.Drawing.Size(525, 336);
            this.tabPageTD.TabIndex = 1;
            this.tabPageTD.Text = "TD-SCDMA";
            this.tabPageTD.UseVisualStyleBackColor = true;
            // 
            // gridCtrlTD
            // 
            this.gridCtrlTD.Location = new System.Drawing.Point(216, 3);
            this.gridCtrlTD.MainView = this.gvTD;
            this.gridCtrlTD.Name = "gridCtrlTD";
            this.gridCtrlTD.ShowOnlyPredefinedDetails = true;
            this.gridCtrlTD.Size = new System.Drawing.Size(306, 330);
            this.gridCtrlTD.TabIndex = 28;
            this.gridCtrlTD.UseEmbeddedNavigator = true;
            this.gridCtrlTD.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvTD});
            // 
            // gvTD
            // 
            this.gvTD.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1});
            this.gvTD.GridControl = this.gridCtrlTD;
            this.gvTD.Name = "gvTD";
            this.gvTD.OptionsBehavior.Editable = false;
            this.gvTD.OptionsView.ShowColumnHeaders = false;
            this.gvTD.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "名称";
            this.gridColumn1.FieldName = "Name";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // treeListTD
            // 
            this.treeListTD.Appearance.Empty.BackColor = System.Drawing.Color.Ivory;
            this.treeListTD.Appearance.Empty.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListTD.Appearance.Empty.Options.UseBackColor = true;
            this.treeListTD.Appearance.Empty.Options.UseForeColor = true;
            this.treeListTD.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(253)))), ((int)(((byte)(245)))), ((int)(((byte)(230)))));
            this.treeListTD.Appearance.EvenRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListTD.Appearance.EvenRow.Options.UseBackColor = true;
            this.treeListTD.Appearance.EvenRow.Options.UseForeColor = true;
            this.treeListTD.Appearance.FooterPanel.BackColor = System.Drawing.Color.NavajoWhite;
            this.treeListTD.Appearance.FooterPanel.BorderColor = System.Drawing.Color.NavajoWhite;
            this.treeListTD.Appearance.FooterPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListTD.Appearance.FooterPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListTD.Appearance.FooterPanel.Options.UseBackColor = true;
            this.treeListTD.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.treeListTD.Appearance.FooterPanel.Options.UseFont = true;
            this.treeListTD.Appearance.FooterPanel.Options.UseForeColor = true;
            this.treeListTD.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListTD.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListTD.Appearance.GroupButton.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListTD.Appearance.GroupButton.Options.UseBackColor = true;
            this.treeListTD.Appearance.GroupButton.Options.UseBorderColor = true;
            this.treeListTD.Appearance.GroupButton.Options.UseForeColor = true;
            this.treeListTD.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListTD.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListTD.Appearance.GroupFooter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListTD.Appearance.GroupFooter.Options.UseBackColor = true;
            this.treeListTD.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.treeListTD.Appearance.GroupFooter.Options.UseForeColor = true;
            this.treeListTD.Appearance.HeaderPanel.BackColor = System.Drawing.Color.BurlyWood;
            this.treeListTD.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.BurlyWood;
            this.treeListTD.Appearance.HeaderPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListTD.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListTD.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.treeListTD.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.treeListTD.Appearance.HeaderPanel.Options.UseFont = true;
            this.treeListTD.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.treeListTD.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.White;
            this.treeListTD.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.treeListTD.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListTD.Appearance.HorzLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListTD.Appearance.HorzLine.Options.UseBackColor = true;
            this.treeListTD.Appearance.HorzLine.Options.UseForeColor = true;
            this.treeListTD.Appearance.OddRow.BackColor = System.Drawing.Color.Bisque;
            this.treeListTD.Appearance.OddRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListTD.Appearance.OddRow.Options.UseBackColor = true;
            this.treeListTD.Appearance.OddRow.Options.UseForeColor = true;
            this.treeListTD.Appearance.Preview.BackColor = System.Drawing.Color.Cornsilk;
            this.treeListTD.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(255)))));
            this.treeListTD.Appearance.Preview.Options.UseBackColor = true;
            this.treeListTD.Appearance.Preview.Options.UseForeColor = true;
            this.treeListTD.Appearance.Preview.Options.UseTextOptions = true;
            this.treeListTD.Appearance.Preview.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.treeListTD.Appearance.Row.BackColor = System.Drawing.Color.Ivory;
            this.treeListTD.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(25)))), ((int)(((byte)(25)))), ((int)(((byte)(112)))));
            this.treeListTD.Appearance.Row.Options.UseBackColor = true;
            this.treeListTD.Appearance.Row.Options.UseForeColor = true;
            this.treeListTD.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(138)))));
            this.treeListTD.Appearance.SelectedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListTD.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeListTD.Appearance.SelectedRow.Options.UseForeColor = true;
            this.treeListTD.Appearance.TreeLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListTD.Appearance.TreeLine.Options.UseForeColor = true;
            this.treeListTD.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListTD.Appearance.VertLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListTD.Appearance.VertLine.Options.UseBackColor = true;
            this.treeListTD.Appearance.VertLine.Options.UseForeColor = true;
            this.treeListTD.Appearance.VertLine.Options.UseTextOptions = true;
            this.treeListTD.Appearance.VertLine.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.treeListTD.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("treeListTD.BackgroundImage")));
            this.treeListTD.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn1});
            this.treeListTD.HorzScrollVisibility = DevExpress.XtraTreeList.ScrollVisibility.Always;
            this.treeListTD.KeyFieldName = "";
            this.treeListTD.Location = new System.Drawing.Point(3, 3);
            this.treeListTD.Name = "treeListTD";
            this.treeListTD.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListTD.OptionsBehavior.AutoChangeParent = false;
            this.treeListTD.OptionsBehavior.AutoNodeHeight = false;
            this.treeListTD.OptionsBehavior.AutoSelectAllInEditor = false;
            this.treeListTD.OptionsBehavior.CloseEditorOnLostFocus = false;
            this.treeListTD.OptionsBehavior.Editable = false;
            this.treeListTD.OptionsBehavior.KeepSelectedOnClick = false;
            this.treeListTD.OptionsBehavior.ResizeNodes = false;
            this.treeListTD.OptionsBehavior.SmartMouseHover = false;
            this.treeListTD.OptionsMenu.EnableFooterMenu = false;
            this.treeListTD.OptionsPrint.PrintHorzLines = false;
            this.treeListTD.OptionsPrint.PrintVertLines = false;
            this.treeListTD.OptionsPrint.UsePrintStyles = true;
            this.treeListTD.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeListTD.OptionsView.AutoWidth = false;
            this.treeListTD.OptionsView.ShowCheckBoxes = true;
            this.treeListTD.OptionsView.ShowColumns = false;
            this.treeListTD.OptionsView.ShowFocusedFrame = false;
            this.treeListTD.OptionsView.ShowHorzLines = false;
            this.treeListTD.OptionsView.ShowIndicator = false;
            this.treeListTD.OptionsView.ShowVertLines = false;
            this.treeListTD.Size = new System.Drawing.Size(207, 330);
            this.treeListTD.TabIndex = 27;
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.Caption = "Registry Keys";
            this.treeListColumn1.FieldName = "Registry Keys";
            this.treeListColumn1.MinWidth = 36;
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.SummaryFooterStrFormat = "Count keys = {0}";
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 0;
            this.treeListColumn1.Width = 200;
            // 
            // tabPageLTE
            // 
            this.tabPageLTE.Controls.Add(this.treeListLTE);
            this.tabPageLTE.Controls.Add(this.gridCtrlLTE);
            this.tabPageLTE.Location = new System.Drawing.Point(4, 22);
            this.tabPageLTE.Name = "tabPageLTE";
            this.tabPageLTE.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageLTE.Size = new System.Drawing.Size(525, 336);
            this.tabPageLTE.TabIndex = 2;
            this.tabPageLTE.Text = "LTE";
            this.tabPageLTE.UseVisualStyleBackColor = true;
            // 
            // treeListLTE
            // 
            this.treeListLTE.Appearance.Empty.BackColor = System.Drawing.Color.Ivory;
            this.treeListLTE.Appearance.Empty.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListLTE.Appearance.Empty.Options.UseBackColor = true;
            this.treeListLTE.Appearance.Empty.Options.UseForeColor = true;
            this.treeListLTE.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(253)))), ((int)(((byte)(245)))), ((int)(((byte)(230)))));
            this.treeListLTE.Appearance.EvenRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListLTE.Appearance.EvenRow.Options.UseBackColor = true;
            this.treeListLTE.Appearance.EvenRow.Options.UseForeColor = true;
            this.treeListLTE.Appearance.FooterPanel.BackColor = System.Drawing.Color.NavajoWhite;
            this.treeListLTE.Appearance.FooterPanel.BorderColor = System.Drawing.Color.NavajoWhite;
            this.treeListLTE.Appearance.FooterPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListLTE.Appearance.FooterPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListLTE.Appearance.FooterPanel.Options.UseBackColor = true;
            this.treeListLTE.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.treeListLTE.Appearance.FooterPanel.Options.UseFont = true;
            this.treeListLTE.Appearance.FooterPanel.Options.UseForeColor = true;
            this.treeListLTE.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListLTE.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListLTE.Appearance.GroupButton.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListLTE.Appearance.GroupButton.Options.UseBackColor = true;
            this.treeListLTE.Appearance.GroupButton.Options.UseBorderColor = true;
            this.treeListLTE.Appearance.GroupButton.Options.UseForeColor = true;
            this.treeListLTE.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListLTE.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListLTE.Appearance.GroupFooter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListLTE.Appearance.GroupFooter.Options.UseBackColor = true;
            this.treeListLTE.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.treeListLTE.Appearance.GroupFooter.Options.UseForeColor = true;
            this.treeListLTE.Appearance.HeaderPanel.BackColor = System.Drawing.Color.BurlyWood;
            this.treeListLTE.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.BurlyWood;
            this.treeListLTE.Appearance.HeaderPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListLTE.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListLTE.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.treeListLTE.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.treeListLTE.Appearance.HeaderPanel.Options.UseFont = true;
            this.treeListLTE.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.treeListLTE.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.White;
            this.treeListLTE.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.treeListLTE.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListLTE.Appearance.HorzLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListLTE.Appearance.HorzLine.Options.UseBackColor = true;
            this.treeListLTE.Appearance.HorzLine.Options.UseForeColor = true;
            this.treeListLTE.Appearance.OddRow.BackColor = System.Drawing.Color.Bisque;
            this.treeListLTE.Appearance.OddRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListLTE.Appearance.OddRow.Options.UseBackColor = true;
            this.treeListLTE.Appearance.OddRow.Options.UseForeColor = true;
            this.treeListLTE.Appearance.Preview.BackColor = System.Drawing.Color.Cornsilk;
            this.treeListLTE.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(255)))));
            this.treeListLTE.Appearance.Preview.Options.UseBackColor = true;
            this.treeListLTE.Appearance.Preview.Options.UseForeColor = true;
            this.treeListLTE.Appearance.Preview.Options.UseTextOptions = true;
            this.treeListLTE.Appearance.Preview.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.treeListLTE.Appearance.Row.BackColor = System.Drawing.Color.Ivory;
            this.treeListLTE.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(25)))), ((int)(((byte)(25)))), ((int)(((byte)(112)))));
            this.treeListLTE.Appearance.Row.Options.UseBackColor = true;
            this.treeListLTE.Appearance.Row.Options.UseForeColor = true;
            this.treeListLTE.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(138)))));
            this.treeListLTE.Appearance.SelectedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListLTE.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeListLTE.Appearance.SelectedRow.Options.UseForeColor = true;
            this.treeListLTE.Appearance.TreeLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListLTE.Appearance.TreeLine.Options.UseForeColor = true;
            this.treeListLTE.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListLTE.Appearance.VertLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListLTE.Appearance.VertLine.Options.UseBackColor = true;
            this.treeListLTE.Appearance.VertLine.Options.UseForeColor = true;
            this.treeListLTE.Appearance.VertLine.Options.UseTextOptions = true;
            this.treeListLTE.Appearance.VertLine.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.treeListLTE.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("treeListLTE.BackgroundImage")));
            this.treeListLTE.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn2});
            this.treeListLTE.HorzScrollVisibility = DevExpress.XtraTreeList.ScrollVisibility.Always;
            this.treeListLTE.KeyFieldName = "";
            this.treeListLTE.Location = new System.Drawing.Point(3, 3);
            this.treeListLTE.Name = "treeListLTE";
            this.treeListLTE.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListLTE.OptionsBehavior.AutoChangeParent = false;
            this.treeListLTE.OptionsBehavior.AutoNodeHeight = false;
            this.treeListLTE.OptionsBehavior.AutoSelectAllInEditor = false;
            this.treeListLTE.OptionsBehavior.CloseEditorOnLostFocus = false;
            this.treeListLTE.OptionsBehavior.Editable = false;
            this.treeListLTE.OptionsBehavior.KeepSelectedOnClick = false;
            this.treeListLTE.OptionsBehavior.ResizeNodes = false;
            this.treeListLTE.OptionsBehavior.SmartMouseHover = false;
            this.treeListLTE.OptionsMenu.EnableFooterMenu = false;
            this.treeListLTE.OptionsPrint.PrintHorzLines = false;
            this.treeListLTE.OptionsPrint.PrintVertLines = false;
            this.treeListLTE.OptionsPrint.UsePrintStyles = true;
            this.treeListLTE.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeListLTE.OptionsView.AutoWidth = false;
            this.treeListLTE.OptionsView.ShowCheckBoxes = true;
            this.treeListLTE.OptionsView.ShowColumns = false;
            this.treeListLTE.OptionsView.ShowFocusedFrame = false;
            this.treeListLTE.OptionsView.ShowHorzLines = false;
            this.treeListLTE.OptionsView.ShowIndicator = false;
            this.treeListLTE.OptionsView.ShowVertLines = false;
            this.treeListLTE.Size = new System.Drawing.Size(207, 330);
            this.treeListLTE.TabIndex = 30;
            // 
            // treeListColumn2
            // 
            this.treeListColumn2.Caption = "Registry Keys";
            this.treeListColumn2.FieldName = "Registry Keys";
            this.treeListColumn2.MinWidth = 36;
            this.treeListColumn2.Name = "treeListColumn2";
            this.treeListColumn2.SummaryFooterStrFormat = "Count keys = {0}";
            this.treeListColumn2.Visible = true;
            this.treeListColumn2.VisibleIndex = 0;
            this.treeListColumn2.Width = 200;
            // 
            // gridCtrlLTE
            // 
            this.gridCtrlLTE.Location = new System.Drawing.Point(216, 3);
            this.gridCtrlLTE.MainView = this.gvLTE;
            this.gridCtrlLTE.Name = "gridCtrlLTE";
            this.gridCtrlLTE.ShowOnlyPredefinedDetails = true;
            this.gridCtrlLTE.Size = new System.Drawing.Size(306, 330);
            this.gridCtrlLTE.TabIndex = 29;
            this.gridCtrlLTE.UseEmbeddedNavigator = true;
            this.gridCtrlLTE.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvLTE});
            // 
            // gvLTE
            // 
            this.gvLTE.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3});
            this.gvLTE.GridControl = this.gridCtrlLTE;
            this.gvLTE.Name = "gvLTE";
            this.gvLTE.OptionsBehavior.Editable = false;
            this.gvLTE.OptionsView.ShowColumnHeaders = false;
            this.gvLTE.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "名称";
            this.gridColumn3.FieldName = "Name";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 0;
            // 
            // btnReverse
            // 
            this.btnReverse.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.btnReverse.Location = new System.Drawing.Point(402, 371);
            this.btnReverse.Name = "btnReverse";
            this.btnReverse.Size = new System.Drawing.Size(56, 23);
            this.btnReverse.TabIndex = 0;
            this.btnReverse.Text = "反选";
            this.btnReverse.UseVisualStyleBackColor = true;
            this.btnReverse.Click += new System.EventHandler(this.btnReverse_Click);
            // 
            // ctxMenu
            // 
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(61, 4);
            // 
            // ItemSelectionCellPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.Window;
            this.Controls.Add(this.tabCtrl);
            this.Controls.Add(this.btnReverse);
            this.Controls.Add(this.queryBtn);
            this.Controls.Add(this.btnOk);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.txtKeyword);
            this.Name = "ItemSelectionCellPanel";
            this.Size = new System.Drawing.Size(536, 406);
            this.tabCtrl.ResumeLayout(false);
            this.tabPageGSM.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlSelected)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSelected)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListGSM)).EndInit();
            this.tabPageTD.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTD)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTD)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListTD)).EndInit();
            this.tabPageLTE.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListLTE)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlLTE)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvLTE)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }
        #endregion

        private System.Windows.Forms.Button btnOk;
        private System.Windows.Forms.TextBox txtKeyword;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Button queryBtn;
        private System.Windows.Forms.TabControl tabCtrl;
        private System.Windows.Forms.TabPage tabPageGSM;
        private System.Windows.Forms.TabPage tabPageTD;
        private System.Windows.Forms.TabPage tabPageLTE;
        private DevExpress.XtraTreeList.TreeList treeListGSM;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colKey;
        private System.Windows.Forms.Button btnReverse;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private DevExpress.XtraGrid.GridControl gridCtrlSelected;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSelected;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.GridControl gridCtrlTD;
        private DevExpress.XtraGrid.Views.Grid.GridView gvTD;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraTreeList.TreeList treeListTD;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        private DevExpress.XtraTreeList.TreeList treeListLTE;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn2;
        private DevExpress.XtraGrid.GridControl gridCtrlLTE;
        private DevExpress.XtraGrid.Views.Grid.GridView gvLTE;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
    }
}
