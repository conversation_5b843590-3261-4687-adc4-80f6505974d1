﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLowSpeedSettingDlg_W
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.chkSynthesis = new System.Windows.Forms.CheckBox();
            this.chkLowSpeedW = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.numLowPer = new DevExpress.XtraEditors.SpinEdit();
            this.num2TpDis = new DevExpress.XtraEditors.SpinEdit();
            this.label29 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.label35 = new System.Windows.Forms.Label();
            this.numULDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numULMin = new DevExpress.XtraEditors.SpinEdit();
            this.label36 = new System.Windows.Forms.Label();
            this.numULMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkUL = new System.Windows.Forms.CheckBox();
            this.label30 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numDLDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numDLMin = new DevExpress.XtraEditors.SpinEdit();
            this.label12 = new System.Windows.Forms.Label();
            this.numDLMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkDL = new System.Windows.Forms.CheckBox();
            this.groupBox1.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLowPer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.num2TpDis.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numULDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numULMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numULMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDLDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDLMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDLMax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.Location = new System.Drawing.Point(355, 211);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 4;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.Location = new System.Drawing.Point(450, 211);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 5;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // chkSynthesis
            // 
            this.chkSynthesis.AutoSize = true;
            this.chkSynthesis.Checked = true;
            this.chkSynthesis.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkSynthesis.Location = new System.Drawing.Point(44, 20);
            this.chkSynthesis.Name = "chkSynthesis";
            this.chkSynthesis.Size = new System.Drawing.Size(108, 16);
            this.chkSynthesis.TabIndex = 6;
            this.chkSynthesis.Text = "综合低速率路段";
            this.chkSynthesis.UseVisualStyleBackColor = true;
            // 
            // chkLowSpeedW
            // 
            this.chkLowSpeedW.AutoSize = true;
            this.chkLowSpeedW.Location = new System.Drawing.Point(283, 20);
            this.chkLowSpeedW.Name = "chkLowSpeedW";
            this.chkLowSpeedW.Size = new System.Drawing.Size(90, 16);
            this.chkLowSpeedW.TabIndex = 7;
            this.chkLowSpeedW.Text = "W低速率路段";
            this.chkLowSpeedW.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.chkLowSpeedW);
            this.groupBox1.Controls.Add(this.chkSynthesis);
            this.groupBox1.Location = new System.Drawing.Point(22, 153);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(501, 46);
            this.groupBox1.TabIndex = 8;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "网络";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label7);
            this.groupBox4.Controls.Add(this.label8);
            this.groupBox4.Controls.Add(this.label16);
            this.groupBox4.Controls.Add(this.label15);
            this.groupBox4.Controls.Add(this.numLowPer);
            this.groupBox4.Controls.Add(this.num2TpDis);
            this.groupBox4.Location = new System.Drawing.Point(22, 84);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(501, 63);
            this.groupBox4.TabIndex = 69;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "持续性";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(222, 28);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 6;
            this.label7.Text = "米";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(49, 28);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(101, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "两采样点间距离≤";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(465, 28);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(11, 12);
            this.label16.TabIndex = 0;
            this.label16.Text = "%";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(304, 28);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(89, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "低速率点占比≥";
            // 
            // numLowPer
            // 
            this.numLowPer.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            0});
            this.numLowPer.Location = new System.Drawing.Point(399, 23);
            this.numLowPer.Name = "numLowPer";
            this.numLowPer.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLowPer.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numLowPer.Properties.IsFloatValue = false;
            this.numLowPer.Properties.Mask.EditMask = "N00";
            this.numLowPer.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numLowPer.Size = new System.Drawing.Size(60, 21);
            this.numLowPer.TabIndex = 2;
            // 
            // num2TpDis
            // 
            this.num2TpDis.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.num2TpDis.Location = new System.Drawing.Point(156, 23);
            this.num2TpDis.Name = "num2TpDis";
            this.num2TpDis.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.num2TpDis.Properties.IsFloatValue = false;
            this.num2TpDis.Properties.Mask.EditMask = "N00";
            this.num2TpDis.Size = new System.Drawing.Size(60, 21);
            this.num2TpDis.TabIndex = 1;
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(291, 50);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(29, 12);
            this.label29.TabIndex = 85;
            this.label29.Text = "Kbps";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(506, 49);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(17, 12);
            this.label1.TabIndex = 83;
            this.label1.Text = "米";
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(370, 49);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(65, 12);
            this.label35.TabIndex = 78;
            this.label35.Text = "持续距离≥";
            // 
            // numULDistance
            // 
            this.numULDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numULDistance.Location = new System.Drawing.Point(440, 43);
            this.numULDistance.Name = "numULDistance";
            this.numULDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numULDistance.Properties.IsFloatValue = false;
            this.numULDistance.Properties.Mask.EditMask = "N00";
            this.numULDistance.Size = new System.Drawing.Size(60, 21);
            this.numULDistance.TabIndex = 79;
            // 
            // numULMin
            // 
            this.numULMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numULMin.Location = new System.Drawing.Point(46, 44);
            this.numULMin.Name = "numULMin";
            this.numULMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numULMin.Properties.IsFloatValue = false;
            this.numULMin.Properties.Mask.EditMask = "N00";
            this.numULMin.Size = new System.Drawing.Size(60, 21);
            this.numULMin.TabIndex = 80;
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(112, 49);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(101, 12);
            this.label36.TabIndex = 84;
            this.label36.Text = "≤Upload  Rate≤";
            // 
            // numULMax
            // 
            this.numULMax.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numULMax.Location = new System.Drawing.Point(225, 44);
            this.numULMax.Name = "numULMax";
            this.numULMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numULMax.Properties.IsFloatValue = false;
            this.numULMax.Properties.Mask.EditMask = "N00";
            this.numULMax.Size = new System.Drawing.Size(60, 21);
            this.numULMax.TabIndex = 81;
            // 
            // chkUL
            // 
            this.chkUL.AutoSize = true;
            this.chkUL.Checked = true;
            this.chkUL.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkUL.Location = new System.Drawing.Point(25, 49);
            this.chkUL.Name = "chkUL";
            this.chkUL.Size = new System.Drawing.Size(15, 14);
            this.chkUL.TabIndex = 82;
            this.chkUL.UseVisualStyleBackColor = true;
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(291, 19);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(29, 12);
            this.label30.TabIndex = 77;
            this.label30.Text = "Kbps";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(506, 18);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 75;
            this.label2.Text = "米";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(370, 18);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 70;
            this.label3.Text = "持续距离≥";
            // 
            // numDLDistance
            // 
            this.numDLDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numDLDistance.Location = new System.Drawing.Point(440, 12);
            this.numDLDistance.Name = "numDLDistance";
            this.numDLDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDLDistance.Properties.IsFloatValue = false;
            this.numDLDistance.Properties.Mask.EditMask = "N00";
            this.numDLDistance.Size = new System.Drawing.Size(60, 21);
            this.numDLDistance.TabIndex = 71;
            // 
            // numDLMin
            // 
            this.numDLMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDLMin.Location = new System.Drawing.Point(46, 13);
            this.numDLMin.Name = "numDLMin";
            this.numDLMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDLMin.Properties.IsFloatValue = false;
            this.numDLMin.Properties.Mask.EditMask = "N00";
            this.numDLMin.Size = new System.Drawing.Size(60, 21);
            this.numDLMin.TabIndex = 72;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(112, 18);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(107, 12);
            this.label12.TabIndex = 76;
            this.label12.Text = "≤DownLoad Rate≤";
            // 
            // numDLMax
            // 
            this.numDLMax.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numDLMax.Location = new System.Drawing.Point(225, 13);
            this.numDLMax.Name = "numDLMax";
            this.numDLMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDLMax.Properties.IsFloatValue = false;
            this.numDLMax.Properties.Mask.EditMask = "N00";
            this.numDLMax.Size = new System.Drawing.Size(60, 21);
            this.numDLMax.TabIndex = 73;
            // 
            // chkDL
            // 
            this.chkDL.AutoSize = true;
            this.chkDL.Checked = true;
            this.chkDL.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkDL.Location = new System.Drawing.Point(25, 18);
            this.chkDL.Name = "chkDL";
            this.chkDL.Size = new System.Drawing.Size(15, 14);
            this.chkDL.TabIndex = 74;
            this.chkDL.UseVisualStyleBackColor = true;
            // 
            // ZTLowSpeedSettingDlg_W
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(550, 246);
            this.Controls.Add(this.label29);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.label35);
            this.Controls.Add(this.numULDistance);
            this.Controls.Add(this.numULMin);
            this.Controls.Add(this.label36);
            this.Controls.Add(this.numULMax);
            this.Controls.Add(this.chkUL);
            this.Controls.Add(this.label30);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numDLDistance);
            this.Controls.Add(this.numDLMin);
            this.Controls.Add(this.label12);
            this.Controls.Add(this.numDLMax);
            this.Controls.Add(this.chkDL);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.Name = "ZTLowSpeedSettingDlg_W";
            this.Text = "低速率分析设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLowPer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.num2TpDis.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numULDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numULMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numULMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDLDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDLMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDLMax.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private System.Windows.Forms.CheckBox chkSynthesis;
        private System.Windows.Forms.CheckBox chkLowSpeedW;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label15;
        private DevExpress.XtraEditors.SpinEdit numLowPer;
        private DevExpress.XtraEditors.SpinEdit num2TpDis;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label35;
        private DevExpress.XtraEditors.SpinEdit numULDistance;
        private DevExpress.XtraEditors.SpinEdit numULMin;
        private System.Windows.Forms.Label label36;
        private DevExpress.XtraEditors.SpinEdit numULMax;
        private System.Windows.Forms.CheckBox chkUL;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit numDLDistance;
        private DevExpress.XtraEditors.SpinEdit numDLMin;
        private System.Windows.Forms.Label label12;
        private DevExpress.XtraEditors.SpinEdit numDLMax;
        private System.Windows.Forms.CheckBox chkDL;
    }
}