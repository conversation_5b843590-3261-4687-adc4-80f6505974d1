﻿using System.Collections.Generic;
using MasterCom.MTGis;
using System.Drawing;
using MasterCom.RAMS.Func.CoverageCheck;
using MapWinGIS;

namespace MasterCom.RAMS
{
    public class CPGridUnitLayer : CustomDrawLayer
    {
        public CPGridUnitLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            CpMColorRangeDic = new Dictionary<string, TextColorRange>();
        }
        CompareResult compareResult = null;
        CompareResult2 compareResult2 = null;
        public void FillData(CompareResult compareResult)
        {
            this.compareResult = compareResult;
            fillLengend();
        }

        public void FillData2(CompareResult2 compareResult)
        {
            this.compareResult2 = compareResult;
            fillLengend2();
        }

        private readonly Dictionary<string, SolidBrush> brushDic = new Dictionary<string, SolidBrush>();
        private void fillLengend()
        {
            CpMColorRangeDic.Clear();
            brushDic.Clear();
            if (compareResult != null && compareResult.FilteredLevelBlocks.Count > 0)
            {
                foreach (CompareGridLevel level in compareResult.FilteredLevelBlocks)
                {
                    CpMColorRangeDic.Add(level.LevelName, level.TextColorRange);
                    brushDic.Add(level.LevelName, new SolidBrush(level.TextColorRange.color));
                }
            }
        }

        private void fillLengend2()
        {
            CpMColorRangeDic.Clear();
            brushDic.Clear();
            if (compareResult2 != null && compareResult2.FilteredLevelBlocks.Count > 0)
            {
                foreach (var level in compareResult2.FilteredLevelBlocks)
                {
                    CpMColorRangeDic.Add(level.LevelName, level.TextColorRange);
                    brushDic.Add(level.LevelName, new SolidBrush(level.TextColorRange.color));
                }
            }
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (compareResult == null)
            {
                return;
            }

            DbRect viewRect;
            Map.FromDisplay(updateRect, out viewRect);
            foreach (CompareGridLevel levelBlk in compareResult.FilteredLevelBlocks)
            {
                if (!CpMColorRangeDic[levelBlk.LevelName].Visible) continue;
                foreach (CompareGridBlock blk in levelBlk.FilteredBlocks)
                {
                    drawInRegionGrid(graphics, viewRect, levelBlk, blk);
                }
            }
        }

        private void drawInRegionGrid(Graphics graphics, DbRect viewRect, CompareGridLevel levelBlk, CompareGridBlock blk)
        {
            if (blk.GetBounds().Within(viewRect))
            {
                foreach (CompareResultGrid grid in blk.Grids)
                {
                    if (grid.Within(viewRect))
                    {
                        DbPoint ltPoint = new DbPoint(grid.LTLng, grid.LTLat);
                        PointF pointLt;
                        this.Map.ToDisplay(ltPoint, out pointLt);
                        DbPoint brPoint = new DbPoint(grid.BRLng, grid.BRLat);
                        PointF pointBr;
                        this.Map.ToDisplay(brPoint, out pointBr);
                        graphics.FillRectangle(brushDic[levelBlk.LevelName], pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
                    }
                }
            }
        }

        public Dictionary<string, TextColorRange> CpMColorRangeDic { get; set; }
    }
}
