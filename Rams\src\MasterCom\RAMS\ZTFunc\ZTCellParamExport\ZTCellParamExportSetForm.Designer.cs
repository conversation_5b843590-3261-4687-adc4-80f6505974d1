﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTCellParamExportSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.cbxDingLi_W = new System.Windows.Forms.CheckBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.cbxDingLiTd = new System.Windows.Forms.CheckBox();
            this.cbxDingLiGsm = new System.Windows.Forms.CheckBox();
            this.cbxGoogle_G = new System.Windows.Forms.CheckBox();
            this.cbxGoogle_W = new System.Windows.Forms.CheckBox();
            this.cbxNastar_G = new System.Windows.Forms.CheckBox();
            this.cbxActix_W = new System.Windows.Forms.CheckBox();
            this.cbxTEMS_G = new System.Windows.Forms.CheckBox();
            this.cbxRiXun_W = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.txSavePath = new System.Windows.Forms.TextBox();
            this.btnPath = new System.Windows.Forms.Button();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(452, 202);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(357, 202);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // cbxDingLi_W
            // 
            this.cbxDingLi_W.AutoSize = true;
            this.cbxDingLi_W.Location = new System.Drawing.Point(35, 29);
            this.cbxDingLi_W.Name = "cbxDingLi_W";
            this.cbxDingLi_W.Size = new System.Drawing.Size(90, 16);
            this.cbxDingLi_W.TabIndex = 25;
            this.cbxDingLi_W.Text = "鼎利(WCDMA)";
            this.cbxDingLi_W.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.cbxDingLiTd);
            this.groupBox3.Controls.Add(this.cbxDingLiGsm);
            this.groupBox3.Controls.Add(this.cbxGoogle_G);
            this.groupBox3.Controls.Add(this.cbxGoogle_W);
            this.groupBox3.Controls.Add(this.cbxNastar_G);
            this.groupBox3.Controls.Add(this.cbxActix_W);
            this.groupBox3.Controls.Add(this.cbxTEMS_G);
            this.groupBox3.Controls.Add(this.cbxRiXun_W);
            this.groupBox3.Controls.Add(this.cbxDingLi_W);
            this.groupBox3.Location = new System.Drawing.Point(15, 12);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(512, 125);
            this.groupBox3.TabIndex = 26;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "模板类型";
            // 
            // cbxDingLiTd
            // 
            this.cbxDingLiTd.AutoSize = true;
            this.cbxDingLiTd.Location = new System.Drawing.Point(35, 97);
            this.cbxDingLiTd.Name = "cbxDingLiTd";
            this.cbxDingLiTd.Size = new System.Drawing.Size(72, 16);
            this.cbxDingLiTd.TabIndex = 33;
            this.cbxDingLiTd.Text = "鼎利(TD)";
            this.cbxDingLiTd.UseVisualStyleBackColor = true;
            // 
            // cbxDingLiGsm
            // 
            this.cbxDingLiGsm.AutoSize = true;
            this.cbxDingLiGsm.Location = new System.Drawing.Point(414, 63);
            this.cbxDingLiGsm.Name = "cbxDingLiGsm";
            this.cbxDingLiGsm.Size = new System.Drawing.Size(78, 16);
            this.cbxDingLiGsm.TabIndex = 32;
            this.cbxDingLiGsm.Text = "鼎利(GSM)";
            this.cbxDingLiGsm.UseVisualStyleBackColor = true;
            // 
            // cbxGoogle_G
            // 
            this.cbxGoogle_G.AutoSize = true;
            this.cbxGoogle_G.Location = new System.Drawing.Point(295, 29);
            this.cbxGoogle_G.Name = "cbxGoogle_G";
            this.cbxGoogle_G.Size = new System.Drawing.Size(90, 16);
            this.cbxGoogle_G.TabIndex = 31;
            this.cbxGoogle_G.Text = "Google(GSM)";
            this.cbxGoogle_G.UseVisualStyleBackColor = true;
            // 
            // cbxGoogle_W
            // 
            this.cbxGoogle_W.AutoSize = true;
            this.cbxGoogle_W.Location = new System.Drawing.Point(156, 29);
            this.cbxGoogle_W.Name = "cbxGoogle_W";
            this.cbxGoogle_W.Size = new System.Drawing.Size(102, 16);
            this.cbxGoogle_W.TabIndex = 30;
            this.cbxGoogle_W.Text = "Google(WCDMA)";
            this.cbxGoogle_W.UseVisualStyleBackColor = true;
            // 
            // cbxNastar_G
            // 
            this.cbxNastar_G.AutoSize = true;
            this.cbxNastar_G.Location = new System.Drawing.Point(295, 63);
            this.cbxNastar_G.Name = "cbxNastar_G";
            this.cbxNastar_G.Size = new System.Drawing.Size(90, 16);
            this.cbxNastar_G.TabIndex = 29;
            this.cbxNastar_G.Text = "NASTAR(GSM)";
            this.cbxNastar_G.UseVisualStyleBackColor = true;
            // 
            // cbxActix_W
            // 
            this.cbxActix_W.AutoSize = true;
            this.cbxActix_W.Location = new System.Drawing.Point(156, 63);
            this.cbxActix_W.Name = "cbxActix_W";
            this.cbxActix_W.Size = new System.Drawing.Size(96, 16);
            this.cbxActix_W.TabIndex = 28;
            this.cbxActix_W.Text = "Actix(WCDMA)";
            this.cbxActix_W.UseVisualStyleBackColor = true;
            // 
            // cbxTEMS_G
            // 
            this.cbxTEMS_G.AutoSize = true;
            this.cbxTEMS_G.Location = new System.Drawing.Point(414, 29);
            this.cbxTEMS_G.Name = "cbxTEMS_G";
            this.cbxTEMS_G.Size = new System.Drawing.Size(78, 16);
            this.cbxTEMS_G.TabIndex = 27;
            this.cbxTEMS_G.Text = "TEMS(GSM)";
            this.cbxTEMS_G.UseVisualStyleBackColor = true;
            // 
            // cbxRiXun_W
            // 
            this.cbxRiXun_W.AutoSize = true;
            this.cbxRiXun_W.Location = new System.Drawing.Point(35, 63);
            this.cbxRiXun_W.Name = "cbxRiXun_W";
            this.cbxRiXun_W.Size = new System.Drawing.Size(90, 16);
            this.cbxRiXun_W.TabIndex = 26;
            this.cbxRiXun_W.Text = "日讯(WCDMA)";
            this.cbxRiXun_W.UseVisualStyleBackColor = true;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(18, 171);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 27;
            this.label1.Text = "导出路径：";
            // 
            // txSavePath
            // 
            this.txSavePath.Location = new System.Drawing.Point(82, 167);
            this.txSavePath.Name = "txSavePath";
            this.txSavePath.Size = new System.Drawing.Size(400, 21);
            this.txSavePath.TabIndex = 28;
            // 
            // btnPath
            // 
            this.btnPath.Location = new System.Drawing.Point(496, 167);
            this.btnPath.Name = "btnPath";
            this.btnPath.Size = new System.Drawing.Size(31, 23);
            this.btnPath.TabIndex = 29;
            this.btnPath.Text = "...";
            this.btnPath.UseVisualStyleBackColor = true;
            this.btnPath.Click += new System.EventHandler(this.btnPath_Click);
            // 
            // ZTCellParamExportSetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(553, 237);
            this.Controls.Add(this.btnPath);
            this.Controls.Add(this.txSavePath);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ZTCellParamExportSetForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "导出模板选择";
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.CheckBox cbxDingLi_W;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.CheckBox cbxTEMS_G;
        private System.Windows.Forms.CheckBox cbxRiXun_W;
        private System.Windows.Forms.CheckBox cbxGoogle_W;
        private System.Windows.Forms.CheckBox cbxNastar_G;
        private System.Windows.Forms.CheckBox cbxActix_W;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txSavePath;
        private System.Windows.Forms.Button btnPath;
        private System.Windows.Forms.CheckBox cbxGoogle_G;
        private System.Windows.Forms.CheckBox cbxDingLiTd;
        private System.Windows.Forms.CheckBox cbxDingLiGsm;
    }
}