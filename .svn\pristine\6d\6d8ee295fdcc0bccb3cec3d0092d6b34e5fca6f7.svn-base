﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellWeakCoverByCellDir_NBIOTScan : ZTDIYCellWeakCoverByCellDir_TDLTEScan
    {
        private static ZTDIYCellWeakCoverByCellDir_NBIOTScan intance = null;
        public new static ZTDIYCellWeakCoverByCellDir_NBIOTScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYCellWeakCoverByCellDir_NBIOTScan();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYCellWeakCoverByCellDir_NBIOTScan()
            : base()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.SCAN_NBIOT_TOPN);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖小区_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33005, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"NBIOT扫频");
            tmpDic.Add("themeName", (object)"LTESCAN_TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override float? getBestRxLev(TestPoint tp)
        {
            if (tp is ScanTestPoint_NBIOT)
            {
                float? rxLev = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", 0];
                if (rxLev == null || rxLev < -120 || rxLev > -10)
                {
                    return null;
                }
                return rxLev;
            }
            return null;
        }

        /// <summary>
        /// 获取区域内NBIOT小区
        /// </summary>
        /// <returns></returns>
        protected override List<LTECell> getLTECellsOfRegion()
        {
            List<LTECell> cellList = new List<LTECell>();
            int index = 1;
            List<LTECell> curCells = MainModel.CellManager.GetCurrentNBIOTCells();
            WaitBox.Text = "开始获取选择区域内小区...";
            foreach (LTECell cell in curCells)
            {
                if (cell.Type == LTEBTSType.Outdoor && condition.Geometorys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                {
                    cellList.Add(cell);
                }
                WaitBox.ProgressPercent = (int)(100.0 * index++ / curCells.Count);
            }
            return cellList;
        }
    }
}
