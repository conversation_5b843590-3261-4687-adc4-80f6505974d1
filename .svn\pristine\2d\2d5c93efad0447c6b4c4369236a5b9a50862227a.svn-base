﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func.ProblemBlock;

namespace MasterCom.RAMS.CQT
{
    public class CQTProblemSummaryAnalysLTE : QueryBase
    {
        public CQTProblemSummaryAnalysLTE(MainModel mainModel, string netType)
            : base(mainModel)
        {
            if (netType == "LTE")
            {
                if (CQTProblemSummaryAnalysGSM.cityLevel(mainModel.DistrictID) != "")
                    iareatype = 28;
                else
                    iareatype = -300;
            }
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override string Name
        {
            get { return "4G问题点统计分析"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22086, this.Name);
        }
        #region 全局变量
        List<ProblemSummaryPont4G> problemPointSummarryList;
        List<ProblemPont4G> problemPointList;
        Dictionary<string, FileInfoItem> CQTFileCountByServ;
        List<ProblemItem> result;
        readonly int iareatype = 0;
        int strcityid = 0;
        string strCityName = "";
        int newpoint = 0;
        #endregion

        protected override void query()
        {
            problemPointSummarryList = new List<ProblemSummaryPont4G>();
            problemPointList = new List<ProblemPont4G>();
            CQTFileCountByServ = new Dictionary<string, FileInfoItem>();
            result = new List<ProblemItem>();
            WaitBox.Show("准备获取CQT点...", seachProblemData);

            showData();
        }
        /// <summary>
        /// 准备查询数据的条件
        /// </summary>
        private void seachProblemData()
        {
            DateTime sDtime = condition.Periods[0].BeginTime;
            DateTime eDtime = condition.Periods[0].EndTime.AddSeconds(-1);
            StringBuilder project = new StringBuilder();
            for (int i = 0; i < condition.Projects.Count; i++)
            {
                if (i < condition.Projects.Count - 1)
                    project.Append(condition.Projects[i].ToString() + ",");
                else
                    project.Append(condition.Projects[i].ToString());
            }
            List<string> logList = getLogFileNameList(sDtime, eDtime);
            if (MainModel.User.DBID == -1)
            {
                WaitBox.ProgressPercent = 30;
                List<int> disid = getDisid();
                for (int chdistrid = 0; chdistrid < disid.Count; chdistrid++)
                {
                    CQTFileCountByServ.Clear();
                    MainModel.DistrictID = disid[chdistrid];
                    strcityid = disid[chdistrid];
                    strCityName = DistrictManager.GetInstance().getDistrictName(strcityid);
                    seachProblemPointData(strcityid, sDtime, eDtime, project.ToString(), logList);
                }
            }
            else
            {
                CQTFileCountByServ.Clear();
                strcityid = MainModel.DistrictID;
                seachProblemPointData(MainModel.DistrictID, sDtime, eDtime, project.ToString(), logList);
            }
            WaitBox.ProgressPercent = 80;
            setproblemPointSummarryList();
            WaitBox.Close();
        }

        private List<int> getDisid()
        {
            List<int> disid = new List<int>();
            //按特定顺序排列查询各是数据
            int[] zhidingID = { 1, 2, 3, 12, 6, 5, 4, 10, 13, 14, 16, 17, 21, 7, 8, 9, 11, 15, 18, 19, 20, 22, 23 };
            for (int idk = 0; idk < zhidingID.Length; idk++)
            {
                if (condition.DistrictIDs.Contains(zhidingID[idk]))
                {
                    disid.Add(zhidingID[idk]);
                }
            }

            return disid;
        }

        private void setproblemPointSummarryList()
        {
            if (problemPointSummarryList.Count != 0)
            {
                WaitBox.Text = "正在进行汇总处理...";
                ProblemSummaryPont4G cityProSummary = new ProblemSummaryPont4G();
                cityProSummary.StrCityType = "汇总";
                cityProSummary.StrCity = "汇总";
                foreach (ProblemSummaryPont4G proSummary in problemPointSummarryList)
                {
                    cityProSummary.ICallCSFBLess += proSummary.ICallCSFBLess;
                    cityProSummary.ICallSuccessLess += proSummary.ICallSuccessLess;
                    cityProSummary.ILessThenColeVoice += proSummary.ILessThenColeVoice;
                    cityProSummary.ILessDownDn += proSummary.ILessDownDn;
                    cityProSummary.IDownDrop += proSummary.IDownDrop;
                    cityProSummary.ILessThenColeData += proSummary.ILessThenColeData;
                    cityProSummary.ITestColeVioce += proSummary.ITestColeVioce;
                    cityProSummary.ITestColeData += proSummary.ITestColeData;
                    cityProSummary.ICityColePoint += proSummary.ICityColePoint;
                    cityProSummary.ITestCole += proSummary.ITestCole;
                }
                problemPointSummarryList.Add(cityProSummary);
            }
        }

        /// <summary>
        /// 获取tb_log表名
        /// </summary>
        private List<string> getLogFileNameList(DateTime tmpDate, DateTime eDtime)
        {
            List<string> logList = new List<string>();
            while (tmpDate <= eDtime)
            {
                string strLogName = string.Format("tb_log_file_{0:yyyy}_{0:MM}", tmpDate);
                if (!logList.Contains(strLogName))
                {
                    logList.Add(strLogName);
                }
                tmpDate = tmpDate.AddDays(1);
            }
            return logList;
        }

        /// <summary>
        /// 查询问题点记录的过程
        /// </summary>
        private void seachProblemPointData(int cityId, DateTime sDtime, DateTime eDtime, string project, List<string> logList)
        {
            WaitBox.Text = "正在获取 " + DistrictManager.GetInstance().getDistrictName(cityId) + " 市的CQT点...";

            foreach (string log in logList)
            {
                DIYQueryCQTFileInfo diyTestCont = new DIYQueryCQTFileInfo(MainModel, log, iareatype, project, false, sDtime, eDtime);
                diyTestCont.Query();//查询测试总数
                foreach (string strkey in diyTestCont.CQTFileCountByServ.Keys)
                {
                    if (!CQTFileCountByServ.ContainsKey(strkey))
                        CQTFileCountByServ.Add(strkey, diyTestCont.CQTFileCountByServ[strkey]);
                }
            }
            DIYQueryEventLoLaInfo diyProblemLoLa = new DIYQueryEventLoLaInfo(MainModel, iareatype);
            diyProblemLoLa.Query();//查询问题点地点的经纬度
            DIYQueryCQTProblemInfo diyProblemInfo = new DIYQueryCQTProblemInfo(MainModel, iareatype,
                sDtime, eDtime, project, "派单问题点");
            diyProblemInfo.Query();//查测试地点问题点各字段
            int iareatypeid = 0;
            if (diyProblemInfo.CQTPromblemDic.Count == 0)
            {
                Dictionary<int, CQTPointTem> CQTPointDic = new Dictionary<int, CQTPointTem>();
                Dictionary<int, string> areaStrCover = new Dictionary<int, string>();
                Dictionary<int, ProblemLoLaInfo> CQTPromblemLoLaDic = new Dictionary<int, ProblemLoLaInfo>();
                dealWithDataXQ(CQTPointDic, diyProblemInfo.CQTPromblemDic, areaStrCover, CQTPromblemLoLaDic);
                dealWithSummarryData();
                return;
            }
            foreach (CQTProjectAreaIDKey proArea in diyProblemInfo.CQTPromblemDic.Keys)
            {
                iareatypeid = diyProblemInfo.CQTPromblemDic[proArea].IAreaType;
                if (true)
                {
                    break;
                }
            }
            MasterCom.RAMS.Stat.DIYQueryCQTPointInfo dqcpi = new MasterCom.RAMS.Stat.DIYQueryCQTPointInfo(MainModel, iareatypeid);
            dqcpi.Query();//查测试地点属性与地点名称
            WaitBox.ProgressPercent = WaitBox.ProgressPercent + 1;

            DIYQueryCQTCoverInfo dCoverInfo = new DIYQueryCQTCoverInfo(MainModel, iareatypeid);
            dCoverInfo.Query();//查询覆盖属性，因为点的属性查询类关联太广，而且覆盖属性表只有广东有
            //所以，分开来查询

            dealWithDataXQ(dqcpi.CQTPointDic, diyProblemInfo.CQTPromblemDic, dCoverInfo.areaStrCover, diyProblemLoLa.CQTPromblemLoLaDic);
            dealWithSummarryData();
        }

        /// <summary>
        /// 处理数据
        /// </summary>
        private void dealWithSummarryData()
        {
            ProblemSummaryPont4G problemInfo = new ProblemSummaryPont4G();
            if (result.Count > 0)
            {
                for (int id = problemPointList.Count - newpoint; id < problemPointList.Count; id++)
                {
                    addProblemInfo(problemInfo, id);
                }
            }
            problemInfo.StrCityType = CQTProblemSummaryAnalysGSM.cityLevel(strcityid);
            problemInfo.StrCity = strCityName;
            problemInfo.ITestColeVioce = getTestCountByServ("语音");
            problemInfo.ITestColeData = getTestCountByServ("数据");
            problemInfo.ITestCole = getTestCountByServ("汇总");
            problemPointSummarryList.Add(problemInfo);
        }

        private void addProblemInfo(ProblemSummaryPont4G problemInfo, int id)
        {
            if (!problemInfo.StrCityColePointList.Contains(problemPointList[id].StrTestPoint))
            {
                problemInfo.ICityColePoint++;
                problemInfo.StrCityColePointList.Add(problemPointList[id].StrTestPoint);
            }
            if (problemPointList[id].StrMainType.Contains("下载速率低"))
            {
                problemInfo.ILessDownDn++;
            }
            if (problemPointList[id].StrMainType.Contains("下载掉线"))
            {
                problemInfo.IDownDrop++;
            }
            if (problemPointList[id].StrMainType.Contains("全程呼叫成功率不达标"))
            {
                problemInfo.ICallSuccessLess++;
            }
            if (problemPointList[id].StrMainType.Contains("CSFB回落不达标"))
            {
                problemInfo.ICallCSFBLess++;
            }
            if (problemPointList[id].StrMainType.Contains("下载速率低") || problemPointList[id].StrMainType.Contains("下载掉线"))
            {
                bool isContians = problemInfo.StrLessThenColeDataList.Contains(problemPointList[id].StrTestPoint);
                if (!isContians)
                {
                    problemInfo.ILessThenColeData++;
                    problemInfo.StrLessThenColeDataList.Add(problemPointList[id].StrTestPoint);
                }
            }
            if (problemPointList[id].StrMainType.Contains("全程呼叫成功率不达标") || problemPointList[id].StrMainType.Contains("CSFB回落不达标"))
            {
                bool isContians = problemInfo.StrLessThenColeVoiceList.Contains(problemPointList[id].StrTestPoint);
                if (!isContians)
                {
                    problemInfo.ILessThenColeVoice++;
                    problemInfo.StrLessThenColeVoiceList.Add(problemPointList[id].StrTestPoint);
                }
            }
        }

        /// <summary>
        /// 对事前信息进行处理，以便对应原来的统计接口
        /// </summary>
        private void dealWithDataXQ(Dictionary<int, CQTPointTem> cqtNameDic,
            Dictionary<CQTProjectAreaIDKey, ProblemItem> CQTPromblemDic, Dictionary<int, string> areaStrCoverDic
            , Dictionary<int, ProblemLoLaInfo> CQTPromblemLoLaDic)
        {
            result.Clear();
            foreach (CQTProjectAreaIDKey projArea in CQTPromblemDic.Keys)
            {
                ProblemItem pT = CQTPromblemDic[projArea];
                pT.StrCoverType = "非室分";
                if (cqtNameDic.ContainsKey(projArea.IAreaID))
                {
                    pT.Strcqtname = cqtNameDic[projArea.IAreaID].Strareaname;
                    pT.Strcqttype = cqtNameDic[projArea.IAreaID].Strcomment;
                }
                if (CQTPromblemLoLaDic.ContainsKey(projArea.IAreaID))
                {
                    pT.DLongitude = CQTPromblemLoLaDic[projArea.IAreaID].DLongitude;
                    pT.DLatitude = CQTPromblemLoLaDic[projArea.IAreaID].DLatitude;
                }
                if (areaStrCoverDic.ContainsKey(projArea.IAreaID))
                {
                    pT.StrCoverType = areaStrCoverDic[projArea.IAreaID];
                }
                if (pT.Strcqtname == null || pT.Strcqttype == null || pT.Strcqtname == "")
                    continue;
                result.Add(pT);
            }
            addProblemPointList();
        }

        private void addProblemPointList()
        {
            newpoint = 0;
            for (int id = 0; id < result.Count; id++)
            {
                newpoint++;
                ProblemPont4G proInfo = new ProblemPont4G();
                if (result[id].StrMainProblem1 != "")
                {
                    proInfo.StrMainType = result[id].StrMainProblem1;
                }
                StringBuilder sb = new StringBuilder(proInfo.StrMainType);
                if (result[id].StrSecProblem1 != "")
                {
                    sb.Append("," + result[id].StrSecProblem1);
                }
                proInfo.StrMainType = sb.ToString();
                proInfo.IID = problemPointList.Count + 1;
                proInfo.StrCity = strCityName;
                proInfo.DTestTime = result[id].Dtime;
                proInfo.StrTestPoint = result[id].Strcqtname;
                proInfo.DLongitude = result[id].DLongitude;
                proInfo.DLatitude = result[id].DLatitude;
                proInfo.StrCoverType = result[id].StrCoverType;
                proInfo.Strcqttype = result[id].Strcqttype;
                proInfo.StrValue8 = result[id].StrValue8;
                problemPointList.Add(proInfo);
            }
        }

        private int getTestCountByServ(string strSevr)
        {
            int iTestCount = 0;
            if (strSevr != "汇总")
            {
                foreach (string strKey in CQTFileCountByServ.Keys)
                {
                    if (strKey.Contains(strSevr))
                    {
                        iTestCount++;
                    }
                }
            } 
            else
            {
                List<int> iTestAreaIDList = new List<int>();
                foreach (string strKey in CQTFileCountByServ.Keys)
                {
                    if (!iTestAreaIDList.Contains(int.Parse(strKey.Split('|')[1])))
                    {
                        iTestAreaIDList.Add(int.Parse(strKey.Split('|')[1]));
                    }
                }
                iTestCount = iTestAreaIDList.Count;
            }
           
            return iTestCount;
        }
        
        /// <summary>
        /// 显示处理结果窗体
        /// </summary>
        private void showData()
        {
            CQTProblemSummary4G cqtproblemShowForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(CQTProblemSummary4G).FullName);
            cqtproblemShowForm = obj == null ? null : obj as CQTProblemSummary4G;
            if (cqtproblemShowForm == null || cqtproblemShowForm.IsDisposed)
            {
                cqtproblemShowForm = new CQTProblemSummary4G(MainModel);
            }
            cqtproblemShowForm.filldataSummary4G(problemPointList, problemPointSummarryList);
            cqtproblemShowForm.Show(MainModel.MainForm);
        }
    }
    public class ProblemPont4G
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int IID { get; set; }
        /// <summary>
        /// 城市
        /// </summary>
        public string StrCity { get; set; }
        /// <summary>
        /// 测试日期
        /// </summary>
        public DateTime DTestTime { get; set; }
        public string StrTestTime
        {
            get
            {
                return DTestTime.ToString("yyyy-MM-dd");
            }
        }
        /// <summary>
        /// 测试点名称
        /// </summary>
        public string StrTestPoint { get; set; }
        /// <summary>
        /// 地点经度
        /// </summary>
        public double DLongitude { get; set; }
        /// <summary>
        /// 地点纬度
        /// </summary>
        public double DLatitude { get; set; }
        /// <summary>
        /// 场所属性
        /// </summary>
        public string Strcqttype { get; set; }
        /// <summary>
        /// 覆盖属性
        /// </summary>
        public string StrCoverType { get; set; }
        /// <summary>
        /// 规划站点
        /// </summary>
        public string StrValue8 { get; set; }
        /// <summary>
        /// 主问题类型
        /// </summary>
        public string StrMainType { get; set; }
        /// <summary>
        /// 次问题类别
        /// </summary>
        public string StrSecondType { get; set; }
        public ProblemPont4G()
        {
            this.IID = 0;
            this.StrCity = "";
            this.DTestTime = DateTime.Now;
            this.StrTestPoint = "";
            this.DLongitude = -1;
            this.DLatitude = -1;
            this.Strcqttype = "";
            this.StrMainType = "";
            this.StrSecondType = "";
        }
    }

    public class ProblemSummaryPont4G
    {
        /// <summary>
        /// 城市类别
        /// </summary>
        public string StrCityType { get; set; }
        /// <summary>
        /// 城市
        /// </summary>
        public string StrCity { get; set; }
        /// <summary>
        /// 下载速率低
        /// </summary>
        public int ILessDownDn { get; set; }
        /// <summary>
        /// 下载掉线
        /// </summary>
        public int IDownDrop { get; set; }
        /// <summary>
        /// 语音指标未达标点汇总
        /// </summary>
        public int ILessThenColeVoice { get; set; }
        /// <summary>
        /// 语音未达标点名
        /// </summary>
        public List<string> StrLessThenColeVoiceList { get; set; }
        /// <summary>
        /// 数据指标未达标点汇总
        /// </summary>
        public int ILessThenColeData { get; set; }
        /// <summary>
        /// 数据未达标点名
        /// </summary>
        public List<string> StrLessThenColeDataList { get; set; }
        /// <summary>
        /// 全程呼叫成功率不达标
        /// </summary>
        public int ICallSuccessLess { get; set; }
        /// <summary>
        /// CSFB回来不达标
        /// </summary>
        public int ICallCSFBLess { get; set; }
        /// <summary>
        /// 地市总问题点数
        /// </summary>
        public int ICityColePoint { get; set; }
        /// <summary>
        /// 地市问题点名
        /// </summary>
        public List<string> StrCityColePointList { get; set; }
        /// <summary>
        /// 测试总数
        /// </summary>
        public int ITestCole { get; set; }
        /// <summary>
        /// 语音测试总数
        /// </summary>
        public int ITestColeVioce { get; set; }
        /// <summary>
        /// 测试总数
        /// </summary>
        public int ITestColeData { get; set; }
        /// <summary>
        /// 语音测试通过率
        /// </summary>
        public string StrTestSucessVioce
        {
            get
            {
                if (ITestColeVioce > 0)
                {
                    return (100 - 100.0 * ILessThenColeVoice / ITestColeVioce).ToString("0.00") + "%";
                }
                else
                {
                    return "--";
                }
            }
        }
        /// <summary>
        ///数据测试通过率
        /// </summary>
        public string StrTestSucessData
        {
            get
            {
                if (ITestColeData > 0)
                {
                    return (100 - 100.0 * ILessThenColeData / ITestColeData).ToString("0.00") + "%";
                }
                else
                {
                    return "--";
                }
            }
        }
        /// <summary>
        /// 测试通过率
        /// </summary>
        public string StrTestSucess
        {
            get 
            {
                if (ITestCole > 0)
                {
                    return (100 - 100.0 * ICityColePoint / ITestCole).ToString("0.00") + "%";
                }
                else
                {
                    return "--";
                }
            }
        }

        public ProblemSummaryPont4G()
        {
            this.StrLessThenColeVoiceList = new List<string>();
            this.StrLessThenColeDataList = new List<string>();
            this.StrCityColePointList = new List<string>();
            this.StrCityType = "";
            this.StrCity = "";
            this.ILessDownDn = 0;
            this.IDownDrop = 0;
            this.ICallSuccessLess = 0;
            this.ICallCSFBLess = 0;
            this.ILessThenColeVoice = 0;
            this.ILessThenColeData = 0;
            this.ICityColePoint = 0;
            this.ITestCole = 0;
        }
    }
}
