﻿using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteWeakMosAnaBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        readonly List<WeakMosItem> WeakMosList = new List<WeakMosItem>();
        double mosGate = 2.8; 

        protected VolteWeakMosAnaBase()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);

            Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("LAC");
            Columns.Add("CI");
            Columns.Add("BSIC");
            Columns.Add("BCCH");
            Columns.Add("lte_PESQMos");
            Columns.Add("lte_POLQA_Score_SWB");
            Columns.Add("mode");
            Columns.Add("lte_gsm_DM_RxLevSub");
            Columns.Add("lte_gsm_DM_RxQualSub");
            Columns.Add("lte_gsm_SC_LAC");
            Columns.Add("lte_gsm_SC_CI");
            Columns.Add("lte_gsm_SC_BCCH");
            Columns.Add("lte_gsm_SC_BSIC");
            Columns.Add("lte_gsm_NC_RxLev");
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27005, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            VolteWeakMosSettingDlg dlg = new VolteWeakMosSettingDlg(mosGate);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                mosGate = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            WeakMosList.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    string mosParamName = "";
                    WeakMosItem item = new WeakMosItem(file.FileName);
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        if (!getMOSParamName(tp, ref mosParamName))
                        {
                            continue;
                        }
                        item = addWeakMosItem(file, mosParamName, item, tp);
                    }

                    addWeakMosList(item);
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private WeakMosItem addWeakMosItem(DTFileDataManager file, string mosParamName, WeakMosItem item, TestPoint tp)
        {
            float? mos = (float?)tp[mosParamName];
            if (mos != null && mos > 0)
            {
                if (mos <= mosGate)
                {
                    item.AddWeakMosPoint(tp, file.TestPoints, file.Events, (float)mos);
                }
                else
                {
                    addWeakMosList(item);
                    item = new WeakMosItem(file.FileName);
                }
            }

            return item;
        }

        private void addWeakMosList(WeakMosItem item)
        {
            if (item.WeakMosTpList.Count > 0)
            {
                item.SN = WeakMosList.Count + 1;
                WeakMosList.Add(item);
            }
        }

        protected virtual bool getMOSParamName(TestPoint tp, ref string mosParamName)
        {
            if (mosParamName == "")
            {
                float? pesq = (float?)tp["lte_PESQMos"];
                float? polqa = (float?)tp["lte_POLQA_Score_SWB"];
                if (pesq != null && pesq > 0 && pesq <= 5)
                {
                    mosParamName = "lte_PESQMos";
                }
                else if (polqa != null && polqa > 0 && polqa <= 5)
                {
                    mosParamName = "lte_POLQA_Score_SWB";
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
        protected override void fireShowForm()
        {
            if (WeakMosList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            VolteWeakMosResultForm frm = MainModel.CreateResultForm(typeof(VolteWeakMosResultForm)) as VolteWeakMosResultForm;
            frm.FillData(WeakMosList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class VolteWeakMosAnaBase_FDD : VolteWeakMosAnaBase
    {
        protected VolteWeakMosAnaBase_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);

            Columns = new List<string>();
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("LAC");
            Columns.Add("CI");
            Columns.Add("BSIC");
            Columns.Add("BCCH");
            Columns.Add("lte_fdd_PESQMos");
            Columns.Add("lte_fdd_POLQA_Score_SWB");
            Columns.Add("mode");
            Columns.Add("lte_fdd_gsm_DM_RxLevSub");
            Columns.Add("lte_fdd_gsm_DM_RxQualSub");
            Columns.Add("lte_fdd_gsm_SC_LAC");
            Columns.Add("lte_fdd_gsm_SC_CI");
            Columns.Add("lte_fdd_gsm_SC_BCCH");
            Columns.Add("lte_fdd_gsm_SC_BSIC");
            Columns.Add("lte_fdd_gsm_NC_RxLev");
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30024, this.Name);
        }
        protected override bool getMOSParamName(TestPoint tp, ref string mosParamName)
        {
            if (mosParamName == "")
            {
                float? pesq = (float?)tp["lte_fdd_PESQMos"];
                float? polqa = (float?)tp["lte_fdd_POLQA_Score_SWB"];
                if (pesq != null && pesq > 0 && pesq <= 5)
                {
                    mosParamName = "lte_fdd_PESQMos";
                }
                else if (polqa != null && polqa > 0 && polqa <= 5)
                {
                    mosParamName = "lte_fdd_POLQA_Score_SWB";
                }
                else
                {
                    return false;
                }
            }
            return true;
        }
    }
}
