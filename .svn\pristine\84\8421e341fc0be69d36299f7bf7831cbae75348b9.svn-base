﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTWInterfereCellsForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.cbxCoFreqCoCpiGrp = new System.Windows.Forms.CheckBox();
            this.label8 = new System.Windows.Forms.Label();
            this.numAngleMin = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.numBeam = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numAngleMax = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.numDistance = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.chkCoCpiGrp = new System.Windows.Forms.CheckBox();
            this.chkCoFreq = new System.Windows.Forms.CheckBox();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCPI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnInterAngle = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIsInBeam = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAngleMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBeam)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAngleMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.cbxCoFreqCoCpiGrp);
            this.panel1.Controls.Add(this.label8);
            this.panel1.Controls.Add(this.numAngleMin);
            this.panel1.Controls.Add(this.label7);
            this.panel1.Controls.Add(this.numBeam);
            this.panel1.Controls.Add(this.label6);
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.numAngleMax);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.btnOK);
            this.panel1.Controls.Add(this.numDistance);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.chkCoCpiGrp);
            this.panel1.Controls.Add(this.chkCoFreq);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(867, 45);
            this.panel1.TabIndex = 0;
            // 
            // cbxCoFreqCoCpiGrp
            // 
            this.cbxCoFreqCoCpiGrp.AutoSize = true;
            this.cbxCoFreqCoCpiGrp.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbxCoFreqCoCpiGrp.Location = new System.Drawing.Point(128, 15);
            this.cbxCoFreqCoCpiGrp.Name = "cbxCoFreqCoCpiGrp";
            this.cbxCoFreqCoCpiGrp.Size = new System.Drawing.Size(108, 16);
            this.cbxCoFreqCoCpiGrp.TabIndex = 15;
            this.cbxCoFreqCoCpiGrp.Text = "同频且同扰码组";
            this.cbxCoFreqCoCpiGrp.UseVisualStyleBackColor = true;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.Location = new System.Drawing.Point(428, 16);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 14;
            this.label8.Text = "度";
            // 
            // numAngleMin
            // 
            this.numAngleMin.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numAngleMin.Increment = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numAngleMin.Location = new System.Drawing.Point(373, 11);
            this.numAngleMin.Maximum = new decimal(new int[] {
            180,
            0,
            0,
            0});
            this.numAngleMin.Name = "numAngleMin";
            this.numAngleMin.Size = new System.Drawing.Size(52, 21);
            this.numAngleMin.TabIndex = 13;
            this.numAngleMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label7.Location = new System.Drawing.Point(721, 16);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(29, 12);
            this.label7.TabIndex = 12;
            this.label7.Text = "度内";
            // 
            // numBeam
            // 
            this.numBeam.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numBeam.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numBeam.Location = new System.Drawing.Point(665, 11);
            this.numBeam.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numBeam.Name = "numBeam";
            this.numBeam.Size = new System.Drawing.Size(52, 21);
            this.numBeam.TabIndex = 11;
            this.numBeam.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numBeam.Value = new decimal(new int[] {
            45,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label6.Location = new System.Drawing.Point(587, 16);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(77, 12);
            this.label6.TabIndex = 10;
            this.label6.Text = "主瓣为小区±";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(554, 16);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 9;
            this.label4.Text = "度";
            // 
            // numAngleMax
            // 
            this.numAngleMax.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numAngleMax.Increment = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numAngleMax.Location = new System.Drawing.Point(498, 11);
            this.numAngleMax.Maximum = new decimal(new int[] {
            180,
            0,
            0,
            0});
            this.numAngleMax.Name = "numAngleMax";
            this.numAngleMax.Size = new System.Drawing.Size(52, 21);
            this.numAngleMax.TabIndex = 7;
            this.numAngleMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numAngleMax.Value = new decimal(new int[] {
            180,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(443, 16);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 8;
            this.label5.Text = "≤夹角≤";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(348, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "米";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(765, 6);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 4;
            this.btnOK.Text = "查询";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // numDistance
            // 
            this.numDistance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDistance.Increment = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numDistance.Location = new System.Drawing.Point(290, 11);
            this.numDistance.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numDistance.Name = "numDistance";
            this.numDistance.Size = new System.Drawing.Size(52, 21);
            this.numDistance.TabIndex = 3;
            this.numDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistance.Value = new decimal(new int[] {
            250,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(248, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "距离≤";
            // 
            // chkCoCpiGrp
            // 
            this.chkCoCpiGrp.AutoSize = true;
            this.chkCoCpiGrp.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkCoCpiGrp.Location = new System.Drawing.Point(57, 15);
            this.chkCoCpiGrp.Name = "chkCoCpiGrp";
            this.chkCoCpiGrp.Size = new System.Drawing.Size(72, 16);
            this.chkCoCpiGrp.TabIndex = 2;
            this.chkCoCpiGrp.Text = "同扰码组";
            this.chkCoCpiGrp.UseVisualStyleBackColor = true;
            // 
            // chkCoFreq
            // 
            this.chkCoFreq.AutoSize = true;
            this.chkCoFreq.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkCoFreq.Location = new System.Drawing.Point(9, 15);
            this.chkCoFreq.Name = "chkCoFreq";
            this.chkCoFreq.Size = new System.Drawing.Size(48, 16);
            this.chkCoFreq.TabIndex = 1;
            this.chkCoFreq.Text = "同频";
            this.chkCoFreq.UseVisualStyleBackColor = true;
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnCellName);
            this.treeListView.AllColumns.Add(this.olvColumnLAC);
            this.treeListView.AllColumns.Add(this.olvColumnCI);
            this.treeListView.AllColumns.Add(this.olvColumnARFCN);
            this.treeListView.AllColumns.Add(this.olvColumnCPI);
            this.treeListView.AllColumns.Add(this.olvColumnDistance);
            this.treeListView.AllColumns.Add(this.olvColumnInterAngle);
            this.treeListView.AllColumns.Add(this.olvColumnIsInBeam);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnARFCN,
            this.olvColumnCPI,
            this.olvColumnDistance,
            this.olvColumnInterAngle,
            this.olvColumnIsInBeam});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 45);
            this.treeListView.MultiSelect = false;
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(867, 388);
            this.treeListView.TabIndex = 1;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名";
            this.olvColumnCellName.Width = 150;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            this.olvColumnLAC.Width = 90;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            this.olvColumnCI.Width = 90;
            // 
            // olvColumnARFCN
            // 
            this.olvColumnARFCN.HeaderFont = null;
            this.olvColumnARFCN.Text = "UARFCN";
            this.olvColumnARFCN.Width = 90;
            // 
            // olvColumnCPI
            // 
            this.olvColumnCPI.HeaderFont = null;
            this.olvColumnCPI.Text = "PSC";
            this.olvColumnCPI.Width = 90;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "距离";
            this.olvColumnDistance.Width = 90;
            // 
            // olvColumnInterAngle
            // 
            this.olvColumnInterAngle.HeaderFont = null;
            this.olvColumnInterAngle.Text = "夹角";
            this.olvColumnInterAngle.Width = 90;
            // 
            // olvColumnIsInBeam
            // 
            this.olvColumnIsInBeam.HeaderFont = null;
            this.olvColumnIsInBeam.Text = "在主瓣内";
            this.olvColumnIsInBeam.Width = 90;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 70);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(129, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // ZTWInterfereCellsForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(867, 433);
            this.Controls.Add(this.treeListView);
            this.Controls.Add(this.panel1);
            this.Name = "ZTWInterfereCellsForm";
            this.Text = "同邻频干扰";
            this.Load += new System.EventHandler(this.ZTTDInterfereCellsForm_Load);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAngleMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBeam)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAngleMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnARFCN;
        private System.Windows.Forms.NumericUpDown numDistance;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox chkCoCpiGrp;
        private System.Windows.Forms.CheckBox chkCoFreq;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private BrightIdeasSoftware.OLVColumn olvColumnCPI;
        private BrightIdeasSoftware.OLVColumn olvColumnInterAngle;
        private BrightIdeasSoftware.OLVColumn olvColumnIsInBeam;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numBeam;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numAngleMax;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numAngleMin;
        private System.Windows.Forms.CheckBox cbxCoFreqCoCpiGrp;
    }
}