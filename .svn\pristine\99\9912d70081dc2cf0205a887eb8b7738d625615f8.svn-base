﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYLowSpeedCellByRegion_LteUep : ZTDIYLowSpeedCellByRegion_LTE
    {
        private static ZTDIYLowSpeedCellByRegion_LteUep instance = null;
        public new static ZTDIYLowSpeedCellByRegion_LteUep GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYLowSpeedCellByRegion_LteUep();
                    }
                }
            }
            return instance;
        }

        protected ZTDIYLowSpeedCellByRegion_LteUep()
            : base()
        {
            init();
        }

        protected new void init()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "低速率小区_LTE_UEP"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 24000, 24007, this.Name);
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            return tp is LTEUepTestPoint && Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
        }
    
#region getCurSelDIYSampleGroupPrepare
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_APP_Speed";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRQ";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSSI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_APP_ThroughputDL";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LowSpeed");
            tmpDic.Add("themeName", (object)"lte_APP_Speed_kb");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
#endregion

    }
 
}
