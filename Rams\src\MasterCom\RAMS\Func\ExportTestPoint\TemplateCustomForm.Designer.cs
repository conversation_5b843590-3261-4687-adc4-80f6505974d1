﻿namespace MasterCom.RAMS.Func.ExportTestPoint
{
    partial class TemplateCustomForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.btnRemoveTemplate = new DevExpress.XtraEditors.SimpleButton();
            this.gridCtrlTmpl = new DevExpress.XtraGrid.GridControl();
            this.gvTmpl = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnNewReport = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.chkIgnore = new System.Windows.Forms.CheckBox();
            this.chkRange = new System.Windows.Forms.CheckBox();
            this.listDisplay = new System.Windows.Forms.ListBox();
            this.btnDown = new DevExpress.XtraEditors.SimpleButton();
            this.btnUp = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddDisCol = new DevExpress.XtraEditors.SimpleButton();
            this.btnModifyCol = new DevExpress.XtraEditors.SimpleButton();
            this.btnRemoveDisCol = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.txtCaption = new DevExpress.XtraEditors.TextEdit();
            this.cbxSysDisplay = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbxParamDisplay = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.cbxParamIdxDisplay = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.numMinValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTmpl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTmpl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCaption.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxSysDisplay.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamDisplay.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamIdxDisplay.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinValue.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControl4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl3);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1133, 595);
            this.splitContainerControl1.SplitterPosition = 307;
            this.splitContainerControl1.TabIndex = 10;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.btnRemoveTemplate);
            this.groupControl4.Controls.Add(this.gridCtrlTmpl);
            this.groupControl4.Controls.Add(this.btnSave);
            this.groupControl4.Controls.Add(this.btnNewReport);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(307, 591);
            this.groupControl4.TabIndex = 0;
            this.groupControl4.Text = "模板列表";
            // 
            // btnRemoveTemplate
            // 
            this.btnRemoveTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRemoveTemplate.Location = new System.Drawing.Point(110, 554);
            this.btnRemoveTemplate.Name = "btnRemoveTemplate";
            this.btnRemoveTemplate.Size = new System.Drawing.Size(87, 27);
            this.btnRemoveTemplate.TabIndex = 3;
            this.btnRemoveTemplate.Text = "删除";
            this.btnRemoveTemplate.Click += new System.EventHandler(this.btnRemoveTemplate_Click);
            // 
            // gridCtrlTmpl
            // 
            this.gridCtrlTmpl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridCtrlTmpl.Location = new System.Drawing.Point(3, 26);
            this.gridCtrlTmpl.MainView = this.gvTmpl;
            this.gridCtrlTmpl.Name = "gridCtrlTmpl";
            this.gridCtrlTmpl.ShowOnlyPredefinedDetails = true;
            this.gridCtrlTmpl.Size = new System.Drawing.Size(299, 522);
            this.gridCtrlTmpl.TabIndex = 1;
            this.gridCtrlTmpl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvTmpl});
            // 
            // gvTmpl
            // 
            this.gvTmpl.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedCell.BorderColor = System.Drawing.Color.Black;
            this.gvTmpl.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvTmpl.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gvTmpl.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Black;
            this.gvTmpl.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvTmpl.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gvTmpl.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1});
            this.gvTmpl.GridControl = this.gridCtrlTmpl;
            this.gvTmpl.Name = "gvTmpl";
            this.gvTmpl.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvTmpl.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvTmpl.OptionsDetail.ShowDetailTabs = false;
            this.gvTmpl.OptionsView.EnableAppearanceEvenRow = true;
            this.gvTmpl.OptionsView.ShowDetailButtons = false;
            this.gvTmpl.OptionsView.ShowGroupPanel = false;
            this.gvTmpl.OptionsView.ShowIndicator = false;
            this.gvTmpl.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gvTmpl_FocusedRowChanged);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "名称";
            this.gridColumn1.FieldName = "Name";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(215, 554);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(87, 27);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "保存";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnNewReport
            // 
            this.btnNewReport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNewReport.Location = new System.Drawing.Point(5, 554);
            this.btnNewReport.Name = "btnNewReport";
            this.btnNewReport.Size = new System.Drawing.Size(87, 27);
            this.btnNewReport.TabIndex = 2;
            this.btnNewReport.Text = "新建";
            this.btnNewReport.Click += new System.EventHandler(this.btnNewReport_Click);
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.chkIgnore);
            this.groupControl3.Controls.Add(this.chkRange);
            this.groupControl3.Controls.Add(this.listDisplay);
            this.groupControl3.Controls.Add(this.btnDown);
            this.groupControl3.Controls.Add(this.btnUp);
            this.groupControl3.Controls.Add(this.btnAddDisCol);
            this.groupControl3.Controls.Add(this.btnModifyCol);
            this.groupControl3.Controls.Add(this.btnRemoveDisCol);
            this.groupControl3.Controls.Add(this.labelControl13);
            this.groupControl3.Controls.Add(this.txtCaption);
            this.groupControl3.Controls.Add(this.cbxSysDisplay);
            this.groupControl3.Controls.Add(this.cbxParamDisplay);
            this.groupControl3.Controls.Add(this.labelControl8);
            this.groupControl3.Controls.Add(this.numMaxValue);
            this.groupControl3.Controls.Add(this.cbxParamIdxDisplay);
            this.groupControl3.Controls.Add(this.labelControl9);
            this.groupControl3.Controls.Add(this.numMinValue);
            this.groupControl3.Controls.Add(this.labelControl10);
            this.groupControl3.Controls.Add(this.labelControl12);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Enabled = false;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(818, 591);
            this.groupControl3.TabIndex = 0;
            this.groupControl3.Text = "模板样式";
            // 
            // chkIgnore
            // 
            this.chkIgnore.AutoSize = true;
            this.chkIgnore.Enabled = false;
            this.chkIgnore.Location = new System.Drawing.Point(46, 217);
            this.chkIgnore.Name = "chkIgnore";
            this.chkIgnore.Size = new System.Drawing.Size(230, 18);
            this.chkIgnore.TabIndex = 4;
            this.chkIgnore.Text = "指标值不在范围内，则过滤掉该采样点";
            this.chkIgnore.UseVisualStyleBackColor = true;
            this.chkIgnore.CheckedChanged += new System.EventHandler(this.chkRange_CheckedChanged);
            // 
            // chkRange
            // 
            this.chkRange.AutoSize = true;
            this.chkRange.Location = new System.Drawing.Point(46, 192);
            this.chkRange.Name = "chkRange";
            this.chkRange.Size = new System.Drawing.Size(74, 18);
            this.chkRange.TabIndex = 4;
            this.chkRange.Text = "限制值域";
            this.chkRange.UseVisualStyleBackColor = true;
            this.chkRange.CheckedChanged += new System.EventHandler(this.chkRange_CheckedChanged);
            // 
            // listDisplay
            // 
            this.listDisplay.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listDisplay.FormattingEnabled = true;
            this.listDisplay.ItemHeight = 14;
            this.listDisplay.Location = new System.Drawing.Point(464, 26);
            this.listDisplay.Name = "listDisplay";
            this.listDisplay.Size = new System.Drawing.Size(349, 564);
            this.listDisplay.TabIndex = 12;
            this.listDisplay.SelectedIndexChanged += new System.EventHandler(this.listDisplay_SelectedIndexChanged);
            // 
            // btnDown
            // 
            this.btnDown.Enabled = false;
            this.btnDown.Location = new System.Drawing.Point(357, 286);
            this.btnDown.Name = "btnDown";
            this.btnDown.Size = new System.Drawing.Size(87, 27);
            this.btnDown.TabIndex = 11;
            this.btnDown.Text = "下移↓";
            this.btnDown.Click += new System.EventHandler(this.btnDown_Click);
            // 
            // btnUp
            // 
            this.btnUp.Enabled = false;
            this.btnUp.Location = new System.Drawing.Point(357, 253);
            this.btnUp.Name = "btnUp";
            this.btnUp.Size = new System.Drawing.Size(87, 27);
            this.btnUp.TabIndex = 10;
            this.btnUp.Text = "上移↑";
            this.btnUp.Click += new System.EventHandler(this.btnUp_Click);
            // 
            // btnAddDisCol
            // 
            this.btnAddDisCol.Enabled = false;
            this.btnAddDisCol.Location = new System.Drawing.Point(357, 151);
            this.btnAddDisCol.Name = "btnAddDisCol";
            this.btnAddDisCol.Size = new System.Drawing.Size(87, 27);
            this.btnAddDisCol.TabIndex = 9;
            this.btnAddDisCol.Text = "添加列→";
            this.btnAddDisCol.Click += new System.EventHandler(this.btnAddDisCol_Click);
            // 
            // btnModifyCol
            // 
            this.btnModifyCol.Enabled = false;
            this.btnModifyCol.Location = new System.Drawing.Point(357, 106);
            this.btnModifyCol.Name = "btnModifyCol";
            this.btnModifyCol.Size = new System.Drawing.Size(87, 27);
            this.btnModifyCol.TabIndex = 8;
            this.btnModifyCol.Text = "修改列";
            this.btnModifyCol.Click += new System.EventHandler(this.btnModifyCol_Click);
            // 
            // btnRemoveDisCol
            // 
            this.btnRemoveDisCol.Enabled = false;
            this.btnRemoveDisCol.Location = new System.Drawing.Point(357, 61);
            this.btnRemoveDisCol.Name = "btnRemoveDisCol";
            this.btnRemoveDisCol.Size = new System.Drawing.Size(87, 27);
            this.btnRemoveDisCol.TabIndex = 7;
            this.btnRemoveDisCol.Text = "←移除列";
            this.btnRemoveDisCol.Click += new System.EventHandler(this.btnRemoveDisCol_Click);
            // 
            // labelControl13
            // 
            this.labelControl13.Location = new System.Drawing.Point(203, 194);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(54, 14);
            this.labelControl13.TabIndex = 4;
            this.labelControl13.Text = "≤指标值≤";
            // 
            // txtCaption
            // 
            this.txtCaption.Location = new System.Drawing.Point(125, 58);
            this.txtCaption.Name = "txtCaption";
            this.txtCaption.Size = new System.Drawing.Size(208, 21);
            this.txtCaption.TabIndex = 0;
            // 
            // cbxSysDisplay
            // 
            this.cbxSysDisplay.Location = new System.Drawing.Point(126, 91);
            this.cbxSysDisplay.Name = "cbxSysDisplay";
            this.cbxSysDisplay.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxSysDisplay.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxSysDisplay.Size = new System.Drawing.Size(208, 21);
            this.cbxSysDisplay.TabIndex = 1;
            // 
            // cbxParamDisplay
            // 
            this.cbxParamDisplay.Location = new System.Drawing.Point(126, 124);
            this.cbxParamDisplay.Name = "cbxParamDisplay";
            this.cbxParamDisplay.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxParamDisplay.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxParamDisplay.Size = new System.Drawing.Size(208, 21);
            this.cbxParamDisplay.TabIndex = 2;
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(44, 160);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(76, 14);
            this.labelControl8.TabIndex = 5;
            this.labelControl8.Text = "指标参数索引:";
            // 
            // numMaxValue
            // 
            this.numMaxValue.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numMaxValue.Enabled = false;
            this.numMaxValue.Location = new System.Drawing.Point(268, 190);
            this.numMaxValue.Name = "numMaxValue";
            this.numMaxValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxValue.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.numMaxValue.Properties.MinValue = new decimal(new int[] {
            99999999,
            0,
            0,
            -2147483648});
            this.numMaxValue.Size = new System.Drawing.Size(65, 21);
            this.numMaxValue.TabIndex = 6;
            // 
            // cbxParamIdxDisplay
            // 
            this.cbxParamIdxDisplay.Location = new System.Drawing.Point(126, 157);
            this.cbxParamIdxDisplay.Name = "cbxParamIdxDisplay";
            this.cbxParamIdxDisplay.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxParamIdxDisplay.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxParamIdxDisplay.Size = new System.Drawing.Size(208, 21);
            this.cbxParamIdxDisplay.TabIndex = 3;
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(68, 127);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(52, 14);
            this.labelControl9.TabIndex = 6;
            this.labelControl9.Text = "指标名称:";
            // 
            // numMinValue
            // 
            this.numMinValue.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numMinValue.Enabled = false;
            this.numMinValue.Location = new System.Drawing.Point(126, 190);
            this.numMinValue.Name = "numMinValue";
            this.numMinValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinValue.Properties.MaxValue = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.numMinValue.Properties.MinValue = new decimal(new int[] {
            99999999,
            0,
            0,
            -2147483648});
            this.numMinValue.Size = new System.Drawing.Size(65, 21);
            this.numMinValue.TabIndex = 5;
            // 
            // labelControl10
            // 
            this.labelControl10.Location = new System.Drawing.Point(68, 94);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(52, 14);
            this.labelControl10.TabIndex = 7;
            this.labelControl10.Text = "指标类别:";
            // 
            // labelControl12
            // 
            this.labelControl12.Location = new System.Drawing.Point(80, 61);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(40, 14);
            this.labelControl12.TabIndex = 1;
            this.labelControl12.Text = "列标题:";
            // 
            // TemplateCustomForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(1133, 595);
            this.Controls.Add(this.splitContainerControl1);
            this.MinimumSize = new System.Drawing.Size(1149, 633);
            this.Name = "TemplateCustomForm";
            this.Text = "导出模板设置";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTmpl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTmpl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCaption.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxSysDisplay.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamDisplay.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxParamIdxDisplay.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinValue.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnNewReport;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.ListBox listDisplay;
        private DevExpress.XtraEditors.SimpleButton btnAddDisCol;
        private DevExpress.XtraEditors.SimpleButton btnRemoveDisCol;
        private DevExpress.XtraEditors.TextEdit txtCaption;
        private DevExpress.XtraEditors.ComboBoxEdit cbxSysDisplay;
        private DevExpress.XtraEditors.ComboBoxEdit cbxParamDisplay;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.ComboBoxEdit cbxParamIdxDisplay;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.SpinEdit numMaxValue;
        private DevExpress.XtraEditors.SpinEdit numMinValue;
        private System.Windows.Forms.CheckBox chkRange;
        private DevExpress.XtraEditors.SimpleButton btnRemoveTemplate;
        private DevExpress.XtraEditors.SimpleButton btnModifyCol;
        private DevExpress.XtraGrid.GridControl gridCtrlTmpl;
        private DevExpress.XtraGrid.Views.Grid.GridView gvTmpl;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.SimpleButton btnDown;
        private DevExpress.XtraEditors.SimpleButton btnUp;
        private System.Windows.Forms.CheckBox chkIgnore;
    }
}