﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckBothAnaByBegion : ZTLteNBCellCheckBothAnaBase
    {
        public ZTLteNBCellCheckBothAnaByBegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static ZTLteNBCellCheckBothAnaByBegion intance = null;
        public static ZTLteNBCellCheckBothAnaByBegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLteNBCellCheckBothAnaByBegion(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "LTE邻区核查(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22024, this.Name);//////
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
    public class LteFddNBCellCheckBothAnaByBegion : ZTLteNBCellCheckBothAnaByBegion
    {
        public LteFddNBCellCheckBothAnaByBegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        private static LteFddNBCellCheckBothAnaByBegion intance = null;
        public new static LteFddNBCellCheckBothAnaByBegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LteFddNBCellCheckBothAnaByBegion(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26017, this.Name);
        }
    }
}