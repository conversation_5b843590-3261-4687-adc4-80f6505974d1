﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.Grid;
using MasterCom.MControls;
using MasterCom.Util;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.MTGis
{
    public partial class CustomThemeLayerProperties : MTLayerPropUserControl
    {
        public CustomThemeLayerProperties()
        {
            InitializeComponent();
            InitCbxSymbol();
            InitDefaultSetting();
        }

        private void InitDefaultSetting()
        {
            tabSetting.TabPages.Remove(tpByValueDic);
            tabSetting.TabPages.Remove(tpByRefLayer);
            dlgEdit.SetDesSetInfoVisible(false);
        }

        private CustomThemeLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as CustomThemeLayer;
            if (layer == null)
            {
                return;
            }
            Text = "专题图设置";
            DataTable dt = layer.GetGridDataInfo();
            PointThemeSetting ptheme = layer.GetThemeSetting();
            List<String> colNamesLabel = new List<string>();
            List<String> colNamesValue = new List<string>();
            foreach(DataColumn dcol in dt.Columns)
            {
                if (dcol.ColumnName != "经度" && dcol.ColumnName != "纬度")
                {
                    colNamesLabel.Add(dcol.ColumnName);
                    colNamesValue.Add(dcol.ColumnName);
                }
            }
            cbxLabelField.DataSource = colNamesLabel;
            cbxValueField.DataSource = colNamesValue;
            this.applyCurrentThemeSetting(ptheme);
            //======
            
        }

        private string valueFieldName = "";
        private List<double> valuesList = new List<double>();
        private Dictionary<string, bool> valuesDic = new Dictionary<string, bool>();
        private void cbxValueField_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(cbxValueField.SelectedItem!=null)
            {
                DataTable dt = layer.GetGridDataInfo();
                string vFieldName = (String)cbxValueField.SelectedItem;
                valueFieldName = vFieldName;
                freshUpdateValueDics(vFieldName,dt);
            }
            
        }

        private void freshUpdateValueDics(string vFieldName,DataTable dt)
        {
            DataColumn fieldColumn = null;
            foreach (DataColumn dcol in dt.Columns)
            {
                if (dcol.ColumnName == vFieldName)
                {
                    fieldColumn = dcol;
                    break;
                }
            }
            valuesList.Clear();
            valuesDic.Clear();
            foreach (DataRow drow in dt.Rows)
            {
                object vValueObj = drow[fieldColumn];
                double vdb;
                if (double.TryParse(vValueObj.ToString(), out vdb))
                {
                    valuesList.Add(vdb);
                }
                if (!valuesDic.ContainsKey(vValueObj.ToString()))
                {
                    valuesDic[vValueObj.ToString()] = true;
                }
            }
            valuesList.Sort();
            if(valuesList.Count>0)
            {
                lbValueRangeDesc.Text = "最小值:" + valuesList[0] + ",最大值:" + valuesList[valuesList.Count - 1] + ",总共" + valuesList.Count+"个";
            }
            else
            {
                lbValueRangeDesc.Text = "无可用取值";
            }
            lbValueDicDesc.Text = "共" + valuesDic.Count + "个不同的取值，请分别设置！";
            
        }

        private void btnAuto_Click(object sender, EventArgs e)
        {
            if(valuesList.Count==0)
            {
                return;
            }
            float minR = (float)valuesList[0]-1;
            float maxR = (float)valuesList[valuesList.Count-1]+1;
            ColorRangeAutoDlg autoDlg = new ColorRangeAutoDlg(minR, maxR);
            if (DialogResult.OK == autoDlg.ShowDialog(this))
            {
                Color fromColor = autoDlg.FromColor;
                Color toColor = autoDlg.ToColor;
                Color viaColor = autoDlg.ViaColor;
                bool useVia = autoDlg.UseVia;
                int segCount = autoDlg.SegCount;
                if (segCount > 1 && segCount <= 100)
                {
                    List<ColorRange> colorRangeCreated = ColorSequenceSupplier.makeColorRangeList(minR, maxR, segCount, fromColor, useVia, viaColor, toColor);
                    colorRanges = colorRangeCreated;
                    updateCountDesc(colorRanges,valuesList);
                    rowCountChanged();
                }
            }
        }
        private void btnDivAvg_Click(object sender, EventArgs e)
        {
            if(valuesList.Count==0)
            {
                return;
            }
            List<ColorRange> crList = colorRanges;
            if(crList.Count>0)
            {
                
                reDivideColorRange(crList, valuesList);

                //=============================================
                updateCountDesc(crList, valuesList);
            }
            else
            {
                MessageBox.Show("请先生成颜色区间！");
            }
        }

        private void reDivideColorRange(List<ColorRange> crList, List<double> vlist)
        {
            float minR = (float)vlist[0] - 1;
            float maxR = (float)vlist[valuesList.Count - 1] + 1;
            int eachStepCount = (int)Math.Ceiling(1.0 * vlist.Count / crList.Count);
            for(int step=0;step<crList.Count;step++)
            {
                ColorRange cr = crList[step];
                int stepStartPos = step * eachStepCount;
                int stepEndPos = (step + 1) * eachStepCount;
                if(stepStartPos==0)
                {
                    cr.minValue = minR;
                }
                else if(stepStartPos<vlist.Count)
                {
                    cr.minValue = (float)vlist[stepStartPos];
                }
                if (stepEndPos < vlist.Count)
                {
                    cr.maxValue = (float)vlist[stepEndPos];
                }
                else
                {
                    cr.maxValue = maxR;
                }
            }
        }

        private void updateCountDesc(List<ColorRange> crList, List<double> valuesList)
        {
            int[] countArray = new int[crList.Count];
            foreach(double v in valuesList)
            {
                for(int i=0;i<crList.Count;i++)
                {
                    ColorRange cr = crList[i];
                    if(v>= cr.minValue && v<cr.maxValue)
                    {
                        countArray[i] = countArray[i] + 1;
                        break;
                    }
                }
            }
            for(int w =0;w<crList.Count;w++)
            {
                int count = countArray[w];
                ColorRange cr = crList[w];
                cr.desInfo = ""+count+"  ("+ (100.0f * count / valuesList.Count).ToString("F1")+"%)";
            }
            rowCountChanged();
        }
        private void rowCountChanged()
        {
            gridColorRange.RowCount = colorRanges.Count;
            foreach (DataGridViewRow row in gridColorRange.Rows)
            {
                row.Height = 18;
            }
            gridColorRange.Invalidate();
        }
        private List<ColorRange> colorRangeLayerOptionList = new List<ColorRange>();
        private List<ColorRange> colorRanges = new List<ColorRange>();
        public Dictionary<String, ValueColorPair> ColorDic
        {
            get
            {
                Dictionary<String, ValueColorPair> dic = new Dictionary<string, ValueColorPair>();
                foreach(ValueColorPair vp in dicColorList)
                {
                    dic[vp.value] = vp;
                }
                return dic;
            }
        }
        private List<ValueColorPair> dicColorList = new List<ValueColorPair>();


        ColorRangeEditDlg dlgEdit = new ColorRangeEditDlg();
        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (valuesList.Count == 0)
            {
                return;
            }
            float minR = (float)valuesList[0]-1;
            float maxR = (float)valuesList[valuesList.Count - 1]+1;
            dlgEdit.Text = "增加";
            dlgEdit.SetDesSetInfoVisible(false);
            dlgEdit.FixMinMaxRange(minR, maxR);
            if (DialogResult.OK == dlgEdit.ShowDialog(this))
            {
                fireAddRangeItem(dlgEdit.TheRange);
                updateCountDesc(colorRanges, valuesList);
                rowCountChanged();
                checkRange();
            }
        }

        private void checkRange()
        {
            bool ret = true;
            for (int i = 0; i < colorRanges.Count - 1; i++)
            {
                if (colorRanges[i].maxValue > colorRanges[i + 1].minValue)
                {
                    ret = false;
                    break;
                }
            }

            if (!ret)
            {
                XtraMessageBox.Show("设置的颜色取值区域有重叠！", "提示");
            }
        }

        private void checkRangeLayerRef()
        {
            bool ret = true;
            for (int i = 0; i < colorRangeLayerOptionList.Count - 1; i++)
            {
                if (colorRangeLayerOptionList[i].maxValue > colorRangeLayerOptionList[i + 1].minValue)
                {
                    ret = false;
                    break;
                }
            }

            if (!ret)
            {
                XtraMessageBox.Show("设置的颜色取值区域有重叠！", "提示");
            }
        }

        private void fireAddRangeItem(ColorRange cr)
        {
            int pos = 0;
            int i = 0;
            for (i = 0; i < colorRanges.Count; i++)
            {
                ColorRange cr0 = colorRanges[i];
                if (cr.minValue < cr0.minValue)
                {
                    pos = i;
                    break;
                }
            }
            if (i == colorRanges.Count)
            {
                colorRanges.Add(cr);
            }
            else
            {
                colorRanges.Insert(pos, cr);
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            fireDoEditAction();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            colorRanges.RemoveAt(gridColorRange.SelectedRows[0].Index);
            rowCountChanged();
        }

        private void btnApplySetting_Click(object sender, EventArgs e)
        {
            PointThemeSetting ps = getThemeSetting();
            if(ps!=null)
            {
                layer.ApplyThemeSetting(ps);
            }
            MainModel.GetInstance().RefreshLegend();
            MainModel.GetInstance().MainForm.MakeLog(new MasterCom.RAMS.UserMng.LogInfoItem(2, 3200, 3209, "应用专题图"));
        }
        private void InitCbxSymbol()
        {
            cbxSymbol.Items.Clear();
            List<string> symbolDescs = new List<string>() { "圆形", "方形", "三角形" };
            foreach (string desc in symbolDescs)
            {
                cbxSymbol.Items.Add(desc);
            }
            cbxSymbol.SelectedIndex = 0;
        }

        private void applyCurrentThemeSetting(PointThemeSetting ptheme)
        {
            if(ptheme!=null)
            {
                cbxShowLabel.Checked = ptheme.drawLabel;
                cbxLabelField.SelectedItem = ptheme.nameField;
                pnForeColor.BackColor = ptheme.labelForeColor;
                pnPointColor.BackColor = ptheme.pointBackColor;
                numLabelFontSize.Value = ptheme.labelFontSize;
                numSymbolSize.Value = ptheme.symbolSize;
                cbxSymbol.SelectedIndex = ptheme.symbolType;
                cbxValueField.SelectedItem = ptheme.valueField;
                rbByValueRange.Checked = ptheme.colorMethod == 1;
                rbByDicValue.Checked = ptheme.colorMethod == 2;
                rbByLayerRef.Checked = ptheme.colorMethod == 3;
                colorRanges = ptheme.colorRangeList;
                colorRangeLayerOptionList = ptheme.colorRangeLayerOptionList;
                dicColorList.Clear();
                foreach(string vstr in ptheme.dicColor.Keys)
                {
                    dicColorList.Add(ptheme.dicColor[vstr]);
                }
                List<object> layerRefList = ptheme.layerRefList;
                if (layerRefList != null)
                {
                    Dictionary<object, bool> checkedDic = new Dictionary<object, bool>();
                    foreach (object lr in layerRefList)
                    {
                        checkedDic[lr] = true;
                    }

                    for (int ww = 0; ww < cbxRefLayerSelectMulti.Properties.Items.Count; ww++)
                    {
                        object lr = cbxRefLayerSelectMulti.Properties.Items[ww].Value;
                        cbxRefLayerSelectMulti.Properties.Items[ww].CheckState = checkedDic.ContainsKey(lr) ? CheckState.Checked : CheckState.Unchecked;
                    }
                }
                rowCountChanged();
                rowCountChangedColorDic();
                rowCountLayerRefGridChanged();
                //
            }
        }
        private PointThemeSetting getThemeSetting()
        {
            PointThemeSetting ps = new PointThemeSetting();
            ps.valueField = valueFieldName;
            ps.symbolType = cbxSymbol.SelectedIndex;
            ps.colorRangeList = colorRanges;
            ps.symbolSize = (int)numSymbolSize.Value;
            ps.labelFontSize = (int)numLabelFontSize.Value;
            ps.labelForeColor = pnForeColor.BackColor;
            ps.pointBackColor = pnPointColor.BackColor;
            ps.dicColor = ColorDic;
            if (rbByDicValue.Checked)
            {
                ps.colorMethod = 2;
            }
            else
            {
                if (rbByLayerRef.Checked)
                {
                    ps.colorMethod = 3;
                }
                else
                {
                    ps.colorMethod = 1;
                }
            }
            ps.layerRefList = new List<object>();
            for (int ww = 0; ww < cbxRefLayerSelectMulti.Properties.Items.Count; ww++)
            {
                if(cbxRefLayerSelectMulti.Properties.Items[ww].CheckState == CheckState.Checked)
                {
                    ps.layerRefList.Add(cbxRefLayerSelectMulti.Properties.Items[ww].Value);
                }
            }
            ps.layerThemeOption = rbLayerMethodNearestDist.Checked ? LayerBase.ThemeOption_NearestDistance : 0;
            ps.colorRangeLayerOptionList = colorRangeLayerOptionList;
            if(cbxShowLabel.Checked)
            {
                if(cbxLabelField.SelectedItem==null)
                {
                    MessageBox.Show("请指定标签列");
                    return null;
                }
                ps.drawLabel = true;
                ps.nameField = (String)cbxLabelField.SelectedItem;
            }
            else
            {
                ps.drawLabel = false;
            }
            #region 判断是打点还是栅格
            ps.symbolStyle = 0;
            if (rdoPoint.Checked)
            {
                ps.symbolStyle = 0;
            }
            else if (rdoGrid.Checked)
            {
                ps.symbolStyle = 1;
            }
            #endregion
            return ps;
        }

        private void gridColorRange_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (!gridColorRange.Enabled)
            {
                e.CellStyle.BackColor = Color.LightGray;
                e.CellStyle.SelectionBackColor = Color.LightGray;
                e.CellStyle.SelectionForeColor = Color.Gray;
                e.CellStyle.ForeColor = Color.Gray;
                return;
            }
            if (e.ColumnIndex == 1)
            {
                if (colorRanges.Count > 0)
                {
                    e.CellStyle.BackColor = Color.FromArgb(255, colorRanges[e.RowIndex].color);
                    e.CellStyle.SelectionBackColor = Color.FromArgb(255, colorRanges[e.RowIndex].color);
                }
                else
                {
                    e.CellStyle.BackColor = Color.LightGray;
                    e.CellStyle.SelectionBackColor = Color.LightGray;
                }
            }
        }

        private void gridColorRange_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (e.RowIndex >= colorRanges.Count)
            {
                return;
            }
            if (e.ColumnIndex == 0)
            {
                e.Value = colorRanges[e.RowIndex].GetRangeDesc(e.RowIndex == colorRanges.Count - 1);
            }
            if (e.ColumnIndex == 2)
            {
                e.Value = colorRanges[e.RowIndex].desInfo;
            }
        }

        private void gridColorRange_DoubleClick(object sender, EventArgs e)
        {
            if (gridColorRange.SelectedRows.Count > 0)
            {
                fireDoEditAction();
            }
        }
        private void fireDoEditAction()
        {
            if (valuesList.Count == 0)
            {
                return;
            }
            float minR = (float)valuesList[0]-1;
            float maxR = (float)valuesList[valuesList.Count - 1]+1;

            try
            {
                ColorRange cr = colorRanges[gridColorRange.SelectedRows[0].Index];
                dlgEdit.Text = "修改";
                dlgEdit.SetDesSetInfoVisible(false);
                dlgEdit.FixMinMaxRange(minR, maxR);
                dlgEdit.TheRange = cr;
                if (DialogResult.OK == dlgEdit.ShowDialog(this))
                {
                    ColorRange ncr = dlgEdit.TheRange;
                    cr.color = ncr.color;
                    cr.minValue = ncr.minValue;
                    cr.maxValue = ncr.maxValue;
                    cr.desInfo = ncr.desInfo;
                    updateCountDesc(colorRanges, valuesList);
                    rowCountChanged();
                    checkRange();
                }
            }
            catch
            {
                XtraMessageBox.Show("颜色范围超出门限范围，请删除颜色设置并重设！", "提示");
            }

        }
        private void gridColorRange_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSel = gridColorRange.SelectedRows.Count > 0;
            btnDelete.Enabled = hasSel;
            btnEdit.Enabled = hasSel;
        }

        private void rbByValueRange_CheckedChanged(object sender, EventArgs e)
        {
            cbxValueField.Enabled = true;
            tabSetting.TabPages.Clear();
            tabSetting.TabPages.Add(tpByValueRange);
        }

        private void rbByDicValue_CheckedChanged(object sender, EventArgs e)
        {
            cbxValueField.Enabled = true;
            tabSetting.TabPages.Clear();
            tabSetting.TabPages.Add(tpByValueDic);
        }

        private void rbByLayerRef_CheckedChanged(object sender, EventArgs e)
        {
            tabSetting.TabPages.Clear();
            tabSetting.TabPages.Add(tpByRefLayer);
            cbxValueField.Enabled = false;
            refreshCurrentSupportedLayer();
        }

        private void refreshCurrentSupportedLayer()
        {
            MapForm mapForm = MainModel.GetInstance().MainForm.GetMapForm();
            List<LayerBase> baseLayers = mapForm.TempLayerBaseVec;
            cbxRefLayerSelectMulti.Properties.Items.Clear();
            MapDTLayer dtLayer = mapForm.GetDTLayer();
            if (!baseLayers.Contains(dtLayer))
            {
                baseLayers.Add(dtLayer);
            }
            foreach(LayerBase baseLayer in baseLayers)
            {
                int themeOption = baseLayer.GetSupportedThemeOptions();
                addLayer(baseLayer, themeOption);
            }
            List<CustomDrawLayer> tocheckCustLayers = new List<CustomDrawLayer>();
            tocheckCustLayers.AddRange(mapForm.TempLayers);
            foreach (CustomDrawLayer custLayer in tocheckCustLayers)
            {
                int themeOption = custLayer.GetSupportedThemeOptions();
                addLayer(custLayer, themeOption);
            }
        }

        private void addLayer(object layer, int themeOption)
        {
            if (themeOption > 0 && rbLayerMethodNearestDist.Checked && (themeOption & LayerBase.ThemeOption_NearestDistance) != 0)
            {
                cbxRefLayerSelectMulti.Properties.Items.Add(layer);
            }
        }

        private void pnForeColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDlg = new ColorDialog();
            colorDlg.Color = this.pnForeColor.BackColor;
            colorDlg.ShowDialog();
            this.pnForeColor.BackColor = colorDlg.Color;
        }

        private void btnResetDicColor_Click(object sender, EventArgs e)
        {
            dicColorList.Clear();

            Dictionary<string, bool> myDic = SortDictionary_Asc(valuesDic);

            int idx=0;
            foreach (String str in myDic.Keys)
            {
                dicColorList.Add(new ValueColorPair(str, ColorSequenceSupplier.getColor(idx++),null));
            }
            rowCountChangedColorDic();
        }

        #region 对字典按key进行排序
        protected Dictionary<string, bool> SortDictionary_Desc(Dictionary<string, bool> dic)
        {
            List<KeyValuePair<string, bool>> myList = new List<KeyValuePair<string, bool>>(dic);
            myList.Sort(delegate(KeyValuePair<string, bool> s1, KeyValuePair<string, bool> s2)
            {
                return s2.Key.CompareTo(s1.Key);
            });
            dic.Clear();
            foreach (KeyValuePair<string, bool> pair in myList)
            {
                dic.Add(pair.Key, pair.Value);
            }
            return dic;
        }

        protected Dictionary<string, bool> SortDictionary_Asc(Dictionary<string, bool> dic)
        {
            List<KeyValuePair<string, bool>> myList = new List<KeyValuePair<string, bool>>(dic);
            myList.Sort(delegate(KeyValuePair<string, bool> s1, KeyValuePair<string, bool> s2)
            {
                return s1.Key.CompareTo(s2.Key);
            });
            dic.Clear();
            foreach (KeyValuePair<string, bool> pair in myList)
            {
                dic.Add(pair.Key, pair.Value);
            }
            return dic;
        }
        #endregion
        


        private void rowCountChangedColorDic()
        {
            gridDicColors.RowCount = dicColorList.Count;
            foreach (DataGridViewRow row in gridDicColors.Rows)
            {
                row.Height = 18;
            }
            gridDicColors.Invalidate();
        }
        private void btnModifyOneDicColor_Click(object sender, EventArgs e)
        {
            fireDoEditDicGridAction();
        }

        private void gridDicColors_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == 1)
            {
                if (dicColorList.Count > 0)
                {
                    if (dicColorList[e.RowIndex].image == null)
                    {
                        e.CellStyle.BackColor = Color.FromArgb(255, dicColorList[e.RowIndex].color);
                        e.CellStyle.SelectionBackColor = Color.FromArgb(255, dicColorList[e.RowIndex].color);
                    }
                    else
                    {
                        e.CellStyle.BackColor = Color.White;
                        e.CellStyle.SelectionBackColor = Color.White;
                    }
                }
                else
                {
                    e.CellStyle.BackColor = Color.LightGray;
                    e.CellStyle.SelectionBackColor = Color.LightGray;
                }
            }
            else if(e.ColumnIndex ==2 && dicColorList[e.RowIndex].image != null)
            {
                e.Value = dicColorList[e.RowIndex].image;
            }
        }

        private void gridDicColors_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (e.RowIndex >= dicColorList.Count)
            {
                return;
            }
            if (e.ColumnIndex == 0)
            {
                e.Value = dicColorList[e.RowIndex].value;
            }
        }

        private void gridDicColors_DoubleClick(object sender, EventArgs e)
        {
            if (gridDicColors.SelectedRows.Count > 0)
            {
                fireDoEditDicGridAction();
            }
        }
        private void fireDoEditDicGridAction()
        {
            if (dicColorList.Count == 0)
            {
                return;
            }
            ValueColorPair vcp = dicColorList[gridDicColors.SelectedRows[0].Index];
            ColorOrImageDlg colorDlg = new ColorOrImageDlg();
            colorDlg.SetColorPair(vcp);
            if(DialogResult.OK == colorDlg.ShowDialog())
            {
                ValueColorPair newVCPValue = colorDlg.GetColorPairValue();
                vcp.color = newVCPValue.color;
                vcp.image = newVCPValue.image;
                rowCountChangedColorDic();
            }
        }
        private void gridDicColors_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSel = gridDicColors.SelectedRows.Count > 0;
            btnModifyOneDicColor.Enabled = hasSel;
        }

        private void btnLayerRefAdd_Click(object sender, EventArgs e)
        {
            float minR = 0;
            float maxR = 999999;
            dlgEdit.Text = "增加";
            dlgEdit.SetDesSetInfoVisible(true);
            dlgEdit.FixMinMaxRange(minR, maxR);
            if (DialogResult.OK == dlgEdit.ShowDialog(this))
            {
                fireAddRangeItemLayerRef(dlgEdit.TheRange);
                rowCountLayerRefGridChanged();
                checkRangeLayerRef();
            }
        }
        private void fireAddRangeItemLayerRef(ColorRange cr)
        {
            int pos = 0;
            int i = 0;
            for (i = 0; i < colorRangeLayerOptionList.Count; i++)
            {
                ColorRange cr0 = colorRangeLayerOptionList[i];
                if (cr.minValue < cr0.minValue)
                {
                    pos = i;
                    break;
                }
            }
            if (i == colorRangeLayerOptionList.Count)
            {
                colorRangeLayerOptionList.Add(cr);
            }
            else
            {
                colorRangeLayerOptionList.Insert(pos, cr);
            }
        }
        private void btnLayerRefEdit_Click(object sender, EventArgs e)
        {
            fireDoEditColorRefLayerAction();
        }

        private void btnLayerRefDel_Click(object sender, EventArgs e)
        {
            colorRangeLayerOptionList.RemoveAt(dataColorRefLayer.SelectedRows[0].Index);
            rowCountLayerRefGridChanged();
        }

        private void dataColorRefLayer_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == 1)
            {
                if (colorRangeLayerOptionList.Count > 0)
                {
                    e.CellStyle.BackColor = Color.FromArgb(255, colorRangeLayerOptionList[e.RowIndex].color);
                    e.CellStyle.SelectionBackColor = Color.FromArgb(255, colorRangeLayerOptionList[e.RowIndex].color);
                }
                else
                {
                    e.CellStyle.BackColor = Color.LightGray;
                    e.CellStyle.SelectionBackColor = Color.LightGray;
                }
            }
        }

        private void dataColorRefLayer_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (e.RowIndex >= colorRangeLayerOptionList.Count)
            {
                return;
            }
            if (e.ColumnIndex == 0)
            {
                e.Value = colorRangeLayerOptionList[e.RowIndex].GetRangeDesc(e.RowIndex == colorRangeLayerOptionList.Count - 1);
            }
            if (e.ColumnIndex == 2)
            {
                e.Value = colorRangeLayerOptionList[e.RowIndex].desInfo;
            }
        }

        private void dataColorRefLayer_DoubleClick(object sender, EventArgs e)
        {
            if (dataColorRefLayer.SelectedRows.Count > 0)
            {
                fireDoEditColorRefLayerAction();
            }
        }

        private void dataColorRefLayer_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSel = dataColorRefLayer.SelectedRows.Count > 0;
            btnLayerRefDel.Enabled = hasSel;
            btnLayerRefEdit.Enabled = hasSel;
        }
        private void fireDoEditColorRefLayerAction()
        {
            float minR = 0;
            float maxR = 999999;

            try
            {
                ColorRange cr = colorRangeLayerOptionList[dataColorRefLayer.SelectedRows[0].Index];
                dlgEdit.Text = "修改";
                dlgEdit.FixMinMaxRange(minR, maxR);
                dlgEdit.SetDesSetInfoVisible(true);
                dlgEdit.TheRange = cr;
                if (DialogResult.OK == dlgEdit.ShowDialog(this))
                {
                    ColorRange ncr = dlgEdit.TheRange;
                    cr.color = ncr.color;
                    cr.minValue = ncr.minValue;
                    cr.maxValue = ncr.maxValue;
                    cr.desInfo = ncr.desInfo;
                    rowCountLayerRefGridChanged();
                    checkRangeLayerRef();
                }
            }
            catch
            {
                XtraMessageBox.Show("颜色范围超出门限范围，请删除颜色设置并重设！", "提示");
            }

        }
        private void rowCountLayerRefGridChanged()
        {
            dataColorRefLayer.RowCount = colorRangeLayerOptionList.Count;
            foreach (DataGridViewRow row in dataColorRefLayer.Rows)
            {
                row.Height = 18;
            }
            dataColorRefLayer.Invalidate();
        }

        private void btnLayerRefDefault_Click(object sender, EventArgs e)
        {
            colorRangeLayerOptionList.Clear();
            colorRangeLayerOptionList.Add(new ColorRange(0, 300, Color.FromArgb(0, 255, 0)));
            colorRangeLayerOptionList.Add(new ColorRange(300, 3000, Color.FromArgb(255, 0, 0)));
            colorRangeLayerOptionList.Add(new ColorRange(3000, 999999, Color.FromArgb(255, 255, 0)));

            rowCountLayerRefGridChanged();
            checkRangeLayerRef();
        }

        private void rdoPoint_CheckedChanged(object sender, EventArgs e)
        {
            if (rdoPoint.Checked)
            {
                groupPoint.Enabled = true;
            }
        }

        private void rdoGrid_CheckedChanged(object sender, EventArgs e)
        {
            if (rdoGrid.Checked)
            {
                groupPoint.Enabled = false;
            }
        }

        private void pnPointColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDlg = new ColorDialog();
            colorDlg.Color = this.pnForeColor.BackColor;
            colorDlg.ShowDialog();
            this.pnPointColor.BackColor = colorDlg.Color;
        }

    }
    public class ValueColorPair
    {
        public string value { get; set; }
        public Color color { get; set; }
        public Image image { get; set; }

        public ValueColorPair(string v,Color c,Image img)
        {
            this.value = v;
            this.color = c;
            this.image = img;
        }
    }
}
