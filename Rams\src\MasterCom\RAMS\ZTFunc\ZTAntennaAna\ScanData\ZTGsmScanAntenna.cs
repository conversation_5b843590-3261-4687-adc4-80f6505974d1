﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTGsmScanAntenna : ZTGsmAntenna
    {
        private static ZTGsmScanAntenna instance = null;
        public new static ZTGsmScanAntenna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTGsmScanAntenna();
                    }
                }
            }
            return instance;
        }
        public ZTGsmScanAntenna() : base()
        {
            init();
        }

        protected override void init()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_SCAN);
            carrierID = CarrierType.ChinaMobile;
            if (Condition != null)
            {
                Condition.ServiceTypes.Clear();
                Condition.ServiceTypes.Add((int)ServiceType.GSM_SCAN);
            }
        }

        public override string Name
        {
            get { return "GSM天线分析_Scan"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 15000, 15042, this.Name);
        }

        #region 统计流程
        protected override bool getConditionBeforeQuery()
        {
            ZTGsmAntenna.isScanStat = true;
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_C/I";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)">GSM扫频");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (mapOp2.CheckPointInRegion(tp.Longitude, tp.Latitude) && tp is ScanTestPoint_G)//进行天线分析算法运算
                {
                    dealNCellData(tp);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
            }
        }

        private void dealNCellData(TestPoint tp)
        {
            for (int i = 0; i < 30; i++)
            {
                short? bcch = (short?)(int?)tp["GSCAN_BCCH", i];
                byte? bsic = (byte?)(int?)tp["GSCAN_BSIC", i];
                if (bcch == null || bsic == null)
                {
                    return;
                }
                Cell mainCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short)bcch, (byte)bsic, tp.Longitude, tp.Latitude);
                if (mainCell == null || mainCell.Direction > 360)
                {
                    return;
                }
                double dAngleDiffRel = 0;
                double dAngLeDiffAbs = 0;
                double cellDistance = 0;
                ZTAntFuncHelper.CellCollection col = new ZTAntFuncHelper.CellCollection()
                {
                    Cell = mainCell,
                    TDCell = null,
                    LteCell = null
                };
                ZTAntFuncHelper.calcSampleAngle(col, tp.Longitude, tp.Latitude, out dAngleDiffRel, out dAngLeDiffAbs, out cellDistance);
                int iAngleDiffRel = (int)dAngleDiffRel;
                int iAngleDiffAbs = (int)dAngLeDiffAbs;

                if (cellDistance > CD.MAX_COV_DISTANCE_GSM)
                    return;

                short? rxlevSub = (short?)(float?)tp["GSCAN_RxLev", i];
                if (rxlevSub == null || rxlevSub > 0 || rxlevSub < -140)
                    return;

                short? c2i = (short?)(int?)tp["GSCAN_C/I", i];
                if (c2i == null)
                    return;

                setCellAngleData(tp, mainCell, cellDistance, iAngleDiffRel, iAngleDiffAbs, rxlevSub, c2i);
            }
        }

        private void setCellAngleData(TestPoint tp, Cell mainCell, double cellDistance, int iAngleDiffRel, int iAngleDiffAbs, short? rxlevSub, short? c2i)
        {
            CellAngleData cellAngleData;
            if (dicCellAngelData.ContainsKey(mainCell.Name))
            {
                cellAngleData = dicCellAngelData[mainCell.Name];
            }
            else
            {
                cellAngleData = new CellAngleData();
                cellAngleData.cell = mainCell;
                cellAngleData.strbscname = mainCell.BelongBTS.BelongBSC.Name;
                cellAngleData.strbtsname = mainCell.BelongBTS.Name;
                cellAngleData.strTime = tp.DateTime.ToString("yyyy-MM-dd");
                string strGridTypeName = "";
                cellAngleData.strcityname = strCityTypeName;
                cellAngleData.strgridname = "";
                isContainPoint(mainCell.Longitude, mainCell.Latitude, ref strGridTypeName);
                if (strGridTypeName == "")
                    strGridTypeName = "无网格号";
                cellAngleData.strgridname = strGridTypeName;
                cellAngleData.cellname = mainCell.Name;
                cellAngleData.strBand = ZTAntFuncHelper.getFreqType(mainCell.BCCH);
                cellAngleData.strCoverType = mainCell.BelongBTS.Type == BTSType.Indoor ? "室内" : "室外";
                cellAngleData.lac = mainCell.LAC;
                cellAngleData.ci = mainCell.CI;
                cellAngleData.cellLongitude = mainCell.Longitude;
                cellAngleData.cellLatitude = mainCell.Latitude;
                cellAngleData.bcch = mainCell.BCCH;
                cellAngleData.anaType = "";
                cellAngleData.ialtitude = mainCell.Altitude;
                cellAngleData.iangle_dir = (int)(mainCell.Direction);
                cellAngleData.iangle_ob = (int)(mainCell.Downword);

                LongLat ll = new LongLat();
                ll.fLongitude = (float)(mainCell.Longitude);
                ll.fLatitude = (float)(mainCell.Latitude);
                cellAngleData.longLatList = ZTAntFuncHelper.getCellEmulateCover(ll, cellAngleData.iangle_dir
                    , cellAngleData.ialtitude, cellAngleData.iangle_ob);
                dicCellAngelData[mainCell.Name] = cellAngleData;
            }
            cellAngleData.calcValue((int)rxlevSub, (int)c2i);
            calcCellInfo(ref cellAngleData, rxlevSub, (Byte?)(float?)500, (Byte?)(float?)500, c2i
                , cellDistance, 0, tp, iAngleDiffAbs);
            calcSectionInfo(ref cellAngleData, rxlevSub, (Byte?)(float?)500, (Byte?)(float?)500, c2i
                , cellDistance, 0, tp, iAngleDiffAbs);
            calcAngleInfo(ref cellAngleData, rxlevSub, (Byte?)(float?)500, (Byte?)(float?)500, c2i
                , cellDistance, 0, tp, iAngleDiffRel);
        }
        #endregion

        #region background处理
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.GSM扫频; }
        }
        #endregion
    }
}
