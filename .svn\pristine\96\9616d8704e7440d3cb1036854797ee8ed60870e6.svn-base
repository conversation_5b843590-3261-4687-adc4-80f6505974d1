﻿using MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class GetWorkParamsHelper_SX : GetWorkParamsHelper<GetWorkParamsHelper_SX>
    {
        protected override StationAcceptWorkParams getWorkParams()
        {
            return new StationAcceptWorkParams_SX();
        }

        protected override void setWorkParams(System.Data.DataRow row, StationAcceptWorkParams workParams)
        {
            //根据工参模板读取
            CellAcceptWorkParam_SX info = new CellAcceptWorkParam_SX();
            info.FillDataByExcel(row);
            if (string.IsNullOrEmpty(info.DistrictName))
            {
                throw (new Exception("地市名为空"));
            }
            StationAcceptWorkParams_SX workParamSum = (StationAcceptWorkParams_SX)workParams;
            workParamSum.AddWorkParamSum(info);
        }

        protected override bool judgeOtherInfo(System.Data.DataTable tb)
        {
            if (!tb.Columns.Contains("地市"))
            {
                reportInfo("表中不包含地市列");
                return false;
            }
            return true;
        }

        protected override bool upLoadWorkParams(StationAcceptWorkParams workParams, StationAcceptCondition condition)
        {
            UpLoadWorkParams_SX upLoadParams = new UpLoadWorkParams_SX(workParams);
            upLoadParams.Query();
            reportInfo("有效工参条数:" + upLoadParams.ValidCount);
            return upLoadParams.SaveInDBSuccess;
        }

        protected override StationAcceptWorkParams readWorkParamsFromDB(StationAcceptCondition condition)
        {
            WorkParamsQuery_SX query = new WorkParamsQuery_SX((StationAcceptCondition_SX)condition);
            query.Query();

            return query.WorkParams;
        }
    }

    public class BtsAcceptWorkParam_SX : BtsAcceptWorkParamBase<string>
    {
        public BtsAcceptWorkParam_SX(CellAcceptWorkParam_SX cellParam)
            : base(cellParam)
        {
            Longitude = cellParam.Longitude;
            Latitude = cellParam.Latitude;

            BtsName = cellParam.BtsName;
        }

        /// <summary>
        /// 文件名中不能包含中文,所以仅包含不带中文部分的基站名(用于匹配基站)
        /// BtsNameFull : 陕西-HLH-TCBM172TLD-40
        /// BtsName : HLH-TCBM172TLD-40
        /// </summary>
        public string BtsName { get; set; }

        private LTEBTS bts = null;
        public AnalyseType AnalysedType { get; set; } = AnalyseType.NeedAnalyse;

        public void AddCellParamsInfo(CellAcceptWorkParam_SX info)
        {
            CellWorkParamDic[info.CellNameKey] = info;
        }

        #region 处理工参
        /// <summary>
        /// 是否是从库里读取的待单验工参
        /// </summary>
        private bool isNewAdd;
        /// <summary>
        /// 待单验站点对应缓存的工参小区
        /// </summary>
        private List<LTECell> oldCells;
        #region 添加单验工参
        public override void LoadCurBtsWorkParam(CellManager cellManager)
        {
            //加载单验工参
            CellAcceptWorkParam_SX cellParam = CellWorkParams[0] as CellAcceptWorkParam_SX;
            int eci = cellParam.ENodeBID * 256 + cellParam.CellID;
            List<LTECell> tmpCells = cellManager.GetLTECellsByECI(eci);
            if (tmpCells != null)
            {
                oldCells = new List<LTECell>(tmpCells.ToArray());
                removeCell(tmpCells, cellManager);
            }
            isNewAdd = addCellInfoToCellManager(ref bts, cellManager);
        }

        /// <summary>
        /// 移除该小区的旧工参
        /// </summary>
        /// <param name="cells"></param>
        private void removeCell(List<LTECell> cells, CellManager cellManager)
        {
            if (cells == null)
            {
                return;
            }
            cells = new List<LTECell>(cells);
            foreach (LTECell oldCell in cells)
            {
                cellManager.Remove(oldCell.BelongBTS);
                foreach (LTECell lteCell in oldCell.BelongBTS.Cells)
                {
                    cellManager.Remove(lteCell);
                    foreach (LTEAntenna antenna in lteCell.Antennas)
                    {
                        cellManager.Remove(antenna);
                    }
                }
            }
        }

        /// <summary>
        /// 添加上传的工参
        /// </summary>
        /// <param name="btsParam"></param>
        /// <param name="bts"></param>
        /// <returns></returns>
        private bool addCellInfoToCellManager(ref LTEBTS bts, CellManager cellManager)
        {
            foreach (CellAcceptWorkParamBase info in CellWorkParams)
            {
                CellAcceptWorkParam_SX cellInfo = (CellAcceptWorkParam_SX)info;
                LTECell cell = CellManager.GetInstance().GetLTECellByECI(DateTime.Now, cellInfo.Eci);
                if (cell != null)
                {
                    bts = cell.BelongBTS;
                    return false;
                }
            }

            bts = new LTEBTS();
            int snapShotId = -1;

            #region 暂时动态添加工参到CellManager，稍后移除
            bts.Fill(snapShotId, 0, 2147483647);
            bts.Name = BtsName;
            bts.BTSID = ENodeBID;
            bts.Type = IsOutDoor ? LTEBTSType.Outdoor : LTEBTSType.Indoor;

            foreach (CellAcceptWorkParamBase info in CellWorkParams)
            {
                CellAcceptWorkParam_SX cellParamInfo = (CellAcceptWorkParam_SX)info;
                bts.Longitude = cellParamInfo.Longitude;
                bts.Latitude = cellParamInfo.Latitude;

                snapShotId--;
                LTECell cell = new LTECell();
                cell.Fill(snapShotId, 0, 2147483647);
                cell.BelongBTS = bts;
                cell.Name = cellParamInfo.CellNameKey;
                cell.TAC = cellParamInfo.Tac;
                cell.ECI = cellParamInfo.ENodeBID * 256 + cellParamInfo.CellID;
                cell.CellID = cellParamInfo.CellID;
                cell.PCI = cellParamInfo.Pci;
                cell.EARFCN = cellParamInfo.Earfcn;
                bts.AddCell(cell);
                cellManager.Add(cell);

                LTEAntenna antenna = new LTEAntenna();
                snapShotId--;
                antenna.Fill(snapShotId, 0, 2147483647);
                antenna.Cell = cell;
                antenna.Longitude = cellParamInfo.Longitude;
                antenna.Latitude = cellParamInfo.Latitude;
                antenna.Direction = (short)cellParamInfo.Direction;
                antenna.Downward = (short)cellParamInfo.Downward;
                antenna.Altitude = cellParamInfo.Altitude;
            }
            cellManager.Add(bts);
            #endregion

            return true;
        }
        #endregion

        #region 移除单验工参
        public override void RemoveCurBtsWorkParam(CellManager cellManager)
        {
            //移除单验工参
            removeNedAddedCell(bts, isNewAdd, cellManager);
            reCoverCells(oldCells, cellManager);
        }

        private void removeNedAddedCell(LTEBTS bts, bool isNewAdd, CellManager cellManager)
        {
            if (isNewAdd)//将动态添加的工参移除
            {
                foreach (LTECell cell in bts.Cells)
                {
                    cellManager.Remove(cell);
                    foreach (LTEAntenna ant in cell.Antennas)
                    {
                        cellManager.Remove(ant);
                    }
                }
                cellManager.Remove(bts);
            }
        }

        /// <summary>
        /// 恢复旧工参
        /// </summary>
        /// <param name="cells"></param>
        private void reCoverCells(List<LTECell> cells, CellManager cellManager)
        {
            if (cells != null)
            {
                List<LTEBTS> lteBtsList = cellManager.GetCurrentLTEBTSs();
                cells = new List<LTECell>(cells);
                foreach (LTECell oldCell in cells)
                {
                    foreach (LTECell lteCell in oldCell.BelongBTS.Cells)
                    {
                        cellManager.Add(lteCell);
                        foreach (LTEAntenna antenna in lteCell.Antennas)
                        {
                            cellManager.Add(antenna);
                        }
                    }

                    if (!lteBtsList.Contains(oldCell.BelongBTS))
                    {
                        cellManager.Add(oldCell.BelongBTS);
                    }
                }
            }
        }
        #endregion
        #endregion
    }

    [Serializable]
    public class CellAcceptWorkParam_SX : CellAcceptWorkParamBase
    {
        public virtual string Token { get { return string.Format("{0}-{1}", Tac, Eci); } }
        public DateTime UpdateTime { get; set; }
        public DateTime ImportTime { get; set; }
        public int Downward { get; set; }
        public int Direction { get; set; }
        public int Altitude { get; set; }
        public string Remark { get; set; }

        public int AnalysedType { get; set; }
        public string AnalysedTypeString { get; set; }
        public string UpdateTimeDesc { get; set; }
        public string ImportTimeDesc { get; set; }
        public bool Selected { get; set; }


        /// <summary>
        /// 文件名中不能包含中文,所以仅包含不带中文部分的基站名(用于匹配基站)
        /// BtsNameFull : 陕西-HLH-TCBM172TLD-40
        /// BtsName : HLH-TCBM172TLD-40
        /// </summary>
        public string BtsName { get; set; }
        ///// <summary>
        ///// 文件名中不能包含中文,所以仅包含不带中文部分,不带地库,电梯标识的小区名(用于匹配小区)
        ///// CellNameFull : 陕西-HLH-TCBM172TLD-40
        /////              : 陕西-HLH-电梯1-TCBM172TLD-40
        ///// CellName : TCBM172TLD-40
        /////          : TCBM172TLD-40
        ///// </summary>
        //public string CellName { get; set; }
        /// <summary>
        /// 部分站点包含地库或电梯,小区名不同,其他参数都相同(用作小区标识Key)
        /// CellNameFull : 陕西-HLH-TCBM172TLD-40
        ///              : 陕西-HLH-电梯1-TCBM172TLD-40
        /// CellName : HLH-TCBM172TLD-40
        ///          : HLH-电梯1-TCBM172TLD-40
        /// </summary>
        public string CellNameKey { get; set; }

        public virtual void FillDataByExcel(System.Data.DataRow row)
        {
            DistrictName = getValid(row, "地市");
            DistrictName = DistrictName.Replace("市", "");
            BtsNameFull = getValid(row, "NODE名称");
            ENodeBID = getValidIntData(getValid(row, "NodeBID"));
            Longitude = getValidDoubleData(getValid(row, "经度"));
            Latitude = getValidDoubleData(getValid(row, "纬度"));
            CoverTypeDes = getValid(row, "覆盖类型");
            CellNameFull = getValid(row, "小区名称");
            CellID = getValidIntData(getValid(row, "CellID"));
            SectorID = getValidIntData(getValid(row, "SectorID"));
            Tac = getValidIntData(getValid(row, "TAC"));
            Eci = ENodeBID * 256 + CellID;
            Pci = getValidIntData(getValid(row, "PCI"));
            Earfcn = getValidIntData(getValid(row, "频点"));
            Direction = (int)getValidDoubleData(getValid(row, "方向角"));
            Downward = (int)getValidDoubleData(getValid(row, "下倾角"));
            Altitude = (int)getValidDoubleData(getValid(row, "挂高"));
            UpdateTime= DateTime.Now;
            ImportTime = UpdateTime;

            CellNameKey = FileNameRuleHelper_SX.GetCellNameKeyByFull(CellNameFull);
        }

        protected string getValid(System.Data.DataRow row, string name)
        {
            try
            {
                if (row[name] == null)
                {
                    return "";
                }
                return row[name].ToString().Trim();
            }
            catch
            {
                throw (new Exception(name + "格式不正确"));
            }
        }

        protected int getValidIntData(string str)
        {
            if (int.TryParse(str, out int res))
            {
                return res;
            }
            return 0;
        }

        protected double getValidDoubleData(string str)
        {
            if (double.TryParse(str, out double res))
            {
                return res;
            }
            return 0;
        }

        public virtual void FillDataByDB(Package package)
        {
            DistrictName = package.Content.GetParamString();
            BtsNameFull = package.Content.GetParamString();
            ENodeBID = package.Content.GetParamInt();
            CellNameFull = package.Content.GetParamString();
            CellID = package.Content.GetParamInt();
            SectorID = package.Content.GetParamInt();
            Tac = package.Content.GetParamInt();
            Eci = package.Content.GetParamInt();
            Earfcn = package.Content.GetParamInt();
            Pci = package.Content.GetParamInt();
            int iLongitude = package.Content.GetParamInt();
            int iLatitude = package.Content.GetParamInt();
            Longitude = (double)iLongitude / 10000000;
            Latitude = (double)iLatitude / 10000000;
            CoverTypeDes = package.Content.GetParamString();
            Altitude = package.Content.GetParamInt();
            Direction = package.Content.GetParamInt();
            Downward = package.Content.GetParamInt();

            string[] btsStr = BtsNameFull.Split(new char[] { '-' }, 2);
            if (btsStr.Length == 2)
            {
                BtsName = btsStr[1];
            }
            CellNameKey = FileNameRuleHelper_SX.GetCellNameKeyByFull(CellNameFull);
            //string[] cellStr = CellNameKey.Split('-');
            //if (cellStr.Length == 3 || cellStr.Length == 4)
            //{
            //    CellName = cellStr[cellStr.Length - 2] + '-' + cellStr[cellStr.Length - 1];
            //}




            //string[] cellStr = CellNameFull.Split(new char[] { '-' }, 2);
            //if (cellStr.Length == 2)
            //{
            //    CellNameKey = cellStr[1];

            //    cellStr = CellNameKey.Split('-');
            //    if (cellStr.Length == 3 || cellStr.Length == 4)
            //    {
            //        CellName = cellStr[cellStr.Length - 2] + '-' + cellStr[cellStr.Length - 1];
            //    }
            //}
        }
    }

    public class StationAcceptWorkParams_SX : StationAcceptWorkParams
    {
        public Dictionary<string, Dictionary<string, BtsAcceptWorkParam_SX>> WorkParamSumDic { get; set; } = new Dictionary<string, Dictionary<string, BtsAcceptWorkParam_SX>>();

        public void setWorkParam(Dictionary<string, Dictionary<string, BtsAcceptWorkParamBase<string>>> workParams)
        {
            foreach (var districtWorkParams in WorkParamSumDic)
            {
                Dictionary<string, BtsAcceptWorkParamBase<string>> curBtsWorkParams = new Dictionary<string, BtsAcceptWorkParamBase<string>>();
                workParams.Add(districtWorkParams.Key, curBtsWorkParams);
                foreach (var btsWorkParams in districtWorkParams.Value)
                {
                    curBtsWorkParams.Add(btsWorkParams.Key, btsWorkParams.Value);
                }
            }
        }

        public void AddWorkParamSum(CellAcceptWorkParam_SX cellInfo)
        {
            if (!WorkParamSumDic.TryGetValue(cellInfo.DistrictName, out var btsInfoIDic))
            {
                btsInfoIDic = new Dictionary<string, BtsAcceptWorkParam_SX>();
                WorkParamSumDic.Add(cellInfo.DistrictName, btsInfoIDic);
            }
            if (!btsInfoIDic.TryGetValue(cellInfo.BtsNameFull, out var btsInfo))
            {
                btsInfo = new BtsAcceptWorkParam_SX(cellInfo);
                btsInfoIDic.Add(cellInfo.BtsNameFull, btsInfo);
            }
            btsInfo.AddCellParamsInfo(cellInfo);
        }
    }
}
