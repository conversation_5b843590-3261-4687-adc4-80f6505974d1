﻿namespace MasterCom.RAMS.Stat
{
    partial class NavigatorDockForm_B
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.popMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.open_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.close_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.fastObjectListView = new BrightIdeasSoftware.FastObjectListView();
            this.olvColumn1 = new BrightIdeasSoftware.OLVColumn();
            this.popMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.fastObjectListView)).BeginInit();
            this.SuspendLayout();
            // 
            // popMenu
            // 
            this.popMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.open_tsmi,
            this.close_tsmi});
            this.popMenu.Name = "popMenu";
            this.popMenu.Size = new System.Drawing.Size(101, 48);
            this.popMenu.Opening += new System.ComponentModel.CancelEventHandler(this.popMenu_Opening);
            // 
            // open_tsmi
            // 
            this.open_tsmi.Name = "open_tsmi";
            this.open_tsmi.Size = new System.Drawing.Size(100, 22);
            this.open_tsmi.Text = "打开";
            this.open_tsmi.Click += new System.EventHandler(this.open_tsmi_Click);
            // 
            // close_tsmi
            // 
            this.close_tsmi.Name = "close_tsmi";
            this.close_tsmi.Size = new System.Drawing.Size(100, 22);
            this.close_tsmi.Text = "关闭";
            this.close_tsmi.Click += new System.EventHandler(this.close_tsmi_Click);
            // 
            // fastObjectListView
            // 
            this.fastObjectListView.AllColumns.Add(this.olvColumn1);
            this.fastObjectListView.BackColor = System.Drawing.SystemColors.Window;
            this.fastObjectListView.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.fastObjectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn1});
            this.fastObjectListView.ContextMenuStrip = this.popMenu;
            this.fastObjectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.fastObjectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.fastObjectListView.FullRowSelect = true;
            this.fastObjectListView.HeaderWordWrap = true;
            this.fastObjectListView.IsNeedShowOverlay = false;
            this.fastObjectListView.Location = new System.Drawing.Point(0, 0);
            this.fastObjectListView.Name = "fastObjectListView";
            this.fastObjectListView.OwnerDraw = true;
            this.fastObjectListView.ShowGroups = true;
            this.fastObjectListView.ShowImagesOnSubItems = true;
            this.fastObjectListView.ShowItemToolTips = true;
            this.fastObjectListView.Size = new System.Drawing.Size(171, 376);
            this.fastObjectListView.TabIndex = 0;
            this.fastObjectListView.UseCompatibleStateImageBehavior = false;
            this.fastObjectListView.UseHotItem = true;
            this.fastObjectListView.UseTranslucentHotItem = true;
            this.fastObjectListView.UseTranslucentSelection = true;
            this.fastObjectListView.View = System.Windows.Forms.View.Details;
            this.fastObjectListView.VirtualMode = true;
            this.fastObjectListView.MouseClick += new System.Windows.Forms.MouseEventHandler(this.fastObjectListView_MouseClick);
            this.fastObjectListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.fastObjectListView_MouseDoubleClick);
            this.fastObjectListView.VisibleChanged += new System.EventHandler(this.FastObjectListView_VisibleChanged);
            // 
            // olvColumn1
            // 
            this.olvColumn1.HeaderFont = null;
            this.olvColumn1.IsTileViewColumn = true;
            this.olvColumn1.Text = "模板名称";
            this.olvColumn1.Width = 164;
            // 
            // NavigatorDockForm_B
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(171, 376);
            this.Controls.Add(this.fastObjectListView);
            this.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "NavigatorDockForm_B";
            this.Text = "报表模板管理";
            this.popMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.fastObjectListView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip popMenu;
        private System.Windows.Forms.ToolStripMenuItem open_tsmi;
        private System.Windows.Forms.ToolStripMenuItem close_tsmi;
        private BrightIdeasSoftware.FastObjectListView fastObjectListView;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
    }
}