﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteSignalToInterenceDataForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.txtRate = new DevExpress.XtraEditors.TextEdit();
            this.label7 = new System.Windows.Forms.Label();
            this.numSignalCellCount = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.btnCalAgin = new System.Windows.Forms.Button();
            this.label6 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.nuRSRPDIFF = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.dataGrid = new DevExpress.XtraGrid.GridControl();
            this.cmOutPutExcel = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.outPutExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtRate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSignalCellCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuRSRPDIFF)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGrid)).BeginInit();
            this.cmOutPutExcel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.txtRate);
            this.groupControl1.Controls.Add(this.label7);
            this.groupControl1.Controls.Add(this.numSignalCellCount);
            this.groupControl1.Controls.Add(this.label8);
            this.groupControl1.Controls.Add(this.btnCalAgin);
            this.groupControl1.Controls.Add(this.label6);
            this.groupControl1.Controls.Add(this.label2);
            this.groupControl1.Controls.Add(this.nuRSRPDIFF);
            this.groupControl1.Controls.Add(this.label1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(814, 64);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "参数设置";
            // 
            // txtRate
            // 
            this.txtRate.EditValue = "0.3";
            this.txtRate.Location = new System.Drawing.Point(482, 31);
            this.txtRate.Name = "txtRate";
            this.txtRate.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.txtRate.Size = new System.Drawing.Size(57, 21);
            this.txtRate.TabIndex = 16;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(376, 37);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(19, 14);
            this.label7.TabIndex = 15;
            this.label7.Text = "个";
            // 
            // numSignalCellCount
            // 
            this.numSignalCellCount.Location = new System.Drawing.Point(298, 34);
            this.numSignalCellCount.Name = "numSignalCellCount";
            this.numSignalCellCount.Size = new System.Drawing.Size(72, 22);
            this.numSignalCellCount.TabIndex = 14;
            this.numSignalCellCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSignalCellCount.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(201, 37);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(100, 14);
            this.label8.TabIndex = 13;
            this.label8.Text = "栅格小区干扰数≥";
            // 
            // btnCalAgin
            // 
            this.btnCalAgin.Location = new System.Drawing.Point(580, 32);
            this.btnCalAgin.Name = "btnCalAgin";
            this.btnCalAgin.Size = new System.Drawing.Size(75, 23);
            this.btnCalAgin.TabIndex = 12;
            this.btnCalAgin.Text = "刷新数据";
            this.btnCalAgin.UseVisualStyleBackColor = true;
            this.btnCalAgin.Click += new System.EventHandler(this.btnCalAgin_Click);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(412, 35);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(64, 14);
            this.label6.TabIndex = 9;
            this.label6.Text = "干扰指数≥";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(174, 36);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(21, 14);
            this.label2.TabIndex = 5;
            this.label2.Text = "dB";
            // 
            // nuRSRPDIFF
            // 
            this.nuRSRPDIFF.Location = new System.Drawing.Point(96, 34);
            this.nuRSRPDIFF.Name = "nuRSRPDIFF";
            this.nuRSRPDIFF.Size = new System.Drawing.Size(72, 22);
            this.nuRSRPDIFF.TabIndex = 4;
            this.nuRSRPDIFF.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.nuRSRPDIFF.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 37);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(76, 14);
            this.label1.TabIndex = 3;
            this.label1.Text = "干扰场强差≤";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.dataGrid);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 64);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(814, 335);
            this.groupControl2.TabIndex = 1;
            this.groupControl2.Text = "结果列表";
            // 
            // dataGrid
            // 
            this.dataGrid.ContextMenuStrip = this.cmOutPutExcel;
            this.dataGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGrid.Location = new System.Drawing.Point(2, 23);
            this.dataGrid.MainView = this.gridView1;
            this.dataGrid.Name = "dataGrid";
            this.dataGrid.Size = new System.Drawing.Size(810, 310);
            this.dataGrid.TabIndex = 0;
            this.dataGrid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.dataGrid.Click += new System.EventHandler(this.dataGrid_Click);
            // 
            // cmOutPutExcel
            // 
            this.cmOutPutExcel.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.outPutExcel});
            this.cmOutPutExcel.Name = "cmOutPutExcel";
            this.cmOutPutExcel.Size = new System.Drawing.Size(125, 26);
            // 
            // outPutExcel
            // 
            this.outPutExcel.Name = "outPutExcel";
            this.outPutExcel.Size = new System.Drawing.Size(124, 22);
            this.outPutExcel.Text = "导出Excel";
            this.outPutExcel.Click += new System.EventHandler(this.outPutExcel_Click);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7});
            this.gridView1.GridControl = this.dataGrid;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "ISN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 85;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "小区名称";
            this.gridColumn2.FieldName = "StrCellName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 140;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "TAC";
            this.gridColumn3.FieldName = "ITac";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 111;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "ECI";
            this.gridColumn4.FieldName = "IEci";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 111;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "受干扰栅格数";
            this.gridColumn5.FieldName = "ISignalCellGridCount";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            this.gridColumn5.Width = 111;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "所有覆盖栅格数";
            this.gridColumn6.FieldName = "ICellGridCount";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 111;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "覆盖干扰指数";
            this.gridColumn7.FieldName = "StrSignalRate";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            this.gridColumn7.Width = 120;
            // 
            // LteSignalToInterenceDataForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(814, 399);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Name = "LteSignalToInterenceDataForm";
            this.Text = "LTE小区干扰指数";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtRate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSignalCellCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuRSRPDIFF)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGrid)).EndInit();
            this.cmOutPutExcel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraGrid.GridControl dataGrid;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown nuRSRPDIFF;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Button btnCalAgin;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numSignalCellCount;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.ContextMenuStrip cmOutPutExcel;
        private System.Windows.Forms.ToolStripMenuItem outPutExcel;
        private DevExpress.XtraEditors.TextEdit txtRate;
    }
}