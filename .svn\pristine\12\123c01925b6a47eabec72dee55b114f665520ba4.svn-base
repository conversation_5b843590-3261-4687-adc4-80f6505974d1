﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.KPIReport
{
    public class CellCoverageAnaQuery : CoverTestPointQuery
    {
        private AreaBase village { get; set; }

        public CAreaCellCoverageInfo AreaCellCoverage { get; set; }

        public CellCoverageAnaQuery(AreaBase areaSum, List<AreaBase> areas)
            : base(areas)
        {
            this.village = areaSum;
            AreaCellCoverage = new CAreaCellCoverageInfo(this.village);
        }
       
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "N_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DM_RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_NC_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_NC_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_NC_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_gsm_SC_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_gsm_SC_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_gsm_SC_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_gsm_SC_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_gsm_DM_RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_gsm_NC_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_gsm_NC_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_gsm_NC_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_td_SC_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_td_SC_CellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_td_SC_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_td_SC_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_td_DM_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_td_NCell_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_td_NCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_td_NCell_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SNeiFreq";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SNeiPSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SNeiRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_Reference_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SNeiFreq";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SNeiPSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_wcdma_SNeiRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_NCell_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_NCell_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_NCell_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_Channel";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PSS_RP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"TD_PCCPCH_RSCP");
            tmpDic.Add("themeName", (object)"TD_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void doWithDTData(MasterCom.RAMS.Model.TestPoint tp)
        {
            TestPointBaseDeal dealer = TestPointFactory.CreateTestPointDeal(tp);
            if (dealer == null) return;

            if (tp is ScanTestPoint_TD ||
                tp is ScanTestPoint_LTE ||
                tp is TestPointScan || tp is ScanTestPoint_G)
            {
                for (int idx = 0; idx < 20; idx++)
                {
                    ICell nbCell = tp.GetNBCell(idx);
                    addCell(nbCell, tp, Convert.ToSingle(dealer.GetNbRxlev(tp, idx)), dealer.GetLAC(tp), dealer.GetCI(tp));
                }
            }
            else
            {
                ICell mainCell = tp.GetMainCell();
                addCell(mainCell, tp, Convert.ToSingle(dealer.GetRxlev(tp)), dealer.GetLAC(tp), dealer.GetCI(tp));
            }
        }

        private void addCell(ICell cell, TestPoint tp, float rxlev, object lac, object ci)
        {
            if (cell != null && rxlev >= -141 && rxlev <= -10)
            {
                AreaCellCoverage.AddCell(cell, rxlev, tp, lac, ci);
            }
        }
    }

    public class CAreaCellCoverageInfo
    {
        public AreaBase Village { get; set; }

        public Dictionary<ICell, CAreaCellInfo> AreaCellMap { get; set; }

        public CAreaCellCoverageInfo(AreaBase village)
        {
            this.Village = village;
            this.AreaCellMap = new Dictionary<ICell, CAreaCellInfo>();
        }

        public void AddCell(ICell cell, float rxlev, TestPoint tp, object lac, object ci)
        {
            CAreaCellInfo areaCellInfo;
            if (!AreaCellMap.TryGetValue(cell, out areaCellInfo))
            {
                areaCellInfo = new CAreaCellInfo(Village, cell);
                AreaCellMap[cell] = areaCellInfo;
            }
            areaCellInfo.AddTestPnt(tp, rxlev);
        }
    }

    public class CAreaCellInfo : IComparable<CAreaCellInfo>
    {
        public AreaBase Village { get; set; }

        public ICell AreaCell { get; set; }

        public List<TestPoint> TestPntVec { get; set; }

        public List<double> DistanceVec { get; set; }

        public float FMaxRxlev { get; set; }

        public float FMinRxlev { get; set; }

        private float fMeanRxlev = 0;
        public float FMeanRxlev
        {
            get
            {
                if (TestPntVec.Count == 0)
                {
                    return 0;
                }
                return (float)Math.Round(fMeanRxlev / TestPntVec.Count, 2);
            }
            set { fMeanRxlev = value; }
        }

        public int SampleNum { get { return TestPntVec.Count; } }

        public double Longitude { get { return AreaCell.Longitude; } }

        public double Latitude { get { return AreaCell.Latitude; } }

        public object LAC
        {
            get
            {
                if (this.AreaCell is Cell)
                {
                    return (AreaCell as Cell).LAC;
                }
                else if (this.AreaCell is TDCell)
                {
                    return (AreaCell as TDCell).LAC;
                }
                else if (this.AreaCell is LTECell)
                {
                    return (AreaCell as LTECell).TAC;
                }
                else if (this.AreaCell is WCell)
                {
                    return (AreaCell as WCell).LAC;
                }
                else if (this.AreaCell is CDCell)
                {
                    return (AreaCell as CDCell).LAC;
                }
                return null;
            }
        }

        public object CI
        {
            get
            {
                if (this.AreaCell is Cell)
                {
                    return (AreaCell as Cell).CI;
                }
                else if (this.AreaCell is TDCell)
                {
                    return (AreaCell as TDCell).CI;
                }
                else if (this.AreaCell is LTECell)
                {
                    return (AreaCell as LTECell).ECI;
                }
                else if (this.AreaCell is WCell)
                {
                    return (AreaCell as WCell).CI;
                }
                else if (this.AreaCell is CDCell)
                {
                    return (AreaCell as CDCell).CI;
                }
                return null;
            }
        }

        public string CellName { get { return AreaCell.Name; } }

        public string VillageName { get { return Village.Name; } }

        public double Distance
        {
            get
            {
                if (DistanceVec.Count == 0)
                    return -10000000;

                double sum = 0;
                foreach (double dis in DistanceVec)
                {
                    sum += dis;
                }
                return Math.Round(sum / DistanceVec.Count, 2);
            }
        }

        public CAreaCellInfo(AreaBase village, ICell cell)
        {
            this.Village = village;
            this.AreaCell = cell;
            this.TestPntVec = new List<TestPoint>();
            this.DistanceVec = new List<double>();

            this.FMaxRxlev = float.MinValue;
            this.FMinRxlev = float.MaxValue;
            this.FMeanRxlev = 0;
        }

        public void AddTestPnt(TestPoint tp, float rxlev)
        {
            this.TestPntVec.Add(tp);
            this.DistanceVec.Add(MathFuncs.GetDistance(tp.Longitude, tp.Latitude, AreaCell.Longitude, AreaCell.Latitude));

            FMaxRxlev = FMaxRxlev > rxlev ? FMaxRxlev : rxlev;
            FMinRxlev = FMinRxlev < rxlev ? FMinRxlev : rxlev;
            fMeanRxlev += rxlev;
        }

        #region IComparable<CAreaCellInfo> 成员

        public int CompareTo(CAreaCellInfo other)
        {
            return other.SampleNum.CompareTo(this.SampleNum);
        }

        #endregion
    }
}
