﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class ScanMultiCoverageGridInfo
    {
        protected ScanMultiCoverageGridInfo(string mgrsString)
        {
            MgrsString = mgrsString;
            double tlLng, tlLat, brLng, brLat;
            MgrsGridConverter.GetGridLngLat(mgrsString, out tlLng, out tlLat, out brLng, out brLat);
            TLLng = tlLng;
            TLLat = tlLat;
            BRLng = brLng;
            BRLat = brLat;
            CentLng = (TLLng + BRLng) / 2;
            CentLat = (TLLat + BRLat) / 2;
            SampleList = new List<MgrsTestPoint>();
        }

        public string MgrsString { get; private set; }
        public double TLLng { get; private set; }
        public double TLLat { get; private set; }
        public double BRLng { get; private set; }
        public double BRLat { get; private set; }
        public double CentLng { get; private set; }
        public double CentLat { get; private set; }
        public int TestPointCount { get; private set; }
        public int InvalidPointCount { get; private set; }

        public string DetailInfo
        {
            get
            {
                return string.Format("栅格编号: {0}\r\n采样点总数: {1}\r\n无效点数: {2}\r\n",
                    MgrsString, TestPointCount, InvalidPointCount);
            }
        }

        public Color GridColor { get; set; }
        public string StrGrid { get; set; }
        public int Coverage { get; set; }

        public double? GridAvgRSRP { get; set; }
        public double? GridAvgSINR { get; set; }

        public List<MgrsTestPoint> SampleList { get; private set; }

        public string CoverageCell { get; set; }
        public List<MgrsCell> CoverageCellList { get; set; }
        /// <summary>
        /// 返回按平均RSRP由大到小排序的List
        /// </summary>
        public List<MgrsCell> CellList { get; set; }

        public virtual void DoWithTestPoint(TestPoint tp)
        {
            var point = getPointInfo();
            if (point != null)
            {
                point.FillData(tp, MgrsString);
                SampleList.Add(point);
            }
        }

        protected virtual MgrsTestPoint getPointInfo()
        {
            return default;
        }

        public void Calculate(ScanMultiCoverageCondition curCondition)
        {
            if (string.IsNullOrEmpty(StrGrid))
            {
                StrGrid = "未匹配到网格";
            }

            var dic = getCells(SampleList);

            foreach (var item in dic.Values)
            {
                item.Calculate();
            }

            CellList = new List<MgrsCell>(dic.Values);
            CellList.Sort();
            CellList.Reverse();

            doWithAllCell(curCondition);

            GridAvgRSRP = getValidData((a) => { return a.Rsrp; });
            GridAvgSINR = getValidData((a) => { return a.Sinr; });
        }

        #region 获取小区信息
        protected virtual Dictionary<ICell, MgrsCell> getCells(List<MgrsTestPoint> testPoints)
        {
            var cellDic = new Dictionary<ICell, MgrsCell>();

            foreach (var curPoint in testPoints)
            {
                for (int i = 0; i < curPoint.CellInfoList.Count; ++i)
                {
                    var info = curPoint.CellInfoList[i];
                    ICell cell = getCell(curPoint, i);
                    if (cell == null)
                    {
                        cell = new UnknowCell(info.Key);
                    }

                    addMgrsCell(cellDic, info, cell, curPoint);
                }
            }

            return cellDic;
        }

        protected virtual void addMgrsCell(Dictionary<ICell, MgrsCell> cellDic, MgrsTestPoint.CellInfo info
            , ICell cell, MgrsTestPoint point)
        {
            if (!cellDic.TryGetValue(cell, out var mgrsCell))
            {
                mgrsCell = getCellInfo();
                if (mgrsCell == null)
                {
                    return;
                }

                if (cell is UnknowCell)
                {
                    mgrsCell.FillData(info.Arfcn, info.Pci);
                }
                else
                {
                    mgrsCell.FillData(cell);
                }
                cellDic.Add(cell, mgrsCell);
            }

            mgrsCell.SetTime(point.Time);
            mgrsCell.Rsrp.Add(info.Rsrp);
            mgrsCell.Sinr.Add(info.Sinr);
        }

        protected virtual MgrsCell getCellInfo()
        {
            return default;
        }

        protected virtual ICell getCell(MgrsTestPoint curPoint, int index)
        {
            return default;
        }
        #endregion

        #region 计算重叠覆盖
        protected virtual void doWithAllCell(ScanMultiCoverageCondition curCondition)
        {
            try
            {
                CoverageCellList = getCoverageCell(curCondition, CellList);
                CoverageCell = getCoverageCellDesc();
            }
            catch
            {
                Coverage = 0;
            }
        }

        protected virtual List<MgrsCell> getCoverageCell(ScanMultiCoverageCondition curCondition, List<MgrsCell> cellList)
        {
            List<MgrsCell> coverageCell = new List<MgrsCell>();
            return coverageCell;
        }

        protected string getCoverageCellDesc()
        {
            StringBuilder strCell = new StringBuilder();
            if (this.CoverageCellList == null)
                return strCell.ToString();
            if (this.CoverageCellList.Count == 0)
                return "无效栅格";
            for (int i = 0; i < CoverageCellList.Count; i++)
            {
                strCell.Append($"{CoverageCellList[i].CellName};");
            }
            return strCell.ToString();
        }
        #endregion

        protected delegate AvgInfo Func(MgrsCell cellInfo);

        protected double? getValidData(Func func)
        {
            if (CellList.Count == 0)
                return null;
            AvgInfo info = new AvgInfo();
            foreach (var cell in CellList)
            {
                var curInfo = func(cell);
                info.Add(curInfo);
            }
            if (info.Count == 0)
                return null;
            info.Calculate();
            return info.Avg;
        }

        public void clearData()
        {
            //this.CellList.Clear();
            this.SampleList.Clear();
        }
    }

    public abstract class MgrsTestPoint
    {
        public void FillData(TestPoint tp, string mgrsString)
        {
            MgrsString = mgrsString;
            Longitude = tp.Longitude;
            Latitude = tp.Latitude;
            Time = tp.DateTime;
            addCellInfo(tp);
        }

        public string MgrsString { get; private set; }
        public double Longitude { get; private set; }
        public double Latitude { get; private set; }
        public DateTime Time { get; private set; }
        public List<CellInfo> CellInfoList { get; private set; } = new List<CellInfo>();

        protected abstract void addCellInfo(TestPoint tp);

        public class CellInfo
        {
            public float? Rsrp { get; set; }
            public float? Sinr { get; set; }
            public int Arfcn { get; set; }
            public int Pci { get; set; }

            public string Key { get { return $"{Arfcn}_{Pci}"; } }
        }
    }

    public abstract class MgrsCell : IComparable<MgrsCell>
    {
        public void FillData(ICell cell)
        {
            this.Cell = cell;
            SetInfos();
        }

        protected virtual void SetInfos()
        {
            Longitude = Cell.Longitude;
            Latitude = Cell.Latitude;
        }

        public AvgInfo Rsrp { get; set; } = new AvgInfo();
        public AvgInfo Sinr { get; set; } = new AvgInfo();

        public ICell Cell { get; protected set; }

        //最近测试点时间，用于关联当前时间点的工参（剔除多层网）
        public DateTime Time { get; protected set; }

        public string CellName { get; protected set; }

        public string Tac { get; protected set; }

        public string Ci { get; protected set; }

        public int Arfcn { get; protected set; }

        public int Pci { get; protected set; }

        public double? Longitude { get; protected set; }

        public double? Latitude { get; protected set; }

        public void FillData(int arfcn, int pci)
        {
            this.Cell = null;
            CellName = arfcn + "-" + pci;
            Arfcn = arfcn;
            Pci = pci;
        }

        public void SetTime(DateTime time)
        {
            if (time > Time)
                Time = time;
        }

        public void Calculate()
        {
            Rsrp.Calculate();
            Sinr.Calculate();
        }

        public int CompareTo(MgrsCell other)
        {
            return Rsrp.Avg.CompareTo(other.Rsrp.Avg);
        }
    }
}
