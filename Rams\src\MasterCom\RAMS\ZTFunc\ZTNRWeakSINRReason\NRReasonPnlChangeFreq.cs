﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRReasonPnlChangeFreq : NRReasonPanelBase
    {
        public NRReasonPnlChangeFreq()
        {
            InitializeComponent();
        }

        public override void AttachReason(NRReasonBase reason)
        {
            base.AttachReason(reason);
            timePersist.ValueChanged -= timePersist_ValueChanged;
            timePersist.Value = NRWeakSINRReason.timeLimit;
            timePersist.ValueChanged += timePersist_ValueChanged;
            numDistanceLimit.ValueChanged -= numDistanceLimit_ValueChanged;
            numDistanceLimit.Value = NRWeakSINRReason.distanceLimit;
            numDistanceLimit.ValueChanged += numDistanceLimit_ValueChanged;
            numHandoverCount.ValueChanged -= numHandoverCount_ValueChanged;
            numHandoverCount.Value = NRWeakSINRReason.handoverCount;
            numHandoverCount.ValueChanged += numHandoverCount_ValueChanged;
        }

        void timePersist_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.timeLimit = (int)timePersist.Value;
        }
        void numDistanceLimit_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.distanceLimit = (int)numDistanceLimit.Value;
        }
        void numHandoverCount_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.handoverCount = (int)numHandoverCount.Value;
        }
    }
}
