﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class PhoneInfoAnaByFile : PhoneInfoAnaByRegion
    {
        protected PhoneInfoAnaByFile()
            : base()
        {
        }

        private static PhoneInfoAnaByFile intance = null;
        public static new PhoneInfoAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new PhoneInfoAnaByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "号码信息(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                string fileName = findPairFileName(file);

                PhoneInfoItem phoneItem = new PhoneInfoItem(file.FileName, fileName);

                dowithData(phoneItem, file);

                if (!string.IsNullOrEmpty(phoneItem.PairPhoneNumber) && !phoneInfoList.Contains(phoneItem))
                {
                    phoneItem.SN = phoneInfoList.Count + 1;
                    phoneInfoList.Add(phoneItem);
                }
            }
        }

        private string findPairFileName(DTFileDataManager fileMngr)
        {
            string fileName = "";
            DIYQueryPeerFileInfo qryFileInfo = new DIYQueryPeerFileInfo(MainModel, fileMngr.LogTable, fileMngr.FileID, fileMngr.FileName);
            qryFileInfo.Query();
            Dictionary<FileInfo, FileInfo> peerFileInfoDic = qryFileInfo.PeerFileInfoDic;
            if (peerFileInfoDic != null)
            {
                foreach (FileInfo file in peerFileInfoDic.Values)
                {
                    if (file.EventCount == fileMngr.FileID)
                    {
                        fileName = file.Name;
                    }
                }
            }
            return fileName;
        }
    }

    public class PhoneInfoAnaByFile_FDD : PhoneInfoAnaByFile
    {
        private static PhoneInfoAnaByFile_FDD instance = null;
        public static new PhoneInfoAnaByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new PhoneInfoAnaByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected PhoneInfoAnaByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get
            {
                return "LTE_FDD号码信息(按文件)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26073, this.Name);
        }
    }
}
