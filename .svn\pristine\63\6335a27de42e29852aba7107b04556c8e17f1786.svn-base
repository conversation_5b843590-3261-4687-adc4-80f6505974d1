﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.BaseInfo;

namespace MasterCom.RAMS.Model
{
    public partial class NewRoleDlg : BaseDialog
    {
        public NewRoleDlg()
        {
            InitializeComponent();
        }

        public NewRoleDlg(int districtID)
            : this()
        {
            this.cmbDistrict.Items.Clear();
            List<Stat.IDNamePair> districtSet = DistrictManager.GetInstance().GetAvailableDistrict();
            foreach (Stat.IDNamePair d in districtSet)
            {
                if (districtID == -1 || d.id == districtID)
                {
                    this.cmbDistrict.Items.Add(d);
                }
            }
        }

        public DataSourceRole Role
        {
            get
            {
                DataSourceRole role = new DataSourceRole();
                role.DistrictID = ((Stat.IDNamePair)(this.cmbDistrict.SelectedItem)).id;
                role.Name = this.txtRoleName.Text;
                role.Description = this.txtRoleDesc.Text;
                role.ID = PermissionManager.GetInstance().GetNewDataSourceRoleID(role.DistrictID);
                return role;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (cmbDistrict.SelectedIndex == -1)
            {
                MessageBox.Show("请选择权限组所属地市！");
                return;
            }
            string name = txtRoleName.Text;
            if (string.IsNullOrEmpty(name))
            {
                MessageBox.Show("权限组名称不能为空！");
                return;
            }
            DialogResult = DialogResult.OK;
        }


    }
}
