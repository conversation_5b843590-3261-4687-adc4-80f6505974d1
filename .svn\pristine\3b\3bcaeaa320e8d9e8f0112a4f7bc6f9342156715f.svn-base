﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using Message = MasterCom.RAMS.Model.Message;
using MasterCom.RAMS.ZTFunc.ZTVolteStatDelayAna;
using MasterCom.RAMS.ZTFunc.ZTCsfbCallStat;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteStatDelayAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        readonly List<int> moCallAttemptEventIds = new List<int> { 1070, 3609 };
        readonly List<int> mtCallAttemptEventIds = new List<int> { 1071, 3616 };

        protected static readonly object lockObj = new object();
        private static VolteStatDelayAnaByRegion instance = null;
        private List<VolteItemsInfo> listVolteItemsInfo = null;
        public static VolteStatDelayAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteStatDelayAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected VolteStatDelayAnaByRegion()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }


        public override string Name
        {
            get
            {
                return "VOLTE时延分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27004, this.Name);
        }

        protected bool checkDelay = true;
        protected int maxDelaySec = 180;
        StringBuilder strbErr = null;

        protected override bool getCondition()
        {
            CallConditionDlg dlg = new CallConditionDlg();
            dlg.SetCondition(checkDelay, maxDelaySec);
            strbErr = new StringBuilder();
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out checkDelay, out maxDelaySec);
            callStatList = new List<VoltePairsInfo>();
            listVolteItemsInfo = new List<VolteItemsInfo>();
            return true;
        }

        protected override void fireShowForm()
        {
            if (strbErr.Length > 0 )
            {
                XtraMessageBox.Show(strbErr.ToString());
            }
            if (listVolteItemsInfo == null || listVolteItemsInfo.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            VolteStatDelayInfoForm frm = MainModel.CreateResultForm(typeof(VolteStatDelayInfoForm)) as VolteStatDelayInfoForm;
            frm.FillData(listVolteItemsInfo);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            callStatList = null;
            listVolteItemsInfo = null;
        }

        /// <summary>
        /// 先进行主被叫关联
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            Dictionary<FileInfo, bool> fileAdded = new Dictionary<FileInfo, bool>();
            relateMoMtFile(moMtPair, fileAdded);

            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                    && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileInfo.Momt == 2 && !fileAdded.ContainsKey(fileInfo))
                {
                    moMtPair[fileInfo] = GetPairFile(fileInfo);
                }
            }

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private void relateMoMtFile(Dictionary<FileInfo, FileInfo> moMtPair, Dictionary<FileInfo, bool> fileAdded)
        {
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileInfo.Momt == 1)
                {//主叫关联被叫
                    FileInfo mtFile = MainModel.FileInfos.Find(delegate (FileInfo x) { return x.ID == fileInfo.EventCount; });
                    if (mtFile == null)//选中的文件中关联不到主被叫文件，再在服务段关联查询
                    {
                        mtFile = GetPairFile(fileInfo);
                    }

                    moMtPair[fileInfo] = mtFile;
                    if (mtFile != null)
                    {
                        fileAdded[mtFile] = true;
                    }
                }
            }
        }

        private FileInfo GetPairFile(FileInfo file)
        {
            FileInfo filePair = null;
            DIYQueryPeerFileInfo qryFileInfo = new DIYQueryPeerFileInfo(MainModel, file.LogTable, file.ID, file.Name,false);
            qryFileInfo.Query();
            if (qryFileInfo.PeerFileInfoDic != null)
            {
                foreach (FileInfo value in qryFileInfo.PeerFileInfoDic.Values)
                {
                    if (value != null)
                    {
                        return value;
                    }
                }
            }
            else
            {
                if (strbErr.Length == 0)
                {
                    strbErr.AppendLine("以下文件未能找到对应的主被叫文件：");
                }
                strbErr.AppendLine(file.Name);
            }
            return filePair;
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == 1)
                {
                    moFile = file;
                }
                else
                {
                    mtFile = file;
                }
            }

            int lastMoMsgIdx = 0;
            int lastMtEventIdx = 0;
            int lastMtMsgIdx = 0;
            if (moFile != null)
            {
                try
                {
                    dealMoFile(moFile, mtFile, lastMoMsgIdx, lastMtEventIdx, lastMtMsgIdx);
                }
                catch (Exception e)
                {
                    MessageBox.Show("文件：" + moFile.FileName + Environment.NewLine + e.Message + Environment.NewLine + e.StackTrace);
                }
            }
            else if (mtFile != null)
            {
                try
                {
                    dealMtFile(mtFile, lastMtMsgIdx);
                }
                catch (Exception e)
                {
                    MessageBox.Show("文件：" + mtFile.FileName + Environment.NewLine + e.Message + Environment.NewLine + e.StackTrace);
                }
            }
        }

        private void dealMoFile(DTFileDataManager moFile, DTFileDataManager mtFile, int lastMoMsgIdx, int lastMtEventIdx, int lastMtMsgIdx)
        {
            VolteCallInfo singleCall = null;
            for (int i = 0; i < moFile.Events.Count; i++)
            {
                Event evt = moFile.Events[i];
                if (moCallAttemptEventIds.Contains(evt.ID) || mtCallAttemptEventIds.Contains(evt.ID))
                {
                    singleCall = new VolteCallInfo(moFile.FileName, moFile.MoMtFlag);
                    singleCall.AddEvent(evt);
                }
                else if (singleCall != null)
                {//保存singlecall的事件
                    bool isAdded = singleCall.AddEvent(evt);
                    if (isAdded)
                    {
                        addTpMsgInfo2Call(singleCall, moFile, ref lastMoMsgIdx);
                        VolteCallInfo mtCall = getMtCallInfo(singleCall, mtFile, ref lastMtEventIdx, ref lastMtMsgIdx);
                        saveCallInfo(singleCall, mtCall);
                        singleCall = null;//清空
                    }
                }
            }
        }

        private void dealMtFile(DTFileDataManager mtFile, int lastMtMsgIdx)
        {
            VolteCallInfo singleCall = null;
            for (int i = 0; i < mtFile.Events.Count; i++)
            {
                Event evt = mtFile.Events[i];
                if (mtCallAttemptEventIds.Contains(evt.ID))
                {
                    singleCall = new VolteCallInfo(mtFile.FileName, mtFile.MoMtFlag);
                    singleCall.AddEvent(evt);
                }
                else if (singleCall != null)
                {//保存singlecall的事件
                    bool isAdded = singleCall.AddEvent(evt);
                    if (isAdded)
                    {
                        addTpMsgInfo2Call(singleCall, mtFile, ref lastMtMsgIdx);
                        saveCallInfo(null, singleCall);
                        singleCall = null;//清空
                    }
                }
            }
        }

        private List<VoltePairsInfo> callStatList = null;
        private void saveCallInfo(VolteCallInfo moCall, VolteCallInfo mtCall)
        {
            VoltePairsInfo info = new VoltePairsInfo(moCall, mtCall);

            if (moCall != null)
            {
                if (moCall.MsgRinging180 != null)
                {
                    moCall.PhoneNum = getPhoneNum(moCall.MsgRinging180, "sip.from.user");
                    if (mtCall != null)
                    {
                        mtCall.PhoneNum = getPhoneNum(moCall.MsgRinging180, "sip.to.host");
                    }
                }
                else if (mtCall != null && mtCall.MsgRinging180 != null)
                {
                    mtCall.PhoneNum = getPhoneNum(mtCall.MsgRinging180, "sip.to.user");
                    moCall.PhoneNum = getPhoneNum(mtCall.MsgRinging180, "sip.from.host");
                }
            }
            else if (mtCall != null && mtCall.MsgRinging180 != null)
            {
                mtCall.PhoneNum = getPhoneNum(mtCall.MsgRinging180, "sip.to.host");
            }

            callStatList.Add(info);
        }
        private string getPhoneNum(Message msg, string strFieldName)
        {
            string phoneNumber = MessageDecodeHelper.GetMsgSingleString(msg, strFieldName);
            return phoneNumber.Replace("\0", "").Trim();
        }

        public static uint[] getPagingTmsiList(Message msg)
        {
            return MessageDecodeHelper.GetMsgMultiUInt(msg, "lte-rrc.pagingRecordList", "lte-rrc.m_TMSI_Uint");
        }
        private void addTpMsgInfo2Call(VolteCallInfo call, DTFileDataManager file, ref int lastMsgIdx)
        {
            if(file.Messages.Count<=0)
            {
                return;
            }
            bool began = false;
            Event beginEvent = call.ListEvents[0];
            Event endEvent = call.ListEvents[call.ListEvents.Count - 1];
            int beginIdx = lastMsgIdx;
            for (; beginIdx >= 0; beginIdx++)
            {
                Message msg = file.Messages[beginIdx];
                if ((beginEvent.DateTime - msg.DateTime).TotalSeconds <= 10)
                {//取call attempt前10秒内的信令
                    break;
                }
            }
            if (beginIdx < 0)
            {
                beginIdx = 0;
            }

            for (int i = beginIdx; i < file.Messages.Count; i++)
            {
                Message msg = file.Messages[i];
                if (msg.SN > endEvent.SN)
                {
                    lastMsgIdx = i;
                    break;
                }
                if (msg.DateTime >= beginEvent.DateTime)
                {
                    began = true;
                }
                call.ListMessage.Add(msg);
                if (msg.ID == 1093626113 && msg.MoMtFlag == 2 && !began)//VOLTE MT: RRC connection request
                {
                    call.MsgRRCConnectionRequest = msg;
                    uint rrcTmsi = MessageDecodeHelper.GetMsgSingleUInt(msg, "lte-rrc.m_TMSI_Uint");
                    for (int j = i - 1; j >= 0; j--)
                    {
                        Message msg1 = file.Messages[j];
                        if (msg1.ID == 1093625344)//VOLTE MT: Paging
                        {
                            uint[] tmsiList = getPagingTmsiList(msg1);
                            foreach (uint tmsi in tmsiList)
                            {
                                if (tmsi == rrcTmsi)
                                {
                                    call.MsgPaging = msg1;
                                    break;
                                }
                            }
                        }
                        if (call.MsgPaging != null || (msg.DateTime - msg1.DateTime).TotalSeconds > 10)
                        {
                            break;
                        }
                    }
                    continue;
                }
                else if (msg.ID == 1107689472 && call.MsgIMS_SIP_INVITE == null)//IMS_SIP_INVITE
                {
                    began = true;
                    call.MsgIMS_SIP_INVITE = msg;
                    continue;
                }
                else if (msg.ID == 0x416b074c)//CSFB:  Extended service request
                {
                    began = true;
                    call.MsgExtendedSR = msg;
                    continue;
                }

                if (began)
                {
                    switch (msg.ID)
                    {
                        //case 1107689472:
                        //    if (call.MsgIMS_SIP_INVITE == null)
                        //    {
                        //        call.MsgIMS_SIP_INVITE = msg;
                        //    }
                        //    break;
                        case 1107361792:
                            if (call.MsgIMS_SIP_ACK == null)
                            {
                                call.MsgIMS_SIP_ACK = msg;
                            }
                            break;
                        case 773:
                            //case 1899627269:
                            if (call.MsgSetup == null)
                            {
                                call.MsgSetup = msg;
                            }
                            break;
                        case 783:
                            if (call.MsgConnectAcknowledge == null)
                            {
                                call.MsgConnectAcknowledge = msg;
                            }
                            break;
                        case 1093626370:
                            if (call.MsgRRCCRComplete == null)
                            {
                                call.MsgRRCCRComplete = msg;
                            }
                            break;
                        case 1107706039:
                            if (call.MsgSessionProgress183 == null)
                            {
                                call.MsgSessionProgress183 = msg;
                            }
                            break;
                        case 1097532101:
                            if (call.MsgEPSBearerCR == null)
                            {
                                call.MsgEPSBearerCR = msg;
                            }
                            break;
                        case 1107706036:
                            if (call.MsgRinging180 == null)
                            {
                                call.MsgRinging180 = msg;
                            }
                            break;
                        case 1097533260:
                            if (call.MsgExtendedSR == null)
                            {
                                call.MsgExtendedSR = msg;
                            }
                            break;
                        case 1575:
                            if (call.MsgPagingResponse == null)
                            {
                                call.MsgPagingResponse = msg;
                            }
                            break;
                        case 1316:
                        case 1899627812:
                            if (call.MsgCMSR == null)
                            {
                                call.MsgCMSR = msg;
                            }
                            break;
                        case 769:
                        case 1899627265:
                            if (call.MsgAlerting == null)
                            {
                                call.MsgAlerting = msg;
                            }
                            break;
                        default:
                            break;
                    }
                }
            }

            int beginTpIdx = getChangeTestpointIndex(file.TestPoints, file.Messages[beginIdx]);
            int endTpIdx = getChangeTestpointIndex(file.TestPoints, file.Messages[lastMsgIdx]);

            beginTpIdx = beginTpIdx == -1 ? 0 : beginTpIdx;
            endTpIdx = endTpIdx == -1 ? 0 : endTpIdx;
            if (beginTpIdx <= endTpIdx)
            {
                for (int i = beginTpIdx; i <= endTpIdx; i++)
                {
                    call.ListPoints.Add(file.TestPoints[i]);
                }
            }
        }
        private static int getChangeTestpointIndex(List<TestPoint> tpList, Message msg)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > msg.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == msg.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        private VolteCallInfo getMtCallInfo(VolteCallInfo moCall, DTFileDataManager mtFile
            , ref int lastMtEentIdx, ref int lastMtMsgIdx)
        {
            if (mtFile == null)
            {
                return null;
            }
            VolteCallInfo mtCall = null;
            if (moCall != null && moCall.CallAttemptResult == "Fail")
            {
                return null;
            }
            if (lastMtEentIdx < 0)
            {
                lastMtEentIdx = 0;
            }
            for (int i = lastMtEentIdx; i < mtFile.Events.Count; i++)
            {
                Event evt = mtFile.Events[i];
                bool isGet = judgeIsGetCallInfo(moCall, mtFile, ref lastMtEentIdx, ref lastMtMsgIdx, ref mtCall, i, evt);
                if (isGet)
                {
                    break;
                }

                if (moCall != null
                    && checkDelay
                    && (evt.DateTime - moCall.ListEvents[moCall.ListEvents.Count - 1].DateTime).TotalSeconds > maxDelaySec)
                {
                    if (mtCall != null)
                    {
                        lastMtEentIdx = i - 2;
                        addTpMsgInfo2Call(mtCall, mtFile, ref lastMtMsgIdx);
                    }
                    break;
                }
            }
            return mtCall;
        }

        private bool judgeIsGetCallInfo(VolteCallInfo moCall, DTFileDataManager mtFile, ref int lastMtEentIdx, ref int lastMtMsgIdx, ref VolteCallInfo mtCall, int i, Event evt)
        {
            if (moCall == null || evt.DateTime >= moCall.EvtCallAttempt.DateTime)
            {
                //mt call attempt事件只会在mo call attempt 后发生
                if (mtCallAttemptEventIds.Contains(evt.ID))
                {
                    if (mtCall != null)
                    {
                        lastMtEentIdx = i - 2;
                        addTpMsgInfo2Call(mtCall, mtFile, ref lastMtMsgIdx);
                        return true;
                    }
                    mtCall = new VolteCallInfo(mtFile.FileName, mtFile.MoMtFlag);
                    mtCall.AddEvent(evt);
                }
                else if (mtCall != null)
                {
                    bool isAdded = mtCall.AddEvent(evt);
                    if (isAdded)
                    {
                        lastMtEentIdx = i - 2;
                        addTpMsgInfo2Call(mtCall, mtFile, ref lastMtMsgIdx);
                        return true;
                    }
                }
            }
            return false;
        }

        protected override void getResultsAfterQuery()//以网格单位统计信息
        {
            if (callStatList == null || callStatList.Count == 0)
            {
                return;
            }
            callStatList.Sort(comparer);
            string lastGridName = "";
            VolteItemsInfo volitem = new VolteItemsInfo();
            foreach (VoltePairsInfo volInfo in callStatList)
            {
                if (volInfo.GridName != lastGridName)
                {
                    if (volitem.ListVoltePairsInfo.Count != 0)
                    {
                        volitem.SN = listVolteItemsInfo.Count + 1;
                        listVolteItemsInfo.Add(volitem);
                    }
                    volitem = new VolteItemsInfo();
                }
                volInfo.Sn = volitem.ListVoltePairsInfo.Count + 1;
                volitem.ListVoltePairsInfo.Add(volInfo);
                lastGridName = volInfo.GridName;
            }
            if (volitem.ListVoltePairsInfo.Count != 0)
            {
                volitem.SN = listVolteItemsInfo.Count + 1;
                listVolteItemsInfo.Add(volitem);
            }
        }

        private readonly Comparer comparer = new Comparer();
        private class Comparer : IComparer<VoltePairsInfo>
        {
            public int Compare(VoltePairsInfo x, VoltePairsInfo y)
            {
                int r = x.GridName.CompareTo(y.GridName);
                if (r != 0)
                {
                    return r;
                }
                else
                {
                    if (x.MoCall != null && y.MoCall != null && x.MoCall.ListEvents.Count > 0 && y.MoCall.ListEvents.Count > 0)
                    {
                        return x.MoCall.ListEvents[0].DateTime.CompareTo(y.MoCall.ListEvents[0].DateTime);
                    }
                    else if (x.MtCall != null && y.MtCall != null && x.MtCall.ListEvents.Count > 0 && y.MtCall.ListEvents.Count > 0)
                    {
                        return x.MtCall.ListEvents[0].DateTime.CompareTo(y.MtCall.ListEvents[0].DateTime);
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
        }
    }

    public class VolteStatDelayAnaByRegion_FDD : VolteStatDelayAnaByRegion
    {
        private static VolteStatDelayAnaByRegion_FDD instance = null;
        public static new VolteStatDelayAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteStatDelayAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        public VolteStatDelayAnaByRegion_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get
            {
                return "VOLTE_FDD时延分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30020, this.Name);
        }
    }
}
