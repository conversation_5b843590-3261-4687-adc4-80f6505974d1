﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.NOP
{
    public class GroupStatModel
    {
        public GroupStatModel()
        {


        }
        public int TaskID
        {
            get;
            set;
        }
        public string GroupName
        {
            get;
            set;
        }
        public string Description
        {
            get;
            set;
        }

        public string CreatTime
        {
            get;
            set;
        }

        public string OrderTime
        {
            get;
            set;
        }
        public string District
        {
            get;
            set;
        }
        public string AreaName
        {
            get;
            set;
        }

        public string TaskName
        {
            get;
            set;
        }

        public string TimeStatus
        {
            get;
            set;
        }
    }


    public class GroupStatResult
    {
        public GroupStatResult(string Groupname)
        {
            groupName = Groupname;
            counts = new List<int>();
            for (int i = 0; i < 16; i++)
            {
                int count = 0;
                counts.Add(count);
            }
        }

        private readonly string groupName;
        public string GroupName
        {
            get { return groupName; }
        }

        private readonly List<int> counts;
        public List<int> Counts
        {
            get { return counts; }
        }

        public int EZhou
        {
            get { return counts[0]; }
        }
        public int EnShi
        {
            get { return counts[1]; }
        }
        public int HuangShi
        {
            get { return counts[2]; }
        }
        public int HuangGang
        {
            get { return counts[3]; }
        }
        public int JingMen
        {
            get { return counts[4]; }
        }
        public int JingZhou
        {
            get { return counts[5]; }
        }
        public int JiangHan
        {
            get { return counts[6]; }
        }
        public int TianMen
        {
            get { return counts[7]; }
        }
        public int QianJiang
        {
            get { return counts[8]; }
        }
        public int ShiYan
        {
            get { return counts[9]; }
        }
        public int SuiZhou
        {
            get { return counts[10]; }
        }
        public int WuHan
        {
            get { return counts[11]; }
        }
        public int XiangYang
        {
            get { return counts[12]; }
        }
        public int XianNing
        {
            get { return counts[13]; }
        }
        public int XiaoGan
        {
            get { return counts[14]; }
        }
        public int YiChang
        {
            get { return counts[15]; }
        }


    }

    public class TaskStatResult
    {
        public TaskStatResult(int taskID, string district, string taskName)
        {
            this.taskName = taskName;
            this.taskID = taskID;
            this.district = district;
            counts = new List<int>();
            for (int i = 0; i < 11; i++)
            {
                int count = 0;
                counts.Add(count);
            }
        }
        private readonly string taskName;
        public string TaskName
        {
            get { return taskName; }
        }

        private readonly int taskID;
        public int TaskID
        {
            get { return taskID; }
        }

        private readonly List<int> counts;
        public List<int> Counts
        {
            get { return counts; }
        }

        private readonly string district;
        public string District
        {
            get { return district; }
        }

        public int T1
        {
            get { return counts[0]; }
        }
        public int T21
        {
            get { return counts[1]; }
        }
        public int T22
        {
            get { return counts[2]; }
        }
        public int T23
        {
            get { return counts[3]; }
        }
        public int T24
        {
            get { return counts[4]; }
        }
        public int T25
        {
            get { return counts[5]; }
        }
        public int T3
        {
            get { return counts[6]; }
        }
        public int T0
        {
            get { return counts[7]; }
        }
        public int T4
        {
            get { return counts[8]; }
        }
        public int T5
        {
            get { return counts[9]; }
        }
        public int T6
        {
            get { return counts[10]; }
        }


    }

    public class DealTimeResult
    {
        public DealTimeResult(string taskName)
        {
            this.taskName = taskName;
            counts = new List<double>();
            times = new List<double>();
            for (int i = 0; i < 9; i++)
            {
                int count = 0;
                counts.Add(count);
                times.Add(count);
            }
        }
        public DealTimeResult(int taskID, string district, string taskName)
        {
            this.taskID = taskID;
            this.district = district;
            this.taskName = taskName;
            counts = new List<double>();
            times = new List<double>();
            for (int i = 0; i < 9; i++)
            {
                int count = 0;
                counts.Add(count);
                times.Add(count);
            }
        }
        private readonly string taskName;
        public string TaskName
        {
            get { return taskName; }
        }

        private readonly int taskID;
        public int TaskID
        {
            get { return taskID; }
        }

        private readonly List<double> counts;
        public List<double> Counts
        {
            get { return counts; }
        }

        private readonly string district;
        public string District
        {
            get { return district; }
        }
        public string DealTime { get; set; }
        public string OrderTime { get; set; }

        private readonly List<double> times;
        public List<double> Times
        {
            get { return times; }
        }

        public string GroupName { get; set; }
        public string Status { get; set; }
        public string LastGroupName { get; set; }
        public string NextGroupName { get; set; }

        public double T1C
        {
            get { return Math.Round(counts[0], 2); }
        }
        public double T1T
        {
            get { return times[0]; }
        }

        public double T21C
        {
            get { return Math.Round(counts[1], 2); }
        }
        public double T21T
        {
            get { return times[1]; }
        }

        public double T22C
        {
            get { return Math.Round(counts[2], 2); }
        }
        public double T22T
        {
            get { return times[2]; }
        }

        public double T23C
        {
            get { return Math.Round(counts[3], 2); }
        }
        public double T23T
        {
            get { return times[3]; }
        }

        public double T24C
        {
            get { return Math.Round(counts[4], 2); }
        }
        public double T24T
        { get { return times[4]; } }

        public double T25C
        {
            get { return Math.Round(counts[5], 2); }
        }
        public double T25T
        {
            get { return times[5]; }
        }

        public double T3C
        {
            get { return Math.Round(counts[6], 2); }
        }
        public double T3T
        {
            get { return times[6]; }
        }

        public double T0C
        {
            get { return Math.Round(counts[7], 2); }
        }
        public double T0T
        {
            get { return times[7]; }
        }

        public double DuBan
        {
            get { return Math.Round(times[8], 2); }
        }
    }

    public class TimerModel
    {
        public int TaskID
        {
            get;
            set;
        }
        public string CurStatus
        {
            get;
            set;
        }
        public string CurUser
        {
            get;
            set;
        }
        public string CityName
        {
            get;
            set;
        }
        public string Status
        {
            get;
            set;
        }
        public string StatusType
        {
            get;
            set;
        }
        public string InsertTime
        {
            get;
            set;
        }

    }
    public class OrderAnalyzerResult
    {
        #region 参数
        private readonly Dictionary<string, double> param;

        public Dictionary<string, double> Param
        {
            get { return param; }
        }
        /// <summary>
        /// 生成工单总数
        /// </summary>
        public double TotalOrders
        {
            get { return param["生成工单总数"]; }
        }
        /// <summary>
        /// T1准确派单数
        /// </summary>
        public double T1ExactOrders
        {
            get { return param["T1准确派单数"]; }
        }
        /// <summary>
        /// T1预处理工单数
        /// </summary>
        public double T1ProDealOrders
        {
            get { return param["T1预处理工单数"]; }
        }
        /// <summary>
        /// T1未超时工单数
        /// </summary>
        public double T1UnTimeoutOrders
        {
            get { return param["T1未超时工单数"]; }
        }
        /// <summary>
        /// T2指标验证失败数
        /// </summary>
        public double T2CheckFaliedOrders
        {
            get { return param["T2指标验证失败数"]; }
        }
        /// <summary>
        /// T2派单T0工单数
        /// </summary>
        public double T2ToT0Orders
        {
            get { return param["T2派单T0工单数"]; }
        }
        /// <summary>
        /// 可执行工单数
        /// </summary>
        public double DealOrders
        {
            get { return param["可执行工单数"]; }
        }
        /// <summary>
        /// 派发到T0工单
        /// </summary>
        public double ToT0Orders
        {
            get { return param["ToT0工单"]; }
        }
        /// <summary>
        /// T2未超时工单数
        /// </summary>
        public double T2UnTimeoutOrders
        {
            get { return param["T2未超时工单数"]; }
        }
        /// <summary>
        /// T2分析工单数
        /// </summary>
        public double T2DealOrders
        {
            get { return param["T2分析工单数"]; }
        }
        /// <summary>
        /// T0未超时工单数
        /// </summary>
        public double T0UnTimeoutOrders
        {
            get { return param["T0未超时工单数"]; }
        }
        /// <summary>
        /// 派发T0工单数
        /// </summary>
        public double T0Orders
        {
            get { return param["派发T0工单数"]; }
        }
        /// <summary>
        /// T0验证失败工单数
        /// </summary>
        public double T0CheckFaliedOrders
        {
            get { return param["T0验证失败工单数"]; }
        }
        /// <summary>
        /// T0回复工单数
        /// </summary>
        public double T0ReplyOrders
        {
            get { return param["T0回复工单数"]; }
        }
        /// <summary>
        /// 质检未超时工单数
        /// </summary>
        public double CheckUnTimeout
        {
            get { return param["T1质检未超时工单数"] + param["T2质检未超时工单数"]; }
        }
        /// <summary>
        /// 质检总工单
        /// </summary>
        public double CheckOrders
        {
            get { return param["T1质检总工单"] + param["T2质检总工单"]; }
        }
        /// <summary>
        /// T1质检未超时工单数
        /// </summary>
        public double T1CheckUnTimeout
        {
            get { return param["T1质检未超时工单数"]; }
        }
        /// <summary>
        /// T1质检总工单
        /// </summary>
        public double T1CheckOrders
        {
            get { return param["T1质检总工单"]; }
        }
        /// <summary>
        /// T2质检未超时工单数
        /// </summary>
        public double T2CheckUnTimeout
        {
            get { return param["T2质检未超时工单数"]; }
        }

        /// <summary>
        /// T2质检总工单
        /// </summary>
        public double T2CheckOrders
        {
            get { return param["T2质检总工单"]; }
        }
        /// <summary>
        /// 工程督办解决工单数
        /// </summary>
        public double DuBanDealOrders
        {
            get { return param["工程督办解决工单数"]; }
        }
        /// <summary>
        /// 工程督办工单数
        /// </summary>
        public double DuBanOrders
        {
            get { return param["工程督办工单数"]; }
        }
        /// <summary>
        /// T1派单准确性
        /// </summary>
        public double T1Exactly
        {
            get
            {
                if (T1ProDealOrders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(T1ExactOrders / T1ProDealOrders, 4) * 100;
                }
            }
        }
        /// <summary>
        /// T1预处理完整性
        /// </summary>
        public double ProDealRate
        {
            get
            {
                if (TotalOrders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(T1ProDealOrders / TotalOrders, 4) * 100;
                }
            }
        }
        /// <summary>
        /// T1预处理及时性
        /// </summary>
        public double T1ProDealTimely
        {
            get
            {
                if (T1ProDealOrders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(T1UnTimeoutOrders / T1ProDealOrders, 4) * 100;
                }
            }
        }
        /// <summary>
        /// 方案准确性
        /// </summary>
        public double T2Exactly
        {
            get
            {
                if (T2ToT0Orders == 0)
                {
                    return 0;
                }
                else
                {
                    return (1 - Math.Round(T2CheckFaliedOrders / T2ToT0Orders, 4)) * 100;
                }
            }
        }
        /// <summary>
        /// 方案可执行性
        /// </summary>
        public double OrderPerformability
        {
            get
            {
                if (T2ToT0Orders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(DealOrders / T2ToT0Orders, 4) * 100;
                }
            }
        }
        /// <summary>
        /// T2方案制定及时性
        /// </summary>
        public double T2Timely
        {
            get
            {
                if (T2DealOrders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(T2UnTimeoutOrders / T2DealOrders, 4) * 100;
                }
            }
        }
        /// <summary>
        /// 分公司落实及时性
        /// </summary>
        public double T0Timely
        {
            get
            {
                if (T0Orders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(T0UnTimeoutOrders / T0Orders, 4) * 100;
                }
            }
        }
        /// <summary>
        ///  分公司落实准确性 
        /// </summary>
        public double T0Exactly
        {
            get
            {
                if (T0ReplyOrders == 0)
                {
                    return 0;
                }
                else
                {
                    return (1 - Math.Round(T0CheckFaliedOrders / T0ReplyOrders, 4)) * 100;
                }
            }
        }
        /// <summary>
        /// 质检及时性 
        /// </summary>
        public double CheckTimely
        {
            get
            {
                if (CheckOrders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(CheckUnTimeout / CheckOrders, 4) * 100;
                }
            }
        }
        /// <summary>
        ///  T1质检及时性
        /// </summary>
        public double T1CheckTimely
        {
            get
            {
                if (T1CheckOrders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(T1CheckUnTimeout / T1CheckOrders, 4) * 100;
                }
            }
        }
        /// <summary>
        /// T2质检及时性
        /// </summary>
        public double T2CheckTimely
        {
            get
            {
                if (T2CheckOrders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(T2CheckUnTimeout / T2CheckOrders, 4) * 100;
                }
            }
        }
        /// <summary>
        /// 督办挂起解决率
        /// </summary>
        public double DuBanDealRate
        {
            get
            {
                if (DuBanOrders == 0)
                {
                    return 0;
                }
                else
                {
                    return Math.Round(DuBanDealOrders / DuBanOrders, 4) * 100;
                }
            }
        }
        #endregion
        public string City
        {
            get;
            set;
        }

        public OrderAnalyzerResult(string city)
        {
            City = city;
            param = new Dictionary<string, double>();
            param.Add("生成工单总数", 0);

            param.Add("T1准确派单数", 0);
            param.Add("T1预处理工单数", 0);

            param.Add("T1未超时工单数", 0);

            param.Add("T2指标验证失败数", 0);
            param.Add("T2派单T0工单数", 0);

            param.Add("可执行工单数", 0);
            param.Add("ToT0工单", 0);

            param.Add("T2未超时工单数", 0);
            param.Add("T2分析工单数", 0);

            param.Add("T0未超时工单数", 0);
            param.Add("派发T0工单数", 0);

            param.Add("T0验证失败工单数", 0);
            param.Add("T0回复工单数", 0);

            param.Add("质检未超时工单数", 0);
            param.Add("质检总工单", 0);

            param.Add("T1质检未超时工单数", 0);
            param.Add("T1质检总工单", 0);

            param.Add("T2质检未超时工单数", 0);
            param.Add("T2质检总工单", 0);

            param.Add("工程督办解决工单数", 0);
            param.Add("工程督办工单数", 0);
        }

    }

}
