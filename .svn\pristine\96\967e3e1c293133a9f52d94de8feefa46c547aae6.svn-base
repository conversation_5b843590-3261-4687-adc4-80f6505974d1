﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Model.RoadProtection
{
    public partial class XtraFormRoadProtection : DevExpress.XtraEditors.XtraForm
    {
        public XtraFormRoadProtection(string network)
        {
            InitializeComponent();
            this.Text = "全省 " + network + " 道路预警概况";
        }

        public void setDataSource(DataTable dt)
        {
            Grid_Export.DataSource = dt;
        }


        public void BuildCellManage()
        {
            foreach (DevExpress.XtraGrid.Columns.GridColumn cn in gview_GSM.Columns)
            {
                if (cn.FieldName != "区域")
                {
                    cn.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
                }
            }
        }



        //到处EXCEL
        private void 导出ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Title = "导出Excel";
            saveFileDialog.Filter = "Excel文件(*.xls)|*.xls";
            DialogResult dialogResult = saveFileDialog.ShowDialog(this);
            if (dialogResult == DialogResult.OK)
            {
                gview_GSM.ExportToXls(saveFileDialog.FileName);
                DevExpress.XtraEditors.XtraMessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void gview_GSM_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column.FieldName == "权值")
            {
                double value = 0;
                try
                {
                    value = Convert.ToDouble(e.CellValue.ToString());
                }
                catch (Exception)
                {
                    value = 0;
                }
                e.DisplayText = Math.Round(value, 2).ToString();
            }
        }
    }
}