﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class GridCompareReport : BaseDialog
    {
        public GridCompareReport()
        {
            InitializeComponent();
            reportFilter.initReportPicker();
        }

        private void lnkReloadRpt_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            KPIReportManager.Instance.Init();
            reportFilter.initReportPicker();
        }

        public ReportStyle Report
        {
            get
            {
                ReportStyle rpt = reportFilter.SelectedReport;
                return rpt;
            }
        }

        public bool IsQueryAllParams
        {
            get { return chkAllParam.Checked; }
        }

        public double DTimeSpan
        {
            get
            {
                return Convert.ToDouble(numTimeSpan.Value);
            }
        }

        public bool IsOnlyCellAna
        {
            get
            {
                return chkOnlyCellAna.Checked;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            ReportStyle rpt = reportFilter.SelectedReport;
            if (rpt == null)
            {
                MessageBox.Show("请选择报表！");
                return;
            }
            if (DTimeSpan != 0.5 && DTimeSpan != (int)DTimeSpan)
            {
                MessageBox.Show("设置的时间粒度不对，请留意标注！");
                return;
            }
            DialogResult = DialogResult.OK;
        }
    }
}
