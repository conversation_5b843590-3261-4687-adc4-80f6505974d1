﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.KPISheet_HaiNan
{
    public class QueryTableCfg : DIYSQLBase
    {
        readonly TableCfgManager tbCfgMng;
        public QueryTableCfg(TableCfgManager tbCfgMng)
            : base(MainModel.GetInstance())
        {
            this.tbCfgMng = tbCfgMng;
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            switch (curTbType)
            {
                case TbType.TestTag:
                    return @"select [testTag],[testDesc] FROM [KPIMNG_DB_HaiNan].[dbo].[tb_cfg_testTag] order by testDesc desc";
                case TbType.AreaInfo:
                    return @"select [cityID],[areaName],[areaType],[归属大区],[归属行政区],[areaDesc] FROM [KPIMNG_DB_CHENGDU].[dbo].[tb_cfg_area]";
                default:
                    return null;
            }
        }

        enum TbType
        {
            TestTag,
            AreaInfo
        }

        TbType curTbType;

        protected override void queryInThread(object o)
        {
            ClientProxy clientProxy = (ClientProxy)o;
            Package package = clientProxy.Package;
            try
            {
                foreach (TbType tbType in Enum.GetValues(typeof(TbType)))
                {
                    if (tbType != TbType.TestTag) continue;
                    curTbType = tbType;
                    string strsql = getSqlTextString();
                    E_VType[] retArrDef = getSqlRetTypeArr();
                    package.Command = Command.DIYSearch;
                    package.SubCommand = SubCommand.Request;
                    if (MainDB)
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                    }
                    else
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                    }
                    package.Content.PrepareAddParam();
                    package.Content.AddParam(strsql);
                    StringBuilder sb = new StringBuilder();
                    if (retArrDef != null)
                    {
                        for (int i = 0; i < retArrDef.Length; i++)
                        {
                            sb.Append((int)retArrDef[i]);
                            sb.Append(",");
                        }
                    }

                    package.Content.AddParam(sb.ToString().TrimEnd(','));
                    clientProxy.Send();
                    receiveRetData(clientProxy);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            if (curTbType==TbType.TestTag)
            {
                int i = 0;
                E_VType[] retArr = new E_VType[2];
                retArr[i++] = E_VType.E_String;
                retArr[i] = E_VType.E_String;
                return retArr;
            }
            else
            {
                int i = 0;
                E_VType[] retArr = new E_VType[6];
                retArr[i++] = E_VType.E_Int;
                retArr[i++] = E_VType.E_String;
                retArr[i++] = E_VType.E_String;
                retArr[i++] = E_VType.E_String;
                retArr[i++] = E_VType.E_String;
                retArr[i] = E_VType.E_String;
                return retArr;
            }
            
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    if (curTbType==TbType.TestTag)
                    {
                        string testName = package.Content.GetParamString();
                        string desc = package.Content.GetParamString();
                        tbCfgMng.TestTagSet.Add(new TestTag(testName, desc));
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

    }



    public class AreaInfo
    {
        public AreaInfo(Content cnt)
        {
            CityID = cnt.GetParamInt();
            AreaName = cnt.GetParamString();
            AreaType = cnt.GetParamString();
            PareantArea = cnt.GetParamString();
            ParentDistrict = cnt.GetParamString();
            Desc = cnt.GetParamString();
        }

        public string Key
        {
            get { return string.Format("{0}&{1}&{2}", CityID, AreaType, AreaName); }
        }

        public int CityID
        {
            get;
            private set;
        }

        public string AreaType
        {
            get;
            private set;
        }

        public string AreaName { get; private set; }

        public string PareantArea
        {
            get;
            private set;
        }

        public string ParentDistrict
        {
            get;
            private set;
        }


        public string Desc { get; private set; }
    }
}
