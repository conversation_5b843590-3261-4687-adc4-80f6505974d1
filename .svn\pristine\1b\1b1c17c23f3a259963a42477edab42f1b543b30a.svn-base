﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTSingleCallHandoverStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTSingleCallHandoverStatForm));
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMileage = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOccupation = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTpCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLevMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxQualMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel4 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel2 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel3 = new System.Windows.Forms.ToolStripLabel();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnSN);
            this.treeListView.AllColumns.Add(this.olvColumnName);
            this.treeListView.AllColumns.Add(this.olvColumnLAC);
            this.treeListView.AllColumns.Add(this.olvColumnCI);
            this.treeListView.AllColumns.Add(this.olvColumnMileage);
            this.treeListView.AllColumns.Add(this.olvColumnOccupation);
            this.treeListView.AllColumns.Add(this.olvColumnSpeed);
            this.treeListView.AllColumns.Add(this.olvColumnTpCount);
            this.treeListView.AllColumns.Add(this.olvColumnMaxDistance);
            this.treeListView.AllColumns.Add(this.olvColumnAvgDistance);
            this.treeListView.AllColumns.Add(this.olvColumnRxLevMin);
            this.treeListView.AllColumns.Add(this.olvColumnRxQualMin);
            this.treeListView.AllowColumnReorder = true;
            this.treeListView.AlternateRowBackColor = System.Drawing.Color.Ivory;
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnMileage,
            this.olvColumnOccupation,
            this.olvColumnSpeed,
            this.olvColumnTpCount,
            this.olvColumnRxLevMin,
            this.olvColumnRxQualMin});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.IsNeedShowOverlay = false;
            this.treeListView.Location = new System.Drawing.Point(0, 25);
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1263, 452);
            this.treeListView.TabIndex = 5;
            this.treeListView.UseAlternatingBackColors = true;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.UseHotItem = true;
            this.treeListView.UseTranslucentHotItem = true;
            this.treeListView.UseTranslucentSelection = true;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.ObjectExpandChanged += new BrightIdeasSoftware.TreeListView.ObjectExpandProxy(this.treeListView_ObjectExpandChanged);
            this.treeListView.DoubleClick += new System.EventHandler(this.treeListView_DoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "Index";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 76;
            // 
            // olvColumnName
            // 
            this.olvColumnName.AspectName = "Name";
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.Text = "名称";
            this.olvColumnName.Width = 234;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.AspectName = "LAC";
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            this.olvColumnLAC.Width = 65;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.AspectName = "CI";
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnMileage
            // 
            this.olvColumnMileage.AspectName = "Mileage";
            this.olvColumnMileage.HeaderFont = null;
            this.olvColumnMileage.Text = "里程(米)";
            this.olvColumnMileage.Width = 64;
            // 
            // olvColumnOccupation
            // 
            this.olvColumnOccupation.AspectName = "Occupation";
            this.olvColumnOccupation.HeaderFont = null;
            this.olvColumnOccupation.Text = "占用时长(s)";
            this.olvColumnOccupation.Width = 86;
            // 
            // olvColumnSpeed
            // 
            this.olvColumnSpeed.AspectName = "Speed";
            this.olvColumnSpeed.HeaderFont = null;
            this.olvColumnSpeed.Text = "平均速度(m/s)";
            this.olvColumnSpeed.Width = 93;
            // 
            // olvColumnTpCount
            // 
            this.olvColumnTpCount.AspectName = "TestPointCount";
            this.olvColumnTpCount.HeaderFont = null;
            this.olvColumnTpCount.Text = "采样点个数";
            this.olvColumnTpCount.Width = 82;
            // 
            // olvColumnMaxDistance
            // 
            this.olvColumnMaxDistance.AspectName = "MaxDistance";
            this.olvColumnMaxDistance.DisplayIndex = 8;
            this.olvColumnMaxDistance.HeaderFont = null;
            this.olvColumnMaxDistance.IsVisible = false;
            this.olvColumnMaxDistance.Text = "最大距离(m)";
            this.olvColumnMaxDistance.Width = 83;
            // 
            // olvColumnAvgDistance
            // 
            this.olvColumnAvgDistance.AspectName = "AvgDistance";
            this.olvColumnAvgDistance.DisplayIndex = 9;
            this.olvColumnAvgDistance.HeaderFont = null;
            this.olvColumnAvgDistance.IsVisible = false;
            this.olvColumnAvgDistance.Text = "平均距离(m)";
            this.olvColumnAvgDistance.Width = 81;
            // 
            // olvColumnRxLevMin
            // 
            this.olvColumnRxLevMin.AspectName = "RxLevMin";
            this.olvColumnRxLevMin.HeaderFont = null;
            this.olvColumnRxLevMin.Text = "最低电平";
            this.olvColumnRxLevMin.Width = 68;
            // 
            // olvColumnRxQualMin
            // 
            this.olvColumnRxQualMin.AspectName = "RxQualMin";
            this.olvColumnRxQualMin.HeaderFont = null;
            this.olvColumnRxQualMin.Text = "最差质量";
            this.olvColumnRxQualMin.Width = 76;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvent,
            this.toolStripMenuItemExpandAll,
            this.toolStripMenuItemCollapseAll,
            this.toolStripMenuItem1,
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(141, 98);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Image = ((System.Drawing.Image)(resources.GetObject("miReplayEvent.Image")));
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(140, 22);
            this.miReplayEvent.Text = "回放事件(&R)";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // toolStripMenuItemExpandAll
            // 
            this.toolStripMenuItemExpandAll.Name = "toolStripMenuItemExpandAll";
            this.toolStripMenuItemExpandAll.Size = new System.Drawing.Size(140, 22);
            this.toolStripMenuItemExpandAll.Text = "全部展开";
            this.toolStripMenuItemExpandAll.Click += new System.EventHandler(this.toolStripMenuItemExpandAll_Click);
            // 
            // toolStripMenuItemCollapseAll
            // 
            this.toolStripMenuItemCollapseAll.Name = "toolStripMenuItemCollapseAll";
            this.toolStripMenuItemCollapseAll.Size = new System.Drawing.Size(140, 22);
            this.toolStripMenuItemCollapseAll.Text = "全部折叠";
            this.toolStripMenuItemCollapseAll.Click += new System.EventHandler(this.toolStripMenuItemCollapseAll_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(137, 6);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(140, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStrip1
            // 
            this.toolStrip1.BackColor = System.Drawing.SystemColors.Control;
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel1,
            this.toolStripLabel4,
            this.toolStripLabel2,
            this.toolStripLabel3});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(1263, 25);
            this.toolStrip1.TabIndex = 6;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(92, 22);
            this.toolStripLabel1.Text = "小区占用时间：";
            // 
            // toolStripLabel4
            // 
            this.toolStripLabel4.Name = "toolStripLabel4";
            this.toolStripLabel4.Size = new System.Drawing.Size(48, 22);
            this.toolStripLabel4.Text = "秒以内 ";
            // 
            // toolStripLabel2
            // 
            this.toolStripLabel2.Name = "toolStripLabel2";
            this.toolStripLabel2.Size = new System.Drawing.Size(114, 22);
            this.toolStripLabel2.Text = "每呼叫切换次数 >=";
            // 
            // toolStripLabel3
            // 
            this.toolStripLabel3.Name = "toolStripLabel3";
            this.toolStripLabel3.Size = new System.Drawing.Size(20, 22);
            this.toolStripLabel3.Text = "次";
            // 
            // ZTSingleCallHandoverStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1263, 477);
            this.Controls.Add(this.treeListView);
            this.Controls.Add(this.toolStrip1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTSingleCallHandoverStatForm";
            this.Text = "切换序列";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel4;
        private System.Windows.Forms.ToolStripLabel toolStripLabel2;
        private System.Windows.Forms.ToolStripLabel toolStripLabel3;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnTpCount;
        private BrightIdeasSoftware.OLVColumn olvColumnOccupation;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMin;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQualMin;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnMileage;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemExpandAll;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemCollapseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
    }
}