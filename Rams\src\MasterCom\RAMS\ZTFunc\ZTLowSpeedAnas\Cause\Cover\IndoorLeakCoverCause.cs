﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class IndoorLeakCoverCause : CauseBase
    {
        public IndoorLeakCoverCause()
        {
            AddSubReason(new LeakCoverOverCover());
            AddSubReason(new LeakCoverHandoverParam());
            AddSubReason(new LeakCoverWeakOutdoor());
        }

        public override string Name
        {
            get { return "室分外泄"; }
        }
        public override string Desc
        {
            get
            {
                return "覆盖小区为室内站";
            }
        }

        public override string Suggestion
        {
            get
            {
                return null;
            }
        }

        [NonSerialized]
        private LTECell s = null;
        public LTECell ServerCell
        {
            get { return s; }
            private set { s = value; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }
                LTECell cell = pnt.GetMainLTECell_TdOrFdd();
                if (cell==null)
                {
                    continue;
                }
                if (cell.Type == LTEBTSType.Indoor)
                {
                    judgeReson(segItem, pnt);
                    if (!segItem.NeedJudge)
                    {
                        return;
                    }
                }
            }
        }

        private void judgeReson(LowSpeedSeg segItem, TestPoint pnt)
        {
            foreach (CauseBase subReason in SubCauses)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    break;
                }
                subReason.JudgeSinglePoint(segItem, pnt);
            }

            if (segItem.IsNeedJudge(pnt))
            {
                UnknowReason r = new UnknowReason();
                r.Parent = this;
                segItem.SetReason(new LowSpeedPointDetail(pnt, r));
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                List<object> list = new List<object>();
                foreach (CauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                SubCauses = new List<CauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    CauseBase cause = (CauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }
    }

    [Serializable]
    public class LeakCoverOverCover : CauseBase
    {
        public override string Name
        {
            get { return "室内小区覆盖过远"; }
        }
        [NonSerialized]
        private ICell serverCell;
        public double Distance { get; set; } = 300;
        public float RSRPMin { get; set; } = -85;
        public override string Desc
        {
            get
            {
                return string.Format("采样点距离室内小区≥{0}米，室内小区信号≥{1}dB", Distance, RSRPMin);
            }
        }

        public override string Suggestion
        {
            get
            {
                return string.Format("控制室内小区{0}的覆盖", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            float? rsrp = (float?)GetRSRP(testPoint);
            if (rsrp >= RSRPMin)
            {
                LTECell cell = testPoint.GetMainLTECell_TdOrFdd();
                if (cell==null)
                {
                    return;
                }
                if (testPoint.Distance2(cell.Longitude, cell.Latitude) >= Distance)
                {
                    LeakCoverOverCover cln = this.Clone() as LeakCoverOverCover;
                    cln.serverCell = cell;
                    segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
                }
            }
        }
        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["distance"] = this.Distance;
                paramDic["rsrpMin"] = this.RSRPMin;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Distance = (double)value["distance"];
                this.RSRPMin = (float)value["rsrpMin"];
            }
        }
    }

    [Serializable]
    public class LeakCoverHandoverParam : CauseBase
    {
        public override string Name
        {
            get { return "切换参数不合理"; }
        }
        [NonSerialized]
        private ICell serverCell;
        public int SecondMin { get; set; } = 5;
        public float RSRPMin { get; set; } = 10;
        public override string Desc
        {
            get
            {
                return "邻区中有室外站，且比当前小区信号强度≥10dB，占用时间≥5秒";
            }
        }

        public override string Suggestion
        {
            get
            {
                return string.Format("控制室内小区{0}的切换参数", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            List<TestPoint> pnts = new List<TestPoint>();
            pnts.Add(testPoint);
            int beginIdx = segItem.TestPoints.IndexOf(testPoint);
            for (int i = beginIdx; i < segItem.TestPoints.Count; i++)
            {
                LTECell sCell = segItem.TestPoints[i].GetMainLTECell_TdOrFdd();
                if (sCell == null || sCell.Type == LTEBTSType.Outdoor)
                {
                    break;
                }
                float? rsrp = (float?)GetRSRP(testPoint);
                for (int n = 0; n < 10; n++)
                {
                    LTECell cell = testPoint.GetNBLTECell_TdOrFdd(n);
                    if (cell == null)
                    {
                        break;
                    }
                    float? nrsrp = (float?)GetNRSRP(testPoint, n);
                    if (cell.Type == LTEBTSType.Outdoor
                        && nrsrp != null && nrsrp - rsrp >= 10)
                    {
                        pnts.Add(testPoint);
                        break;
                    }
                }
            }
            setReason(segItem, testPoint, pnts);
        }

        private void setReason(LowSpeedSeg segItem, TestPoint testPoint, List<TestPoint> pnts)
        {
            if (pnts.Count > 1)
            {
                TestPoint last = pnts[pnts.Count - 1];
                if (last.Time - testPoint.Time >= SecondMin)
                {
                    foreach (TestPoint pnt in pnts)
                    {
                        LeakCoverHandoverParam cln = this.Clone() as LeakCoverHandoverParam;
                        cln.serverCell = pnt.GetMainLTECell_TdOrFdd();
                        segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                    }
                }
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["secondMin"] = this.SecondMin;
                paramDic["rsrpMin"] = this.RSRPMin;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.SecondMin = (int)value["secondMin"];
                this.RSRPMin = (float)value["rsrpMin"];
            }
        }
    }

    [Serializable]
    public class LeakCoverWeakOutdoor : CauseBase
    {
        public override string Name
        {
            get { return "室外覆盖不足"; }
        }
        [NonSerialized]
        private ICell serverCell;
        public double DistanceMin { get; set; } = 300;
        public float RSRPDiffMax { get; set; } = 10;
        public override string Desc
        {
            get
            {
                return string.Format("采样点距离室内小区≥{0}米，邻区中有室外站，与室内信号强度差≤{1}dB", DistanceMin, RSRPDiffMax);
            }
        }

        public override string Suggestion
        {
            get
            {
                return string.Format("加强室外站{0}的覆盖", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            float? rsrp = (float?)GetRSRP(testPoint);
            serverCell = testPoint.GetMainLTECell_TdOrFdd();
            if (testPoint.Distance2(serverCell.Longitude, serverCell.Latitude) >= DistanceMin)
            {
                for (int i = 0; i < 10; i++)
                {
                    LTECell cell = testPoint.GetNBLTECell_TdOrFdd(i);
                    if (cell==null)
                    {
                        return;
                    }
                    if (cell.Type == LTEBTSType.Outdoor)
                    {
                        float? nRsrp = (float?)GetNRSRP(testPoint, i);
                        if (nRsrp != null && nRsrp >= -141
                            && Math.Abs((float)nRsrp - (float)rsrp) <= RSRPDiffMax)
                        {
                            LeakCoverWeakOutdoor cln = this.Clone() as LeakCoverWeakOutdoor;
                            segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
                            return;
                        }
                    }
                }

            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["distanceMin"] = this.DistanceMin;
                paramDic["rsrpDiffMax"] = this.RSRPDiffMax;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.DistanceMin = (double)value["distanceMin"];
                this.RSRPDiffMax = (float)value["rsrpDiffMax"];
            }
        }
    }


}
