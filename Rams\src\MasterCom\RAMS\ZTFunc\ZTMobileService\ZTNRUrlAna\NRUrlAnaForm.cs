﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRUrlAnaForm : MinCloseForm
    {
        public NRUrlAnaForm()
        {
            InitializeComponent();
            DisposeWhenClose = true;

            gvBroURL.RowClick += gvBroURL_RowClick;
            gvDowURL.RowClick += gvDowURL_RowClick;
            gvVideoURL.RowClick += gvVideoURL_RowClick;

            gvBroFile.RowClick += gvBroFile_RowClick;
            gvDowFile.RowClick += gvDowFile_RowClick;
            gvVideoFile.RowClick += gvVideoFile_RowClick;

            miExportExcel.Click += MiExportExcel_Click;

        }

        private List<NRUrlAnaInfoByFile> resultFiles = null;
        private List<NRUrlAnaInfoByRegion> resultFilesRegion = null;
        private bool isByRegion = false;

        public void FillData(List<NRUrlAnaInfo> result, bool byRegion)
        {
            isByRegion = byRegion;
            if (isByRegion)
            {
                gvBroFile.Columns[3].Visible = false;
                gvBroFile.Columns[4].Visible = false;
                gvDowFile.Columns[3].Visible = false;
                gvDowFile.Columns[4].Visible = false;
                gvVideoFile.Columns[3].Visible = false;
                gvVideoFile.Columns[4].Visible = false;

                resultFilesRegion = new List<NRUrlAnaInfoByRegion>();
                foreach (var item in result)
                {
                    resultFilesRegion.Add(item as NRUrlAnaInfoByRegion);
                }
                resultFiles = null;

                gcBrowse.DataSource = resultFilesRegion;
                gcBrowse.RefreshDataSource();
                gcDownload.DataSource = resultFilesRegion;
                gcDownload.RefreshDataSource();
                gcVideo.DataSource = resultFilesRegion;
                gcVideo.RefreshDataSource();
            }
            else
            {
                gvBroFile.Columns[1].Visible = false;
                gvBroFile.Columns[2].Visible = false;
                gvDowFile.Columns[1].Visible = false;
                gvDowFile.Columns[2].Visible = false;
                gvVideoFile.Columns[1].Visible = false;
                gvVideoFile.Columns[2].Visible = false;

                resultFiles = new List<NRUrlAnaInfoByFile>();
                foreach (var item in result)
                {
                    resultFiles.Add(item as NRUrlAnaInfoByFile);
                }
                resultFilesRegion = null;

                gcBrowse.DataSource = resultFiles;
                gcBrowse.RefreshDataSource();
                gcDownload.DataSource = resultFiles;
                gcDownload.RefreshDataSource();
                gcVideo.DataSource = resultFiles;
                gcVideo.RefreshDataSource();
            }
        }

        #region row click event
        void gvVideoURL_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            NRUrlVideoUrl Video = v as NRUrlVideoUrl;
            if (Video != null)
            {
                gcVideoDet.DataSource = Video.Events;
                gcVideoDet.RefreshDataSource();
            }
        }

        void gvVideoFile_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();

            setSubData(v, gcVideoDet, getVideoURLEvents);
        }

        void gvDowURL_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            NRUrlDowUrl Dow = v as NRUrlDowUrl;
            if (Dow != null)
            {
                gcDowDet.DataSource = Dow.Events;
                gcDowDet.RefreshDataSource();
            }
        }

        void gvDowFile_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            setSubData(v, gcVideoDet, getDownURLEvents);
        }

        void gvBroURL_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();
            NRUrlBroUrl Bro = v as NRUrlBroUrl;
            if (Bro != null)
            {
                gcBroDetail.DataSource = Bro.Events;
                gcBroDetail.RefreshDataSource();
            }
        }

        void gvBroFile_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetFocusedRow();

            setSubData(v, gcVideoDet, getBroURLEvents);
        }

        delegate List<NRUrlEvent> Func(NRUrlAnaInfo info);

        private void setSubData(object v, DevExpress.XtraGrid.GridControl gc, Func func)
        {
            NRUrlAnaInfo info;
            if (isByRegion)
            {
                info = v as NRUrlAnaInfoByRegion;
            }
            else
            {
                info = v as NRUrlAnaInfoByFile;
            }

            if (info != null)
            {
                gc.DataSource = func(info);
                gc.RefreshDataSource();
            }
        }

        private List<NRUrlEvent> getVideoURLEvents(NRUrlAnaInfo info)
        {
            List<NRUrlEvent> evtList = new List<NRUrlEvent>();
            foreach (var url in info.Videos)
            {
                evtList.AddRange(url.Events);
            }
            return evtList;
        }

        private List<NRUrlEvent> getDownURLEvents(NRUrlAnaInfo info)
        {
            List<NRUrlEvent> evtList = new List<NRUrlEvent>();
            foreach (var url in info.Downs)
            {
                evtList.AddRange(url.Events);
            }
            return evtList;
        }

        private List<NRUrlEvent> getBroURLEvents(NRUrlAnaInfo info)
        {
            List<NRUrlEvent> evtList = new List<NRUrlEvent>();
            foreach (var url in info.Bros)
            {
                evtList.AddRange(url.Events);
            }
            return evtList;
        }
        #endregion

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (resultFiles == null && resultFilesRegion == null)
            {
                return;
            }
            if (resultFiles != null)
            {
                var summmarize = new ZTMobileService.NRUrlSummarize();
                ZTMobileService.SummarizeModel result = summmarize.Summarize(resultFiles);

                #region 浏览
                List<NPOIRow> summBroTables = new List<NPOIRow>();
                NPOIRow summBroTitleRow = new NPOIRow();
                summBroTitleRow.AddCellValue("地市");
                summBroTitleRow.AddCellValue("URL");
                summBroTitleRow.AddCellValue("HTTP登陆尝试次数");
                summBroTitleRow.AddCellValue("HTTP登陆成功次数");
                summBroTitleRow.AddCellValue("HTTP完全加载次数");
                summBroTitleRow.AddCellValue("HTTP登陆成功率");
                summBroTitleRow.AddCellValue("HTTP登陆时延(S)");
                summBroTitleRow.AddCellValue("HTTP浏览成功率");
                summBroTitleRow.AddCellValue("HTTP浏览时长(S)");
                summBroTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                summBroTables.Add(summBroTitleRow);
                foreach (var summBro in result.Bros)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(summBro.DistrictName);
                    row.AddCellValue(summBro.Url);
                    row.AddCellValue(summBro.DisplayCount);
                    row.AddCellValue(summBro.DisSucCount);
                    row.AddCellValue(summBro.CompleteCount);
                    row.AddCellValue(summBro.DisplayRate);
                    row.AddCellValue(summBro.DisplayDelay);
                    row.AddCellValue(summBro.CompleteRate);
                    row.AddCellValue(summBro.Time);
                    row.AddCellValue(summBro.Speed);
                    summBroTables.Add(row);
                }

                List<NPOIRow> broTables = new List<NPOIRow>();
                NPOIRow broTitleRow = new NPOIRow();
                broTitleRow.AddCellValue("地市");
                broTitleRow.AddCellValue("文件名");
                broTitleRow.AddCellValue("URL");
                broTitleRow.AddCellValue("HTTP登陆尝试次数");
                broTitleRow.AddCellValue("HTTP登陆成功次数");
                broTitleRow.AddCellValue("HTTP完全加载次数");
                broTitleRow.AddCellValue("HTTP登陆成功率");
                broTitleRow.AddCellValue("HTTP登陆时延(S)");
                broTitleRow.AddCellValue("HTTP浏览成功率");
                broTitleRow.AddCellValue("HTTP浏览时长(S)");
                broTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                broTables.Add(broTitleRow);
                addFileData(broTables, addBroUrlInfo);

                List<NPOIRow> broEvtTables = new List<NPOIRow>();
                NPOIRow broEvtTitleRow = new NPOIRow();
                broEvtTitleRow.AddCellValue("文件名");
                broEvtTitleRow.AddCellValue("URL");
                broEvtTitleRow.AddCellValue("序号");
                broEvtTitleRow.AddCellValue("是否失败");
                broEvtTitleRow.AddCellValue("浏览时长(S)");
                broEvtTitleRow.AddCellValue("开始时间");
                broEvtTitleRow.AddCellValue("结束时间");
                broEvtTitleRow.AddCellValue("开始事件名称");
                broEvtTitleRow.AddCellValue("结束事件名称");
                broEvtTitleRow.AddCellValue("失败原因");
                broEvtTitleRow.AddCellValue("业务测试字节数");
                broEvtTables.Add(broEvtTitleRow);
                addFileData(broEvtTables, addBroEvts);
                #endregion
                #region 下载
                List<NPOIRow> summDownTables = new List<NPOIRow>();
                NPOIRow summDownTitleRow = new NPOIRow();
                summDownTitleRow.AddCellValue("地市");
                summDownTitleRow.AddCellValue("URL");
                summDownTitleRow.AddCellValue("HTTP下载尝试次数");
                summDownTitleRow.AddCellValue("HTTP下载成功次数");
                summDownTitleRow.AddCellValue("HTTP下载成功率");
                summDownTitleRow.AddCellValue("HTTP下载掉线次数");
                summDownTitleRow.AddCellValue("HTTP下载掉线率");
                summDownTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                summDownTitleRow.AddCellValue("应用层下载速率(含掉线)(kbps)");
                summDownTables.Add(summDownTitleRow);
                foreach (var summDown in result.Downs)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(summDown.DistrictName);
                    row.AddCellValue(summDown.Url);
                    row.AddCellValue(summDown.Dowcount);
                    row.AddCellValue(summDown.DowSuc);
                    row.AddCellValue(summDown.DowSucRate);
                    row.AddCellValue(summDown.DowFail);
                    row.AddCellValue(summDown.DowFaiRate);
                    row.AddCellValue(summDown.SucSpeed);
                    row.AddCellValue(summDown.Speed);
                    summDownTables.Add(row);
                }

                List<NPOIRow> downTables = new List<NPOIRow>();
                NPOIRow downTitleRow = new NPOIRow();
                downTitleRow.AddCellValue("地市");
                downTitleRow.AddCellValue("文件名");
                downTitleRow.AddCellValue("URL");
                downTitleRow.AddCellValue("HTTP下载尝试次数");
                downTitleRow.AddCellValue("HTTP下载成功次数");
                downTitleRow.AddCellValue("HTTP下载成功率");
                downTitleRow.AddCellValue("HTTP下载掉线次数");
                downTitleRow.AddCellValue("HTTP下载掉线率");
                downTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                downTitleRow.AddCellValue("应用层下载速率(含掉线)(kbps)");
                downTables.Add(downTitleRow);
                addFileData(downTables, addDownUrlInfo);

                List<NPOIRow> downEvtTables = new List<NPOIRow>();
                NPOIRow downEvtTitleRow = new NPOIRow();
                downEvtTitleRow.AddCellValue("文件名");
                downEvtTitleRow.AddCellValue("URL");
                downEvtTitleRow.AddCellValue("序号");
                downEvtTitleRow.AddCellValue("是否失败");
                downEvtTitleRow.AddCellValue("下载时长(S)");
                downEvtTitleRow.AddCellValue("开始时间");
                downEvtTitleRow.AddCellValue("结束时间");
                downEvtTitleRow.AddCellValue("开始事件名称");
                downEvtTitleRow.AddCellValue("结束事件名称");
                downEvtTitleRow.AddCellValue("失败原因");
                downEvtTitleRow.AddCellValue("业务测试字节数");
                downEvtTables.Add(downEvtTitleRow);
                addFileData(downEvtTables, addDownEvts);
                #endregion
                #region 流媒体
                List<NPOIRow> summVideoTables = new List<NPOIRow>();
                NPOIRow summVideoTitleRow = new NPOIRow();
                summVideoTitleRow.AddCellValue("地市");
                summVideoTitleRow.AddCellValue("URL");
                summVideoTitleRow.AddCellValue("流媒体业务发起次数");
                summVideoTitleRow.AddCellValue("流媒体业务成功次数");
                summVideoTitleRow.AddCellValue("流媒体业务成功率");
                summVideoTitleRow.AddCellValue("流媒体加载时延(S)");
                summVideoTitleRow.AddCellValue("流媒体时长（s）");
                summVideoTitleRow.AddCellValue("流媒体卡顿时长(S)");
                summVideoTitleRow.AddCellValue("流媒体播放总时长（s）");
                summVideoTitleRow.AddCellValue("流媒体播放卡顿次数");
                summVideoTitleRow.AddCellValue("流媒体播放超时比例");
                summVideoTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                summVideoTitleRow.AddCellValue("流媒体加载速率(kbps)");
                summVideoTables.Add(summVideoTitleRow);
                foreach (var summVideo in result.Videos)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(summVideo.DistrictName);
                    row.AddCellValue(summVideo.Url);
                    row.AddCellValue(summVideo.ReqCount);
                    row.AddCellValue(summVideo.SucCount);
                    row.AddCellValue(summVideo.SucRate);
                    row.AddCellValue(summVideo.Delay);
                    row.AddCellValue(summVideo.Time);
                    row.AddCellValue(summVideo.RebufferTime);
                    row.AddCellValue(summVideo.PlayTime);
                    row.AddCellValue(summVideo.RebufferCount);
                    row.AddCellValue(summVideo.TimeoutRate);
                    row.AddCellValue(summVideo.DownSpeed);
                    row.AddCellValue(summVideo.LoadSpeed);
                    summVideoTables.Add(row);
                }

                List<NPOIRow> videoTables = new List<NPOIRow>();
                NPOIRow videoTitleRow = new NPOIRow();
                videoTitleRow.AddCellValue("地市");
                videoTitleRow.AddCellValue("文件名");
                videoTitleRow.AddCellValue("URL");
                videoTitleRow.AddCellValue("流媒体业务发起次数");
                videoTitleRow.AddCellValue("流媒体业务成功次数");
                videoTitleRow.AddCellValue("流媒体业务成功率");
                videoTitleRow.AddCellValue("流媒体加载时延(S)");
                videoTitleRow.AddCellValue("流媒体时长（s）");
                videoTitleRow.AddCellValue("流媒体卡顿时长(S)");
                videoTitleRow.AddCellValue("流媒体播放总时长（s）");
                videoTitleRow.AddCellValue("流媒体播放卡顿次数");
                videoTitleRow.AddCellValue("流媒体播放超时比例");
                videoTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                videoTitleRow.AddCellValue("流媒体加载速率(kbps)");
                videoTables.Add(videoTitleRow);
                addFileData(videoTables, addVideoUrlInfo);

                List<NPOIRow> videoEvtTables = new List<NPOIRow>();
                NPOIRow videoEvtTitleRow = new NPOIRow();
                videoEvtTitleRow.AddCellValue("文件名");
                videoEvtTitleRow.AddCellValue("URL");
                videoEvtTitleRow.AddCellValue("序号");
                videoEvtTitleRow.AddCellValue("是否失败");
                videoEvtTitleRow.AddCellValue("时长(S)");
                videoEvtTitleRow.AddCellValue("开始时间");
                videoEvtTitleRow.AddCellValue("结束时间");
                videoEvtTitleRow.AddCellValue("开始事件名称");
                videoEvtTitleRow.AddCellValue("结束事件名称");
                videoEvtTitleRow.AddCellValue("失败原因");
                videoEvtTitleRow.AddCellValue("业务测试字节数");
                videoEvtTables.Add(videoEvtTitleRow);
                addFileData(videoEvtTables, addVideoEvts);
                #endregion
                ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { summBroTables, summDownTables, summVideoTables, broTables, broEvtTables, downTables, downEvtTables, videoTables, videoEvtTables },
                    new List<string>() { "http浏览汇总", "http下载汇总", "流媒体汇总", "http浏览统计", "http浏览详情", "http下载统计", "http下载详情", "流媒体统计", "流媒体详情" });
            }
            else if (resultFilesRegion != null)
            {
                #region
                List<NPOIRow> broTables = new List<NPOIRow>();
                NPOIRow broTitleRow = new NPOIRow();
                broTitleRow.AddCellValue("图层类型");
                broTitleRow.AddCellValue("网格信息");
                broTitleRow.AddCellValue("URL");
                broTitleRow.AddCellValue("HTTP登陆尝试次数");
                broTitleRow.AddCellValue("HTTP登陆成功次数");
                broTitleRow.AddCellValue("HTTP完全加载次数");
                broTitleRow.AddCellValue("HTTP登陆成功率");
                broTitleRow.AddCellValue("HTTP登陆时延(S)");
                broTitleRow.AddCellValue("HTTP浏览成功率");
                broTitleRow.AddCellValue("HTTP浏览时长(S)");
                broTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                broTables.Add(broTitleRow);
                addRegionData(broTables, addBroUrlInfo);

                List<NPOIRow> broEvtTables = new List<NPOIRow>();
                NPOIRow broEvtTitleRow = new NPOIRow();
                broEvtTitleRow.AddCellValue("图层类型");
                broEvtTitleRow.AddCellValue("网格信息");
                //broEvtTitleRow.AddCellValue("文件名");
                broEvtTitleRow.AddCellValue("URL");
                broEvtTitleRow.AddCellValue("序号");
                broEvtTitleRow.AddCellValue("是否失败");
                broEvtTitleRow.AddCellValue("浏览时长(S)");
                broEvtTitleRow.AddCellValue("开始时间");
                broEvtTitleRow.AddCellValue("结束时间");
                broEvtTitleRow.AddCellValue("开始事件名称");
                broEvtTitleRow.AddCellValue("结束事件名称");
                broEvtTitleRow.AddCellValue("失败原因");
                broEvtTitleRow.AddCellValue("业务测试字节数");
                broEvtTables.Add(broEvtTitleRow);
                addRegionData(broEvtTables, addBroEvts);
                #endregion
                #region
                List<NPOIRow> downTables = new List<NPOIRow>();
                NPOIRow downTitleRow = new NPOIRow();
                downTitleRow.AddCellValue("图层类型");
                downTitleRow.AddCellValue("网格信息");
                downTitleRow.AddCellValue("URL");
                downTitleRow.AddCellValue("HTTP下载尝试次数");
                downTitleRow.AddCellValue("HTTP下载成功次数");
                downTitleRow.AddCellValue("HTTP下载成功率");
                downTitleRow.AddCellValue("HTTP下载掉线次数");
                downTitleRow.AddCellValue("HTTP下载掉线率");
                downTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                downTitleRow.AddCellValue("应用层下载速率(含掉线)(kbps)");
                downTables.Add(downTitleRow);
                addRegionData(downTables, addDownUrlInfo);

                List<NPOIRow> downEvtTables = new List<NPOIRow>();
                NPOIRow downEvtTitleRow = new NPOIRow();
                downEvtTitleRow.AddCellValue("图层类型");
                downEvtTitleRow.AddCellValue("网格信息");
                //downEvtTitleRow.AddCellValue("文件名");
                downEvtTitleRow.AddCellValue("URL");
                downEvtTitleRow.AddCellValue("序号");
                downEvtTitleRow.AddCellValue("是否失败");
                downEvtTitleRow.AddCellValue("下载时长(S)");
                downEvtTitleRow.AddCellValue("开始时间");
                downEvtTitleRow.AddCellValue("结束时间");
                downEvtTitleRow.AddCellValue("开始事件名称");
                downEvtTitleRow.AddCellValue("结束事件名称");
                downEvtTitleRow.AddCellValue("失败原因");
                downEvtTitleRow.AddCellValue("业务测试字节数");
                downEvtTables.Add(downEvtTitleRow);
                addRegionData(downEvtTables, addDownEvts);
                #endregion
                #region
                List<NPOIRow> videoTables = new List<NPOIRow>();
                NPOIRow videoTitleRow = new NPOIRow();
                videoTitleRow.AddCellValue("图层类型");
                videoTitleRow.AddCellValue("网格信息");
                videoTitleRow.AddCellValue("URL");
                videoTitleRow.AddCellValue("流媒体业务发起次数");
                videoTitleRow.AddCellValue("流媒体业务成功次数");
                videoTitleRow.AddCellValue("流媒体业务成功率");
                videoTitleRow.AddCellValue("流媒体加载时延(S)");
                videoTitleRow.AddCellValue("流媒体时长(S)");
                videoTitleRow.AddCellValue("流媒体卡顿时长(S)");
                videoTitleRow.AddCellValue("流媒体播放总时长(S)");
                videoTitleRow.AddCellValue("流媒体播放卡顿次数");
                videoTitleRow.AddCellValue("流媒体播放超时比例");
                videoTitleRow.AddCellValue("应用层下载速率(不含掉线)(kbps)");
                videoTitleRow.AddCellValue("流媒体加载速率(kbps)");
                videoTables.Add(videoTitleRow);
                addRegionData(videoTables, addVideoEvts);

                List<NPOIRow> videoEvtTables = new List<NPOIRow>();
                NPOIRow videoEvtTitleRow = new NPOIRow();
                videoEvtTitleRow.AddCellValue("图层类型");
                videoEvtTitleRow.AddCellValue("网格信息");
                //videoEvtTitleRow.AddCellValue("文件名");
                videoEvtTitleRow.AddCellValue("URL");
                videoEvtTitleRow.AddCellValue("序号");
                videoEvtTitleRow.AddCellValue("是否失败");
                videoEvtTitleRow.AddCellValue("时长(S)");
                videoEvtTitleRow.AddCellValue("开始时间");
                videoEvtTitleRow.AddCellValue("结束时间");
                videoEvtTitleRow.AddCellValue("开始事件名称");
                videoEvtTitleRow.AddCellValue("结束事件名称");
                videoEvtTitleRow.AddCellValue("失败原因");
                videoEvtTitleRow.AddCellValue("业务测试字节数");
                videoEvtTables.Add(videoEvtTitleRow);
                addRegionData(videoEvtTables, addVideoEvts);
                #endregion

                ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { broTables, broEvtTables, downTables, downEvtTables, videoTables, videoEvtTables },
                    new List<string>() { "http浏览统计", "http浏览详情", "http下载统计", "http下载详情", "流媒体统计", "流媒体详情" });
            }
        }

        private void addBroUrlInfo(List<NPOIRow> broTables, NRUrlAnaInfo dtlModel, List<object> fileValues)
        {
            foreach (var dtlBro in dtlModel.Bros)
            {
                List<object> broValues = new List<object>(fileValues);
                broValues.Add(dtlBro.Url);
                broValues.Add(dtlBro.DisCount);
                broValues.Add(dtlBro.DisSuc);
                broValues.Add(dtlBro.Complete);
                broValues.Add(dtlBro.DisSucRate * 100);
                broValues.Add(dtlBro.DisDelay);
                broValues.Add(dtlBro.SucRate * 100);
                broValues.Add(dtlBro.Time);
                broValues.Add(dtlBro.Speed);
                NPOIRow broRow = new NPOIRow();
                broRow.cellValues.AddRange(broValues);
                broTables.Add(broRow);
            }
        }

        private void addBroEvts(List<NPOIRow> broEvtTables, NRUrlAnaInfo dtlModel, List<object> fileValues)
        {
            foreach (var dtlBro in dtlModel.Bros)
            {
                foreach (var evt in dtlBro.Events)
                {
                    List<object> evtValues = new List<object>(fileValues);
                    //evtValues.Add(evt.FileName);
                    evtValues.Add(evt.Url);
                    evtValues.Add(evt.SN);
                    evtValues.Add(evt.IsFail);
                    evtValues.Add(evt.TimeSpan);
                    evtValues.Add(evt.StartTime);
                    evtValues.Add(evt.EndTime);
                    evtValues.Add(evt.StartName);
                    evtValues.Add(evt.EvtEndName);
                    evtValues.Add(evt.FailReason);
                    evtValues.Add(evt.Bytes);
                    NPOIRow evtRow = new NPOIRow();
                    evtRow.cellValues.AddRange(evtValues);
                    broEvtTables.Add(evtRow);
                }
            }
        }

        private void addDownUrlInfo(List<NPOIRow> downTables, NRUrlAnaInfo dtlModel, List<object> fileValues)
        {
            foreach (var dtlDown in dtlModel.Downs)
            {
                List<object> downValues = new List<object>(fileValues);
                downValues.Add(dtlDown.Url);
                downValues.Add(dtlDown.Dowcount);
                downValues.Add(dtlDown.DowSuc);
                downValues.Add(dtlDown.DowSucRate * 100);
                downValues.Add(dtlDown.DowFail);
                downValues.Add(dtlDown.DowFaiRate * 100);
                downValues.Add(dtlDown.SucSpeed);
                downValues.Add(dtlDown.Speed);

                NPOIRow downRow = new NPOIRow();
                downRow.cellValues.AddRange(downValues);
                downTables.Add(downRow);
            }
        }

        private void addDownEvts(List<NPOIRow> downEvtTables, NRUrlAnaInfo dtlModel, List<object> fileValues)
        {
            foreach (var dtlDown in dtlModel.Downs)
            {
                foreach (var evt in dtlDown.Events)
                {
                    List<object> evtValues = new List<object>(fileValues);
                    //evtValues.Add(evt.FileName);
                    evtValues.Add(evt.Url);
                    evtValues.Add(evt.SN);
                    evtValues.Add(evt.IsFail);
                    evtValues.Add(evt.TimeSpan);
                    evtValues.Add(evt.StartTime);
                    evtValues.Add(evt.EndTime);
                    evtValues.Add(evt.StartName);
                    evtValues.Add(evt.EvtEndName);
                    evtValues.Add(evt.FailReason);
                    evtValues.Add(evt.Bytes);
                    NPOIRow evtRow = new NPOIRow();
                    evtRow.cellValues.AddRange(evtValues);
                    downEvtTables.Add(evtRow);
                }
            }
        }

        private void addVideoUrlInfo(List<NPOIRow> videoTables, NRUrlAnaInfo dtlModel, List<object> fileValues)
        {
            foreach (var dtlVideo in dtlModel.Videos)
            {
                List<object> videoValues = new List<object>(fileValues);
                videoValues.Add(dtlVideo.Url);
                videoValues.Add(dtlVideo.ReqCount);
                videoValues.Add(dtlVideo.SucCount);
                videoValues.Add(dtlVideo.SucRate * 100);
                videoValues.Add(dtlVideo.Delay);
                videoValues.Add(dtlVideo.Time);
                videoValues.Add(dtlVideo.RebufferTime);
                videoValues.Add(dtlVideo.PlayTime);
                videoValues.Add(dtlVideo.RebufferCount);
                videoValues.Add(dtlVideo.TimeoutRate * 100);
                videoValues.Add(dtlVideo.DownSpeed);
                videoValues.Add(dtlVideo.LoadSpeed);

                NPOIRow videoRow = new NPOIRow();
                videoRow.cellValues.AddRange(videoValues);
                videoTables.Add(videoRow);
            }
        }

        private void addVideoEvts(List<NPOIRow> videoEvtTables, NRUrlAnaInfo dtlModel, List<object> fileValues)
        {
            foreach (var dtlVideo in dtlModel.Videos)
            {
                foreach (var evt in dtlVideo.Events)
                {
                    List<object> evtValues = new List<object>(fileValues);
                    //evtValues.Add(evt.FileName);
                    evtValues.Add(evt.Url);
                    evtValues.Add(evt.SN);
                    evtValues.Add(evt.IsFail);
                    evtValues.Add(evt.TimeSpan);
                    evtValues.Add(evt.StartTime);
                    evtValues.Add(evt.EndTime);
                    evtValues.Add(evt.StartName);
                    evtValues.Add(evt.EvtEndName);
                    evtValues.Add(evt.FailReason);
                    evtValues.Add(evt.Bytes);
                    NPOIRow evtRow = new NPOIRow();
                    evtRow.cellValues.AddRange(evtValues);
                    videoEvtTables.Add(evtRow);
                }
            }
        }

        delegate void FillDataFunc(List<NPOIRow> evtTables, NRUrlAnaInfo dtlModel, List<object> fileValues);

        private void addFileData(List<NPOIRow> evtTables, FillDataFunc func)
        {
            foreach (var dtlModel in resultFiles)
            {
                List<object> fileValues = new List<object>();
                fileValues.Add(dtlModel.FileName);
                func(evtTables, dtlModel, fileValues);
            }
        }

        private void addRegionData(List<NPOIRow> evtTables, FillDataFunc func)
        {
            foreach (var dtlModel in resultFilesRegion)
            {
                List<object> fileValues = new List<object>();
                fileValues.Add(dtlModel.RegionName);
                fileValues.Add(dtlModel.GridName);
                func(evtTables, dtlModel, fileValues);
            }
        }

        private void miReplayFile_Click(object sender, EventArgs e)
        {
            NRUrlEvent evt = null;
            if (gvBroDet.IsFocusedView)
            {
                evt = gvBroDet.GetFocusedRow() as NRUrlEvent;
            }
            else if (gvDowDet.IsFocusedView)
            {
                evt = gvDowDet.GetFocusedRow() as NRUrlEvent;
            }
            else if (gvVideoDet.IsFocusedView)
            {
                evt = gvVideoDet.GetFocusedRow() as NRUrlEvent;
            }
            if (evt != null)
            {
                DateTime begin = DateTime.Parse(evt.StartTime);
                DateTime end = DateTime.Parse(evt.EndTime);
                FileReplayer.ReplayOnePart(evt.EventStart, new TimePeriod(begin, end));
            }
        }
    }
}
