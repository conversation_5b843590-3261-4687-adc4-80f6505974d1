<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">2_集团最新指标-长呼-备注集团差异</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">导频污染栅格比例</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MOS均值</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">软切换比例;采样点中激活集&gt;1即为处于软切换状态，∑（采样点中激活集&gt;1）/总采样点</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">GSM-评估指标</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxLevSub≥-75dBm的采样点比例</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Rxlevsub≥-80dBm&amp;C/I≥12dB的采样点占总采样点的比例</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">C/I≥12dBm的采样点比例</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">差点栅格：栅格内C/I≤12dB的采样点占此栅格内总采样点的比例大于等于20%的为差点栅格</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MOS均值</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">项目</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">覆盖</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">干扰</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">话音质量</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MOS值分布：MOS值小于等于3的采样点占所有MOS采样点的比例
</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">差点栅格：栅格内RxlevSub≤-90dBm的采样点占此栅格内总采样点的比例大于等于20%的为差点栅格</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MOS值分布:MOS值小于等于3的采样点占所有MOS采样点的比例</Item>
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">WCDMA-评估指标</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">项目</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Wx_5D0A0402+Wx_5D0A0403+Wx_5D0A0404+Wx_5D0A0405+Wx_5D0A0406)/(Wx_5D0A0401+Wx_5D0A0402+Wx_5D0A0403+Wx_5D0A0404+Wx_5D0A0405+Wx_5D0A0406) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_6D0A09) }</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Wx_6D0A01+Wx_6D0A02+Wx_6D0A03+Wx_6D0A04)/(Wx_6D0A08) }%</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A01070B)/(Mx_5A010709) }%</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010605+Mx_5A010705+Mx_5A01070A+Mx_5A01060A)/(Mx_5A010609+Mx_5A010709) }%</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Mx_5A010201)&gt;0?((Mx_5A010207+Mx_5A010206+Mx_5A010205)/(Mx_5A010201)):-99999}@&gt;0.2</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Mx_5A010709+Mx_5A010609) &gt;0?((Mx_5A010601+Mx_5A010602+Mx_5A010603+Mx_5A010604+Mx_5A010701+Mx_5A010702+Mx_5A010703+Mx_5A010704)/(Mx_5A010709+Mx_5A010609)):-99999}@&gt;0.2</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_5D0A051F)&gt;0?(((Wx_5D0A0504)+(Wx_5D0A0505)+(Wx_5D0A0506))/(Wx_5D0A051F)):-99999}@&gt;0.03</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010B55+Mx_5A010B56+Mx_5A010B57+Mx_5A010B58)/Mx_5A010B5C }%</Item>
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B53}</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[414]}</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[659]}</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[413])/(evtIdCount[412]) }%</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010217)/Mx_5A010201}%</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[658])/(evtIdCount[657])}%</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">覆盖</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">干扰和3G占比</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">保持性</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">业务质量</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">话音保持</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((Mx_0837+Mx_0835)/60000)/(evtIdCount[5]+evtIdCount[6]+value9[5]+value9[6]+evtIdCount[906]+evtIdCount[907]+value9[906]+value9[907]+evtIdCount[902]+evtIdCount[903]+evtIdCount[413]) }分钟/次</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Mx_0838+Mx_0836)/1000*(evtIdCount[5]+evtIdCount[6]+value9[5]+value9[6]+evtIdCount[906]+evtIdCount[907]+value9[906]+value9[907]+evtIdCount[902]+evtIdCount[903]+evtIdCount[413])  }公里/次</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_0827/60000)/(evtIdCount[505]+evtIdCount[517]+value9[505]+value9[517]+evtIdCount[658]) }分钟/次</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_0828)/1000*(evtIdCount[505]+evtIdCount[517]+value9[505]+value9[517]+evtIdCount[658]) }公里/次</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">64</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">话音业务掉话率：话音业务掉话率=话音业务掉话次数/话音业务通话次数(存在差异)</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">话音连续业务掉话差点次数：差点定义：连续2次发生话音业务掉话事件为1个差点；如连续2次业务掉话计差点1个，如连续3次语音业务掉话，那么差点为2个，依此类推(存在差异)</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">时间掉话比：通话总时长/（信令掉话次数+业务掉话次数）(存在差异)</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">里程掉话比：测试总里程/（信令掉话次数+业务掉话次数）(存在差异)</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">话音业务掉话率：话音业务掉话率=话音业务掉话次数/话音业务通话次数(存在差异)</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">话音连续业务掉话差点次数：差点定义：连续2次发生话音业务掉话事件为1个差点；如连续2次业务掉话计差点1个，如连续3次语音业务掉话，那么差点为2个，依此类推(存在差异)</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">时间掉话比（秒）：通话总时长/（信令掉话次数+业务掉话次数）(存在差异)</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">里程掉话比（米）：测试总里程/（信令掉话次数+业务掉话次数）(存在差异)</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColInfo" />
      <Item typeName="IList" key="ColWidth">
        <Item typeName="Int32">103</Item>
        <Item typeName="Int32">590</Item>
        <Item typeName="Int32">168</Item>
        <Item typeName="Int32">123</Item>
        <Item typeName="Int32">114</Item>
        <Item typeName="Int32">108</Item>
        <Item typeName="Int32">104</Item>
        <Item typeName="Int32">102</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
      </Item>
    </Item>
  </Config>
</Configs>