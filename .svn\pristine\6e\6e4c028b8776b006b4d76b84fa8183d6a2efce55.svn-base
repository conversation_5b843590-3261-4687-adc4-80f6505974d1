﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.MTGis
{
    public class VisibleScale
    {
        public double ScaleMin { get; set; }
        public double ScaleMax { get; set; }
        public VisibleScale(double scaleMin, double scaleMax)
        {
            ScaleMin = scaleMin;
            ScaleMax = scaleMax;
        }
        public bool IsWithinScale(double value)
        {
            return value >= ScaleMin && value <= ScaleMax;
        }
    }
}
