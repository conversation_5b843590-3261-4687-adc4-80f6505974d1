﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 由于新疆部分地市历史工参存在问题
    /// 故单验时匹配工参改为匹配最新工参,而非匹配采样点时间的历史工参
    /// 为了防止散弹式修改,添加此类统一控制
    /// </summary>
    public class StationAcceptCellHelper_XJ
    {
        private static StationAcceptCellHelper_XJ instance = null;
        public static StationAcceptCellHelper_XJ Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new StationAcceptCellHelper_XJ();
                }
                return instance;
            }
        }

        /// <summary>
        /// 目前并未加成可配置项,仅在代码中统一控制
        /// </summary>
        public bool IsByTestTime { get; set; } = false;

        #region LTECell
        public LTECell GetLTECell(TestPoint tp)
        {
            return tp.GetMainCell_LTE(IsByTestTime);
        }

        public List<LTECell> GetLTECellList(TestPoint tp)
        {
            return tp.GetCellList_LTE(IsByTestTime);
        }

        public List<LTECell> GetLTECellListByEarfcnPci(TestPoint tp)
        {
            return tp.GetCellListByEARFCN_PCI_LTE(IsByTestTime);
        }

        public List<LTECell> GetLTECellListByEarfcnPci(DateTime time, int? earfcn, int? pci)
        {
            return CellManager.GetInstance().GetLTECellListByEARFCNPCI(GetValidDate(time), earfcn, pci);
        }

        public LTECell GetLTECellByEarfcnPci(TestPoint tp, int? earfcn, int? pci)
        {
            return BackgroundFunc.MultiStationAutoAcceptManager.GetLTECellByEARFCNPCI(getTpDate(tp), earfcn, pci, tp.Longitude, tp.Latitude, tp.FileName);
        }

        public LTECell GetLTECellByTacEci(TestPoint tp)
        {
            return CellManager.GetInstance().GetNearestLTECellByTACCI(getTpDate(tp)
                , (int?)(ushort?)tp["lte_TAC"], (int?)tp["lte_ECI"], tp.Longitude, tp.Latitude);
        }

        public LTECell GetLTECellByTacEci(DateTime time, int tac, int eci)
        {
            return CellManager.GetInstance().GetLTECell(GetValidDate(time), tac, eci);
        }
        #endregion

        #region NRCell
        public NRCell GetNRCell(TestPoint tp)
        {
            return tp.GetMainCell_NR(IsByTestTime);
        }

        public List<NRCell> GetNRCellListByEarfcnPci(TestPoint tp, int? arfcn, int? pci)
        {
            return CellManager.GetInstance().GetNRCellListByARFCNPCI(getTpDate(tp), arfcn, pci);
        }

        public NRCell GetNBCell_NR(TestPoint tp, int index)
        {
            return tp.GetNBCell_NR(IsByTestTime, index);
        }
        #endregion

        public DateTime GetValidDate(DateTime time)
        {
            if (IsByTestTime)
            {
                return time;
            }
            return DateTime.Now;
        }

        private DateTime getTpDate(TestPoint tp)
        {
            if (IsByTestTime)
            {
                return tp.DateTime;
            }
            return DateTime.Now;
        }
    }
}
