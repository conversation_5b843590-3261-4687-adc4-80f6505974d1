﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class SmallBtsAcpAutoVolteVoiceMo : AcpAutoRrcRate
    {
        public SmallBtsAcpAutoVolteVoiceMo()
        {
            evtRequList = new List<int> { 1070 };
            evtSuccList = new List<int> { 1072 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != 1)
            {
                return false;
            }
            return fileInfo.Name.ToUpper().Contains("VOLTE");
        }

        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>
            {
                { KpiKey.VolteAudioCallRequestCnt, kpiCell.RequestCnt },
                { KpiKey.VolteAudioCallSucceedCnt, kpiCell.SucceedCnt }
            };
            return kpiInfos;
        }
    }

    class SmallBtsAcpAutoVolteVoiceMt : AcpAutoRrcRate
    {
        public SmallBtsAcpAutoVolteVoiceMt()
        {
            evtRequList = new List<int> { 1071 };
            evtSuccList = new List<int> { 1073 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != 2)
            {
                return false;
            }
            return fileInfo.Name.ToUpper().Contains("VOLTE");
        }

        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>
            {
                { KpiKey.VolteVideoCallRequestCnt, kpiCell.RequestCnt },
                { KpiKey.VolteVideoCallSucceedCnt, kpiCell.SucceedCnt }
            };
            return kpiInfos;
        }
    }
}
