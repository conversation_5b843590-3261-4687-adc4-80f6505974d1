﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraFormMustTestDesc : DevExpress.XtraEditors.XtraForm
    {
        MainModel MainModel;
        List<MustTestRoadDesc> musttestroaddescList_remove = new List<MustTestRoadDesc>();
        List<MustTestRoadDesc> musttestroaddescList_all = new List<MustTestRoadDesc>();
        List<MustTestRoadDesc> musttestroaddescList_select = new List<MustTestRoadDesc>();
        List<string> strSelected = new List<string>();

        List<MustTestRoadDesc> musttestrodadesclist_part = new List<MustTestRoadDesc>();

        public XtraFormMustTestDesc(MainModel mainModel)
        {
            InitializeComponent();
            MainModel = mainModel;
        }


        public void setData(List<MustTestRoadDesc> mymusttestroaddesclist)
        {
            strSelected.Clear();
            musttestroaddescList_remove.Clear();
            musttestroaddescList_all.Clear();
            musttestroaddescList_select.Clear();
            MainModel.MusttestroaddescList_select.Clear();

            //默认显示必测道路
            musttestroaddescList_all = mymusttestroaddesclist;
            Dictionary<string, List<MustTestRoadManager>> mtGridRoadinfoDicNewDraw = MainModel.MtGridRoadinfoDicNewDraw;

            foreach (string item in mtGridRoadinfoDicNewDraw.Keys)
            {
                foreach (MustTestRoadDesc item2 in musttestroaddescList_all)
                {
                    if (item2.RoadName == item)
                    {
                        musttestroaddescList_remove.Add(item2);
                    }
                }
            }

            gridControl1.DataSource = musttestroaddescList_remove;
            musttestroaddescList_select = new List<MustTestRoadDesc>(musttestroaddescList_remove);
            MainModel.MusttestroaddescList_select = musttestroaddescList_select;
            musttestrodadesclist_part = new List<MustTestRoadDesc>(musttestroaddescList_select);
            print(musttestroaddescList_remove, "全部必测道路中");

        }

        //数据变动
        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            gridControl1.DataSource = null;
            if (checkBox1.Checked)//选中显示所有道路
            {
                gridControl1.DataSource =musttestroaddescList_all;

                for (int i = 0; i < gridView1.RowCount; i++)
                {
                    string strtemp=gridView1.GetRowCellValue(i, "RoadName").ToString();
                    if (musttestroaddescList_remove.Exists(delegate(MustTestRoadDesc p) { return p.RoadName == strtemp; }))
                    {
                        this.gridView1.SetRowCellValue(i, "Check", true);
                    }
                    else
                    {
                        this.gridView1.SetRowCellValue(i, "Check", false);
                    }
                }

                MainModel.MusttestroaddescList_select = new List<MustTestRoadDesc>(musttestroaddescList_remove);
                strSelected.Clear();
                foreach (MustTestRoadDesc item in musttestroaddescList_remove)
                {
                   
                        strSelected.Add(item.RoadName);
                    
                }
                print(musttestroaddescList_remove, "全部必测道路中(包含所有道路)"); 
            }
            else
            {
                gridControl1.DataSource = new List<MustTestRoadDesc>(musttestroaddescList_select);
                print(musttestroaddescList_select, "全部必测道路中");
            }
        }


        private void print(List<MustTestRoadDesc> mtrdescList,string strdesc)
        {
            int eventnum = 0;
            double lasterror=0;//持续差
            double permeate=0;//渗透率
            double mileage=0;//总公里
            int maxnum = mtrdescList.Count;

            foreach (MustTestRoadDesc item in mtrdescList)
            {
                if (item.EventNum > 0)
                {
                    eventnum = eventnum + item.EventNum;
                }
                if (item.LastError > 0)
                {
                    lasterror = lasterror + item.LastError;
                }
                if (item.Permeate > 0)
                {
                    permeate = permeate + item.Permeate;
                }
                if (item.Mileage > 0)
                {
                    mileage = mileage + item.Mileage;
                }
            }


            //统计必测道路占比
            double mileageAll=0;
            foreach (MustTestRoadDesc item in musttestroaddescList_all)
            {
                mileageAll = mileageAll + item.Mileage;
            }


            if (strdesc.IndexOf("所有") != -1) //所有道路
            {
                textBox1.Text = strdesc + " , 事件总数 :" + eventnum + "个 , 持续差占比 :" + Math.Round((lasterror / maxnum ), 2) + "% , 渗透率 :" + Math.Round((permeate / maxnum), 2) + "% , 道路里程 :" + Math.Round(mileage,2) + "米 , 共 " + maxnum + " 条道路.";
            }
            else //必测道路
            {
                textBox1.Text = strdesc + " , 事件总数 :" + eventnum + "个 , 持续差占比 :" + Math.Round((lasterror / maxnum ), 2) + "% , 渗透率 :" + Math.Round((permeate / maxnum), 2) + "% , 道路里程 :" + Math.Round(mileage,2) + "米 , 共 " + maxnum + " 条道路 , 必测道路占比 :" + Math.Round((mileage / mileageAll * 100), 2) + "%";
            }
        }

        //全选
        private void checkBox2_CheckedChanged(object sender, EventArgs e)
        {
            strSelected.Clear();
            musttestroaddescList_select.Clear();
            for (int i = 0; i < gridView1.RowCount; i++)
            {
                if (checkBox2.Checked)
                {
                    this.gridView1.SetRowCellValue(i, "Check", true);
                    strSelected.Add(gridView1.GetRowCellValue(i, "RoadName").ToString());
                }
                else
                {
                    this.gridView1.SetRowCellValue(i, "Check", false);

                }
            }

            //重新统计
            foreach (MustTestRoadDesc item in musttestroaddescList_all)
            {
                if (strSelected.Contains(item.RoadName))
                {
                    musttestroaddescList_select.Add(item);
                }
            }

            MainModel.MusttestroaddescList_select = musttestroaddescList_select;
            print(musttestroaddescList_select, "全部必测道路中");
        }

        //选中道路
        private void gridView1_CellValueChanging(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            gridView1.SetRowCellValue(e.RowHandle, e.Column, e.Value);

            strSelected.Clear();
            string value = "";
            for (int i = 0; i < gridView1.RowCount; i++)
            {
                value = gridView1.GetRowCellValue(i, "Check").ToString();
                if (value == "True")
                {
                    strSelected.Add(gridView1.GetRowCellValue(i, "RoadName").ToString());
                }
                else
                {
                    if (strSelected.Contains(gridView1.GetRowCellValue(i, "RoadName").ToString()))
                    {
                        strSelected.Remove(gridView1.GetRowCellValue(i, "RoadName").ToString());
                    }
                }
            }

            musttestroaddescList_select.Clear();
            MainModel.MusttestroaddescList_select.Clear();

            foreach (MustTestRoadDesc item in musttestroaddescList_all)
            {
                if (strSelected.Contains(item.RoadName))
                {
                    musttestroaddescList_select.Add(item);
                }
            }

            MainModel.MusttestroaddescList_select = musttestroaddescList_select;
            print(musttestroaddescList_select, "全部必测道路中");
        }

        //选择道路级别
        private void comboBoxEdit1_EditValueChanged(object sender, EventArgs e)
        {
            musttestroaddescList_select.Clear();
            MainModel.MusttestroaddescList_select.Clear();
            strSelected.Clear();

            List<int> gridLevel = new List<int>();


            for (int i = 0; i < comboBoxEdit1.Properties.Items.Count; i++)
			{
                if (comboBoxEdit1.Properties.Items[i].CheckState == CheckState.Checked)
                {
                    gridLevel.Add(Convert.ToInt32(comboBoxEdit1.Properties.Items[i].Value));
                }
			}

            foreach (MustTestRoadDesc item in musttestroaddescList_all)
            {
                if (gridLevel.Contains(item.GridRoaditype))
                {
                    musttestroaddescList_select.Add(item);
                }
            }
            musttestrodadesclist_part = new List<MustTestRoadDesc>(musttestroaddescList_select);
            gridControl1.DataSource = musttestrodadesclist_part;

            //重新统计
            MainModel.MusttestroaddescList_select = musttestroaddescList_select;
            
            print(musttestroaddescList_select, "全部必测道路中");
        }

        //定位道路
        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            //
        }

        private void button1_Click(object sender, EventArgs e)
        {
            MainModel.FireDTDataChanged(this);
        }

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

    }
}