﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class TDScanCellInfo
    {
        public double Longitude
        {
            get
            {
                return Cell.Longitude;
            }
        }
        public double Latitude
        {
            get
            {
                return Cell.Latitude;
            }
        }
        public string CellName
        {
            get
            {
                return Cell.Name;
            }
        }
        public int Lac
        {
            get
            {
                return Cell.LAC;
            }
        }
        public int Ci
        {
            get
            {
                return Cell.CI;
            }
        }
        public int Freq
        {
            get
            {
                return Cell.FREQ;
            }
        }
        public int Cpi
        {
            get
            {
                return Cell.CPI;
            }
        }
        public string TypeDesc
        {
            get
            {
                return Cell.Type == TDNodeBType.Outdoor ? "室外" : "室内";
            }
        }
        public int TotalSampleCount
        {
            get;
            private set;
        }
        public int FirstSampleCount
        {
            get;
            private set;
        }
        public double TotalAvgRxlev
        {
            get
            {
                return Math.Round(totalAvgRxlev, 2);
            }
        }
        public double FirstAvgRxlev
        {
            get
            {
                return Math.Round(firstAvgRxlev, 2);
            }
        }

        public TDScanCellInfo(TDCell tdCell)
        {
            Cell = tdCell;
            totalAvgRxlev = 0;
            totalRxlevSum = 0;
            firstAvgRxlev = 0;
            firstRxlevSum = 0;
        }

        public void AppendRxlev(double rxlev, bool isFirst)
        {
            ++TotalSampleCount;
            totalRxlevSum += rxlev;
            totalAvgRxlev = totalRxlevSum / TotalSampleCount;

            if (isFirst)
            {
                ++FirstSampleCount;
                firstRxlevSum += rxlev;
                firstAvgRxlev = firstRxlevSum / FirstSampleCount;
            }
        }

        private readonly TDCell Cell;
        private double totalAvgRxlev;
        private double firstAvgRxlev;
        private double totalRxlevSum;
        private double firstRxlevSum;
    }

    public class TDScanCellInfoStater
    {
        private readonly Dictionary<TDCell, TDScanCellInfo> cellInfoDic;
        private readonly DTDisplayParameterInfo rscpParam;

        public TDScanCellInfoStater()
        {
            cellInfoDic = new Dictionary<TDCell, TDScanCellInfo>();
            rscpParam = DTDisplayParameterManager.GetInstance()["TDSCDMA_SCAN", "PCCPCH_RSCP"];
        }
        public void StatTestPoint(TestPoint tp)
        {
            for (int i = 0; i < 50; ++i)
            {
                TDCell cell = tp.GetCell_TDScan(i);
                float? rxlev = (float?)tp["TDS_PCCPCH_RSCP", i];
                if (cell == null || rxlev == null || rxlev >= rscpParam.ValueMax || rxlev <= rscpParam.ValueMin)
                {
                    break;
                }

                if (!cellInfoDic.ContainsKey(cell))
                {
                    cellInfoDic.Add(cell, new TDScanCellInfo(cell));
                }
                cellInfoDic[cell].AppendRxlev((double)rxlev, i == 0);
            }
        }
        public List<TDScanCellInfo> GetStatResult()
        {
            return new List<TDScanCellInfo>(cellInfoDic.Values);
        }
        public void Clear()
        {
            cellInfoDic.Clear();
        }
    }

    public class WScanCellInfo
    {
        public double Longitude
        {
            get
            {
                return Cell.Longitude;
            }
        }
        public double Latitude
        {
            get
            {
                return Cell.Latitude;
            }
        }
        public string CellName
        {
            get
            {
                return Cell.Name;
            }
        }
        public int Lac
        {
            get
            {
                return Cell.LAC;
            }
        }
        public int Ci
        {
            get
            {
                return Cell.CI;
            }
        }
        public int Freq
        {
            get
            {
                return Cell.UARFCN;
            }
        }
        public int Cpi
        {
            get
            {
                return Cell.PSC;
            }
        }
        public string TypeDesc
        {
            get
            {
                return Cell.Type == WNodeBType.Outdoor ? "室外" : "室内";
            }
        }
        public int TotalSampleCount
        {
            get;
            private set;
        }
        public int FirstSampleCount
        {
            get;
            private set;
        }
        public double TotalAvgRxlev
        {
            get
            {
                return Math.Round(totalAvgRxlev, 2);
            }
        }
        public double FirstAvgRxlev
        {
            get
            {
                return Math.Round(firstAvgRxlev, 2);
            }
        }

        public WScanCellInfo(WCell wCell)
        {
            Cell = wCell;
            totalAvgRxlev = 0;
            totalRxlevSum = 0;
            firstAvgRxlev = 0;
            firstRxlevSum = 0;
        }

        public void AppendRxlev(double rxlev, bool isFirst)
        {
            ++TotalSampleCount;
            totalRxlevSum += rxlev;
            totalAvgRxlev = totalRxlevSum / TotalSampleCount;

            if (isFirst)
            {
                ++FirstSampleCount;
                firstRxlevSum += rxlev;
                firstAvgRxlev = firstRxlevSum / FirstSampleCount;
            }
        }

        private readonly WCell Cell;
        private double totalAvgRxlev;
        private double firstAvgRxlev;
        private double totalRxlevSum;
        private double firstRxlevSum;
    }

    public class WScanCellInfoStater
    {
        private readonly Dictionary<WCell, WScanCellInfo> cellInfoDic;
        private readonly DTDisplayParameterInfo rscpParam;

        public WScanCellInfoStater()
        {
            cellInfoDic = new Dictionary<WCell, WScanCellInfo>();
            rscpParam = DTDisplayParameterManager.GetInstance()["WCDMA_SCAN", "CPICHTotalRSCP"];
        }
        public void StatTestPoint(TestPoint tp)
        {
            for (int i = 0; i < 50; ++i)
            {
                WCell cell = tp.GetCell_WScan(i);
                float? rxlev = (float?)tp["WS_CPICHTotalRSCP", i];
                if (cell == null || rxlev == null || rxlev >= rscpParam.ValueMax || rxlev <= rscpParam.ValueMin)
                {
                    break;
                }

                if (!cellInfoDic.ContainsKey(cell))
                {
                    cellInfoDic.Add(cell, new WScanCellInfo(cell));
                }
                cellInfoDic[cell].AppendRxlev((double)rxlev, i == 0);
            }
        }
        public List<WScanCellInfo> GetStatResult()
        {
            return new List<WScanCellInfo>(cellInfoDic.Values);
        }
        public void Clear()
        {
            cellInfoDic.Clear();
        }
    }
}
