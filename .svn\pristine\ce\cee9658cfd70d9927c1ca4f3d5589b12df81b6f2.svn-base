﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLowSpeedQuery_LteUep : ZTLowSpeedAnaByRegion_LTE
    {
        public ZTLowSpeedQuery_LteUep()
            : base()
        {
        }
        public override string Name
        {
            get { return "低速率路段分析_LTE_UEP"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 24000, 22006, this.Name);
        }

        protected override void init()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
            carrierID = CarrierType.ChinaMobile;
        }

        protected override float? getSpeed(TestPoint tp)
        {
            return (float?)tp["lte_uep_AppSpeed_Mb"];
        }

        protected override void getResultsAfterQuery()
        {
            MainModel.DTDataManager = newManager;
            MainModel.FireSetDefaultMapSerialTheme("LTE_UEP", "AppSpeed(Mbps)");
        }

        protected override void getLTEUepPointDetail(TempData data, ref List<string> lacciList, ref List<int> freqList)
        {
            #region  LTE 速率,pccpch_rscp,pccpch_c2i,dpch_rscp,dpch_c2i,bler,pdsch_rscp,pdsch_c2i,scch_rscp,scch_c2i等指标  最大值,最小值,均值
            foreach (TestPoint tPoint in tps)
            {
                int? speed = (int?)(float?)tPoint["lte_uep_AppSpeed"];
                float? rsrp = (float?)tPoint["lte_uep_RSRP"];
                float? sinr = (float?)tPoint["lte_uep_SINR"];
                int? throughput_DL = null;
                setSpeed(data, speed);
                setRsrp(data, rsrp);
                setSinr(data, sinr);
                setThroughputDL(data, throughput_DL);

                addTPParam(lacciList, freqList, tPoint);
                newManager.Add(tPoint);
            }
            #endregion
        }

        private void setSpeed(TempData data, int? speed)
        {
            if (speed != null && speed >= 0)
            {
                data.speed_sampleNum++;
                data.low_speed = speed > data.low_speed ? data.low_speed : (int)speed;
                data.high_speed = speed > data.high_speed ? (int)speed : data.high_speed;
                data.mean_speed += (int)speed;
                //data.distance_speed_0 += calculateDistance0Speed((int)speed, tPoint, tpPrev);
            }
        }

        private void setRsrp(TempData data, float? rsrp)
        {
            if (rsrp != null && rsrp >= -141 && rsrp <= 25)
            {
                data.rsrp_sampleNum++;
                data.low_rsrp = (rsrp > data.low_rsrp) ? data.low_rsrp : (float)rsrp;
                data.high_rsrp = (rsrp > data.high_rsrp) ? (float)rsrp : data.high_rsrp;
                data.mean_rsrp += (float)rsrp;
            }
        }

        private void setSinr(TempData data, float? sinr)
        {
            if (sinr != null && sinr >= -20 && sinr <= 50)
            {
                data.sinr_sampleNum++;
                data.low_sinr = (sinr > data.low_sinr) ? data.low_sinr : (int)sinr;
                data.high_sinr = (sinr > data.high_sinr) ? (int)sinr : data.high_sinr;
                data.mean_sinr += (int)sinr;
            }
        }

        private void setThroughputDL(TempData data, int? throughput_DL)
        {
            if (throughput_DL != null && throughput_DL >= 0)
            {
                data.throughput_DL_sampleNum++;
                data.low_throughput_DL = (throughput_DL > data.low_throughput_DL) ? data.low_throughput_DL : (float)throughput_DL;
                data.high_throughput_DL = (throughput_DL > data.high_throughput_DL) ? (float)throughput_DL : data.high_throughput_DL;
                data.mean_throughput_DL += (float)throughput_DL;
            }
        }

        private void addTPParam(List<string> lacciList, List<int> freqList, TestPoint tPoint)
        {
            int? lac = (int?)tPoint["lte_uep_TAC"];
            int? ci = (int?)tPoint["lte_uep_ECI"];
            int? bcch = (int?)tPoint["lte_uep_EARFCN"];
            if (ci != null && ci > 0)
            {
                string lacci = lac.ToString() + "_" + ci.ToString();
                if (!lacciList.Contains(lacci))
                {
                    lacciList.Add(lacci);
                }
            }
            if (bcch != null && bcch > 0 && bcch < 65535
                && !freqList.Contains((int)bcch))
            {
                freqList.Add((int)bcch);
            }
        }
    }

}
