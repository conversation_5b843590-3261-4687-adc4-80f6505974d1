using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakQualDetailSettingPanel : UserControl
    {
        public WeakQualDetailSettingPanel()
        {
            InitializeComponent();
        }

        public int RxlevMean
        {
            get { return (int)spinEditRxlevMean.Value; }
        }
        public int Total900
        {
            get { return (int)spinEditTotal900.Value; }
        }
        public int Total1800
        {
            get { return (int)spinEditTotal1800.Value; }
        }
        public float RoadStru
        {
            get { return (float)spinEditStru.Value; }
        }
        /*public int RoadStruPercent
        {
            get { return (int)spinEditStruPercent.Value; }
        }*/
    }
}
